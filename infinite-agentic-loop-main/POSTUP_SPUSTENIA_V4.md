# Postup Spustenia Generovania PM Komponentov v4

## Prehľad Novej v4 Špecifikácie

Nová v4 špecifikácia (`specs/invent_new_ui_v4.md`) je zameran<PERSON> na **projektový manažment** a obsahuje:

### Kľúčové Charakteristiky:
- **6 typov projektových dokumentov** (plány, technická, obchodná, procesná, komunikačná, kvalitná)
- **6 inteligentných funkcionalít** (ka<PERSON><PERSON><PERSON><PERSON><PERSON>, suggest<PERSON><PERSON>ne dopl<PERSON>, verzio<PERSON><PERSON> kontrola, deadline predikcia, template generovanie, cross-reference analýza)
- **6 projektových perspektív** (č<PERSON>ová, zdrojová, riziková, kvalit<PERSON>, stakeholder, deliverable)
- **6 manažérskych nástrojov** (dashboard, plánovanie, komunik<PERSON><PERSON>, analytika, kola<PERSON><PERSON>cia, templates)

### Špecializované Komponenty:
- **<PERSON><PERSON><PERSON><PERSON><PERSON>** (5 typov)
- **<PERSON><PERSON><PERSON><PERSON><PERSON>** (5 typov)
- **Analytick<PERSON> a Reportovacie** (5 typov)
- **Kolaboračné a Komunikačné** (5 typov)

## Kroky na Spustenie Generovania

### Krok 1: Príprava Výstupného Adresára
```bash
# Vytvorte nový adresár pre PM komponenty v4
mkdir -p infinite-agentic-loop-main/src_pm_v4
```

### Krok 2: Overenie Špecifikácie
```bash
# Skontrolujte, že špecifikácia existuje a je správna
ls -la infinite-agentic-loop-main/specs/invent_new_ui_v4.md
```

### Krok 3: Spustenie Infinite Agentic Loop
Použite Claude command s nasledujúcimi argumentmi:

```
@infinite specs/invent_new_ui_v4.md src_pm_v4 5
```

**Vysvetlenie argumentov:**
- `specs/invent_new_ui_v4.md` - cesta k novej v4 špecifikácii
- `src_pm_v4` - výstupný adresár pre PM komponenty
- `5` - počet iterácií na vygenerovanie (alebo "infinite" pre nekonečné generovanie)

### Krok 4: Alternatívne Manuálne Spustenie

Ak infinite command nefunguje, môžete spustiť manuálne:

#### A. Analýza Špecifikácie
1. Prečítajte si `specs/invent_new_ui_v4.md`
2. Identifikujte kľúčové charakteristiky
3. Naplánujte prvých 5 komponentov

#### B. Generovanie Prvej Vlny (Iterácie 1-5)
Vytvorte komponenty s týmito charakteristikami:

**pm_component_1.html** - Inteligentný Dokumentový Hub
- Typ: Dokumentové Manažment
- Funkcie: AI kategorizácia, verziová kontrola, search
- Perspektíva: Dokumentová organizácia

**pm_component_2.html** - Adaptívny Gantt Plánovač
- Typ: Projektové Plánovanie  
- Funkcie: Auto-optimalizácia, resource scheduling
- Perspektíva: Časová a zdrojová

**pm_component_3.html** - Project Health Dashboard
- Typ: Analytické a Reportovacie
- Funkcie: Real-time monitoring, KPI tracking
- Perspektíva: Kvalitná a riziková

**pm_component_4.html** - Smart Meeting Planner
- Typ: Kolaboračné a Komunikačné
- Funkcie: AI-assisted planning, agenda management
- Perspektíva: Stakeholder komunikácia

**pm_component_5.html** - Template Generátor
- Typ: Dokumentové Manažment
- Funkcie: Automatické vytváranie šablón, standardizácia
- Perspektíva: Procesná optimalizácia

## Technické Požiadavky na Komponenty

### Povinné Elementy v Každom Komponente:
1. **HTML štruktúra** podľa v4 template
2. **Pomenovanie súboru**: `pm_component_[cislo].html`
3. **Slovenský jazyk** vo všetkých textoch
4. **Realistické projektové dáta** (názvy projektov, dokumenty, deadlines)
5. **Interaktívne funkcie** (klikanie, filtrovanie, editácia)

### Povinné CSS Triedy:
```css
.pm-component { /* Hlavný kontajner */ }
.pm-header { /* Hlavička komponentu */ }
.pm-content { /* Hlavný obsah */ }
.pm-sidebar { /* Bočný panel ak potrebný */ }
.pm-actions { /* Akčné tlačidlá */ }
.pm-status { /* Status indikátory */ }
.pm-data { /* Dátové sekcie */ }
```

### Povinné JavaScript Funkcie:
```javascript
// Inicializácia komponentu
function initPMComponent() { }

// Načítanie projektových dát
function loadProjectData() { }

// Aktualizácia UI na základe dát
function updateUI() { }

// Spracovanie používateľských akcií
function handleUserAction() { }

// Automatické návrhy a optimalizácie
function provideSuggestions() { }
```

## Kontrolný Zoznam pre Každý Komponent

### Funkčnosť:
- [ ] Komponent sa načíta bez chýb
- [ ] Všetky interaktívne elementy fungujú
- [ ] Dáta sa zobrazujú správne
- [ ] Automatické funkcie pracujú
- [ ] Responsive dizajn funguje

### Obsah:
- [ ] Slovenský jazyk vo všetkých textoch
- [ ] Realistické projektové scenáre
- [ ] Konzistentné pomenovanie
- [ ] Správne kategorizácia podľa v4 spec
- [ ] Implementované kľúčové charakteristiky

### Technické:
- [ ] Validný HTML5 kód
- [ ] CSS bez chýb
- [ ] JavaScript bez syntax errors
- [ ] Optimalizovaný výkon
- [ ] Cross-browser kompatibilita

## Očakávané Výsledky

Po úspešnom spustení budete mať:

1. **Adresár `src_pm_v4/`** s 5 novými PM komponentmi
2. **Súbory `pm_component_1.html` až `pm_component_5.html`**
3. **Každý komponent** implementuje špecifické PM funkcionality
4. **Všetky komponenty** sú v slovenskom jazyku
5. **Realistické projektové dáta** v každom komponente

## Riešenie Problémov

### Ak sa komponenty negenerujú:
1. Skontrolujte syntax v `specs/invent_new_ui_v4.md`
2. Overte, že adresár `src_pm_v4` existuje
3. Skúste manuálne vytvoriť prvý komponent ako test

### Ak komponenty obsahujú chyby:
1. Skontrolujte HTML validáciu
2. Otestujte JavaScript v browser console
3. Overte CSS syntax

### Ak obsah nie je v slovenčine:
1. Skontrolujte `lang="sk"` v HTML
2. Overte, že v4 spec obsahuje slovenské texty
3. Manuálne upravte texty ak potrebné

## Ďalšie Kroky

Po úspešnom vygenerovaní prvých 5 komponentov môžete:

1. **Spustiť ďalšiu vlnu** s argumentom `@infinite specs/invent_new_ui_v4.md src_pm_v4 infinite`
2. **Testovať komponenty** otvorením v prehliadači
3. **Upraviť špecifikáciu** ak potrebujete zmeny
4. **Vytvoriť demo stránku** s odkazmi na všetky komponenty

## Kontakt a Podpora

Ak máte problémy so spustením, skontrolujte:
- Syntax v špecifikačnom súbore
- Existenciu výstupného adresára  
- Správnosť argumentov pre infinite command
