<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cards Enhanced</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary: #ec4899;
            --success: #10b981;
            --danger: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            --radius: 12px;
            --radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            background: var(--gray-50);
            padding: 2rem 1rem;
            min-height: 100vh;
        }

        main {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: var(--gray-600);
            font-size: 1.125rem;
            margin-bottom: 3rem;
        }

        /* Card Grid */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        /* Base Card Styles */
        .card {
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            position: relative;
            transition: var(--transition);
            transform-style: preserve-3d;
            cursor: pointer;
            will-change: transform, box-shadow;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .card.selected {
            outline: 3px solid var(--primary);
            outline-offset: -3px;
        }

        /* Card Image with Lazy Loading */
        .card-image {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            background: var(--gray-200);
        }

        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), filter 0.3s ease;
        }

        .card:hover .card-image img {
            transform: scale(1.05);
        }

        .card-image.loading::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .card-image img.blur-up {
            filter: blur(20px);
        }

        .card-image img.loaded {
            filter: blur(0);
        }

        /* Card Content */
        .card-content {
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .card-description {
            color: var(--gray-600);
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        /* Action Overlay */
        .card-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transform: translateY(-10px);
            transition: var(--transition);
            z-index: 10;
        }

        .card:hover .card-actions,
        .card:focus-within .card-actions {
            opacity: 1;
            transform: translateY(0);
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
            box-shadow: var(--shadow-md);
        }

        .action-btn:hover {
            background: white;
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .action-btn svg {
            width: 20px;
            height: 20px;
            stroke: var(--gray-700);
            stroke-width: 2;
            fill: none;
        }

        .action-btn.liked svg {
            fill: var(--danger);
            stroke: var(--danger);
        }

        /* Flip Card */
        .flip-card {
            perspective: 1000px;
            height: 400px;
        }

        .flip-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }

        .flip-card.flipped .flip-card-inner {
            transform: rotateY(180deg);
        }

        .flip-card-front,
        .flip-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .flip-card-back {
            transform: rotateY(180deg);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .flip-trigger {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-fast);
            z-index: 10;
        }

        .flip-trigger:hover {
            transform: scale(1.1);
            background: var(--primary-dark);
        }

        /* Expandable Card */
        .expandable-card {
            overflow: visible;
        }

        .expandable-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .expandable-card.expanded .expandable-content {
            max-height: 300px;
        }

        .expand-trigger {
            background: none;
            border: none;
            color: var(--primary);
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition-fast);
            margin-top: 1rem;
        }

        .expand-trigger:hover {
            color: var(--primary-dark);
        }

        .expand-trigger svg {
            width: 20px;
            height: 20px;
            transition: transform 0.3s ease;
        }

        .expandable-card.expanded .expand-trigger svg {
            transform: rotate(180deg);
        }

        /* Loading Skeleton */
        .card-skeleton {
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .skeleton {
            background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .skeleton-image {
            height: 200px;
        }

        .skeleton-content {
            padding: 1.5rem;
        }

        .skeleton-title {
            height: 24px;
            width: 70%;
            margin-bottom: 1rem;
            border-radius: 4px;
        }

        .skeleton-text {
            height: 16px;
            margin-bottom: 0.5rem;
            border-radius: 4px;
        }

        .skeleton-text:last-child {
            width: 80%;
        }

        /* Selection Mode */
        .selection-mode {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem 1.5rem;
            background: white;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
        }

        .selection-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .selection-count {
            color: var(--gray-600);
            font-weight: 500;
        }

        .selection-actions {
            margin-left: auto;
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 0.875rem;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        /* Mobile Touch Support */
        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .card {
                touch-action: pan-y;
                user-select: none;
            }

            .card.swiping {
                transition: none;
            }

            .card-actions {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Focus Styles */
        .card:focus-visible,
        .action-btn:focus-visible,
        .flip-trigger:focus-visible,
        .expand-trigger:focus-visible {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        /* Print Styles */
        @media print {
            .card {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid var(--gray-300);
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Cards - Enhanced</h1>
        <p class="subtitle">Beautiful, interactive cards with smooth animations and intelligent behaviors</p>

        <!-- Selection Mode -->
        <div class="selection-mode" id="selectionMode" style="display: none;">
            <input type="checkbox" class="selection-checkbox" id="selectAll">
            <span class="selection-count">0 selected</span>
            <div class="selection-actions">
                <button class="btn btn-primary">Share</button>
                <button class="btn btn-secondary">Archive</button>
            </div>
        </div>

        <!-- Card Grid -->
        <div class="card-grid" id="cardGrid">
            <!-- Loading Skeletons -->
            <div class="card card-skeleton">
                <div class="skeleton skeleton-image"></div>
                <div class="skeleton-content">
                    <div class="skeleton skeleton-title"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                </div>
            </div>
            <div class="card card-skeleton">
                <div class="skeleton skeleton-image"></div>
                <div class="skeleton-content">
                    <div class="skeleton skeleton-title"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                </div>
            </div>
            <div class="card card-skeleton">
                <div class="skeleton skeleton-image"></div>
                <div class="skeleton-content">
                    <div class="skeleton skeleton-title"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                </div>
            </div>
        </div>

        <!-- Special Cards Section -->
        <h2 style="margin: 3rem 0 1.5rem; font-size: 1.875rem; font-weight: 700;">Special Card Types</h2>
        
        <div class="card-grid">
            <!-- Flip Card -->
            <div class="flip-card" id="flipCard">
                <div class="flip-card-inner">
                    <div class="flip-card-front card">
                        <div class="card-image">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200'%3E%3Cdefs%3E%3ClinearGradient id='g' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234f46e5;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23ec4899;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='400' height='200' fill='url(%23g)'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='white' font-family='Arial' font-size='24' font-weight='bold'%3EFlip Me%3C/text%3E%3C/svg%3E" alt="Flip card front">
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Interactive Flip Card</h3>
                            <p class="card-description">Click the button to reveal additional information on the back of this card.</p>
                            <button class="flip-trigger" aria-label="Flip card">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flip-card-back">
                        <h3 style="font-size: 1.5rem; margin-bottom: 1rem;">Additional Details</h3>
                        <p style="margin-bottom: 1.5rem;">This is the back of the card with more detailed information that was hidden from the initial view.</p>
                        <ul style="text-align: left; margin-bottom: 1.5rem;">
                            <li>Hidden feature #1</li>
                            <li>Secret detail #2</li>
                            <li>Extra information #3</li>
                        </ul>
                        <button class="btn" style="background: white; color: var(--primary);" onclick="document.getElementById('flipCard').classList.remove('flipped')">
                            Flip Back
                        </button>
                    </div>
                </div>
            </div>

            <!-- Expandable Card -->
            <div class="card expandable-card" id="expandableCard">
                <div class="card-image">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200'%3E%3Cdefs%3E%3ClinearGradient id='g2' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%2310b981;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%236366f1;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='400' height='200' fill='url(%23g2)'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='white' font-family='Arial' font-size='24' font-weight='bold'%3EExpandable%3C/text%3E%3C/svg%3E" alt="Expandable card">
                </div>
                <div class="card-content">
                    <h3 class="card-title">Expandable Content Card</h3>
                    <p class="card-description">This card can expand to show additional content with a smooth animation.</p>
                    <button class="expand-trigger">
                        <span>Show more</span>
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M19 9l-7 7-7-7"/>
                        </svg>
                    </button>
                    <div class="expandable-content">
                        <div style="padding-top: 1rem; border-top: 1px solid var(--gray-200);">
                            <h4 style="font-weight: 600; margin-bottom: 0.5rem;">Extended Information</h4>
                            <p style="color: var(--gray-600); margin-bottom: 1rem;">Here's the additional content that was hidden. It smoothly animates into view when expanded.</p>
                            <ul style="color: var(--gray-600); padding-left: 1.5rem;">
                                <li>Extra detail that wasn't visible before</li>
                                <li>Another piece of hidden information</li>
                                <li>More content to explore</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn" aria-label="Like">
                        <svg viewBox="0 0 24 24">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                        </svg>
                    </button>
                    <button class="action-btn" aria-label="Share">
                        <svg viewBox="0 0 24 24">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                            <polyline points="16 6 12 2 8 6"/>
                            <line x1="12" y1="2" x2="12" y2="15"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Card data
        const cardData = [
            {
                title: "Mountain Vista",
                description: "Experience breathtaking views from the mountain peaks with crystal clear skies.",
                image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop",
                author: "Alex Chen",
                date: "2 days ago"
            },
            {
                title: "Ocean Breeze",
                description: "Feel the calming waves and gentle sea breeze at this pristine beach location.",
                image: "https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=400&h=200&fit=crop",
                author: "Sarah Miller",
                date: "1 week ago"
            },
            {
                title: "Forest Trail",
                description: "Discover hidden paths through ancient forests filled with wildlife and wonder.",
                image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=200&fit=crop",
                author: "David Park",
                date: "3 days ago"
            },
            {
                title: "Desert Sunset",
                description: "Watch the sun paint the desert landscape in brilliant shades of orange and red.",
                image: "https://images.unsplash.com/photo-1509316785289-025f5b846b35?w=400&h=200&fit=crop",
                author: "Maria Garcia",
                date: "5 days ago"
            },
            {
                title: "City Lights",
                description: "Experience the vibrant energy of the city as it comes alive after dark.",
                image: "https://images.unsplash.com/photo-1514565131-fce0801e5785?w=400&h=200&fit=crop",
                author: "James Wilson",
                date: "1 day ago"
            },
            {
                title: "Lakeside Calm",
                description: "Find peace and tranquility by the still waters of this mountain lake.",
                image: "https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=400&h=200&fit=crop",
                author: "Emma Thompson",
                date: "4 days ago"
            }
        ];

        // Create card HTML
        function createCard(data, index) {
            return `
                <div class="card" data-index="${index}" tabindex="0" role="article" aria-label="${data.title}">
                    <div class="card-image loading">
                        <img 
                            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='10'%3E%3Crect width='20' height='10' fill='%23e5e7eb'/%3E%3C/svg%3E"
                            data-src="${data.image}" 
                            alt="${data.title}"
                            class="blur-up"
                        >
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">${data.title}</h3>
                        <p class="card-description">${data.description}</p>
                        <div class="card-meta">
                            <span>${data.author}</span>
                            <span>${data.date}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="action-btn like-btn" aria-label="Like ${data.title}">
                            <svg viewBox="0 0 24 24">
                                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                            </svg>
                        </button>
                        <button class="action-btn" aria-label="Share ${data.title}">
                            <svg viewBox="0 0 24 24">
                                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                                <polyline points="16 6 12 2 8 6"/>
                                <line x1="12" y1="2" x2="12" y2="15"/>
                            </svg>
                        </button>
                        <button class="action-btn" aria-label="More options for ${data.title}">
                            <svg viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="1"/>
                                <circle cx="12" cy="5" r="1"/>
                                <circle cx="12" cy="19" r="1"/>
                            </svg>
                        </button>
                    </div>
                </div>
            `;
        }

        // Initialize cards
        const cardGrid = document.getElementById('cardGrid');
        setTimeout(() => {
            cardGrid.innerHTML = cardData.map((data, index) => createCard(data, index)).join('');
            initializeCards();
        }, 1500);

        // Initialize card functionality
        function initializeCards() {
            // Lazy loading images
            const images = document.querySelectorAll('.card-image img[data-src]');
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const parent = img.parentElement;
                        
                        // Load high-res image
                        const highResImg = new Image();
                        highResImg.src = img.dataset.src;
                        highResImg.onload = () => {
                            img.src = highResImg.src;
                            setTimeout(() => {
                                img.classList.add('loaded');
                                parent.classList.remove('loading');
                            }, 50);
                        };
                        
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));

            // Like button functionality
            document.querySelectorAll('.like-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    btn.classList.toggle('liked');
                    
                    // Haptic feedback on mobile
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10);
                    }
                });
            });

            // Card selection
            let selectedCards = new Set();
            let selectionMode = false;

            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('click', (e) => {
                    if (e.target.closest('.action-btn') || e.target.closest('.flip-trigger') || e.target.closest('.expand-trigger')) {
                        return;
                    }

                    if (e.shiftKey || selectionMode) {
                        const index = card.dataset.index;
                        if (selectedCards.has(index)) {
                            selectedCards.delete(index);
                            card.classList.remove('selected');
                        } else {
                            selectedCards.add(index);
                            card.classList.add('selected');
                        }
                        updateSelectionMode();
                    }
                });

                // Keyboard navigation
                card.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        card.click();
                    }
                });
            });

            // Selection mode controls
            function updateSelectionMode() {
                const selectionModeEl = document.getElementById('selectionMode');
                if (selectedCards.size > 0) {
                    selectionMode = true;
                    selectionModeEl.style.display = 'flex';
                    selectionModeEl.querySelector('.selection-count').textContent = `${selectedCards.size} selected`;
                } else {
                    selectionMode = false;
                    selectionModeEl.style.display = 'none';
                }
            }

            document.getElementById('selectAll').addEventListener('change', (e) => {
                const cards = document.querySelectorAll('.card[data-index]');
                if (e.target.checked) {
                    cards.forEach(card => {
                        selectedCards.add(card.dataset.index);
                        card.classList.add('selected');
                    });
                } else {
                    selectedCards.clear();
                    cards.forEach(card => card.classList.remove('selected'));
                }
                updateSelectionMode();
            });

            // Touch gestures for mobile
            let touchStartX = 0;
            let touchStartY = 0;
            let currentCard = null;

            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('touchstart', (e) => {
                    touchStartX = e.touches[0].clientX;
                    touchStartY = e.touches[0].clientY;
                    currentCard = card;
                }, { passive: true });

                card.addEventListener('touchmove', (e) => {
                    if (!touchStartX || !currentCard) return;

                    const touchEndX = e.touches[0].clientX;
                    const touchEndY = e.touches[0].clientY;
                    const diffX = touchStartX - touchEndX;
                    const diffY = touchStartY - touchEndY;

                    // Only handle horizontal swipes
                    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                        currentCard.classList.add('swiping');
                        currentCard.style.transform = `translateX(${-diffX * 0.5}px)`;
                    }
                }, { passive: true });

                card.addEventListener('touchend', () => {
                    if (currentCard) {
                        currentCard.classList.remove('swiping');
                        currentCard.style.transform = '';
                        currentCard = null;
                    }
                    touchStartX = 0;
                    touchStartY = 0;
                });
            });
        }

        // Flip card functionality
        document.querySelector('.flip-trigger').addEventListener('click', (e) => {
            e.stopPropagation();
            document.getElementById('flipCard').classList.add('flipped');
        });

        // Expandable card functionality
        document.querySelector('.expand-trigger').addEventListener('click', (e) => {
            e.stopPropagation();
            const card = document.getElementById('expandableCard');
            const isExpanded = card.classList.contains('expanded');
            card.classList.toggle('expanded');
            
            const trigger = e.currentTarget;
            const text = trigger.querySelector('span');
            text.textContent = isExpanded ? 'Show more' : 'Show less';
            
            // Announce state change for screen readers
            const announcement = document.createElement('div');
            announcement.setAttribute('role', 'status');
            announcement.setAttribute('aria-live', 'polite');
            announcement.className = 'sr-only';
            announcement.textContent = isExpanded ? 'Content collapsed' : 'Content expanded';
            document.body.appendChild(announcement);
            setTimeout(() => announcement.remove(), 1000);
        });

        // Respect reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--transition', 'none');
            document.documentElement.style.setProperty('--transition-fast', 'none');
        }

        // Screen reader only content
        const style = document.createElement('style');
        style.textContent = '.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); white-space: nowrap; border: 0; }';
        document.head.appendChild(style);
    </script>
</body>
</html>