<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Enhanced</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f5f7fa;
            padding: 40px 20px;
            line-height: 1.6;
            color: #2c3e50;
        }

        main {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 40px;
            text-align: center;
            color: #1a1a1a;
            font-weight: 600;
        }

        .component-showcase {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .button-group h3 {
            font-size: 1.1rem;
            color: #555;
            font-weight: 500;
            margin-bottom: 8px;
        }

        /* Enhanced Button Base Styles */
        .btn {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            font-family: inherit;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            user-select: none;
            text-decoration: none;
            white-space: nowrap;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-style: preserve-3d;
            overflow: hidden;
            outline: none;
            min-width: 120px;
        }

        /* Ripple Effect Container */
        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        /* Button Variants */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .btn-secondary:hover:not(:disabled) {
            background: #cbd5e1;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .btn-success {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        }

        .btn-success:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .btn-danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
        }

        /* Active State */
        .btn:active:not(:disabled) {
            transform: translateY(0) scale(0.98);
        }

        /* Focus State */
        .btn:focus-visible {
            outline: 3px solid currentColor;
            outline-offset: 3px;
        }

        /* Disabled State */
        .btn:disabled {
            cursor: not-allowed;
            opacity: 0.6;
            transform: none !important;
        }

        /* Loading State */
        .btn-loading {
            color: transparent;
            pointer-events: none;
        }

        .btn-loading .btn-text {
            visibility: hidden;
        }

        /* Spinner */
        .spinner {
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .btn-loading .spinner {
            opacity: 1;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Success Animation */
        .btn-success-state {
            background: #22c55e !important;
            color: white !important;
        }

        .success-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .btn-success-state .success-icon {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }

        .btn-success-state .btn-text {
            visibility: hidden;
        }

        /* Error Animation */
        .btn-error {
            animation: shake 0.5s;
            background: #ef4444 !important;
            color: white !important;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        /* Icon Support */
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        .btn-icon svg {
            width: 100%;
            height: 100%;
        }

        /* Progress Bar */
        .btn-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 0 0 8px 8px;
            overflow: hidden;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .btn-loading-progress .btn-progress {
            opacity: 1;
        }

        .btn-progress-bar {
            height: 100%;
            background: white;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Button Groups */
        .btn-group-horizontal {
            display: inline-flex;
            gap: 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-group-horizontal .btn {
            border-radius: 0;
            margin: 0;
            box-shadow: none;
        }

        .btn-group-horizontal .btn:first-child {
            border-radius: 8px 0 0 8px;
        }

        .btn-group-horizontal .btn:last-child {
            border-radius: 0 8px 8px 0;
        }

        .btn-group-horizontal .btn:not(:last-child) {
            border-right: 1px solid rgba(255,255,255,0.2);
        }

        /* Tooltip */
        .tooltip {
            position: absolute;
            bottom: calc(100% + 10px);
            left: 50%;
            transform: translateX(-50%) scale(0.8);
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0,0,0,0.9);
        }

        .btn:hover .tooltip {
            opacity: 1;
            transform: translateX(-50%) scale(1);
        }

        /* Demo Section */
        .demo-section {
            margin-top: 40px;
            padding-top: 40px;
            border-top: 1px solid #e5e7eb;
        }

        .demo-controls {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin-bottom: 24px;
        }

        .code-snippet {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin-top: 20px;
        }

        /* Mobile Optimization */
        @media (max-width: 768px) {
            .button-grid {
                grid-template-columns: 1fr;
            }

            .btn-group-horizontal {
                flex-direction: column;
                width: 100%;
            }

            .btn-group-horizontal .btn {
                border-radius: 0;
                border-right: none !important;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            }

            .btn-group-horizontal .btn:first-child {
                border-radius: 8px 8px 0 0;
            }

            .btn-group-horizontal .btn:last-child {
                border-radius: 0 0 8px 8px;
                border-bottom: none;
            }
        }

        /* Reduced Motion */
        @media (prefers-reduced-motion: reduce) {
            .btn {
                transition: none;
            }

            .spinner {
                animation: none;
                opacity: 1;
            }

            .btn-error {
                animation: none;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Button - Enhanced</h1>
        
        <div class="component-showcase">
            <div class="button-grid">
                <!-- Primary Buttons -->
                <div class="button-group">
                    <h3>Primary Actions</h3>
                    <button class="btn btn-primary" onclick="handleClick(this)">
                        <span class="btn-text">Click Me</span>
                        <div class="spinner"></div>
                        <svg class="success-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.7 5.3L8 14L3.3 9.3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                    
                    <button class="btn btn-primary" onclick="handleProgressClick(this)">
                        <span class="btn-text">Upload File</span>
                        <div class="spinner"></div>
                        <div class="btn-progress">
                            <div class="btn-progress-bar"></div>
                        </div>
                    </button>
                    
                    <button class="btn btn-primary" disabled>
                        <span class="btn-text">Disabled</span>
                        <span class="tooltip">Complete form to enable</span>
                    </button>
                </div>

                <!-- Secondary Buttons -->
                <div class="button-group">
                    <h3>Secondary Actions</h3>
                    <button class="btn btn-secondary" onclick="handleClick(this)">
                        <span class="btn-text">Save Draft</span>
                        <div class="spinner"></div>
                        <svg class="success-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.7 5.3L8 14L3.3 9.3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                    
                    <button class="btn btn-secondary">
                        <span class="btn-icon">
                            <svg viewBox="0 0 20 20" fill="currentColor">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                            </svg>
                        </span>
                        <span class="btn-text">Edit</span>
                    </button>
                    
                    <button class="btn btn-secondary">
                        <span class="btn-icon">
                            <svg viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                            </svg>
                        </span>
                        <span class="btn-text">Add Item</span>
                    </button>
                </div>

                <!-- Success/Danger Buttons -->
                <div class="button-group">
                    <h3>Contextual Actions</h3>
                    <button class="btn btn-success" onclick="handleClick(this)">
                        <span class="btn-text">Confirm</span>
                        <div class="spinner"></div>
                        <svg class="success-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.7 5.3L8 14L3.3 9.3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                    
                    <button class="btn btn-danger" onclick="handleErrorClick(this)">
                        <span class="btn-text">Delete</span>
                        <div class="spinner"></div>
                    </button>
                    
                    <button class="btn btn-danger" disabled>
                        <span class="btn-text">Permanently Delete</span>
                        <span class="tooltip">Action cannot be undone</span>
                    </button>
                </div>
            </div>

            <!-- Button Groups -->
            <div class="demo-section">
                <h3 style="margin-bottom: 20px;">Button Groups</h3>
                
                <div class="btn-group-horizontal">
                    <button class="btn btn-primary" onclick="handleClick(this)">
                        <span class="btn-text">Previous</span>
                        <div class="spinner"></div>
                    </button>
                    <button class="btn btn-primary" onclick="handleClick(this)">
                        <span class="btn-text">Current</span>
                        <div class="spinner"></div>
                    </button>
                    <button class="btn btn-primary" onclick="handleClick(this)">
                        <span class="btn-text">Next</span>
                        <div class="spinner"></div>
                    </button>
                </div>
            </div>

            <!-- Interactive Demo -->
            <div class="demo-section">
                <h3 style="margin-bottom: 20px;">Interactive Demo</h3>
                <div class="demo-controls">
                    <button class="btn btn-primary" onclick="simulateLoading(this)">
                        <span class="btn-text">Simulate Loading</span>
                        <div class="spinner"></div>
                        <svg class="success-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.7 5.3L8 14L3.3 9.3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                    
                    <button class="btn btn-secondary" onclick="simulateProgress(this)">
                        <span class="btn-text">Simulate Progress</span>
                        <div class="spinner"></div>
                        <div class="btn-progress">
                            <div class="btn-progress-bar"></div>
                        </div>
                    </button>
                    
                    <button class="btn btn-danger" onclick="simulateError(this)">
                        <span class="btn-text">Simulate Error</span>
                        <div class="spinner"></div>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Ripple effect for all buttons
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                if (this.disabled) return;
                
                const rect = this.getBoundingClientRect();
                const ripple = document.createElement('span');
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    top: ${y}px;
                    left: ${x}px;
                    pointer-events: none;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                `;
                
                this.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            });
        });

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Basic click handler with loading state
        function handleClick(button) {
            if (button.classList.contains('btn-loading')) return;
            
            button.classList.add('btn-loading');
            
            setTimeout(() => {
                button.classList.remove('btn-loading');
                button.classList.add('btn-success-state');
                
                setTimeout(() => {
                    button.classList.remove('btn-success-state');
                }, 1500);
            }, 1500);
        }

        // Progress click handler
        function handleProgressClick(button) {
            if (button.classList.contains('btn-loading')) return;
            
            button.classList.add('btn-loading', 'btn-loading-progress');
            const progressBar = button.querySelector('.btn-progress-bar');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;
                
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        button.classList.remove('btn-loading', 'btn-loading-progress');
                        button.classList.add('btn-success-state');
                        progressBar.style.width = '0%';
                        
                        setTimeout(() => {
                            button.classList.remove('btn-success-state');
                        }, 1500);
                    }, 300);
                }
            }, 200);
        }

        // Error click handler
        function handleErrorClick(button) {
            if (button.classList.contains('btn-loading')) return;
            
            button.classList.add('btn-loading');
            
            setTimeout(() => {
                button.classList.remove('btn-loading');
                button.classList.add('btn-error');
                
                setTimeout(() => {
                    button.classList.remove('btn-error');
                }, 500);
            }, 1000);
        }

        // Demo functions
        function simulateLoading(button) {
            handleClick(button);
        }

        function simulateProgress(button) {
            handleProgressClick(button);
        }

        function simulateError(button) {
            handleErrorClick(button);
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                const activeElement = document.activeElement;
                if (activeElement && activeElement.classList.contains('btn')) {
                    e.preventDefault();
                    activeElement.click();
                }
            }
        });

        // Touch feedback enhancement
        let touchTimeout;
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
                clearTimeout(touchTimeout);
            });
            
            button.addEventListener('touchend', function() {
                touchTimeout = setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            });
        });

        // Accessibility announcements
        const announcer = document.createElement('div');
        announcer.setAttribute('role', 'status');
        announcer.setAttribute('aria-live', 'polite');
        announcer.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
        document.body.appendChild(announcer);

        function announce(message) {
            announcer.textContent = message;
            setTimeout(() => announcer.textContent = '', 1000);
        }

        // Enhance button clicks with announcements
        const originalHandleClick = window.handleClick;
        window.handleClick = function(button) {
            originalHandleClick(button);
            announce('Processing...');
            setTimeout(() => announce('Action completed successfully'), 1500);
        };

        // Focus management for button groups
        document.querySelectorAll('.btn-group-horizontal').forEach(group => {
            const buttons = group.querySelectorAll('.btn');
            
            buttons.forEach((btn, index) => {
                btn.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowRight' && index < buttons.length - 1) {
                        e.preventDefault();
                        buttons[index + 1].focus();
                    } else if (e.key === 'ArrowLeft' && index > 0) {
                        e.preventDefault();
                        buttons[index - 1].focus();
                    }
                });
            });
        });
    </script>
</body>
</html>