<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Notifications Enhanced</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Main container */
        main {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Demo controls */
        .controls {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .control-group {
            margin-bottom: 1.5rem;
        }

        .control-group h3 {
            margin-bottom: 0.5rem;
            color: #4a5568;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .demo-button {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .demo-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .demo-button:hover::before {
            width: 300px;
            height: 300px;
        }

        .demo-button:active {
            transform: scale(0.98);
        }

        .demo-button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .demo-button.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .demo-button.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .demo-button.info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }

        /* Position selector */
        .position-selector {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .position-button {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .position-button:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .position-button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* Toast container positions */
        .toast-container {
            position: fixed;
            z-index: 1000;
            pointer-events: none;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            transition: all 0.3s ease;
        }

        .toast-container.top-right {
            top: 0;
            right: 0;
            align-items: flex-end;
        }

        .toast-container.top-left {
            top: 0;
            left: 0;
            align-items: flex-start;
        }

        .toast-container.top-center {
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            align-items: center;
        }

        .toast-container.bottom-right {
            bottom: 0;
            right: 0;
            align-items: flex-end;
            flex-direction: column-reverse;
        }

        .toast-container.bottom-left {
            bottom: 0;
            left: 0;
            align-items: flex-start;
            flex-direction: column-reverse;
        }

        .toast-container.bottom-center {
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            align-items: center;
            flex-direction: column-reverse;
        }

        /* Toast styles */
        .toast {
            pointer-events: auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            min-width: 300px;
            max-width: 500px;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            animation: slideIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            transition: all 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .toast-container.top-left .toast,
        .toast-container.bottom-left .toast {
            animation-name: slideInLeft;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .toast-container.top-center .toast,
        .toast-container.bottom-center .toast {
            animation-name: slideInTop;
        }

        @keyframes slideInTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .toast.removing {
            animation: slideOut 0.3s ease forwards;
        }

        @keyframes slideOut {
            to {
                transform: translateX(110%);
                opacity: 0;
            }
        }

        .toast-container.top-left .toast.removing,
        .toast-container.bottom-left .toast.removing {
            animation-name: slideOutLeft;
        }

        @keyframes slideOutLeft {
            to {
                transform: translateX(-110%);
                opacity: 0;
            }
        }

        .toast:hover {
            transform: scale(1.02);
            box-shadow: 0 15px 50px rgba(0,0,0,0.15);
        }

        /* Toast icon */
        .toast-icon {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            position: relative;
        }

        .toast-icon svg {
            width: 100%;
            height: 100%;
        }

        .toast.success .toast-icon {
            color: #10b981;
        }

        .toast.error .toast-icon {
            color: #ef4444;
        }

        .toast.warning .toast-icon {
            color: #f59e0b;
        }

        .toast.info .toast-icon {
            color: #3b82f6;
        }

        /* Toast content */
        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.25rem;
            color: #1a202c;
        }

        .toast-message {
            font-size: 0.875rem;
            color: #4a5568;
        }

        /* Toast actions */
        .toast-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }

        .toast-action {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            background: rgba(0,0,0,0.05);
            color: #4a5568;
        }

        .toast-action:hover {
            background: rgba(0,0,0,0.1);
        }

        .toast-action.primary {
            background: #667eea;
            color: white;
        }

        .toast-action.primary:hover {
            background: #5a67d8;
        }

        /* Close button */
        .toast-close {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: none;
            cursor: pointer;
            color: #a0aec0;
            transition: all 0.2s ease;
            border-radius: 4px;
        }

        .toast-close:hover {
            background: rgba(0,0,0,0.05);
            color: #4a5568;
        }

        /* Progress bar */
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: currentColor;
            opacity: 0.2;
            transition: width linear;
        }

        .toast.success .toast-progress {
            color: #10b981;
        }

        .toast.error .toast-progress {
            color: #ef4444;
        }

        .toast.warning .toast-progress {
            color: #f59e0b;
        }

        .toast.info .toast-progress {
            color: #3b82f6;
        }

        /* Mobile responsiveness */
        @media (max-width: 640px) {
            .toast {
                min-width: calc(100vw - 2rem);
                max-width: calc(100vw - 2rem);
            }
            
            .toast-container {
                padding: 0.5rem;
            }
            
            .button-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility focus styles */
        .toast:focus-visible,
        .toast-action:focus-visible,
        .toast-close:focus-visible {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .toast {
                animation: fadeIn 0.3s ease;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            .toast.removing {
                animation: fadeOut 0.3s ease forwards;
            }
            
            @keyframes fadeOut {
                to { opacity: 0; }
            }
        }

        /* Stats display */
        .stats {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            text-align: center;
        }

        .stat {
            padding: 1rem;
            background: #f7fafc;
            border-radius: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #718096;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <main>
        <h1>Toast Notifications - Enhanced</h1>
        
        <div class="controls">
            <div class="control-group">
                <h3>Toast Position</h3>
                <div class="position-selector">
                    <button class="position-button active" data-position="top-right">Top Right</button>
                    <button class="position-button" data-position="top-center">Top Center</button>
                    <button class="position-button" data-position="top-left">Top Left</button>
                    <button class="position-button" data-position="bottom-right">Bottom Right</button>
                    <button class="position-button" data-position="bottom-center">Bottom Center</button>
                    <button class="position-button" data-position="bottom-left">Bottom Left</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Create Toasts</h3>
                <div class="button-grid">
                    <button class="demo-button success" data-type="success">Success Toast</button>
                    <button class="demo-button error" data-type="error">Error Toast</button>
                    <button class="demo-button warning" data-type="warning">Warning Toast</button>
                    <button class="demo-button info" data-type="info">Info Toast</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Special Toasts</h3>
                <div class="button-grid">
                    <button class="demo-button info" id="actionToast">Toast with Actions</button>
                    <button class="demo-button warning" id="undoToast">Undo Toast</button>
                    <button class="demo-button success" id="multiToast">Multiple Toasts</button>
                    <button class="demo-button error" id="persistentToast">Persistent Toast</button>
                </div>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-value" id="activeCount">0</div>
                <div class="stat-label">Active Toasts</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="queueCount">0</div>
                <div class="stat-label">Queued</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="totalCount">0</div>
                <div class="stat-label">Total Shown</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="dismissedCount">0</div>
                <div class="stat-label">Dismissed</div>
            </div>
        </div>
    </main>

    <!-- Toast containers will be created dynamically -->

    <script>
        // Toast notification system
        class ToastSystem {
            constructor() {
                this.toasts = new Map();
                this.queue = [];
                this.position = 'top-right';
                this.maxVisible = 5;
                this.defaultDuration = 5000;
                this.stats = {
                    total: 0,
                    active: 0,
                    queued: 0,
                    dismissed: 0
                };
                this.init();
            }

            init() {
                this.createContainers();
                this.setupEventListeners();
                this.updateStats();
            }

            createContainers() {
                const positions = ['top-left', 'top-center', 'top-right', 'bottom-left', 'bottom-center', 'bottom-right'];
                positions.forEach(position => {
                    const container = document.createElement('div');
                    container.className = `toast-container ${position}`;
                    container.style.display = position === this.position ? 'flex' : 'none';
                    document.body.appendChild(container);
                });
            }

            setupEventListeners() {
                // Position buttons
                document.querySelectorAll('.position-button').forEach(button => {
                    button.addEventListener('click', () => {
                        this.setPosition(button.dataset.position);
                        document.querySelectorAll('.position-button').forEach(b => b.classList.remove('active'));
                        button.classList.add('active');
                    });
                });

                // Toast type buttons
                document.querySelectorAll('.demo-button[data-type]').forEach(button => {
                    button.addEventListener('click', () => {
                        const type = button.dataset.type;
                        this.showToast({
                            type,
                            title: this.getTitle(type),
                            message: this.getMessage(type)
                        });
                    });
                });

                // Special toasts
                document.getElementById('actionToast').addEventListener('click', () => {
                    this.showToast({
                        type: 'info',
                        title: 'New Message',
                        message: 'You have a new message from Sarah',
                        actions: [
                            { text: 'View', primary: true, callback: () => console.log('View clicked') },
                            { text: 'Dismiss', callback: () => {} }
                        ],
                        duration: 0 // No auto-dismiss
                    });
                });

                document.getElementById('undoToast').addEventListener('click', () => {
                    this.showToast({
                        type: 'warning',
                        title: 'Item Deleted',
                        message: 'The item has been moved to trash',
                        actions: [
                            { text: 'Undo', primary: true, callback: () => {
                                this.showToast({
                                    type: 'success',
                                    title: 'Restored',
                                    message: 'Item has been restored'
                                });
                            }}
                        ],
                        duration: 10000
                    });
                });

                document.getElementById('multiToast').addEventListener('click', () => {
                    const messages = [
                        'First notification',
                        'Second notification',
                        'Third notification',
                        'Fourth notification',
                        'Fifth notification',
                        'Sixth notification (queued)',
                        'Seventh notification (queued)'
                    ];
                    
                    messages.forEach((message, index) => {
                        setTimeout(() => {
                            this.showToast({
                                type: ['success', 'info', 'warning'][index % 3],
                                title: `Notification ${index + 1}`,
                                message
                            });
                        }, index * 200);
                    });
                });

                document.getElementById('persistentToast').addEventListener('click', () => {
                    this.showToast({
                        type: 'error',
                        title: 'Connection Lost',
                        message: 'Please check your internet connection',
                        duration: 0, // No auto-dismiss
                        closable: false // Can't be closed manually
                    });
                    
                    // Simulate reconnection after 5 seconds
                    setTimeout(() => {
                        this.showToast({
                            type: 'success',
                            title: 'Connected',
                            message: 'Connection restored'
                        });
                        // Find and remove the persistent toast
                        this.toasts.forEach((toast, id) => {
                            if (toast.config.title === 'Connection Lost') {
                                this.removeToast(id);
                            }
                        });
                    }, 5000);
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        // Dismiss all toasts
                        this.toasts.forEach((toast, id) => {
                            if (toast.config.closable !== false) {
                                this.removeToast(id);
                            }
                        });
                    }
                });
            }

            setPosition(position) {
                this.position = position;
                document.querySelectorAll('.toast-container').forEach(container => {
                    container.style.display = container.classList.contains(position) ? 'flex' : 'none';
                });
                
                // Move existing toasts to new container
                const newContainer = document.querySelector(`.toast-container.${position}`);
                this.toasts.forEach(toast => {
                    newContainer.appendChild(toast.element);
                });
            }

            showToast(config) {
                const id = Date.now() + Math.random();
                const toast = {
                    id,
                    config: {
                        duration: this.defaultDuration,
                        closable: true,
                        ...config
                    },
                    element: null,
                    progressInterval: null
                };

                // Check if we need to queue this toast
                if (this.toasts.size >= this.maxVisible) {
                    this.queue.push(toast);
                    this.stats.queued++;
                    this.updateStats();
                    return;
                }

                this.createToastElement(toast);
                this.toasts.set(id, toast);
                this.stats.total++;
                this.stats.active++;
                this.updateStats();

                // Auto-dismiss if duration is set
                if (toast.config.duration > 0) {
                    this.startProgress(toast);
                }
            }

            createToastElement(toast) {
                const element = document.createElement('div');
                element.className = `toast ${toast.config.type}`;
                element.setAttribute('role', 'alert');
                element.setAttribute('aria-live', 'polite');
                element.tabIndex = 0;

                // Icon
                const icon = document.createElement('div');
                icon.className = 'toast-icon';
                icon.innerHTML = this.getIcon(toast.config.type);
                element.appendChild(icon);

                // Content
                const content = document.createElement('div');
                content.className = 'toast-content';
                
                const title = document.createElement('div');
                title.className = 'toast-title';
                title.textContent = toast.config.title;
                content.appendChild(title);
                
                const message = document.createElement('div');
                message.className = 'toast-message';
                message.textContent = toast.config.message;
                content.appendChild(message);
                
                element.appendChild(content);

                // Actions
                if (toast.config.actions && toast.config.actions.length > 0) {
                    const actions = document.createElement('div');
                    actions.className = 'toast-actions';
                    
                    toast.config.actions.forEach(action => {
                        const button = document.createElement('button');
                        button.className = `toast-action ${action.primary ? 'primary' : ''}`;
                        button.textContent = action.text;
                        button.addEventListener('click', () => {
                            action.callback();
                            if (action.dismiss !== false) {
                                this.removeToast(toast.id);
                            }
                        });
                        actions.appendChild(button);
                    });
                    
                    element.appendChild(actions);
                }

                // Close button
                if (toast.config.closable !== false) {
                    const closeButton = document.createElement('button');
                    closeButton.className = 'toast-close';
                    closeButton.innerHTML = '<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 1L1 13M1 1L13 13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>';
                    closeButton.setAttribute('aria-label', 'Close notification');
                    closeButton.addEventListener('click', () => this.removeToast(toast.id));
                    element.appendChild(closeButton);
                }

                // Progress bar
                if (toast.config.duration > 0) {
                    const progress = document.createElement('div');
                    progress.className = 'toast-progress';
                    progress.style.width = '100%';
                    element.appendChild(progress);
                }

                // Event listeners
                element.addEventListener('mouseenter', () => this.pauseProgress(toast));
                element.addEventListener('mouseleave', () => this.resumeProgress(toast));
                
                // Touch swipe to dismiss
                let touchStartX = 0;
                element.addEventListener('touchstart', (e) => {
                    touchStartX = e.touches[0].clientX;
                });
                
                element.addEventListener('touchend', (e) => {
                    const touchEndX = e.changedTouches[0].clientX;
                    const diff = touchEndX - touchStartX;
                    if (Math.abs(diff) > 50 && toast.config.closable !== false) {
                        this.removeToast(toast.id);
                    }
                });

                // Click to dismiss (if no actions)
                if (!toast.config.actions || toast.config.actions.length === 0) {
                    element.style.cursor = 'pointer';
                    element.addEventListener('click', () => {
                        if (toast.config.closable !== false) {
                            this.removeToast(toast.id);
                        }
                    });
                }

                toast.element = element;
                
                // Add to container
                const container = document.querySelector(`.toast-container.${this.position}`);
                container.appendChild(element);
                
                // Focus for accessibility
                element.focus();
            }

            startProgress(toast) {
                if (!toast.config.duration || toast.progressInterval) return;
                
                const progress = toast.element.querySelector('.toast-progress');
                if (!progress) return;
                
                const startTime = Date.now();
                const duration = toast.config.duration;
                
                toast.progressInterval = setInterval(() => {
                    const elapsed = Date.now() - startTime;
                    const percentage = Math.max(0, 100 - (elapsed / duration) * 100);
                    progress.style.width = `${percentage}%`;
                    progress.style.transition = 'width 100ms linear';
                    
                    if (percentage <= 0) {
                        this.removeToast(toast.id);
                    }
                }, 100);
            }

            pauseProgress(toast) {
                if (toast.progressInterval) {
                    clearInterval(toast.progressInterval);
                    toast.progressInterval = null;
                    const progress = toast.element.querySelector('.toast-progress');
                    if (progress) {
                        toast.pausedWidth = progress.style.width;
                    }
                }
            }

            resumeProgress(toast) {
                if (!toast.config.duration || toast.progressInterval) return;
                
                const progress = toast.element.querySelector('.toast-progress');
                if (!progress || !toast.pausedWidth) return;
                
                const remainingPercentage = parseFloat(toast.pausedWidth);
                const remainingDuration = (remainingPercentage / 100) * toast.config.duration;
                
                const startTime = Date.now();
                toast.progressInterval = setInterval(() => {
                    const elapsed = Date.now() - startTime;
                    const percentage = Math.max(0, remainingPercentage - (elapsed / remainingDuration) * remainingPercentage);
                    progress.style.width = `${percentage}%`;
                    
                    if (percentage <= 0) {
                        this.removeToast(toast.id);
                    }
                }, 100);
            }

            removeToast(id) {
                const toast = this.toasts.get(id);
                if (!toast) return;
                
                // Clear progress interval
                if (toast.progressInterval) {
                    clearInterval(toast.progressInterval);
                }
                
                // Animate out
                toast.element.classList.add('removing');
                
                setTimeout(() => {
                    toast.element.remove();
                    this.toasts.delete(id);
                    this.stats.active--;
                    this.stats.dismissed++;
                    this.updateStats();
                    
                    // Process queue
                    if (this.queue.length > 0 && this.toasts.size < this.maxVisible) {
                        const nextToast = this.queue.shift();
                        this.stats.queued--;
                        this.createToastElement(nextToast);
                        this.toasts.set(nextToast.id, nextToast);
                        this.stats.active++;
                        this.updateStats();
                        
                        if (nextToast.config.duration > 0) {
                            this.startProgress(nextToast);
                        }
                    }
                }, 300);
            }

            updateStats() {
                document.getElementById('activeCount').textContent = this.stats.active;
                document.getElementById('queueCount').textContent = this.stats.queued;
                document.getElementById('totalCount').textContent = this.stats.total;
                document.getElementById('dismissedCount').textContent = this.stats.dismissed;
            }

            getIcon(type) {
                const icons = {
                    success: '<svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
                    error: '<svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
                    warning: '<svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>',
                    info: '<svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
                };
                return icons[type] || icons.info;
            }

            getTitle(type) {
                const titles = {
                    success: 'Success!',
                    error: 'Error!',
                    warning: 'Warning!',
                    info: 'Information'
                };
                return titles[type] || 'Notification';
            }

            getMessage(type) {
                const messages = {
                    success: 'Your action was completed successfully.',
                    error: 'Something went wrong. Please try again.',
                    warning: 'Please review this important information.',
                    info: 'Here\'s something you might want to know.'
                };
                return messages[type] || 'This is a notification message.';
            }
        }

        // Initialize the toast system
        const toastSystem = new ToastSystem();

        // Show a welcome toast after page load
        setTimeout(() => {
            toastSystem.showToast({
                type: 'info',
                title: 'Welcome!',
                message: 'Try the different toast types and positions.',
                duration: 7000
            });
        }, 500);
    </script>
</body>
</html>