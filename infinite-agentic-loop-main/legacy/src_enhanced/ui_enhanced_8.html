<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Enhanced</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: #e0e7ff;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.2s ease;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        main {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            padding: 40px;
        }

        h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: var(--gray-600);
            margin-bottom: 32px;
        }

        .upload-container {
            background: var(--gray-50);
            border-radius: 16px;
            padding: 40px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .upload-zone {
            border: 2px dashed var(--gray-300);
            border-radius: 12px;
            padding: 60px 40px;
            text-align: center;
            transition: var(--transition);
            position: relative;
            background: white;
        }

        .upload-zone.drag-over {
            border-color: var(--primary);
            background: var(--primary-light);
            transform: scale(1.02);
        }

        .upload-zone.drag-over .upload-icon {
            animation: bounce 0.5s ease infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .upload-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: var(--gray-100);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .upload-icon svg {
            width: 36px;
            height: 36px;
            stroke: var(--gray-500);
            transition: var(--transition);
        }

        .upload-zone.drag-over .upload-icon {
            background: var(--primary);
        }

        .upload-zone.drag-over .upload-icon svg {
            stroke: white;
        }

        .upload-text h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--gray-800);
        }

        .upload-text p {
            color: var(--gray-600);
            margin-bottom: 20px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-button {
            background: var(--primary);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .file-button:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .file-button svg {
            width: 20px;
            height: 20px;
        }

        .supported-formats {
            margin-top: 16px;
            font-size: 14px;
            color: var(--gray-500);
        }

        .file-list {
            margin-top: 32px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .file-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 16px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .file-item:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .file-preview {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
        }

        .file-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .file-preview.loading::after {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            stroke: var(--gray-500);
        }

        .file-info {
            flex: 1;
            min-width: 0;
        }

        .file-name {
            font-weight: 600;
            color: var(--gray-800);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 4px;
        }

        .file-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 14px;
            color: var(--gray-500);
        }

        .file-size {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .file-status {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-icon {
            width: 16px;
            height: 16px;
        }

        .status-uploading {
            color: var(--primary);
        }

        .status-success {
            color: var(--success);
        }

        .status-error {
            color: var(--error);
        }

        .file-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-button {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background: var(--gray-100);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .action-button:hover {
            background: var(--gray-200);
        }

        .action-button svg {
            width: 18px;
            height: 18px;
            stroke: var(--gray-600);
        }

        .action-button.danger:hover {
            background: var(--error);
        }

        .action-button.danger:hover svg {
            stroke: white;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gray-200);
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary) 0%, #8b5cf6 100%);
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: shimmer 1.5s ease infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .paste-hint {
            position: absolute;
            top: 16px;
            right: 16px;
            background: var(--gray-800);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-10px);
            transition: var(--transition);
            pointer-events: none;
        }

        .paste-hint.show {
            opacity: 1;
            transform: translateY(0);
        }

        .upload-stats {
            margin-top: 32px;
            padding: 20px;
            background: var(--gray-50);
            border-radius: 12px;
            display: flex;
            justify-content: space-around;
            gap: 20px;
        }

        .stat {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--gray-600);
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: var(--error);
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-message svg {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        @media (max-width: 640px) {
            main {
                padding: 24px;
            }

            .upload-container {
                padding: 24px;
            }

            .upload-zone {
                padding: 40px 20px;
            }

            .file-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .upload-stats {
                flex-direction: column;
                gap: 16px;
            }
        }

        /* Accessibility */
        .file-input:focus + .file-button {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>File Upload - Enhanced</h1>
        <p class="subtitle">Drag & drop files or click to browse. Supports images, documents, and more.</p>

        <div class="upload-container">
            <div class="upload-zone" id="uploadZone">
                <div class="upload-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                </div>
                <div class="upload-text">
                    <h3>Drop files here or click to upload</h3>
                    <p>You can also paste images from your clipboard</p>
                    <div class="file-input-wrapper">
                        <input type="file" class="file-input" id="fileInput" multiple accept="image/*,.pdf,.doc,.docx,.txt,.zip">
                        <button class="file-button">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            Choose Files
                        </button>
                    </div>
                    <p class="supported-formats">Supported: Images, PDF, DOC, TXT, ZIP (Max 10MB per file)</p>
                </div>
            </div>

            <div class="paste-hint" id="pasteHint">Image copied! Press Ctrl+V to upload</div>

            <div class="file-list" id="fileList"></div>

            <div class="upload-stats" id="uploadStats" style="display: none;">
                <div class="stat">
                    <div class="stat-value" id="totalFiles">0</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="uploadedFiles">0</div>
                    <div class="stat-label">Uploaded</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="totalSize">0 MB</div>
                    <div class="stat-label">Total Size</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        class FileUploadEnhanced {
            constructor() {
                this.uploadZone = document.getElementById('uploadZone');
                this.fileInput = document.getElementById('fileInput');
                this.fileList = document.getElementById('fileList');
                this.pasteHint = document.getElementById('pasteHint');
                this.uploadStats = document.getElementById('uploadStats');
                
                this.files = new Map();
                this.uploadQueue = [];
                this.isUploading = false;
                this.maxFileSize = 10 * 1024 * 1024; // 10MB
                this.chunkSize = 1024 * 1024; // 1MB chunks
                
                this.init();
            }

            init() {
                // File input
                this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                
                // Drag and drop
                this.uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
                this.uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
                this.uploadZone.addEventListener('drop', (e) => this.handleDrop(e));
                
                // Paste from clipboard
                document.addEventListener('paste', (e) => this.handlePaste(e));
                
                // Copy event detection
                document.addEventListener('copy', () => this.showPasteHint());
                
                // Prevent default drag behavior
                document.addEventListener('dragover', (e) => e.preventDefault());
                document.addEventListener('drop', (e) => e.preventDefault());
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                this.processFiles(files);
                e.target.value = ''; // Reset input
            }

            handleDragOver(e) {
                e.preventDefault();
                e.stopPropagation();
                this.uploadZone.classList.add('drag-over');
            }

            handleDragLeave(e) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.uploadZone.contains(e.relatedTarget)) {
                    this.uploadZone.classList.remove('drag-over');
                }
            }

            handleDrop(e) {
                e.preventDefault();
                e.stopPropagation();
                this.uploadZone.classList.remove('drag-over');
                
                const files = Array.from(e.dataTransfer.files);
                this.processFiles(files);
            }

            handlePaste(e) {
                const items = Array.from(e.clipboardData.items);
                const files = [];
                
                items.forEach(item => {
                    if (item.type.indexOf('image') !== -1) {
                        const file = item.getAsFile();
                        if (file) {
                            // Give pasted images a name
                            const renamedFile = new File([file], `pasted-image-${Date.now()}.png`, {
                                type: file.type
                            });
                            files.push(renamedFile);
                        }
                    }
                });
                
                if (files.length > 0) {
                    this.processFiles(files);
                    this.hidePasteHint();
                }
            }

            showPasteHint() {
                this.pasteHint.classList.add('show');
                setTimeout(() => this.hidePasteHint(), 3000);
            }

            hidePasteHint() {
                this.pasteHint.classList.remove('show');
            }

            processFiles(files) {
                files.forEach(file => {
                    const validation = this.validateFile(file);
                    if (validation.valid) {
                        this.addFile(file);
                    } else {
                        this.showError(validation.error);
                    }
                });
                
                this.updateStats();
                this.processUploadQueue();
            }

            validateFile(file) {
                // Check file size
                if (file.size > this.maxFileSize) {
                    return {
                        valid: false,
                        error: `${file.name} exceeds the 10MB size limit`
                    };
                }
                
                // Check file type
                const allowedTypes = [
                    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                    'application/pdf', 'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'text/plain', 'application/zip'
                ];
                
                if (!allowedTypes.includes(file.type) && !file.type.startsWith('image/')) {
                    return {
                        valid: false,
                        error: `${file.name} is not a supported file type`
                    };
                }
                
                return { valid: true };
            }

            addFile(file) {
                const fileId = this.generateId();
                const fileData = {
                    id: fileId,
                    file: file,
                    status: 'pending',
                    progress: 0,
                    uploaded: 0,
                    element: null
                };
                
                this.files.set(fileId, fileData);
                this.uploadQueue.push(fileId);
                
                const element = this.createFileElement(fileData);
                fileData.element = element;
                this.fileList.appendChild(element);
                
                if (this.uploadStats.style.display === 'none') {
                    this.uploadStats.style.display = 'flex';
                }
                
                // Load preview for images
                if (file.type.startsWith('image/')) {
                    this.loadImagePreview(fileData);
                }
            }

            createFileElement(fileData) {
                const div = document.createElement('div');
                div.className = 'file-item';
                div.id = `file-${fileData.id}`;
                
                const isImage = fileData.file.type.startsWith('image/');
                
                div.innerHTML = `
                    <div class="file-preview ${isImage ? 'loading' : ''}">
                        ${this.getFileIcon(fileData.file)}
                    </div>
                    <div class="file-info">
                        <div class="file-name">${fileData.file.name}</div>
                        <div class="file-meta">
                            <div class="file-size">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                </svg>
                                ${this.formatFileSize(fileData.file.size)}
                            </div>
                            <div class="file-status status-uploading">
                                <svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                                <span>Waiting...</span>
                            </div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="action-button" onclick="fileUpload.retryUpload('${fileData.id}')" title="Retry" style="display: none;">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23 4 23 10 17 10"></polyline>
                                <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                            </svg>
                        </button>
                        <button class="action-button danger" onclick="fileUpload.removeFile('${fileData.id}')" title="Remove">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                `;
                
                return div;
            }

            getFileIcon(file) {
                if (file.type.startsWith('image/')) {
                    return `<img src="" alt="${file.name}" style="display: none;">`;
                }
                
                let icon;
                if (file.type === 'application/pdf') {
                    icon = '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline>';
                } else if (file.type.includes('zip')) {
                    icon = '<path d="M22 11v1a10 10 0 1 1-9-10"></path><polyline points="22 2 12 12.01 8 8"></polyline>';
                } else {
                    icon = '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline>';
                }
                
                return `<svg class="file-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">${icon}</svg>`;
            }

            loadImagePreview(fileData) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const preview = fileData.element.querySelector('.file-preview');
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    img.style.display = 'block';
                    preview.classList.remove('loading');
                };
                reader.readAsDataURL(fileData.file);
            }

            async processUploadQueue() {
                if (this.isUploading || this.uploadQueue.length === 0) return;
                
                this.isUploading = true;
                const fileId = this.uploadQueue.shift();
                const fileData = this.files.get(fileId);
                
                if (fileData && fileData.status === 'pending') {
                    await this.uploadFile(fileData);
                }
                
                this.isUploading = false;
                this.processUploadQueue();
            }

            async uploadFile(fileData) {
                fileData.status = 'uploading';
                this.updateFileStatus(fileData, 'Uploading...', 'status-uploading');
                
                try {
                    // Simulate chunked upload
                    const chunks = Math.ceil(fileData.file.size / this.chunkSize);
                    
                    for (let i = 0; i < chunks; i++) {
                        // Simulate upload delay
                        await this.delay(300 + Math.random() * 200);
                        
                        // Update progress
                        fileData.progress = Math.min(((i + 1) / chunks) * 100, 100);
                        this.updateProgress(fileData);
                        
                        // Simulate random failure (5% chance)
                        if (Math.random() < 0.05 && i > 0) {
                            throw new Error('Network error');
                        }
                    }
                    
                    // Upload complete
                    fileData.status = 'completed';
                    fileData.uploaded = fileData.file.size;
                    this.updateFileStatus(fileData, 'Uploaded', 'status-success');
                    this.showSuccess(fileData);
                    
                } catch (error) {
                    fileData.status = 'error';
                    this.updateFileStatus(fileData, 'Failed', 'status-error');
                    this.showRetryButton(fileData);
                }
                
                this.updateStats();
            }

            updateFileStatus(fileData, text, className) {
                const statusElement = fileData.element.querySelector('.file-status');
                statusElement.className = `file-status ${className}`;
                statusElement.querySelector('span').textContent = text;
            }

            updateProgress(fileData) {
                const progressFill = fileData.element.querySelector('.progress-fill');
                progressFill.style.width = `${fileData.progress}%`;
            }

            showSuccess(fileData) {
                const element = fileData.element;
                element.style.animation = 'none';
                element.offsetHeight; // Trigger reflow
                element.style.animation = 'slideIn 0.3s ease';
                
                // Hide progress bar after animation
                setTimeout(() => {
                    const progressBar = element.querySelector('.progress-bar');
                    progressBar.style.opacity = '0';
                }, 500);
            }

            showRetryButton(fileData) {
                const retryButton = fileData.element.querySelector('.action-button');
                retryButton.style.display = 'flex';
            }

            retryUpload(fileId) {
                const fileData = this.files.get(fileId);
                if (fileData) {
                    fileData.status = 'pending';
                    fileData.progress = 0;
                    this.updateProgress(fileData);
                    this.updateFileStatus(fileData, 'Waiting...', 'status-uploading');
                    
                    const retryButton = fileData.element.querySelector('.action-button');
                    retryButton.style.display = 'none';
                    
                    const progressBar = fileData.element.querySelector('.progress-bar');
                    progressBar.style.opacity = '1';
                    
                    this.uploadQueue.push(fileId);
                    this.processUploadQueue();
                }
            }

            removeFile(fileId) {
                const fileData = this.files.get(fileId);
                if (fileData) {
                    // Cancel upload if in progress
                    if (fileData.status === 'uploading') {
                        const index = this.uploadQueue.indexOf(fileId);
                        if (index > -1) {
                            this.uploadQueue.splice(index, 1);
                        }
                    }
                    
                    // Remove with animation
                    fileData.element.style.transform = 'translateX(100%)';
                    fileData.element.style.opacity = '0';
                    
                    setTimeout(() => {
                        fileData.element.remove();
                        this.files.delete(fileId);
                        this.updateStats();
                        
                        if (this.files.size === 0) {
                            this.uploadStats.style.display = 'none';
                        }
                    }, 300);
                }
            }

            updateStats() {
                let totalSize = 0;
                let uploadedCount = 0;
                
                this.files.forEach(fileData => {
                    totalSize += fileData.file.size;
                    if (fileData.status === 'completed') {
                        uploadedCount++;
                    }
                });
                
                document.getElementById('totalFiles').textContent = this.files.size;
                document.getElementById('uploadedFiles').textContent = uploadedCount;
                document.getElementById('totalSize').textContent = this.formatFileSize(totalSize);
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            showError(message) {
                const error = document.createElement('div');
                error.className = 'error-message';
                error.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    ${message}
                `;
                
                this.uploadZone.appendChild(error);
                
                setTimeout(() => {
                    error.style.opacity = '0';
                    setTimeout(() => error.remove(), 300);
                }, 3000);
            }

            generateId() {
                return 'file-' + Math.random().toString(36).substr(2, 9);
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Initialize the enhanced file upload
        const fileUpload = new FileUploadEnhanced();
    </script>
</body>
</html>