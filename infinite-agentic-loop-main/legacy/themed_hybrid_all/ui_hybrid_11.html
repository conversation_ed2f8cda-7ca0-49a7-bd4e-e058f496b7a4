<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ocean Depths Advanced Search - Themed Hybrid UI #11</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(180deg, #001f3f 0%, #003366 20%, #004080 50%, #000033 100%);
            min-height: 100vh;
            color: #e0f7fa;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated bubbles background */
        .bubble {
            position: absolute;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
            border-radius: 50%;
            opacity: 0.6;
            animation: rise 10s infinite linear;
        }

        @keyframes rise {
            from {
                transform: translateY(100vh) translateX(0) scale(1);
            }
            to {
                transform: translateY(-100px) translateX(100px) scale(0.5);
            }
        }

        /* Water ripple effect */
        .water-ripple {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(transparent 30%, rgba(0,100,200,0.1) 50%, transparent 70%);
            animation: ripple 8s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes ripple {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(20px); }
        }

        /* Main container */
        .ocean-depths-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 10;
        }

        /* Title with bioluminescence */
        .title {
            text-align: center;
            font-size: 3em;
            margin-bottom: 40px;
            text-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff;
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff; }
            to { text-shadow: 0 0 30px #00ffff, 0 0 50px #00ffff, 0 0 70px #00ffff; }
        }

        /* Periscope Search Bar */
        .periscope-search {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin: 0 auto 40px;
        }

        .periscope-lens {
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: radial-gradient(circle, #1a5490 0%, #0d47a1 50%, #001f3f 100%);
            border-radius: 50%;
            border: 3px solid #00acc1;
            box-shadow: 0 0 20px rgba(0,172,193,0.5);
        }

        .search-input {
            width: 100%;
            padding: 20px 60px 20px 20px;
            font-size: 1.2em;
            background: rgba(0,50,100,0.8);
            border: 2px solid #00acc1;
            border-radius: 50px;
            color: #e0f7fa;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.5), 0 0 20px rgba(0,172,193,0.3);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.5), 0 0 30px rgba(0,255,255,0.5);
        }

        .search-button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            padding: 15px 25px;
            background: linear-gradient(135deg, #00acc1 0%, #0277bd 100%);
            border: none;
            border-radius: 50px;
            color: white;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .search-button:hover {
            background: linear-gradient(135deg, #00bcd4 0%, #0288d1 100%);
            box-shadow: 0 0 20px rgba(0,188,212,0.5);
        }

        /* Control Panel with Valves */
        .control-panel {
            display: flex;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
            justify-content: center;
        }

        /* Filter Valves */
        .filter-valve {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .valve-wheel {
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, #1565c0 0%, #0d47a1 50%, #001f3f 100%);
            border-radius: 50%;
            border: 4px solid #00acc1;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
            box-shadow: 0 5px 20px rgba(0,0,0,0.5);
        }

        .valve-wheel::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 4px;
            background: #00acc1;
            box-shadow: 0 0 10px rgba(0,172,193,0.5);
        }

        .valve-wheel::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(90deg);
            width: 80%;
            height: 4px;
            background: #00acc1;
            box-shadow: 0 0 10px rgba(0,172,193,0.5);
        }

        .valve-wheel.active {
            transform: rotate(45deg);
            border-color: #00ffff;
            box-shadow: 0 5px 30px rgba(0,255,255,0.5);
        }

        .valve-label {
            text-align: center;
            margin-top: 10px;
            font-size: 0.9em;
            color: #b3e5fc;
        }

        /* Sonar Controls (Advanced Operators) */
        .sonar-controls {
            background: rgba(0,50,100,0.6);
            border: 2px solid #00acc1;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }

        .sonar-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3em;
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0,255,255,0.5);
        }

        .operator-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .operator-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #004080 0%, #002050 100%);
            border: 1px solid #00acc1;
            border-radius: 25px;
            color: #b3e5fc;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .operator-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(0,255,255,0.3);
            transition: width 0.3s, height 0.3s;
        }

        .operator-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .operator-btn:hover {
            border-color: #00ffff;
            color: #ffffff;
            box-shadow: 0 0 15px rgba(0,255,255,0.5);
        }

        /* Autocomplete Bubbles */
        .autocomplete-bubbles {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            margin-top: 10px;
            display: none;
        }

        .autocomplete-bubbles.active {
            display: block;
        }

        .bubble-suggestion {
            background: rgba(0,100,200,0.8);
            border: 1px solid #00acc1;
            border-radius: 30px;
            padding: 10px 20px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: bubbleIn 0.5s ease;
        }

        @keyframes bubbleIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .bubble-suggestion:hover {
            background: rgba(0,150,255,0.9);
            transform: translateX(10px);
            box-shadow: 0 0 20px rgba(0,255,255,0.3);
        }

        /* Ship's Log (History) */
        .ships-log {
            background: rgba(0,50,100,0.6);
            border: 2px solid #00acc1;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }

        .log-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0,255,255,0.5);
        }

        .log-entry {
            background: rgba(0,30,60,0.5);
            border-left: 3px solid #00acc1;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            background: rgba(0,50,100,0.7);
            border-left-color: #00ffff;
            transform: translateX(5px);
        }

        .log-timestamp {
            font-size: 0.8em;
            color: #80deea;
        }

        /* Treasure Chest (Saved Searches) */
        .treasure-chest {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 80px;
            height: 60px;
            cursor: pointer;
            z-index: 100;
        }

        .chest-body {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(135deg, #8b6914 0%, #cdaa3d 50%, #8b6914 100%);
            border-radius: 5px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.5);
        }

        .chest-lid {
            position: absolute;
            bottom: 35px;
            width: 100%;
            height: 25px;
            background: linear-gradient(135deg, #cdaa3d 0%, #eedc82 50%, #cdaa3d 100%);
            border-radius: 5px 5px 0 0;
            transform-origin: bottom;
            transition: transform 0.3s ease;
        }

        .treasure-chest:hover .chest-lid {
            transform: rotateX(-45deg);
        }

        .saved-searches {
            position: fixed;
            bottom: 100px;
            right: 20px;
            background: rgba(0,50,100,0.9);
            border: 2px solid #cdaa3d;
            border-radius: 10px;
            padding: 20px;
            display: none;
            max-width: 300px;
            backdrop-filter: blur(10px);
        }

        .saved-searches.open {
            display: block;
            animation: treasureOpen 0.5s ease;
        }

        @keyframes treasureOpen {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Fish School Results */
        .results-ocean {
            min-height: 400px;
            position: relative;
            background: rgba(0,30,60,0.3);
            border-radius: 20px;
            padding: 20px;
            overflow: hidden;
        }

        .fish-result {
            background: linear-gradient(135deg, #004080 0%, #0066cc 100%);
            border: 1px solid #00acc1;
            border-radius: 50px 20px 20px 50px;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            transition: all 0.3s ease;
            animation: swim 20s ease-in-out infinite;
        }

        @keyframes swim {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(10px) rotate(1deg); }
            50% { transform: translateX(-10px) rotate(-1deg); }
            75% { transform: translateX(5px) rotate(0.5deg); }
        }

        .fish-result::before {
            content: '>';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2em;
            color: #004080;
        }

        .fish-result:hover {
            background: linear-gradient(135deg, #0066cc 0%, #0099ff 100%);
            transform: translateX(20px);
            box-shadow: 0 0 30px rgba(0,153,255,0.3);
        }

        .result-title {
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #00ffff;
        }

        .result-description {
            color: #b3e5fc;
            line-height: 1.6;
        }

        /* Pressure Gauge */
        .pressure-gauge {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, #001f3f 0%, #000033 100%);
            border-radius: 50%;
            border: 3px solid #00acc1;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 20px rgba(0,172,193,0.5);
        }

        .gauge-needle {
            position: absolute;
            width: 3px;
            height: 40px;
            background: #ff0000;
            bottom: 50%;
            left: 50%;
            transform-origin: bottom;
            transform: translateX(-50%) rotate(0deg);
            transition: transform 0.5s ease;
            box-shadow: 0 0 10px rgba(255,0,0,0.5);
        }

        .gauge-center {
            width: 20px;
            height: 20px;
            background: #00acc1;
            border-radius: 50%;
            position: relative;
            z-index: 10;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .title {
                font-size: 2em;
            }

            .control-panel {
                gap: 10px;
            }

            .filter-valve {
                width: 80px;
                height: 80px;
            }

            .periscope-lens {
                display: none;
            }

            .search-input {
                padding: 15px 50px 15px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="water-ripple"></div>
    
    <!-- Animated bubbles -->
    <div id="bubbleContainer"></div>
    
    <div class="ocean-depths-container">
        <h1 class="title">Ocean Depths Search</h1>
        
        <!-- Periscope Search Bar -->
        <div class="periscope-search">
            <div class="periscope-lens"></div>
            <input type="text" class="search-input" id="searchInput" placeholder="Explore the depths...">
            <button class="search-button" onclick="performSearch()">Dive</button>
            
            <!-- Autocomplete Bubbles -->
            <div class="autocomplete-bubbles" id="autocompleteBubbles"></div>
        </div>
        
        <!-- Control Panel with Filter Valves -->
        <div class="control-panel">
            <div class="filter-valve">
                <div class="valve-wheel" onclick="toggleFilter(this, 'recent')" data-filter="recent"></div>
                <div class="valve-label">Recent</div>
            </div>
            <div class="filter-valve">
                <div class="valve-wheel" onclick="toggleFilter(this, 'deep')" data-filter="deep"></div>
                <div class="valve-label">Deep Search</div>
            </div>
            <div class="filter-valve">
                <div class="valve-wheel" onclick="toggleFilter(this, 'images')" data-filter="images"></div>
                <div class="valve-label">Images</div>
            </div>
            <div class="filter-valve">
                <div class="valve-wheel" onclick="toggleFilter(this, 'videos')" data-filter="videos"></div>
                <div class="valve-label">Videos</div>
            </div>
            <div class="filter-valve">
                <div class="valve-wheel" onclick="toggleFilter(this, 'archived')" data-filter="archived"></div>
                <div class="valve-label">Archived</div>
            </div>
        </div>
        
        <!-- Sonar Controls (Advanced Operators) -->
        <div class="sonar-controls">
            <div class="sonar-title">Sonar Operations</div>
            <div class="operator-buttons">
                <button class="operator-btn" onclick="addOperator('AND')">AND</button>
                <button class="operator-btn" onclick="addOperator('OR')">OR</button>
                <button class="operator-btn" onclick="addOperator('NOT')">NOT</button>
                <button class="operator-btn" onclick="addOperator('*')">Wildcard</button>
                <button class="operator-btn" onclick="addOperator('\"\"')">Exact</button>
                <button class="operator-btn" onclick="addOperator('~')">Fuzzy</button>
                <button class="operator-btn" onclick="addOperator('^')">Boost</button>
                <button class="operator-btn" onclick="addOperator('[]')">Range</button>
            </div>
        </div>
        
        <!-- Ship's Log (History) -->
        <div class="ships-log">
            <div class="log-title">Ship's Log</div>
            <div id="searchHistory"></div>
        </div>
        
        <!-- Results Ocean -->
        <div class="results-ocean" id="resultsOcean">
            <div class="fish-result">
                <div class="result-title">Deep Sea Discovery</div>
                <div class="result-description">Begin your exploration of the ocean depths...</div>
            </div>
        </div>
    </div>
    
    <!-- Treasure Chest (Saved Searches) -->
    <div class="treasure-chest" onclick="toggleSavedSearches()">
        <div class="chest-lid"></div>
        <div class="chest-body"></div>
    </div>
    
    <div class="saved-searches" id="savedSearches">
        <h3 style="color: #cdaa3d; margin-bottom: 15px;">Treasured Searches</h3>
        <div id="savedSearchesList"></div>
        <button class="operator-btn" style="margin-top: 10px;" onclick="saveCurrentSearch()">Save Current</button>
    </div>
    
    <!-- Pressure Gauge -->
    <div class="pressure-gauge">
        <div class="gauge-needle" id="pressureNeedle"></div>
        <div class="gauge-center"></div>
    </div>
    
    <script>
        // Initialize bubbles
        function createBubbles() {
            const container = document.getElementById('bubbleContainer');
            for (let i = 0; i < 20; i++) {
                const bubble = document.createElement('div');
                bubble.className = 'bubble';
                bubble.style.width = Math.random() * 40 + 10 + 'px';
                bubble.style.height = bubble.style.width;
                bubble.style.left = Math.random() * 100 + '%';
                bubble.style.animationDelay = Math.random() * 10 + 's';
                bubble.style.animationDuration = Math.random() * 10 + 10 + 's';
                container.appendChild(bubble);
            }
        }
        
        // Active filters
        let activeFilters = new Set();
        
        // Search history
        let searchHistory = [];
        
        // Saved searches
        let savedSearches = JSON.parse(localStorage.getItem('oceanSearches') || '[]');
        
        // Toggle filter
        function toggleFilter(element, filter) {
            element.classList.toggle('active');
            if (activeFilters.has(filter)) {
                activeFilters.delete(filter);
            } else {
                activeFilters.add(filter);
            }
            updatePressure();
        }
        
        // Add operator to search
        function addOperator(operator) {
            const input = document.getElementById('searchInput');
            if (operator === '\"\"') {
                const start = input.selectionStart;
                const end = input.selectionEnd;
                const text = input.value;
                input.value = text.substring(0, start) + '\"' + text.substring(start, end) + '\"' + text.substring(end);
                input.focus();
                input.setSelectionRange(end + 2, end + 2);
            } else if (operator === '[]') {
                input.value += ' [TO ]';
                input.focus();
                input.setSelectionRange(input.value.length - 4, input.value.length - 4);
            } else {
                input.value += ' ' + operator + ' ';
                input.focus();
            }
        }
        
        // Autocomplete functionality
        const searchInput = document.getElementById('searchInput');
        const autocompleteBubbles = document.getElementById('autocompleteBubbles');
        const suggestions = [
            'coral reefs ecosystem',
            'deep sea creatures',
            'ocean currents patterns',
            'marine biodiversity',
            'underwater volcanoes',
            'bioluminescent organisms',
            'ocean floor mapping',
            'submarine canyons',
            'hydrothermal vents',
            'abyssal plains'
        ];
        
        searchInput.addEventListener('input', function() {
            const value = this.value.toLowerCase();
            if (value.length > 2) {
                const matches = suggestions.filter(s => s.toLowerCase().includes(value));
                showAutocomplete(matches);
            } else {
                hideAutocomplete();
            }
        });
        
        function showAutocomplete(matches) {
            autocompleteBubbles.innerHTML = '';
            if (matches.length > 0) {
                autocompleteBubbles.classList.add('active');
                matches.slice(0, 5).forEach((match, index) => {
                    const bubble = document.createElement('div');
                    bubble.className = 'bubble-suggestion';
                    bubble.textContent = match;
                    bubble.style.animationDelay = index * 0.1 + 's';
                    bubble.onclick = () => {
                        searchInput.value = match;
                        hideAutocomplete();
                    };
                    autocompleteBubbles.appendChild(bubble);
                });
            }
        }
        
        function hideAutocomplete() {
            autocompleteBubbles.classList.remove('active');
        }
        
        // Perform search
        function performSearch() {
            const query = searchInput.value;
            if (query.trim()) {
                // Add to history
                addToHistory(query);
                
                // Generate results
                generateResults(query);
                
                // Update pressure
                updatePressure();
                
                // Hide autocomplete
                hideAutocomplete();
            }
        }
        
        // Add to history
        function addToHistory(query) {
            const timestamp = new Date().toLocaleTimeString();
            searchHistory.unshift({ query, timestamp });
            if (searchHistory.length > 10) searchHistory.pop();
            updateHistoryDisplay();
        }
        
        // Update history display
        function updateHistoryDisplay() {
            const historyContainer = document.getElementById('searchHistory');
            historyContainer.innerHTML = '';
            searchHistory.forEach(entry => {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `
                    <div>${entry.query}</div>
                    <div class="log-timestamp">${entry.timestamp}</div>
                `;
                logEntry.onclick = () => {
                    searchInput.value = entry.query;
                    performSearch();
                };
                historyContainer.appendChild(logEntry);
            });
        }
        
        // Generate results
        function generateResults(query) {
            const resultsOcean = document.getElementById('resultsOcean');
            resultsOcean.innerHTML = '';
            
            const sampleResults = [
                { title: 'Deep Ocean Mysteries', description: 'Exploring the unknown depths where light cannot reach...' },
                { title: 'Bioluminescent Wonders', description: 'Creatures that create their own light in the darkness...' },
                { title: 'Underwater Canyons', description: 'Massive geological formations carved by ancient currents...' },
                { title: 'Marine Life Adaptations', description: 'How organisms survive extreme pressure and darkness...' },
                { title: 'Ocean Current Patterns', description: 'The invisible rivers that flow through our seas...' }
            ];
            
            sampleResults.forEach((result, index) => {
                const fishResult = document.createElement('div');
                fishResult.className = 'fish-result';
                fishResult.style.animationDelay = index * 2 + 's';
                fishResult.innerHTML = `
                    <div class="result-title">${result.title}</div>
                    <div class="result-description">${result.description}</div>
                `;
                resultsOcean.appendChild(fishResult);
            });
        }
        
        // Toggle saved searches
        function toggleSavedSearches() {
            const savedSearchesPanel = document.getElementById('savedSearches');
            savedSearchesPanel.classList.toggle('open');
            if (savedSearchesPanel.classList.contains('open')) {
                updateSavedSearchesList();
            }
        }
        
        // Save current search
        function saveCurrentSearch() {
            const query = searchInput.value;
            if (query.trim() && !savedSearches.includes(query)) {
                savedSearches.push(query);
                localStorage.setItem('oceanSearches', JSON.stringify(savedSearches));
                updateSavedSearchesList();
            }
        }
        
        // Update saved searches list
        function updateSavedSearchesList() {
            const list = document.getElementById('savedSearchesList');
            list.innerHTML = '';
            savedSearches.forEach(search => {
                const item = document.createElement('div');
                item.className = 'log-entry';
                item.textContent = search;
                item.onclick = () => {
                    searchInput.value = search;
                    performSearch();
                    toggleSavedSearches();
                };
                list.appendChild(item);
            });
        }
        
        // Update pressure gauge
        function updatePressure() {
            const needle = document.getElementById('pressureNeedle');
            const pressure = (activeFilters.size * 15) + (searchHistory.length * 5);
            needle.style.transform = `translateX(-50%) rotate(${Math.min(pressure, 180)}deg)`;
        }
        
        // Keyboard shortcuts
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Initialize
        createBubbles();
        updateSavedSearchesList();
        
        // Click outside to close autocomplete
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !autocompleteBubbles.contains(e.target)) {
                hideAutocomplete();
            }
        });
    </script>
</body>
</html>