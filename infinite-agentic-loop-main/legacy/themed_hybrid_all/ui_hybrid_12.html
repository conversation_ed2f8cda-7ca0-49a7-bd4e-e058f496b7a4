<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Art Deco Profile Dashboard - Iteration 12</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Bebas+Neue&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Playfair Display', serif;
            background: #0a0a0a;
            color: #f4e7d1;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Art Deco Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 10px,
                    rgba(212, 175, 55, 0.03) 10px,
                    rgba(212, 175, 55, 0.03) 20px
                ),
                repeating-linear-gradient(
                    -45deg,
                    transparent,
                    transparent 10px,
                    rgba(212, 175, 55, 0.03) 10px,
                    rgba(212, 175, 55, 0.03) 20px
                );
            pointer-events: none;
            z-index: 1;
        }

        .dashboard {
            position: relative;
            z-index: 2;
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* Header with Art Deco Frame */
        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .header h1 {
            font-family: 'Bebas Neue', cursive;
            font-size: 4rem;
            letter-spacing: 0.2em;
            color: #d4af37;
            text-shadow: 
                0 0 20px rgba(212, 175, 55, 0.5),
                2px 2px 4px rgba(0, 0, 0, 0.8);
            margin-bottom: 10px;
        }

        .header-ornament {
            width: 300px;
            height: 40px;
            margin: 0 auto;
            background: linear-gradient(90deg, 
                transparent 0%, 
                #d4af37 20%, 
                #d4af37 80%, 
                transparent 100%
            );
            position: relative;
            clip-path: polygon(
                0 50%, 10% 0, 90% 0, 100% 50%, 
                90% 100%, 10% 100%
            );
        }

        /* Main Grid Layout */
        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        /* Profile Section */
        .profile-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 2px solid #d4af37;
            border-radius: 0;
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-conic-gradient(
                from 0deg at 50% 50%,
                transparent 0deg,
                rgba(212, 175, 55, 0.1) 10deg,
                transparent 20deg
            );
            animation: rotate 60s linear infinite;
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        /* Avatar Container */
        .avatar-container {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto 30px;
            z-index: 2;
        }

        .avatar-frame {
            position: absolute;
            inset: -20px;
            background: conic-gradient(
                from 45deg,
                #d4af37 0deg,
                #f4e7d1 45deg,
                #d4af37 90deg,
                #8b6914 135deg,
                #d4af37 180deg,
                #f4e7d1 225deg,
                #d4af37 270deg,
                #8b6914 315deg,
                #d4af37 360deg
            );
            clip-path: polygon(
                50% 0%, 100% 25%, 100% 75%, 50% 100%, 
                0% 75%, 0% 25%
            );
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .avatar {
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
            clip-path: polygon(
                50% 0%, 100% 25%, 100% 75%, 50% 100%, 
                0% 75%, 0% 25%
            );
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Bebas Neue', cursive;
            font-size: 4rem;
            color: #d4af37;
        }

        .profile-name {
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 10px;
            color: #f4e7d1;
            position: relative;
            z-index: 2;
        }

        .profile-title {
            font-size: 1rem;
            text-align: center;
            color: #d4af37;
            letter-spacing: 0.2em;
            text-transform: uppercase;
            position: relative;
            z-index: 2;
        }

        /* Stats as Golden Ratio Meters */
        .stats-container {
            margin-top: 40px;
            position: relative;
            z-index: 2;
        }

        .stat-meter {
            margin-bottom: 25px;
        }

        .stat-label {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 8px;
            color: #d4af37;
        }

        .meter-track {
            height: 20px;
            background: #1a1a1a;
            border: 1px solid #d4af37;
            position: relative;
            overflow: hidden;
        }

        .meter-fill {
            height: 100%;
            background: linear-gradient(90deg, 
                #8b6914 0%, 
                #d4af37 50%, 
                #f4e7d1 100%
            );
            transition: width 1s ease-out;
            position: relative;
        }

        .meter-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 100%
            );
            animation: slide 2s infinite;
        }

        @keyframes slide {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .meter-value {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-family: 'Bebas Neue', cursive;
            color: #0a0a0a;
            font-weight: bold;
            z-index: 2;
        }

        /* Main Content Area */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        /* Activity Feed as Newspaper */
        .activity-feed {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 2px solid #d4af37;
            padding: 30px;
            position: relative;
        }

        .feed-header {
            border-bottom: 3px double #d4af37;
            padding-bottom: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .feed-title {
            font-family: 'Bebas Neue', cursive;
            font-size: 2.5rem;
            letter-spacing: 0.3em;
            color: #d4af37;
        }

        .feed-date {
            font-size: 0.9rem;
            color: #8b6914;
            margin-top: 5px;
        }

        .feed-columns {
            column-count: 2;
            column-gap: 30px;
            column-rule: 1px solid #d4af37;
        }

        .feed-item {
            break-inside: avoid;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(212, 175, 55, 0.05);
            border-left: 3px solid #d4af37;
        }

        .feed-item-time {
            font-size: 0.8rem;
            color: #8b6914;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .feed-item-text {
            margin-top: 5px;
            line-height: 1.6;
        }

        /* Settings Panel */
        .settings-panel {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 2px solid #d4af37;
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .control-group {
            padding: 20px;
            background: rgba(212, 175, 55, 0.05);
            border: 1px solid #d4af37;
            position: relative;
        }

        .control-label {
            font-family: 'Bebas Neue', cursive;
            font-size: 1.2rem;
            letter-spacing: 0.1em;
            color: #d4af37;
            margin-bottom: 15px;
        }

        .luxury-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #1a1a1a;
            border: 2px solid #d4af37;
            cursor: pointer;
            transition: all 0.3s;
        }

        .luxury-switch::before {
            content: '';
            position: absolute;
            width: 22px;
            height: 22px;
            top: 2px;
            left: 2px;
            background: #d4af37;
            transition: transform 0.3s;
        }

        .luxury-switch.active::before {
            transform: translateX(30px);
        }

        .luxury-slider {
            width: 100%;
            height: 30px;
            background: #1a1a1a;
            border: 2px solid #d4af37;
            position: relative;
            cursor: pointer;
            margin-top: 10px;
        }

        .slider-fill {
            height: 100%;
            background: linear-gradient(90deg, #8b6914, #d4af37);
            width: 50%;
            transition: width 0.3s;
        }

        /* Achievements as Medallions */
        .achievements-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 2px solid #d4af37;
            padding: 30px;
            margin-top: 30px;
        }

        .achievements-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .achievements-title {
            font-family: 'Bebas Neue', cursive;
            font-size: 2rem;
            letter-spacing: 0.2em;
            color: #d4af37;
        }

        .medallions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
        }

        .medallion {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .medallion:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .medallion-outer {
            position: absolute;
            inset: 0;
            background: conic-gradient(
                from 0deg,
                #d4af37 0deg,
                #f4e7d1 60deg,
                #d4af37 120deg,
                #8b6914 180deg,
                #d4af37 240deg,
                #f4e7d1 300deg,
                #d4af37 360deg
            );
            border-radius: 50%;
            animation: rotate 20s linear infinite;
        }

        .medallion-inner {
            position: absolute;
            inset: 10px;
            background: #1a1a1a;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Bebas Neue', cursive;
            font-size: 2rem;
            color: #d4af37;
        }

        .medallion-name {
            text-align: center;
            margin-top: 10px;
            font-size: 0.8rem;
            color: #d4af37;
        }

        /* Connections Constellation */
        .connections-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border: 2px solid #d4af37;
            padding: 30px;
            margin-top: 30px;
            position: relative;
            overflow: hidden;
        }

        .constellation-canvas {
            width: 100%;
            height: 300px;
            position: relative;
        }

        .connection-node {
            position: absolute;
            width: 60px;
            height: 60px;
            background: #1a1a1a;
            border: 2px solid #d4af37;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Bebas Neue', cursive;
            color: #d4af37;
            cursor: pointer;
            transition: all 0.3s;
            z-index: 2;
        }

        .connection-node:hover {
            transform: scale(1.2);
            background: #d4af37;
            color: #0a0a0a;
            box-shadow: 0 0 30px rgba(212, 175, 55, 0.8);
        }

        .connection-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                #d4af37 50%, 
                transparent 100%
            );
            transform-origin: left center;
            z-index: 1;
            opacity: 0.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .feed-columns {
                column-count: 1;
            }

            .header h1 {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Header -->
        <header class="header">
            <h1>PROFILE LUXE</h1>
            <div class="header-ornament"></div>
        </header>

        <!-- Main Grid -->
        <div class="main-grid">
            <!-- Profile Section -->
            <aside class="profile-section">
                <!-- Avatar -->
                <div class="avatar-container">
                    <div class="avatar-frame"></div>
                    <div class="avatar">VL</div>
                </div>
                <h2 class="profile-name">Victoria Luxmore</h2>
                <p class="profile-title">Elite Member Since 1925</p>

                <!-- Stats Meters -->
                <div class="stats-container">
                    <div class="stat-meter">
                        <div class="stat-label">Prestige Level</div>
                        <div class="meter-track">
                            <div class="meter-fill" style="width: 85%">
                                <span class="meter-value">85</span>
                            </div>
                        </div>
                    </div>
                    <div class="stat-meter">
                        <div class="stat-label">Social Influence</div>
                        <div class="meter-track">
                            <div class="meter-fill" style="width: 72%">
                                <span class="meter-value">72</span>
                            </div>
                        </div>
                    </div>
                    <div class="stat-meter">
                        <div class="stat-label">Activity Score</div>
                        <div class="meter-track">
                            <div class="meter-fill" style="width: 90%">
                                <span class="meter-value">90</span>
                            </div>
                        </div>
                    </div>
                    <div class="stat-meter">
                        <div class="stat-label">Elite Status</div>
                        <div class="meter-track">
                            <div class="meter-fill" style="width: 95%">
                                <span class="meter-value">95</span>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Activity Feed -->
                <section class="activity-feed">
                    <div class="feed-header">
                        <h2 class="feed-title">SOCIETY CHRONICLE</h2>
                        <p class="feed-date">December 6, 1925</p>
                    </div>
                    <div class="feed-columns">
                        <article class="feed-item">
                            <time class="feed-item-time">10:45 PM</time>
                            <p class="feed-item-text">Attended the exclusive Gatsby soirée at the Plaza. Spectacular evening of jazz and champagne.</p>
                        </article>
                        <article class="feed-item">
                            <time class="feed-item-time">8:30 PM</time>
                            <p class="feed-item-text">Achieved Diamond Elite status. Privileges now include access to the Penthouse Lounge.</p>
                        </article>
                        <article class="feed-item">
                            <time class="feed-item-time">6:15 PM</time>
                            <p class="feed-item-text">New connection established with Charleston M. Distinguished member of the Arts Society.</p>
                        </article>
                        <article class="feed-item">
                            <time class="feed-item-time">4:00 PM</time>
                            <p class="feed-item-text">Portfolio update: Art collection valued at new heights. Monet acquisition confirmed.</p>
                        </article>
                        <article class="feed-item">
                            <time class="feed-item-time">2:30 PM</time>
                            <p class="feed-item-text">Received invitation to the Annual Metropolis Gala. Black tie mandatory.</p>
                        </article>
                        <article class="feed-item">
                            <time class="feed-item-time">12:00 PM</time>
                            <p class="feed-item-text">Luncheon at the Ritz with fellow Elite members. Discussed upcoming charity auction.</p>
                        </article>
                    </div>
                </section>

                <!-- Settings Panel -->
                <section class="settings-panel">
                    <div class="control-group">
                        <h3 class="control-label">PRIVACY MODE</h3>
                        <div class="luxury-switch" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-group">
                        <h3 class="control-label">NOTIFICATIONS</h3>
                        <div class="luxury-switch active" onclick="toggleSwitch(this)"></div>
                    </div>
                    <div class="control-group">
                        <h3 class="control-label">VISIBILITY</h3>
                        <div class="luxury-slider" onclick="adjustSlider(event, this)">
                            <div class="slider-fill"></div>
                        </div>
                    </div>
                    <div class="control-group">
                        <h3 class="control-label">EXCLUSIVITY</h3>
                        <div class="luxury-slider" onclick="adjustSlider(event, this)">
                            <div class="slider-fill" style="width: 80%"></div>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <!-- Achievements Section -->
        <section class="achievements-section">
            <div class="achievements-header">
                <h2 class="achievements-title">MEDALLIONS OF DISTINCTION</h2>
            </div>
            <div class="medallions-grid">
                <div class="medallion">
                    <div class="medallion-outer"></div>
                    <div class="medallion-inner">★</div>
                    <p class="medallion-name">ELITE STATUS</p>
                </div>
                <div class="medallion">
                    <div class="medallion-outer"></div>
                    <div class="medallion-inner">♦</div>
                    <p class="medallion-name">DIAMOND TIER</p>
                </div>
                <div class="medallion">
                    <div class="medallion-outer"></div>
                    <div class="medallion-inner">♠</div>
                    <p class="medallion-name">HIGH SOCIETY</p>
                </div>
                <div class="medallion">
                    <div class="medallion-outer"></div>
                    <div class="medallion-inner">♣</div>
                    <p class="medallion-name">CLUB MEMBER</p>
                </div>
                <div class="medallion">
                    <div class="medallion-outer"></div>
                    <div class="medallion-inner">♥</div>
                    <p class="medallion-name">PHILANTHROPIST</p>
                </div>
                <div class="medallion">
                    <div class="medallion-outer"></div>
                    <div class="medallion-inner">⚜</div>
                    <p class="medallion-name">ARISTOCRAT</p>
                </div>
            </div>
        </section>

        <!-- Connections Constellation -->
        <section class="connections-section">
            <h2 class="achievements-title" style="text-align: center; margin-bottom: 30px;">SOCIAL CONSTELLATION</h2>
            <div class="constellation-canvas" id="constellation">
                <!-- Connection nodes will be dynamically positioned -->
            </div>
        </section>
    </div>

    <script>
        // Toggle luxury switches
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // Adjust luxury sliders
        function adjustSlider(event, slider) {
            const rect = slider.getBoundingClientRect();
            const percentage = ((event.clientX - rect.left) / rect.width) * 100;
            const fill = slider.querySelector('.slider-fill');
            fill.style.width = Math.max(0, Math.min(100, percentage)) + '%';
        }

        // Animate stat meters on load
        window.addEventListener('load', () => {
            const meters = document.querySelectorAll('.meter-fill');
            meters.forEach((meter, index) => {
                const targetWidth = meter.style.width;
                meter.style.width = '0%';
                setTimeout(() => {
                    meter.style.width = targetWidth;
                }, 100 + index * 200);
            });
        });

        // Create connections constellation
        function createConstellation() {
            const canvas = document.getElementById('constellation');
            const connections = [
                { id: 'CM', x: 20, y: 30 },
                { id: 'JG', x: 80, y: 20 },
                { id: 'DF', x: 50, y: 50 },
                { id: 'NK', x: 30, y: 70 },
                { id: 'TW', x: 70, y: 80 },
                { id: 'ES', x: 90, y: 60 },
                { id: 'RL', x: 10, y: 50 }
            ];

            // Create nodes
            connections.forEach(conn => {
                const node = document.createElement('div');
                node.className = 'connection-node';
                node.textContent = conn.id;
                node.style.left = conn.x + '%';
                node.style.top = conn.y + '%';
                node.style.transform = 'translate(-50%, -50%)';
                canvas.appendChild(node);
            });

            // Create connection lines
            for (let i = 0; i < connections.length; i++) {
                for (let j = i + 1; j < connections.length; j++) {
                    if (Math.random() > 0.5) { // Random connections
                        const line = document.createElement('div');
                        line.className = 'connection-line';
                        
                        const x1 = connections[i].x;
                        const y1 = connections[i].y;
                        const x2 = connections[j].x;
                        const y2 = connections[j].y;
                        
                        const distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
                        
                        line.style.width = distance + '%';
                        line.style.left = x1 + '%';
                        line.style.top = y1 + '%';
                        line.style.transform = `rotate(${angle}deg)`;
                        
                        canvas.appendChild(line);
                    }
                }
            }
        }

        createConstellation();

        // Add hover effects to medallions
        document.querySelectorAll('.medallion').forEach(medallion => {
            medallion.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = '';
                }, 10);
            });
        });

        // Parallax effect for background pattern
        document.addEventListener('mousemove', (e) => {
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;
            
            document.body.style.backgroundPosition = `${x * 20}px ${y * 20}px`;
        });
    </script>
</body>
</html>