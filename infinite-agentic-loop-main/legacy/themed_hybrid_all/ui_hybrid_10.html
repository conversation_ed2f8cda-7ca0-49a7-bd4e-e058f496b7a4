<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architectural Brutalism Dashboard Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            line-height: 1.6;
            padding: 40px;
            min-height: 100vh;
            overflow-x: hidden;
        }

        main {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            font-size: 3em;
            font-weight: 900;
            letter-spacing: -0.02em;
            color: #fff;
            margin-bottom: 40px;
            text-transform: uppercase;
            text-shadow: 4px 4px 0 #000;
        }

        /* Brutalist Dashboard Container */
        .hybrid-component {
            background: #2a2a2a;
            padding: 0;
            border: 8px solid #333;
            box-shadow: 
                0 10px 0 #1a1a1a,
                0 20px 0 #0a0a0a,
                0 20px 40px rgba(0,0,0,0.8);
            position: relative;
        }

        /* Control Panel Header */
        .control-panel {
            background: linear-gradient(to bottom, #404040, #303030);
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 4px solid #1a1a1a;
            position: relative;
        }

        .control-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: repeating-linear-gradient(
                90deg,
                #ff6b6b 0,
                #ff6b6b 20px,
                #ffcc00 20px,
                #ffcc00 40px
            );
        }

        /* Industrial Switches (Filters) */
        .filter-bank {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .industrial-switch {
            position: relative;
            width: 80px;
            height: 40px;
            background: #1a1a1a;
            border: 3px solid #333;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .industrial-switch:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 0 #000;
        }

        .switch-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
            color: #999;
        }

        .switch-handle {
            position: absolute;
            left: 3px;
            top: 3px;
            width: 34px;
            height: 28px;
            background: linear-gradient(to bottom, #666, #444);
            border: 2px solid #222;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .industrial-switch.active .switch-handle {
            left: 39px;
            background: linear-gradient(to bottom, #ff6b6b, #e55a5a);
            box-shadow: 0 0 10px #ff6b6b;
        }

        /* Control Buttons */
        .control-buttons {
            display: flex;
            gap: 15px;
        }

        .brutalist-button {
            background: #333;
            border: 3px solid #444;
            color: #fff;
            padding: 10px 20px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
            font-size: 12px;
        }

        .brutalist-button:hover {
            transform: translate(-2px, -2px);
            box-shadow: 2px 2px 0 #ff6b6b;
        }

        .brutalist-button:active {
            transform: translate(0, 0);
            box-shadow: none;
        }

        .brutalist-button.refresh {
            background: #444;
            border-color: #555;
        }

        .brutalist-button.export {
            background: #3a3a3a;
            border-color: #4a4a4a;
        }

        .brutalist-button.settings {
            background: #383838;
            border-color: #484848;
            padding: 10px 15px;
        }

        /* Main Dashboard Content */
        .dashboard-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            min-height: 500px;
        }

        /* Chart Area - Concrete Blocks */
        .chart-container {
            background: #1a1a1a;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .concrete-chart {
            height: 400px;
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            gap: 20px;
            position: relative;
        }

        .chart-block {
            flex: 1;
            background: linear-gradient(to bottom, #4a4a4a, #3a3a3a);
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 4px solid #2a2a2a;
            box-shadow: 
                inset 0 -4px 0 #1a1a1a,
                0 4px 0 #0a0a0a;
        }

        .chart-block:hover {
            transform: translateY(-10px);
            box-shadow: 
                inset 0 -4px 0 #1a1a1a,
                0 14px 0 #0a0a0a,
                0 14px 20px rgba(0,0,0,0.5);
        }

        .chart-block::before {
            content: attr(data-value);
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 900;
            font-size: 18px;
            color: #ff6b6b;
            text-shadow: 2px 2px 0 #000;
        }

        .chart-block::after {
            content: attr(data-label);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 700;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #666;
        }

        /* Grid Overlay */
        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            background-image: 
                repeating-linear-gradient(0deg, transparent, transparent 39px, #2a2a2a 39px, #2a2a2a 40px),
                repeating-linear-gradient(90deg, transparent, transparent 19.5%, #2a2a2a 19.5%, #2a2a2a 20%);
            opacity: 0.3;
        }

        /* Side Panel - Settings & Alerts */
        .side-panel {
            background: #262626;
            border-left: 4px solid #1a1a1a;
            display: flex;
            flex-direction: column;
        }

        /* Settings Panel */
        .settings-panel {
            padding: 30px 20px;
            border-bottom: 4px solid #1a1a1a;
        }

        .panel-title {
            font-weight: 900;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 20px;
            color: #999;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .brutalist-slider {
            width: 100%;
            height: 8px;
            background: #1a1a1a;
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
            position: relative;
        }

        .brutalist-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: linear-gradient(to bottom, #666, #444);
            border: 3px solid #222;
            cursor: pointer;
        }

        .brutalist-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: linear-gradient(to bottom, #666, #444);
            border: 3px solid #222;
            cursor: pointer;
        }

        .brutalist-select {
            width: 100%;
            padding: 8px 12px;
            background: #1a1a1a;
            border: 3px solid #333;
            color: #999;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            font-size: 11px;
        }

        /* Alert System */
        .alert-panel {
            flex: 1;
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
        }

        .alert-lights {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .alert-indicator {
            aspect-ratio: 1;
            background: #1a1a1a;
            border: 4px solid #2a2a2a;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }

        .alert-indicator::before {
            content: attr(data-alert);
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 9px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #555;
            white-space: nowrap;
        }

        .alert-indicator.active {
            animation: alertPulse 1s infinite;
            background: #ff6b6b;
            border-color: #ff4444;
            box-shadow: 
                0 0 20px #ff6b6b,
                inset 0 0 20px rgba(255,255,255,0.3);
        }

        .alert-indicator.warning {
            background: #ffcc00;
            border-color: #ffaa00;
            box-shadow: 
                0 0 20px #ffcc00,
                inset 0 0 20px rgba(255,255,255,0.3);
        }

        @keyframes alertPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(0.95); }
        }

        /* Export Blueprint Overlay */
        .blueprint-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.95);
            display: none;
            z-index: 1000;
            padding: 40px;
            overflow: auto;
        }

        .blueprint-overlay.active {
            display: block;
        }

        .blueprint-content {
            max-width: 1200px;
            margin: 0 auto;
            background: #001a33;
            border: 4px solid #003366;
            padding: 40px;
            position: relative;
        }

        .blueprint-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                repeating-linear-gradient(0deg, transparent, transparent 19px, #003366 19px, #003366 20px),
                repeating-linear-gradient(90deg, transparent, transparent 19px, #003366 19px, #003366 20px);
            opacity: 0.2;
            pointer-events: none;
        }

        .blueprint-title {
            font-size: 24px;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #66ccff;
            margin-bottom: 30px;
            text-shadow: 2px 2px 0 #003366;
        }

        .blueprint-data {
            font-family: 'Courier New', monospace;
            color: #66ccff;
            line-height: 1.8;
            position: relative;
            z-index: 1;
        }

        .blueprint-close {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: #003366;
            border: 3px solid #66ccff;
            color: #66ccff;
            font-size: 24px;
            font-weight: 900;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .blueprint-close:hover {
            background: #66ccff;
            color: #001a33;
            transform: rotate(90deg);
        }

        /* Rotating Refresh Animation */
        @keyframes concreteRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .brutalist-button.refresh.rotating {
            animation: concreteRotate 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .side-panel {
                border-left: none;
                border-top: 4px solid #1a1a1a;
                display: grid;
                grid-template-columns: 1fr 1fr;
            }
            
            .settings-panel {
                border-right: 4px solid #1a1a1a;
                border-bottom: none;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .control-panel {
                flex-direction: column;
                gap: 20px;
                align-items: stretch;
            }
            
            .filter-bank {
                justify-content: space-around;
            }
            
            .control-buttons {
                justify-content: space-around;
            }
            
            .chart-container {
                padding: 20px;
            }
            
            .concrete-chart {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Dashboard Widget - Architectural Brutalism Theme</h1>
        
        <div class="hybrid-component">
            <!-- Control Panel with Filters and Actions -->
            <div class="control-panel">
                <div class="filter-bank">
                    <div class="industrial-switch" data-filter="revenue">
                        <span class="switch-label">Revenue</span>
                        <div class="switch-handle"></div>
                    </div>
                    <div class="industrial-switch" data-filter="users">
                        <span class="switch-label">Users</span>
                        <div class="switch-handle"></div>
                    </div>
                    <div class="industrial-switch" data-filter="traffic">
                        <span class="switch-label">Traffic</span>
                        <div class="switch-handle"></div>
                    </div>
                    <div class="industrial-switch" data-filter="sales">
                        <span class="switch-label">Sales</span>
                        <div class="switch-handle"></div>
                    </div>
                </div>
                
                <div class="control-buttons">
                    <button class="brutalist-button refresh">↻ Refresh</button>
                    <button class="brutalist-button export">⬇ Export</button>
                    <button class="brutalist-button settings">⚙</button>
                </div>
            </div>
            
            <!-- Main Dashboard Content -->
            <div class="dashboard-content">
                <!-- Chart Container with Concrete Blocks -->
                <div class="chart-container">
                    <div class="concrete-chart" id="chart">
                        <div class="chart-block" data-value="87%" data-label="Jan" style="height: 87%"></div>
                        <div class="chart-block" data-value="92%" data-label="Feb" style="height: 92%"></div>
                        <div class="chart-block" data-value="76%" data-label="Mar" style="height: 76%"></div>
                        <div class="chart-block" data-value="84%" data-label="Apr" style="height: 84%"></div>
                        <div class="chart-block" data-value="95%" data-label="May" style="height: 95%"></div>
                        <div class="chart-block" data-value="79%" data-label="Jun" style="height: 79%"></div>
                    </div>
                    <div class="grid-overlay"></div>
                </div>
                
                <!-- Side Panel with Settings and Alerts -->
                <div class="side-panel">
                    <!-- Settings Panel -->
                    <div class="settings-panel">
                        <h3 class="panel-title">Control Infrastructure</h3>
                        
                        <div class="control-group">
                            <label class="control-label">Update Frequency</label>
                            <input type="range" class="brutalist-slider" min="1" max="60" value="30">
                        </div>
                        
                        <div class="control-group">
                            <label class="control-label">Data Granularity</label>
                            <select class="brutalist-select">
                                <option>Hourly</option>
                                <option>Daily</option>
                                <option selected>Monthly</option>
                                <option>Yearly</option>
                            </select>
                        </div>
                        
                        <div class="control-group">
                            <label class="control-label">Alert Threshold</label>
                            <input type="range" class="brutalist-slider" min="0" max="100" value="75">
                        </div>
                    </div>
                    
                    <!-- Alert Panel -->
                    <div class="alert-panel">
                        <h3 class="panel-title">Warning System</h3>
                        
                        <div class="alert-lights">
                            <div class="alert-indicator" data-alert="CPU Load"></div>
                            <div class="alert-indicator active" data-alert="Memory"></div>
                            <div class="alert-indicator" data-alert="Network"></div>
                            <div class="alert-indicator warning" data-alert="Storage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Blueprint Export Overlay -->
        <div class="blueprint-overlay" id="blueprintOverlay">
            <div class="blueprint-content">
                <div class="blueprint-grid"></div>
                <button class="blueprint-close" onclick="closeBlueprint()">×</button>
                <h2 class="blueprint-title">System Architecture Blueprint</h2>
                <pre class="blueprint-data" id="blueprintData"></pre>
            </div>
        </div>
    </main>

    <script>
        // Industrial Switch Functionality
        const switches = document.querySelectorAll('.industrial-switch');
        const chartBlocks = document.querySelectorAll('.chart-block');
        
        switches.forEach(switchEl => {
            switchEl.addEventListener('click', function() {
                this.classList.toggle('active');
                updateChartData();
            });
        });
        
        // Chart Block Interactions
        chartBlocks.forEach(block => {
            block.addEventListener('click', function() {
                // Animate block
                this.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                this.style.transform = 'translateY(-20px) scale(1.05)';
                
                setTimeout(() => {
                    this.style.transform = 'translateY(0) scale(1)';
                }, 600);
                
                // Flash value
                const value = this.getAttribute('data-value');
                this.setAttribute('data-value', '▓▓▓');
                setTimeout(() => {
                    this.setAttribute('data-value', value);
                }, 300);
            });
        });
        
        // Refresh Functionality
        const refreshBtn = document.querySelector('.brutalist-button.refresh');
        refreshBtn.addEventListener('click', function() {
            this.classList.add('rotating');
            
            // Simulate data refresh
            setTimeout(() => {
                updateChartData();
                this.classList.remove('rotating');
                
                // Flash all blocks
                chartBlocks.forEach((block, index) => {
                    setTimeout(() => {
                        const newHeight = Math.floor(Math.random() * 30) + 70;
                        block.style.height = newHeight + '%';
                        block.setAttribute('data-value', newHeight + '%');
                    }, index * 100);
                });
            }, 2000);
        });
        
        // Export Blueprint Functionality
        const exportBtn = document.querySelector('.brutalist-button.export');
        exportBtn.addEventListener('click', function() {
            generateBlueprint();
            document.getElementById('blueprintOverlay').classList.add('active');
        });
        
        function generateBlueprint() {
            const activeFilters = Array.from(switches)
                .filter(s => s.classList.contains('active'))
                .map(s => s.getAttribute('data-filter'));
            
            const chartData = Array.from(chartBlocks).map(block => ({
                label: block.getAttribute('data-label'),
                value: block.getAttribute('data-value')
            }));
            
            const blueprint = `DASHBOARD CONFIGURATION BLUEPRINT
===============================================
Generated: ${new Date().toISOString()}
Version: 1.0.0

ACTIVE FILTERS:
${activeFilters.length ? activeFilters.map(f => `  - ${f.toUpperCase()}`).join('\n') : '  - NONE'}

CHART DATA STRUCTURE:
${chartData.map(d => `  ${d.label}: ${d.value}`).join('\n')}

SYSTEM ALERTS:
  - CPU Load: NORMAL
  - Memory: CRITICAL
  - Network: NORMAL
  - Storage: WARNING

SETTINGS:
  - Update Frequency: 30s
  - Data Granularity: MONTHLY
  - Alert Threshold: 75%

BUILD INSTRUCTIONS:
1. Initialize concrete block rendering engine
2. Configure industrial switch matrix
3. Establish alert monitoring pipeline
4. Activate data refresh mechanism
5. Enable blueprint export system

END BLUEPRINT
===============================================`;
            
            document.getElementById('blueprintData').textContent = blueprint;
        }
        
        function closeBlueprint() {
            document.getElementById('blueprintOverlay').classList.remove('active');
        }
        
        // Settings Panel Interactions
        const sliders = document.querySelectorAll('.brutalist-slider');
        sliders.forEach(slider => {
            slider.addEventListener('input', function() {
                // Visual feedback
                const percent = (this.value - this.min) / (this.max - this.min);
                this.style.background = `linear-gradient(to right, #ff6b6b 0%, #ff6b6b ${percent * 100}%, #1a1a1a ${percent * 100}%, #1a1a1a 100%)`;
            });
        });
        
        // Alert System
        const alertIndicators = document.querySelectorAll('.alert-indicator');
        
        // Simulate random alerts
        setInterval(() => {
            const randomAlert = alertIndicators[Math.floor(Math.random() * alertIndicators.length)];
            const alertType = Math.random() > 0.5 ? 'active' : 'warning';
            
            // Clear other alerts occasionally
            if (Math.random() > 0.7) {
                alertIndicators.forEach(alert => {
                    alert.classList.remove('active', 'warning');
                });
            }
            
            // Set new alert
            randomAlert.classList.add(alertType);
        }, 5000);
        
        // Update chart based on active filters
        function updateChartData() {
            const activeFilters = Array.from(switches)
                .filter(s => s.classList.contains('active'))
                .map(s => s.getAttribute('data-filter'));
            
            // Simulate different data based on filters
            chartBlocks.forEach((block, index) => {
                let baseValue = parseInt(block.style.height);
                
                activeFilters.forEach(filter => {
                    switch(filter) {
                        case 'revenue':
                            baseValue += Math.random() * 10 - 5;
                            break;
                        case 'users':
                            baseValue += Math.random() * 15 - 7.5;
                            break;
                        case 'traffic':
                            baseValue += Math.random() * 20 - 10;
                            break;
                        case 'sales':
                            baseValue += Math.random() * 12 - 6;
                            break;
                    }
                });
                
                baseValue = Math.max(20, Math.min(100, baseValue));
                block.style.height = Math.floor(baseValue) + '%';
                block.setAttribute('data-value', Math.floor(baseValue) + '%');
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'r' && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                refreshBtn.click();
            } else if (e.key === 'e' && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                exportBtn.click();
            } else if (e.key === 'Escape') {
                closeBlueprint();
            }
        });
        
        // Initialize sliders with gradient
        sliders.forEach(slider => {
            slider.dispatchEvent(new Event('input'));
        });
    </script>
</body>
</html>