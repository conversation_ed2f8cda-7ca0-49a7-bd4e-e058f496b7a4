<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organic Nature Search Hub</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #f5f5dc 0%, #e8e3d3 100%);
            color: #2d3a24;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background vines */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10,50 Q30,20 50,50 T90,50' stroke='%23a8b89c' stroke-width='0.5' fill='none' opacity='0.3'/%3E%3C/svg%3E");
            background-size: 200px 200px;
            animation: floatVines 30s ease-in-out infinite;
            pointer-events: none;
            opacity: 0.3;
        }

        @keyframes floatVines {
            0%, 100% { transform: translateX(0) translateY(0); }
            50% { transform: translateX(-20px) translateY(-10px); }
        }

        main {
            width: 100%;
            max-width: 800px;
            position: relative;
            z-index: 1;
        }

        h1 {
            text-align: center;
            color: #4a5c3a;
            margin-bottom: 40px;
            font-size: 2.5em;
            font-weight: 300;
            position: relative;
        }

        h1::after {
            content: '🌿';
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%) rotate(-20deg);
            font-size: 0.6em;
            animation: sway 4s ease-in-out infinite;
        }

        @keyframes sway {
            0%, 100% { transform: translateY(-50%) rotate(-20deg); }
            50% { transform: translateY(-50%) rotate(20deg); }
        }

        .hybrid-component {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 
                0 10px 40px rgba(74, 92, 58, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            position: relative;
            overflow: visible;
        }

        /* Decorative leaf corners */
        .hybrid-component::before,
        .hybrid-component::after {
            content: '🍃';
            position: absolute;
            font-size: 2em;
            opacity: 0.3;
            animation: leafFloat 6s ease-in-out infinite;
        }

        .hybrid-component::before {
            top: -10px;
            left: -10px;
            transform: rotate(-45deg);
        }

        .hybrid-component::after {
            bottom: -10px;
            right: -10px;
            transform: rotate(135deg);
            animation-delay: 3s;
        }

        @keyframes leafFloat {
            0%, 100% { transform: translateY(0) rotate(inherit); }
            50% { transform: translateY(-5px) rotate(calc(inherit + 10deg)); }
        }

        /* Search Container */
        .search-container {
            position: relative;
            margin-bottom: 30px;
        }

        .search-wrapper {
            position: relative;
            background: linear-gradient(135deg, #f0f4e8 0%, #e8eede 100%);
            border-radius: 25px;
            border: 2px solid #c5d4b5;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-wrapper:focus-within {
            transform: scale(1.02);
            box-shadow: 0 8px 30px rgba(139, 168, 112, 0.2);
            border-color: #8ba870;
        }

        .search-input {
            width: 100%;
            padding: 20px 60px 20px 25px;
            font-size: 18px;
            font-family: inherit;
            border: none;
            background: transparent;
            color: #2d3a24;
            outline: none;
        }

        .search-input::placeholder {
            color: #7a8b6a;
            transition: opacity 0.3s;
        }

        .search-input:focus::placeholder {
            opacity: 0.5;
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            transition: transform 0.3s ease-out;
        }

        .search-icon:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .search-icon svg {
            width: 100%;
            height: 100%;
            stroke: #7a8b6a;
            stroke-width: 2;
            fill: none;
            transition: stroke 0.3s;
        }

        .search-wrapper:focus-within .search-icon svg {
            stroke: #4a5c3a;
        }

        /* Growing vine animation on search */
        .search-vine {
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #8ba870 0%, #a4b896 50%, #8ba870 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-wrapper:focus-within .search-vine {
            transform: scaleX(1);
        }

        /* Autocomplete Dropdown */
        .autocomplete {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            margin-top: 10px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(74, 92, 58, 0.15);
            overflow: hidden;
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
            pointer-events: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;
        }

        .autocomplete.active {
            opacity: 1;
            transform: translateY(0) scale(1);
            pointer-events: all;
        }

        .autocomplete-item {
            padding: 15px 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease-out;
        }

        .autocomplete-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #8ba870;
            transform: scaleY(0);
            transition: transform 0.3s ease-out;
        }

        .autocomplete-item:hover {
            background: #f0f4e8;
            padding-left: 35px;
        }

        .autocomplete-item:hover::before {
            transform: scaleY(1);
        }

        .autocomplete-icon {
            font-size: 1.2em;
            opacity: 0;
            transform: scale(0) rotate(-180deg);
            transition: all 0.3s ease-out;
        }

        .autocomplete-item:hover .autocomplete-icon {
            opacity: 1;
            transform: scale(1) rotate(0);
        }

        /* Recent Items (Seed Pods) */
        .recent-items {
            margin-bottom: 30px;
        }

        .recent-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: #4a5c3a;
        }

        .recent-header h3 {
            font-size: 1.1em;
            font-weight: 400;
        }

        .seed-pods {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .seed-pod {
            background: linear-gradient(135deg, #e8d5b7 0%, #d4c4a0 100%);
            border: 1px solid #c9b897;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease-out;
            position: relative;
            overflow: hidden;
        }

        .seed-pod::before {
            content: '🌰';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0;
            transition: all 0.3s ease-out;
        }

        .seed-pod:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 15px rgba(180, 160, 120, 0.3);
            padding-left: 30px;
            background: linear-gradient(135deg, #f0e2c4 0%, #dcc8a5 100%);
        }

        .seed-pod:hover::before {
            left: 8px;
            opacity: 1;
        }

        /* Filters (Unfolding Leaves) */
        .filters {
            margin-bottom: 30px;
        }

        .filters-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .filters-header h3 {
            color: #4a5c3a;
            font-size: 1.1em;
            font-weight: 400;
        }

        .filter-toggle {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2em;
            transition: transform 0.3s ease-out;
        }

        .filter-toggle.active {
            transform: rotate(180deg);
        }

        .filter-leaves {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .filter-leaves.open {
            max-height: 200px;
            opacity: 1;
            margin-top: 15px;
        }

        .filter-leaf {
            background: linear-gradient(135deg, #d4e4c5 0%, #c5d4b5 100%);
            border: 1px solid #b5c4a5;
            border-radius: 15px 5px 15px 5px;
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s ease-out;
            position: relative;
            overflow: hidden;
        }

        .filter-leaf::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, #a4b896 0%, #8ba870 100%);
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }

        .filter-leaf.active::before {
            opacity: 1;
        }

        .filter-leaf span {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #4a5c3a;
            font-size: 14px;
            transition: color 0.3s;
        }

        .filter-leaf.active span {
            color: white;
        }

        .filter-leaf:hover {
            transform: scale(1.05) rotate(-2deg);
            box-shadow: 0 4px 15px rgba(139, 168, 112, 0.2);
        }

        /* Results Preview */
        .results-preview {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .results-preview.active {
            opacity: 1;
            transform: translateY(0);
        }

        .results-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e5e5;
        }

        .results-header h3 {
            color: #4a5c3a;
            font-size: 1.1em;
            font-weight: 400;
        }

        .results-count {
            color: #7a8b6a;
            font-size: 14px;
        }

        .result-item {
            background: linear-gradient(135deg, #fafaf5 0%, #f5f5f0 100%);
            border: 1px solid #e5e5e5;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease-out;
        }

        .result-item::before {
            content: '';
            position: absolute;
            left: -100%;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(139, 168, 112, 0.1) 50%, transparent 100%);
            transition: left 0.5s ease-out;
        }

        .result-item:hover::before {
            left: 100%;
        }

        .result-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(139, 168, 112, 0.1);
            border-color: #c5d4b5;
        }

        .result-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3a24;
            margin-bottom: 8px;
        }

        .result-description {
            font-size: 14px;
            color: #7a8b6a;
            line-height: 1.5;
        }

        .result-meta {
            display: flex;
            gap: 15px;
            margin-top: 10px;
            font-size: 12px;
            color: #a0a0a0;
        }

        /* Loading animation */
        .loading-roots {
            display: none;
            justify-content: center;
            align-items: center;
            height: 100px;
            gap: 10px;
        }

        .loading-roots.active {
            display: flex;
        }

        .root {
            width: 4px;
            height: 40px;
            background: linear-gradient(to bottom, #8ba870, #4a5c3a);
            border-radius: 2px;
            animation: growRoot 1.2s ease-in-out infinite;
        }

        .root:nth-child(2) { animation-delay: 0.1s; }
        .root:nth-child(3) { animation-delay: 0.2s; }
        .root:nth-child(4) { animation-delay: 0.3s; }
        .root:nth-child(5) { animation-delay: 0.4s; }

        @keyframes growRoot {
            0%, 100% {
                transform: scaleY(0.4);
                opacity: 0.5;
            }
            50% {
                transform: scaleY(1);
                opacity: 1;
            }
        }

        /* Breathing idle animation */
        .breathing {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
    </style>
</head>
<body>
    <main>
        <h1>Search Hub - Organic Nature Theme</h1>
        
        <div class="hybrid-component breathing">
            <!-- Search Bar with Growing Vine -->
            <div class="search-container">
                <div class="search-wrapper">
                    <input 
                        type="text" 
                        class="search-input" 
                        placeholder="Plant your search seeds..."
                        id="searchInput"
                    >
                    <div class="search-icon" id="searchIcon">
                        <svg viewBox="0 0 24 24">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </div>
                    <div class="search-vine"></div>
                </div>
                
                <!-- Autocomplete Dropdown (Blooming Flowers) -->
                <div class="autocomplete" id="autocomplete">
                    <div class="autocomplete-item">
                        <span class="autocomplete-icon">🌸</span>
                        <span>Organic vegetables delivery</span>
                    </div>
                    <div class="autocomplete-item">
                        <span class="autocomplete-icon">🌺</span>
                        <span>Organic farming techniques</span>
                    </div>
                    <div class="autocomplete-item">
                        <span class="autocomplete-icon">🌼</span>
                        <span>Organic garden design ideas</span>
                    </div>
                    <div class="autocomplete-item">
                        <span class="autocomplete-icon">🌻</span>
                        <span>Organic skincare products</span>
                    </div>
                </div>
            </div>
            
            <!-- Recent Items (Seed Pods) -->
            <div class="recent-items">
                <div class="recent-header">
                    <span>🌱</span>
                    <h3>Recently Planted</h3>
                </div>
                <div class="seed-pods">
                    <div class="seed-pod">Garden tools</div>
                    <div class="seed-pod">Composting guide</div>
                    <div class="seed-pod">Native plants</div>
                    <div class="seed-pod">Herb garden</div>
                    <div class="seed-pod">Soil health</div>
                </div>
            </div>
            
            <!-- Filters (Unfolding Leaves) -->
            <div class="filters">
                <div class="filters-header">
                    <h3>🍀 Filter by Category</h3>
                    <button class="filter-toggle" id="filterToggle">🍃</button>
                </div>
                <div class="filter-leaves" id="filterLeaves">
                    <div class="filter-leaf" data-filter="plants">
                        <span>🌿 Plants</span>
                    </div>
                    <div class="filter-leaf" data-filter="tools">
                        <span>🛠️ Tools</span>
                    </div>
                    <div class="filter-leaf" data-filter="guides">
                        <span>📖 Guides</span>
                    </div>
                    <div class="filter-leaf" data-filter="seeds">
                        <span>🌰 Seeds</span>
                    </div>
                    <div class="filter-leaf" data-filter="organic">
                        <span>🌾 Organic</span>
                    </div>
                    <div class="filter-leaf" data-filter="indoor">
                        <span>🪴 Indoor</span>
                    </div>
                </div>
            </div>
            
            <!-- Loading Animation -->
            <div class="loading-roots" id="loadingRoots">
                <div class="root"></div>
                <div class="root"></div>
                <div class="root"></div>
                <div class="root"></div>
                <div class="root"></div>
            </div>
            
            <!-- Results Preview (Natural Growth) -->
            <div class="results-preview" id="resultsPreview">
                <div class="results-header">
                    <h3>🌳 Growing Results</h3>
                    <span class="results-count">5 branches found</span>
                </div>
                <div class="result-item">
                    <div class="result-title">Organic Vegetable Garden Starter Kit</div>
                    <div class="result-description">
                        Everything you need to start your own organic vegetable garden, including heirloom seeds, 
                        natural fertilizers, and comprehensive growing guides.
                    </div>
                    <div class="result-meta">
                        <span>🌟 4.8 rating</span>
                        <span>💚 Eco-friendly</span>
                        <span>📦 Ships in 2 days</span>
                    </div>
                </div>
                <div class="result-item">
                    <div class="result-title">Complete Guide to Companion Planting</div>
                    <div class="result-description">
                        Learn which plants grow better together and create a thriving ecosystem in your garden
                        using natural symbiotic relationships.
                    </div>
                    <div class="result-meta">
                        <span>📚 Digital guide</span>
                        <span>🌱 Beginner friendly</span>
                        <span>⏱️ 45 min read</span>
                    </div>
                </div>
                <div class="result-item">
                    <div class="result-title">Handcrafted Cedar Raised Garden Beds</div>
                    <div class="result-description">
                        Beautiful, sustainably sourced cedar garden beds that naturally resist pests and weather
                        while providing the perfect growing environment.
                    </div>
                    <div class="result-meta">
                        <span>🪵 Sustainable wood</span>
                        <span>🔨 Easy assembly</span>
                        <span>🎯 Custom sizes</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Nature-inspired search hub functionality
        const searchInput = document.getElementById('searchInput');
        const autocomplete = document.getElementById('autocomplete');
        const filterToggle = document.getElementById('filterToggle');
        const filterLeaves = document.getElementById('filterLeaves');
        const resultsPreview = document.getElementById('resultsPreview');
        const loadingRoots = document.getElementById('loadingRoots');
        const seedPods = document.querySelectorAll('.seed-pod');
        const filterItems = document.querySelectorAll('.filter-leaf');
        
        let searchTimeout;
        let activeFilters = new Set();
        
        // Search input handling with vine growth
        searchInput.addEventListener('input', (e) => {
            const value = e.target.value;
            
            clearTimeout(searchTimeout);
            
            if (value.length > 0) {
                // Show autocomplete with blooming animation
                autocomplete.classList.add('active');
                
                // Simulate search delay with root growth
                searchTimeout = setTimeout(() => {
                    showSearchResults();
                }, 800);
            } else {
                autocomplete.classList.remove('active');
                resultsPreview.classList.remove('active');
            }
        });
        
        // Click outside to close autocomplete
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                autocomplete.classList.remove('active');
            }
        });
        
        // Autocomplete item selection
        document.querySelectorAll('.autocomplete-item').forEach(item => {
            item.addEventListener('click', () => {
                const text = item.querySelector('span:last-child').textContent;
                searchInput.value = text;
                autocomplete.classList.remove('active');
                showSearchResults();
                
                // Add to recent items
                addToRecentItems(text);
            });
        });
        
        // Filter toggle animation
        filterToggle.addEventListener('click', () => {
            filterToggle.classList.toggle('active');
            filterLeaves.classList.toggle('open');
        });
        
        // Filter leaf selection
        filterItems.forEach(leaf => {
            leaf.addEventListener('click', () => {
                leaf.classList.toggle('active');
                const filter = leaf.dataset.filter;
                
                if (activeFilters.has(filter)) {
                    activeFilters.delete(filter);
                } else {
                    activeFilters.add(filter);
                }
                
                // Trigger filtered search if there's a search term
                if (searchInput.value) {
                    showSearchResults();
                }
            });
        });
        
        // Seed pod (recent item) clicks
        seedPods.forEach(pod => {
            pod.addEventListener('click', () => {
                searchInput.value = pod.textContent;
                showSearchResults();
            });
        });
        
        // Show search results with growth animation
        function showSearchResults() {
            // Show loading roots
            resultsPreview.classList.remove('active');
            loadingRoots.classList.add('active');
            
            // Simulate loading time
            setTimeout(() => {
                loadingRoots.classList.remove('active');
                resultsPreview.classList.add('active');
                
                // Update results count based on filters
                const count = 5 - activeFilters.size;
                document.querySelector('.results-count').textContent = 
                    `${count} branch${count !== 1 ? 'es' : ''} found`;
            }, 1200);
        }
        
        // Add to recent items
        function addToRecentItems(text) {
            const seedPodsContainer = document.querySelector('.seed-pods');
            const existingPods = Array.from(seedPods);
            
            // Check if already exists
            const exists = existingPods.some(pod => pod.textContent === text);
            if (!exists) {
                // Create new seed pod
                const newPod = document.createElement('div');
                newPod.className = 'seed-pod';
                newPod.textContent = text.substring(0, 20) + (text.length > 20 ? '...' : '');
                
                // Add click handler
                newPod.addEventListener('click', () => {
                    searchInput.value = text;
                    showSearchResults();
                });
                
                // Add with growth animation
                newPod.style.transform = 'scale(0)';
                seedPodsContainer.insertBefore(newPod, seedPodsContainer.firstChild);
                
                // Animate growth
                setTimeout(() => {
                    newPod.style.transform = 'scale(1)';
                    newPod.style.transition = 'transform 0.3s ease-out';
                }, 10);
                
                // Remove oldest if too many
                if (seedPodsContainer.children.length > 8) {
                    const oldest = seedPodsContainer.lastChild;
                    oldest.style.transform = 'scale(0)';
                    setTimeout(() => oldest.remove(), 300);
                }
            }
        }
        
        // Result item hover sound effect (optional - using CSS only for now)
        document.querySelectorAll('.result-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                // Could add subtle nature sound here
                item.style.transform = 'translateX(5px)';
            });
        });
        
        // Initial breathing animation
        const component = document.querySelector('.hybrid-component');
        setInterval(() => {
            component.classList.add('breathing');
        }, 100);
    </script>
</body>
</html>