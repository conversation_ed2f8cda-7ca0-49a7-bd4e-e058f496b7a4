<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Handcrafted Paper Content Card</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Patrick+Hand&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Patrick Hand', cursive;
            background: linear-gradient(135deg, #f5f1e8 0%, #fafaf0 100%);
            min-height: 100vh;
            padding: 2rem;
            position: relative;
            overflow-x: hidden;
        }
        
        /* Wood texture background */
        body::before {
            content: '';
            position: fixed;
            inset: 0;
            background-image: 
                repeating-linear-gradient(90deg, rgba(139, 90, 43, 0.03) 0px, transparent 1px, transparent 2px, rgba(139, 90, 43, 0.03) 3px),
                repeating-linear-gradient(0deg, rgba(139, 90, 43, 0.03) 0px, transparent 1px, transparent 2px, rgba(139, 90, 43, 0.03) 3px);
            pointer-events: none;
        }
        
        main {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        h1 {
            font-family: 'Kalam', cursive;
            font-size: 2.5rem;
            color: #4a3f36;
            text-align: center;
            margin-bottom: 3rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }
        
        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 2px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 2"><path d="M0,1 Q25,0 50,1 T100,1" stroke="%234a3f36" fill="none" stroke-width="0.5"/></svg>');
            opacity: 0.5;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2.5rem;
            margin-top: 3rem;
        }
        
        .paper-card {
            position: relative;
            background: #fefef4;
            padding: 1.5rem;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
            transition: transform 0.3s ease, filter 0.3s ease;
            cursor: pointer;
        }
        
        .paper-card::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: #fefef4;
            z-index: -1;
            clip-path: polygon(
                0% 2%, 2% 0%, 15% 1%, 30% 0%, 45% 2%, 60% 0%, 75% 1%, 90% 0%, 100% 2%,
                99% 15%, 100% 30%, 98% 45%, 100% 60%, 99% 75%, 100% 90%, 98% 100%,
                85% 99%, 70% 100%, 55% 98%, 40% 100%, 25% 99%, 10% 100%, 0% 98%,
                1% 85%, 0% 70%, 2% 55%, 0% 40%, 1% 25%, 0% 10%
            );
        }
        
        .paper-card:hover {
            transform: translateY(-4px) rotate(0.5deg);
            filter: drop-shadow(0 8px 16px rgba(0,0,0,0.15));
        }
        
        /* Paper texture overlay */
        .paper-texture {
            position: absolute;
            inset: 0;
            opacity: 0.3;
            background-image: 
                repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(0,0,0,.02) 35px, rgba(0,0,0,.02) 70px),
                repeating-linear-gradient(-45deg, transparent, transparent 35px, rgba(0,0,0,.02) 35px, rgba(0,0,0,.02) 70px);
            pointer-events: none;
        }
        
        /* Bookmark ribbon */
        .bookmark-ribbon {
            position: absolute;
            top: -5px;
            right: 20px;
            width: 30px;
            height: 60px;
            background: #d32f2f;
            clip-path: polygon(0 0, 100% 0, 100% 85%, 50% 100%, 0 85%);
            transition: all 0.3s ease;
            cursor: pointer;
            z-index: 10;
        }
        
        .bookmark-ribbon::before {
            content: '★';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 1.2rem;
        }
        
        .bookmark-ribbon.active {
            background: #fbc02d;
            transform: translateY(5px);
        }
        
        /* Content preview */
        .card-preview {
            margin-bottom: 1rem;
        }
        
        .card-image {
            width: 100%;
            height: 200px;
            background: #f5f5dc;
            position: relative;
            overflow: hidden;
            margin-bottom: 1rem;
            clip-path: polygon(
                0% 1%, 1% 0%, 99% 0%, 100% 1%,
                100% 99%, 99% 100%, 1% 100%, 0% 99%
            );
        }
        
        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            filter: sepia(0.1) contrast(0.9);
        }
        
        .sketch-overlay {
            position: absolute;
            inset: 0;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M10,10 Q50,5 90,10 M10,90 Q50,95 90,90 M10,10 Q5,50 10,90 M90,10 Q95,50 90,90" stroke="%23000" fill="none" stroke-width="0.2" opacity="0.3"/></svg>');
            pointer-events: none;
        }
        
        .card-title {
            font-family: 'Kalam', cursive;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            position: relative;
        }
        
        .card-description {
            color: #5a6c7d;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        /* Handwritten metadata */
        .metadata {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #7a8694;
        }
        
        .metadata span {
            position: relative;
            padding-bottom: 2px;
        }
        
        .metadata span::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 1"><path d="M0,0.5 Q5,0 10,0.5 T20,0.5" stroke="%237a8694" fill="none" stroke-width="0.5"/></svg>');
            opacity: 0.3;
        }
        
        /* Paper cutout action buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            position: relative;
        }
        
        .paper-button {
            padding: 0.7rem 1.2rem;
            background: #fefef4;
            border: none;
            font-family: 'Patrick Hand', cursive;
            font-size: 1rem;
            color: #4a3f36;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
        }
        
        .paper-button::before {
            content: '';
            position: absolute;
            inset: -1px;
            background: inherit;
            z-index: -1;
            clip-path: polygon(
                0% 5%, 5% 0%, 95% 0%, 100% 5%,
                100% 95%, 95% 100%, 5% 100%, 0% 95%
            );
        }
        
        .paper-button:hover {
            transform: translateY(-2px) rotate(-1deg);
            filter: drop-shadow(4px 4px 8px rgba(0,0,0,0.15));
        }
        
        .paper-button:active {
            transform: translateY(0) rotate(0);
            filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.1));
        }
        
        .paper-button.primary {
            background: #e8f5e9;
            color: #2e7d32;
        }
        
        .paper-button.secondary {
            background: #fff3e0;
            color: #e65100;
        }
        
        /* Share paper airplane */
        .share-button {
            position: relative;
            overflow: visible;
        }
        
        .paper-airplane {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            opacity: 0;
            pointer-events: none;
        }
        
        .paper-airplane.flying {
            animation: fly-away 1.5s ease-out forwards;
        }
        
        @keyframes fly-away {
            0% {
                opacity: 1;
                transform: translate(-50%, -50%) rotate(0deg) scale(1);
            }
            100% {
                opacity: 0;
                transform: translate(200px, -200px) rotate(45deg) scale(0.5);
            }
        }
        
        /* Modal as unfolding paper */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }
        
        .modal-overlay.active {
            opacity: 1;
            pointer-events: all;
        }
        
        .paper-modal {
            background: #fefef4;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            position: relative;
            filter: drop-shadow(0 10px 30px rgba(0,0,0,0.3));
            transform: scale(0.7) rotateX(90deg);
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            transform-style: preserve-3d;
            perspective: 1000px;
        }
        
        .modal-overlay.active .paper-modal {
            transform: scale(1) rotateX(0);
        }
        
        .paper-modal::before {
            content: '';
            position: absolute;
            inset: -3px;
            background: #fefef4;
            z-index: -1;
            clip-path: polygon(
                0% 3%, 3% 0%, 20% 2%, 40% 0%, 60% 2%, 80% 0%, 97% 3%, 100% 3%,
                98% 20%, 100% 40%, 98% 60%, 100% 80%, 97% 97%, 97% 100%,
                80% 98%, 60% 100%, 40% 98%, 20% 100%, 3% 97%, 0% 97%,
                2% 80%, 0% 60%, 2% 40%, 0% 20%
            );
        }
        
        .modal-content {
            padding: 2.5rem;
            overflow-y: auto;
            max-height: 80vh;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-family: 'Kalam', cursive;
            font-size: 2rem;
            color: #2c3e50;
        }
        
        .close-button {
            background: none;
            border: none;
            font-size: 2rem;
            color: #7a8694;
            cursor: pointer;
            transition: transform 0.3s ease;
            font-family: 'Patrick Hand', cursive;
        }
        
        .close-button:hover {
            transform: rotate(90deg);
        }
        
        .modal-body {
            line-height: 1.8;
            color: #4a5568;
        }
        
        .modal-body p {
            margin-bottom: 1rem;
        }
        
        /* Fold lines on modal */
        .fold-lines {
            position: absolute;
            inset: 0;
            pointer-events: none;
            opacity: 0.1;
            background-image: 
                linear-gradient(90deg, #000 1px, transparent 1px),
                linear-gradient(0deg, #000 1px, transparent 1px);
            background-size: 200px 200px;
            background-position: center;
        }
        
        /* Coffee stain decoration */
        .coffee-stain {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(139, 90, 43, 0.1) 0%, rgba(139, 90, 43, 0.05) 50%, transparent 70%);
            top: 20px;
            right: 30px;
            transform: rotate(45deg);
        }
        
        /* Pencil sketch decorations */
        .pencil-sketch {
            position: absolute;
            opacity: 0.2;
            pointer-events: none;
        }
        
        .sketch-1 {
            bottom: 10px;
            left: 10px;
            width: 50px;
            height: 50px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><circle cx="25" cy="25" r="20" fill="none" stroke="%234a3f36" stroke-width="0.5" stroke-dasharray="1,2"/><path d="M15,25 Q25,15 35,25 T25,35" fill="none" stroke="%234a3f36" stroke-width="0.5"/></svg>');
        }
        
        .sketch-2 {
            top: 50%;
            right: -20px;
            width: 40px;
            height: 100px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 100"><path d="M20,10 Q10,30 20,50 T20,90" fill="none" stroke="%234a3f36" stroke-width="0.5"/><circle cx="20" cy="30" r="5" fill="none" stroke="%234a3f36" stroke-width="0.5"/><circle cx="20" cy="70" r="5" fill="none" stroke="%234a3f36" stroke-width="0.5"/></svg>');
        }
        
        /* Loading state */
        .loading-sketch {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-left: 0.5rem;
            animation: sketch-draw 1.5s ease-in-out infinite;
        }
        
        @keyframes sketch-draw {
            0%, 100% {
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M5,10 L10,10" stroke="%234a3f36" fill="none" stroke-width="1"/></svg>');
            }
            50% {
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M5,10 L15,10" stroke="%234a3f36" fill="none" stroke-width="1"/></svg>');
            }
        }
        
        /* Share popup */
        .share-popup {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #fefef4;
            padding: 1rem;
            margin-bottom: 0.5rem;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        .share-popup.active {
            opacity: 1;
            pointer-events: all;
        }
        
        .share-popup::before {
            content: '';
            position: absolute;
            inset: -1px;
            background: #fefef4;
            z-index: -1;
            clip-path: polygon(
                0% 5%, 5% 0%, 95% 0%, 100% 5%,
                100% 85%, 90% 90%, 50% 100%, 10% 90%, 0% 85%
            );
        }
        
        .share-options {
            display: flex;
            gap: 0.5rem;
        }
        
        .share-option {
            padding: 0.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2rem;
            transition: transform 0.2s ease;
        }
        
        .share-option:hover {
            transform: scale(1.2) rotate(5deg);
        }
    </style>
</head>
<body>
    <main>
        <h1>Content Card - Handcrafted Paper Theme</h1>
        
        <div class="content-grid">
            <!-- Article Card -->
            <div class="paper-card" data-id="1">
                <div class="paper-texture"></div>
                <div class="bookmark-ribbon" onclick="toggleBookmark(event, this)"></div>
                <div class="coffee-stain"></div>
                
                <div class="card-preview">
                    <div class="card-image">
                        <img src="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 200'><rect fill='%23e8dcc6' width='400' height='200'/><text x='50%' y='50%' text-anchor='middle' dy='.3em' fill='%234a3f36' font-family='Patrick Hand' font-size='24'>Mountain Sketch</text><path d='M50,150 L150,50 L250,150 M200,100 L300,150' stroke='%234a3f36' fill='none' stroke-width='2'/><circle cx='320' cy='40' r='20' fill='none' stroke='%234a3f36' stroke-width='1.5'/></svg>" alt="Mountain landscape">
                        <div class="sketch-overlay"></div>
                    </div>
                    <h3 class="card-title">The Art of Mountain Sketching</h3>
                    <p class="card-description">Exploring the timeless techniques of capturing mountain landscapes with pencil and paper...</p>
                </div>
                
                <div class="metadata">
                    <span>📅 March 15, 2024</span>
                    <span>✏️ 5 min read</span>
                    <span>👁️ 1.2k views</span>
                </div>
                
                <div class="action-buttons">
                    <button class="paper-button primary" onclick="openModal(1)">
                        Read More
                    </button>
                    <button class="paper-button secondary share-button" onclick="toggleShare(event, this)">
                        Share ✈️
                        <svg class="paper-airplane" viewBox="0 0 20 20">
                            <path d="M2,2 L18,10 L2,18 L5,10 Z" fill="#e65100"/>
                        </svg>
                        <div class="share-popup">
                            <div class="share-options">
                                <button class="share-option" onclick="shareVia('twitter')">🐦</button>
                                <button class="share-option" onclick="shareVia('facebook')">📘</button>
                                <button class="share-option" onclick="shareVia('email')">📧</button>
                                <button class="share-option" onclick="shareVia('copy')">📋</button>
                            </div>
                        </div>
                    </button>
                </div>
                
                <div class="pencil-sketch sketch-1"></div>
            </div>
            
            <!-- Recipe Card -->
            <div class="paper-card" data-id="2">
                <div class="paper-texture"></div>
                <div class="bookmark-ribbon" onclick="toggleBookmark(event, this)"></div>
                
                <div class="card-preview">
                    <div class="card-image">
                        <img src="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 200'><rect fill='%23f5e6d3' width='400' height='200'/><text x='50%' y='30%' text-anchor='middle' dy='.3em' fill='%234a3f36' font-family='Patrick Hand' font-size='24'>Grandma's Recipe</text><circle cx='200' cy='120' r='40' fill='none' stroke='%234a3f36' stroke-width='2'/><path d='M160,120 Q200,80 240,120' fill='none' stroke='%234a3f36' stroke-width='1.5'/><circle cx='180' cy='110' r='3' fill='%234a3f36'/><circle cx='220' cy='110' r='3' fill='%234a3f36'/></svg>" alt="Recipe illustration">
                        <div class="sketch-overlay"></div>
                    </div>
                    <h3 class="card-title">Handwritten Apple Pie Recipe</h3>
                    <p class="card-description">A treasured family recipe passed down through generations, written on aged paper...</p>
                </div>
                
                <div class="metadata">
                    <span>🥧 Dessert</span>
                    <span>⏱️ 2 hours</span>
                    <span>⭐ 4.9 rating</span>
                </div>
                
                <div class="action-buttons">
                    <button class="paper-button primary" onclick="openModal(2)">
                        View Recipe
                    </button>
                    <button class="paper-button secondary share-button" onclick="toggleShare(event, this)">
                        Share ✈️
                        <svg class="paper-airplane" viewBox="0 0 20 20">
                            <path d="M2,2 L18,10 L2,18 L5,10 Z" fill="#e65100"/>
                        </svg>
                        <div class="share-popup">
                            <div class="share-options">
                                <button class="share-option" onclick="shareVia('twitter')">🐦</button>
                                <button class="share-option" onclick="shareVia('facebook')">📘</button>
                                <button class="share-option" onclick="shareVia('email')">📧</button>
                                <button class="share-option" onclick="shareVia('copy')">📋</button>
                            </div>
                        </div>
                    </button>
                </div>
                
                <div class="pencil-sketch sketch-2"></div>
            </div>
            
            <!-- Journal Entry Card -->
            <div class="paper-card" data-id="3">
                <div class="paper-texture"></div>
                <div class="bookmark-ribbon active" onclick="toggleBookmark(event, this)"></div>
                
                <div class="card-preview">
                    <div class="card-image">
                        <img src="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 200'><rect fill='%23ede4d3' width='400' height='200'/><text x='50%' y='30%' text-anchor='middle' dy='.3em' fill='%234a3f36' font-family='Patrick Hand' font-size='20'>Travel Journal</text><path d='M100,100 Q150,80 200,100 T300,100' stroke='%234a3f36' fill='none' stroke-width='1' stroke-dasharray='2,3'/><path d='M150,130 L170,110 L190,120 L210,105 L230,115 L250,100' stroke='%234a3f36' fill='none' stroke-width='1.5'/></svg>" alt="Journal sketch">
                        <div class="sketch-overlay"></div>
                    </div>
                    <h3 class="card-title">Adventures in Tuscany</h3>
                    <p class="card-description">Hand-drawn maps and stories from a summer journey through Italian countryside...</p>
                </div>
                
                <div class="metadata">
                    <span>📍 Travel</span>
                    <span>📖 8 pages</span>
                    <span>💭 32 comments</span>
                </div>
                
                <div class="action-buttons">
                    <button class="paper-button primary" onclick="openModal(3)">
                        Open Journal
                    </button>
                    <button class="paper-button secondary share-button" onclick="toggleShare(event, this)">
                        Share ✈️
                        <svg class="paper-airplane" viewBox="0 0 20 20">
                            <path d="M2,2 L18,10 L2,18 L5,10 Z" fill="#e65100"/>
                        </svg>
                        <div class="share-popup">
                            <div class="share-options">
                                <button class="share-option" onclick="shareVia('twitter')">🐦</button>
                                <button class="share-option" onclick="shareVia('facebook')">📘</button>
                                <button class="share-option" onclick="shareVia('email')">📧</button>
                                <button class="share-option" onclick="shareVia('copy')">📋</button>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Modal Overlay -->
    <div class="modal-overlay" onclick="closeModal(event)">
        <div class="paper-modal" onclick="event.stopPropagation()">
            <div class="fold-lines"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Content Title</h2>
                    <button class="close-button" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <!-- Content will be dynamically inserted -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Content data
        const contentData = {
            1: {
                title: "The Art of Mountain Sketching",
                content: `
                    <p>There's something magical about sitting on a mountainside with nothing but a sketchbook and pencil. The rough texture of handmade paper beneath your fingers connects you to centuries of artists who've attempted to capture nature's grandeur.</p>
                    
                    <p>I learned this technique from an old artist in the Alps, who showed me how to use quick, confident strokes to suggest the massive scale of mountain peaks. "Don't try to draw every rock," he said, his weathered hands moving the pencil with practiced ease. "Let the paper breathe."</p>
                    
                    <p>The key is in the observation. Watch how shadows play across the ridges at different times of day. Notice how atmospheric perspective makes distant peaks appear lighter, almost ghostlike. These subtleties, captured in graphite on paper, can convey more emotion than any photograph.</p>
                    
                    <p>My favorite papers for mountain sketching are those with a bit of tooth - rough enough to catch the graphite and create texture, but smooth enough for fine details. I often carry several weights, switching between them as the drawing develops.</p>
                    
                    <p>Remember, the goal isn't perfection. It's about capturing a moment, a feeling, the essence of standing small before nature's monuments. Let your lines be loose, your shading suggestive. The best mountain sketches feel alive, as if the wind might blow through them at any moment.</p>
                `
            },
            2: {
                title: "Grandma's Handwritten Apple Pie Recipe",
                content: `
                    <p><em>Found tucked between the pages of her cookbook, written in fountain pen on paper now yellowed with age...</em></p>
                    
                    <p><strong>For the Crust (makes enough for top and bottom):</strong><br>
                    2½ cups flour<br>
                    1 tsp salt<br>
                    1 cup cold butter (Grandma's note: "must be COLD!")<br>
                    ¼ to ½ cup ice water</p>
                    
                    <p><strong>For the Filling:</strong><br>
                    6-8 tart apples (she preferred Granny Smith)<br>
                    ¾ cup sugar (adjust to taste)<br>
                    2 tbsp flour<br>
                    1 tsp cinnamon<br>
                    ¼ tsp nutmeg<br>
                    Pinch of salt<br>
                    2 tbsp butter</p>
                    
                    <p><em>Her handwritten notes in the margin:</em> "The secret is in the apples - they should make you pucker just a little when raw. And always, always use real butter. None of that margarine nonsense."</p>
                    
                    <p><strong>Instructions:</strong><br>
                    Mix flour and salt. Cut in cold butter until mixture resembles coarse crumbs. Add ice water gradually until dough comes together. Divide in half, wrap, and chill for at least an hour.</p>
                    
                    <p>Peel and slice apples thin. Toss with sugar, flour, and spices. Roll out bottom crust, fill with apples, dot with butter. Cover with top crust, seal edges, cut vents.</p>
                    
                    <p>Bake at 425°F for 15 minutes, then reduce to 350°F for 35-45 minutes until golden brown. <em>(Another note: "If the edges brown too quick, cover with foil")</em></p>
                    
                    <p><em>At the bottom, in shaky handwriting:</em> "Made this for your grandfather every Sunday. Now it's your turn to carry on the tradition. All my love, Grandma"</p>
                `
            },
            3: {
                title: "Adventures in Tuscany - Travel Journal",
                content: `
                    <p><strong>Day 1 - Arrival in Florence</strong><br>
                    <em>Sketched from the Piazzale Michelangelo at sunset</em></p>
                    
                    <p>The paper of this journal already feels different here - maybe it's the Tuscan air, or maybe it's just my imagination. The city spreads below like a Renaissance painting come to life. Terra cotta roofs catch the golden hour light, and I try to capture it with quick pencil strokes.</p>
                    
                    <p><strong>Day 3 - The Hills of Chianti</strong><br>
                    <em>Wine stains on this page are intentional... mostly</em></p>
                    
                    <p>Spent the morning sketching vineyards. The way the vines create these perfect lines across the hillsides - it's like nature's own ruled paper. Met an old vintner who laughed at my attempts to draw his ancient olive trees. "You cannot draw age," he said, "you must feel it." I think I understand.</p>
                    
                    <p><strong>Day 5 - Siena's Secret Gardens</strong><br>
                    <em>Pressed flowers between these pages</em></p>
                    
                    <p>Found a hidden garden behind the cathedral. The kind of place that makes you want to fill every page with drawings. Stone benches worn smooth by centuries, fountains singing softly, cats lounging in patches of sun. My sketches can't capture the smell of jasmine or the sound of church bells, but they try.</p>
                    
                    <p><strong>Day 8 - The Coast at Cinque Terre</strong><br>
                    <em>Salt spray has warped these pages slightly</em></p>
                    
                    <p>Different paper would have been better for the coast - this handmade stock doesn't love the humidity. But there's something perfect about the way the moisture makes my ink lines bleed slightly, like the boundaries between sea and sky in the morning mist.</p>
                    
                    <p><strong>Last Day - Reflections</strong><br>
                    This journal is fuller than just sketches and words. It holds pressed flowers, wine stains, smudges of local soil, even a few tears of joy. The paper has absorbed this journey, becoming part of the story itself. That's the magic of keeping a handwritten travel journal - it becomes an artifact of the adventure, not just a record of it.</p>
                `
            }
        };
        
        // Toggle bookmark
        function toggleBookmark(event, element) {
            event.stopPropagation();
            element.classList.toggle('active');
            
            const isActive = element.classList.contains('active');
            const cardTitle = element.closest('.paper-card').querySelector('.card-title').textContent;
            
            if (isActive) {
                showNotification(`"${cardTitle}" added to bookmarks`);
            } else {
                showNotification(`"${cardTitle}" removed from bookmarks`);
            }
        }
        
        // Open modal
        function openModal(contentId) {
            const modal = document.querySelector('.modal-overlay');
            const modalTitle = modal.querySelector('.modal-title');
            const modalBody = modal.querySelector('.modal-body');
            
            const content = contentData[contentId];
            modalTitle.textContent = content.title;
            modalBody.innerHTML = content.content;
            
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
        
        // Close modal
        function closeModal(event) {
            if (event && event.target !== event.currentTarget) return;
            
            const modal = document.querySelector('.modal-overlay');
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
        
        // Toggle share popup
        function toggleShare(event, button) {
            event.stopPropagation();
            
            // Close all other share popups
            document.querySelectorAll('.share-popup').forEach(popup => {
                if (popup !== button.querySelector('.share-popup')) {
                    popup.classList.remove('active');
                }
            });
            
            const popup = button.querySelector('.share-popup');
            popup.classList.toggle('active');
        }
        
        // Share functionality
        function shareVia(platform) {
            event.stopPropagation();
            
            const card = event.target.closest('.paper-card');
            const title = card.querySelector('.card-title').textContent;
            const shareButton = card.querySelector('.share-button');
            const airplane = shareButton.querySelector('.paper-airplane');
            
            // Close popup
            const popup = shareButton.querySelector('.share-popup');
            popup.classList.remove('active');
            
            // Animate paper airplane
            airplane.classList.add('flying');
            setTimeout(() => {
                airplane.classList.remove('flying');
            }, 1500);
            
            // Show notification
            const messages = {
                twitter: `Shared "${title}" on Twitter`,
                facebook: `Shared "${title}" on Facebook`,
                email: `Email composed for "${title}"`,
                copy: `Link copied for "${title}"`
            };
            
            showNotification(messages[platform]);
        }
        
        // Show notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                bottom: 2rem;
                left: 50%;
                transform: translateX(-50%);
                background: #fefef4;
                padding: 1rem 2rem;
                filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
                z-index: 2000;
                font-family: 'Patrick Hand', cursive;
                color: #4a3f36;
                clip-path: polygon(
                    0% 5%, 5% 0%, 95% 0%, 100% 5%,
                    100% 95%, 95% 100%, 5% 100%, 0% 95%
                );
                animation: slide-up 0.3s ease-out;
            `;
            
            // Add animation keyframes if not already present
            if (!document.querySelector('#notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    @keyframes slide-up {
                        from {
                            opacity: 0;
                            transform: translateX(-50%) translateY(20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateX(-50%) translateY(0);
                        }
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slide-up 0.3s ease-out reverse';
                setTimeout(() => notification.remove(), 300);
            }, 2000);
        }
        
        // Close share popups when clicking outside
        document.addEventListener('click', () => {
            document.querySelectorAll('.share-popup.active').forEach(popup => {
                popup.classList.remove('active');
            });
        });
        
        // Add hover effect to cards for subtle animation
        document.querySelectorAll('.paper-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = `translateY(-4px) rotate(${Math.random() * 2 - 1}deg)`;
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.querySelectorAll('.share-popup.active').forEach(popup => {
                    popup.classList.remove('active');
                });
            }
        });
    </script>
</body>
</html>