<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steampunk Machinery Communication Hub</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Libre Baskerville', serif;
            background: #1a1511;
            color: #d4af37;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 50%, rgba(184, 134, 11, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 20%, rgba(218, 165, 32, 0.05) 0%, transparent 50%);
        }
        
        h1 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 2rem;
            color: #daa520;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                         0 0 20px rgba(212, 175, 55, 0.3);
            letter-spacing: 2px;
        }
        
        .communication-hub {
            width: 100%;
            max-width: 1400px;
            background: linear-gradient(145deg, #2a2217, #1f1813);
            border: 3px solid #8b6914;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 
                inset 0 0 50px rgba(212, 175, 55, 0.1),
                0 0 30px rgba(212, 175, 55, 0.2),
                0 10px 20px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }
        
        /* Decorative gears */
        .gear {
            position: absolute;
            opacity: 0.1;
            animation: rotate 20s linear infinite;
        }
        
        .gear-1 {
            top: -50px;
            right: -50px;
            width: 150px;
            height: 150px;
            animation-duration: 30s;
        }
        
        .gear-2 {
            bottom: -30px;
            left: -30px;
            width: 100px;
            height: 100px;
            animation-direction: reverse;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* Main layout grid */
        .hub-grid {
            display: grid;
            grid-template-columns: 300px 1fr 250px;
            gap: 20px;
            position: relative;
            z-index: 1;
        }
        
        /* Participant Manifest (Left Panel) */
        .passenger-manifest {
            background: #1f1611;
            border: 2px solid #8b6914;
            border-radius: 10px;
            padding: 15px;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
        }
        
        .manifest-header {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            text-align: center;
            margin-bottom: 15px;
            color: #daa520;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .participant {
            background: linear-gradient(145deg, #2a2217, #1f1813);
            border: 1px solid #8b6914;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .participant:hover {
            transform: translateX(5px);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
        }
        
        .participant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #8b6914;
            background: #2a2217;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #daa520;
        }
        
        .participant-info {
            flex: 1;
        }
        
        .participant-name {
            font-weight: 600;
            color: #daa520;
            font-size: 0.9rem;
        }
        
        .participant-status {
            font-size: 0.8rem;
            color: #b8860b;
            font-style: italic;
        }
        
        .steam-indicator {
            position: absolute;
            right: 10px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            box-shadow: 0 0 10px #4ade80;
            animation: steam-pulse 2s ease-in-out infinite;
        }
        
        @keyframes steam-pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }
        
        /* Center Panel */
        .center-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        /* Projection Apparatus (Screen Share) */
        .projection-apparatus {
            background: #0a0908;
            border: 3px solid #8b6914;
            border-radius: 10px;
            height: 400px;
            position: relative;
            overflow: hidden;
            box-shadow: 
                inset 0 0 50px rgba(0, 0, 0, 0.8),
                0 0 20px rgba(212, 175, 55, 0.2);
        }
        
        .projection-screen {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: radial-gradient(ellipse at center, #1a1511 0%, #0a0908 100%);
            position: relative;
        }
        
        .projection-content {
            text-align: center;
            padding: 20px;
        }
        
        .projection-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: projection-flicker 3s ease-in-out infinite;
        }
        
        @keyframes projection-flicker {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        
        .projection-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }
        
        /* Brass Valves (Video Controls) */
        .brass-valves {
            background: linear-gradient(145deg, #2a2217, #1f1813);
            border: 2px solid #8b6914;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
        }
        
        .valve-control {
            position: relative;
            width: 80px;
            height: 80px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .valve-control:hover {
            transform: scale(1.1);
        }
        
        .valve-wheel {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, #daa520 0%, #8b6914 50%, #654321 100%);
            border: 3px solid #654321;
            position: relative;
            box-shadow: 
                inset 0 0 20px rgba(0, 0, 0, 0.5),
                0 5px 10px rgba(0, 0, 0, 0.5);
            transition: transform 0.5s ease;
        }
        
        .valve-control.active .valve-wheel {
            transform: rotate(180deg);
            box-shadow: 
                inset 0 0 20px rgba(212, 175, 55, 0.5),
                0 5px 10px rgba(0, 0, 0, 0.5),
                0 0 20px rgba(212, 175, 55, 0.5);
        }
        
        .valve-spokes {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: 60%;
        }
        
        .valve-spoke {
            position: absolute;
            width: 2px;
            height: 50%;
            background: #654321;
            left: 50%;
            transform-origin: bottom;
        }
        
        .valve-spoke:nth-child(1) { transform: translateX(-50%) rotate(0deg); }
        .valve-spoke:nth-child(2) { transform: translateX(-50%) rotate(45deg); }
        .valve-spoke:nth-child(3) { transform: translateX(-50%) rotate(90deg); }
        .valve-spoke:nth-child(4) { transform: translateX(-50%) rotate(135deg); }
        
        .valve-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, #daa520 0%, #8b6914 100%);
            border: 2px solid #654321;
        }
        
        .valve-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: #b8860b;
            text-transform: uppercase;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        
        /* Right Panel */
        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        /* Phonograph Cylinder (Recording) */
        .phonograph-cylinder {
            background: linear-gradient(145deg, #2a2217, #1f1813);
            border: 2px solid #8b6914;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .cylinder-mechanism {
            width: 120px;
            height: 40px;
            background: linear-gradient(90deg, #8b6914 0%, #daa520 25%, #8b6914 50%, #daa520 75%, #8b6914 100%);
            border-radius: 20px;
            margin: 0 auto 15px;
            position: relative;
            box-shadow: 
                inset 0 0 10px rgba(0, 0, 0, 0.5),
                0 5px 10px rgba(0, 0, 0, 0.3);
        }
        
        .cylinder-mechanism.recording {
            animation: cylinder-spin 2s linear infinite;
        }
        
        @keyframes cylinder-spin {
            from { background-position: 0 0; }
            to { background-position: 100px 0; }
        }
        
        .record-button {
            background: radial-gradient(circle at 30% 30%, #dc2626 0%, #991b1b 100%);
            border: 3px solid #7f1d1d;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            margin: 0 auto;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 
                inset 0 0 20px rgba(0, 0, 0, 0.5),
                0 5px 10px rgba(0, 0, 0, 0.5);
        }
        
        .record-button:hover {
            transform: scale(1.1);
            box-shadow: 
                inset 0 0 20px rgba(220, 38, 38, 0.5),
                0 5px 10px rgba(0, 0, 0, 0.5),
                0 0 20px rgba(220, 38, 38, 0.5);
        }
        
        .record-button.recording {
            animation: record-pulse 1s ease-in-out infinite;
        }
        
        @keyframes record-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* Steam Puffs (Reactions) */
        .steam-reactions {
            background: linear-gradient(145deg, #2a2217, #1f1813);
            border: 2px solid #8b6914;
            border-radius: 10px;
            padding: 15px;
        }
        
        .reactions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }
        
        .steam-button {
            background: radial-gradient(circle at 30% 30%, #654321 0%, #3e2723 100%);
            border: 2px solid #8b6914;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .steam-button:hover {
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
        }
        
        .steam-puff {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
            border-radius: 50%;
            opacity: 0;
            pointer-events: none;
        }
        
        .steam-button:active .steam-puff {
            animation: steam-rise 1s ease-out;
        }
        
        @keyframes steam-rise {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(0) scale(0.5);
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateX(-50%) translateY(-50px) scale(1.5);
            }
        }
        
        /* Pneumatic Tube Messages (Chat) */
        .pneumatic-chat {
            grid-column: 1 / -1;
            background: linear-gradient(145deg, #1f1611, #141210);
            border: 2px solid #8b6914;
            border-radius: 10px;
            padding: 20px;
            height: 300px;
            display: flex;
            flex-direction: column;
        }
        
        .tube-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 15px;
            padding-right: 10px;
        }
        
        .tube-messages::-webkit-scrollbar {
            width: 10px;
        }
        
        .tube-messages::-webkit-scrollbar-track {
            background: #1f1611;
            border-radius: 5px;
        }
        
        .tube-messages::-webkit-scrollbar-thumb {
            background: #8b6914;
            border-radius: 5px;
        }
        
        .message-capsule {
            background: linear-gradient(145deg, #2a2217, #1f1813);
            border: 1px solid #8b6914;
            border-radius: 20px;
            padding: 12px 20px;
            margin-bottom: 10px;
            position: relative;
            animation: tube-arrive 0.5s ease-out;
        }
        
        @keyframes tube-arrive {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .message-sender {
            font-weight: 600;
            color: #daa520;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .message-text {
            color: #d4af37;
            font-size: 0.95rem;
            line-height: 1.4;
        }
        
        .message-time {
            position: absolute;
            top: 5px;
            right: 15px;
            font-size: 0.7rem;
            color: #8b6914;
            font-style: italic;
        }
        
        .tube-input-container {
            display: flex;
            gap: 10px;
        }
        
        .tube-input {
            flex: 1;
            background: #1f1611;
            border: 2px solid #8b6914;
            border-radius: 20px;
            padding: 10px 20px;
            color: #d4af37;
            font-family: 'Libre Baskerville', serif;
            font-size: 0.95rem;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .tube-input:focus {
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
            border-color: #daa520;
        }
        
        .tube-send {
            background: radial-gradient(circle at 30% 30%, #8b6914 0%, #654321 100%);
            border: 2px solid #654321;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #daa520;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .tube-send:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
        }
        
        /* Decorative elements */
        .brass-plate {
            position: absolute;
            background: linear-gradient(145deg, #daa520 0%, #8b6914 100%);
            border: 1px solid #654321;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 0.7rem;
            color: #1f1611;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .hub-label {
            top: 10px;
            right: 20px;
        }
        
        /* Gauge decorations */
        .pressure-gauge {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle at 30% 30%, #2a2217 0%, #1f1813 100%);
            border: 3px solid #8b6914;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
        }
        
        .gauge-needle {
            position: absolute;
            width: 2px;
            height: 20px;
            background: #dc2626;
            transform-origin: bottom;
            animation: gauge-wobble 3s ease-in-out infinite;
        }
        
        @keyframes gauge-wobble {
            0%, 100% { transform: rotate(-30deg); }
            50% { transform: rotate(30deg); }
        }
        
        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .hub-grid {
                grid-template-columns: 1fr;
            }
            
            .passenger-manifest,
            .right-panel {
                display: none;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Communication Hub - Steampunk Machinery Theme</h1>
        
        <div class="communication-hub">
            <!-- Decorative gears -->
            <svg class="gear gear-1" viewBox="0 0 100 100">
                <path d="M50 15 L55 5 L45 5 Z M50 85 L55 95 L45 95 Z M85 50 L95 55 L95 45 Z M15 50 L5 55 L5 45 Z" fill="#8b6914"/>
                <circle cx="50" cy="50" r="35" fill="none" stroke="#8b6914" stroke-width="3"/>
                <circle cx="50" cy="50" r="20" fill="none" stroke="#8b6914" stroke-width="2"/>
            </svg>
            
            <svg class="gear gear-2" viewBox="0 0 100 100">
                <path d="M50 15 L55 5 L45 5 Z M50 85 L55 95 L45 95 Z M85 50 L95 55 L95 45 Z M15 50 L5 55 L5 45 Z" fill="#8b6914"/>
                <circle cx="50" cy="50" r="35" fill="none" stroke="#8b6914" stroke-width="3"/>
            </svg>
            
            <!-- Brass plate label -->
            <div class="brass-plate hub-label">Victorian Comm Station</div>
            
            <!-- Pressure gauge decoration -->
            <div class="pressure-gauge">
                <div class="gauge-needle"></div>
            </div>
            
            <div class="hub-grid">
                <!-- Passenger Manifest (Participant List) -->
                <div class="passenger-manifest">
                    <h2 class="manifest-header">Passenger Manifest</h2>
                    <div class="participant">
                        <div class="participant-avatar">VW</div>
                        <div class="participant-info">
                            <div class="participant-name">Victoria Whitmore</div>
                            <div class="participant-status">Chief Engineer</div>
                        </div>
                        <div class="steam-indicator"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">AT</div>
                        <div class="participant-info">
                            <div class="participant-name">Augustus Thornbury</div>
                            <div class="participant-status">Navigator</div>
                        </div>
                        <div class="steam-indicator"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">ES</div>
                        <div class="participant-info">
                            <div class="participant-name">Eleanor Sterling</div>
                            <div class="participant-status">Communications</div>
                        </div>
                        <div class="steam-indicator"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">RB</div>
                        <div class="participant-info">
                            <div class="participant-name">Reginald Blackwood</div>
                            <div class="participant-status">Guest Observer</div>
                        </div>
                        <div class="steam-indicator" style="background: #fbbf24;"></div>
                    </div>
                </div>
                
                <!-- Center Panel -->
                <div class="center-panel">
                    <!-- Projection Apparatus (Screen Share) -->
                    <div class="projection-apparatus">
                        <div class="projection-screen">
                            <div class="projection-content">
                                <div class="projection-icon">🎭</div>
                                <h3 style="color: #daa520; margin-bottom: 10px;">Projection Chamber Active</h3>
                                <p style="color: #b8860b; font-style: italic;">Awaiting visual transmission...</p>
                            </div>
                            <div class="projection-controls">
                                <button class="tube-send" style="width: 50px; height: 50px;">
                                    <span>📽️</span>
                                </button>
                                <button class="tube-send" style="width: 50px; height: 50px;">
                                    <span>🔍</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Brass Valves (Video Controls) -->
                    <div class="brass-valves">
                        <div class="valve-control" id="camera-valve">
                            <div class="valve-wheel">
                                <div class="valve-spokes">
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                </div>
                                <div class="valve-center"></div>
                            </div>
                            <div class="valve-label">Camera</div>
                        </div>
                        
                        <div class="valve-control active" id="audio-valve">
                            <div class="valve-wheel">
                                <div class="valve-spokes">
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                </div>
                                <div class="valve-center"></div>
                            </div>
                            <div class="valve-label">Audio</div>
                        </div>
                        
                        <div class="valve-control" id="screen-valve">
                            <div class="valve-wheel">
                                <div class="valve-spokes">
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                    <div class="valve-spoke"></div>
                                </div>
                                <div class="valve-center"></div>
                            </div>
                            <div class="valve-label">Screen</div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Panel -->
                <div class="right-panel">
                    <!-- Phonograph Cylinder (Recording) -->
                    <div class="phonograph-cylinder">
                        <h3 style="color: #daa520; margin-bottom: 15px; font-size: 1rem;">Recording Chamber</h3>
                        <div class="cylinder-mechanism"></div>
                        <button class="record-button" id="record-btn"></button>
                        <p style="color: #b8860b; margin-top: 10px; font-size: 0.8rem;">00:00:00</p>
                    </div>
                    
                    <!-- Steam Puffs (Reactions) -->
                    <div class="steam-reactions">
                        <h3 style="color: #daa520; margin-bottom: 15px; font-size: 1rem; text-align: center;">Steam Signals</h3>
                        <div class="reactions-grid">
                            <button class="steam-button">
                                👍
                                <div class="steam-puff"></div>
                            </button>
                            <button class="steam-button">
                                ❤️
                                <div class="steam-puff"></div>
                            </button>
                            <button class="steam-button">
                                😂
                                <div class="steam-puff"></div>
                            </button>
                            <button class="steam-button">
                                👏
                                <div class="steam-puff"></div>
                            </button>
                            <button class="steam-button">
                                🎩
                                <div class="steam-puff"></div>
                            </button>
                            <button class="steam-button">
                                ⚙️
                                <div class="steam-puff"></div>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Pneumatic Tube Messages (Chat) -->
                <div class="pneumatic-chat">
                    <div class="tube-messages" id="chat-messages">
                        <div class="message-capsule">
                            <div class="message-time">14:32</div>
                            <div class="message-sender">Victoria Whitmore</div>
                            <div class="message-text">Greetings, fellow inventors! The atmospheric pressure readings are optimal for today's demonstration.</div>
                        </div>
                        <div class="message-capsule">
                            <div class="message-time">14:33</div>
                            <div class="message-sender">Augustus Thornbury</div>
                            <div class="message-text">Splendid! I've calibrated the projection apparatus to maximum clarity. The aetheric interference should be minimal.</div>
                        </div>
                        <div class="message-capsule">
                            <div class="message-time">14:35</div>
                            <div class="message-sender">Eleanor Sterling</div>
                            <div class="message-text">All pneumatic tubes are functioning at peak efficiency. Message velocity has increased by 23% since last fortnight.</div>
                        </div>
                    </div>
                    <div class="tube-input-container">
                        <input type="text" class="tube-input" id="chat-input" placeholder="Compose your pneumatic message...">
                        <button class="tube-send" id="send-btn">➤</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Valve controls
        const valves = document.querySelectorAll('.valve-control');
        valves.forEach(valve => {
            valve.addEventListener('click', function() {
                this.classList.toggle('active');
                
                // Add steam effect
                const steamEffect = document.createElement('div');
                steamEffect.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 100px;
                    height: 100px;
                    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
                    border-radius: 50%;
                    animation: steam-expand 1s ease-out forwards;
                    pointer-events: none;
                `;
                this.appendChild(steamEffect);
                
                setTimeout(() => steamEffect.remove(), 1000);
            });
        });
        
        // Add CSS for steam expansion
        const style = document.createElement('style');
        style.textContent = `
            @keyframes steam-expand {
                from {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(0);
                }
                to {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(2);
                }
            }
        `;
        document.head.appendChild(style);
        
        // Recording functionality
        const recordBtn = document.getElementById('record-btn');
        const cylinderMech = document.querySelector('.cylinder-mechanism');
        let isRecording = false;
        let recordingTime = 0;
        let recordingInterval;
        
        recordBtn.addEventListener('click', function() {
            isRecording = !isRecording;
            this.classList.toggle('recording');
            cylinderMech.classList.toggle('recording');
            
            if (isRecording) {
                recordingInterval = setInterval(() => {
                    recordingTime++;
                    const hours = Math.floor(recordingTime / 3600).toString().padStart(2, '0');
                    const minutes = Math.floor((recordingTime % 3600) / 60).toString().padStart(2, '0');
                    const seconds = (recordingTime % 60).toString().padStart(2, '0');
                    this.nextElementSibling.textContent = `${hours}:${minutes}:${seconds}`;
                }, 1000);
            } else {
                clearInterval(recordingInterval);
            }
        });
        
        // Chat functionality
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const chatMessages = document.getElementById('chat-messages');
        
        function sendMessage() {
            const text = chatInput.value.trim();
            if (text) {
                const time = new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
                const messageHtml = `
                    <div class="message-capsule">
                        <div class="message-time">${time}</div>
                        <div class="message-sender">You</div>
                        <div class="message-text">${text}</div>
                    </div>
                `;
                chatMessages.insertAdjacentHTML('beforeend', messageHtml);
                chatInput.value = '';
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                // Add pneumatic tube sound effect simulation
                sendBtn.style.transform = 'scale(0.9)';
                setTimeout(() => sendBtn.style.transform = 'scale(1)', 200);
            }
        }
        
        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
        
        // Steam reactions
        const steamButtons = document.querySelectorAll('.steam-button');
        steamButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Create multiple steam puffs
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        const puff = document.createElement('div');
                        puff.className = 'steam-puff';
                        puff.style.left = `${40 + (Math.random() * 20)}%`;
                        puff.style.animation = 'steam-rise 1s ease-out forwards';
                        this.appendChild(puff);
                        setTimeout(() => puff.remove(), 1000);
                    }, i * 100);
                }
            });
        });
        
        // Participant status updates
        setInterval(() => {
            const indicators = document.querySelectorAll('.steam-indicator');
            indicators.forEach(indicator => {
                if (Math.random() > 0.7) {
                    indicator.style.animationDuration = Math.random() * 2 + 1 + 's';
                }
            });
        }, 5000);
        
        // Add some ambient gauge movements
        const gaugeNeedle = document.querySelector('.gauge-needle');
        setInterval(() => {
            const rotation = -30 + Math.random() * 60;
            gaugeNeedle.style.transform = `rotate(${rotation}deg)`;
        }, 2000);
    </script>
</body>
</html>