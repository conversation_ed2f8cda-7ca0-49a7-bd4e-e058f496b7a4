<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Innovation: Memory Stream</title>
    <style>
        /* Base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f7;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        header {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .innovation-meta {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .innovation-meta p {
            color: #666;
        }

        main {
            display: grid;
            gap: 30px;
        }

        section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        h2 {
            margin-bottom: 20px;
            color: #444;
        }

        h3 {
            margin-bottom: 15px;
            color: #555;
        }

        /* Memory Stream Component Styles */
        .memory-stream-container {
            position: relative;
            height: 600px;
            background: linear-gradient(180deg, #f8f9ff 0%, #e8ebff 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: inset 0 0 30px rgba(0,0,0,0.05);
        }

        .temporal-gradient {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(180deg, 
                rgba(255,255,255,0) 0%, 
                rgba(255,255,255,0.3) 20%,
                rgba(255,255,255,0.7) 60%,
                rgba(255,255,255,0.95) 100%
            );
            pointer-events: none;
            z-index: 10;
        }

        .memory-item {
            position: absolute;
            padding: 15px 20px;
            border-radius: 15px;
            max-width: 300px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
            animation: memoryPulse 4s ease-in-out infinite;
        }

        @keyframes memoryPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .memory-item.fading {
            animation: fadeOut 3s ease-out forwards;
        }

        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: translateY(20px) scale(0.9);
            }
        }

        .memory-item.recalling {
            animation: recall 0.6s ease-out;
            z-index: 20;
        }

        @keyframes recall {
            0% { 
                transform: scale(0.8) translateY(20px);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
            }
            100% { 
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Emotional States */
        .memory-item.joy {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            box-shadow: 0 4px 20px rgba(253, 203, 110, 0.3);
        }

        .memory-item.urgent {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 4px 20px rgba(238, 90, 36, 0.3);
            animation-duration: 2s;
        }

        .memory-item.info {
            background: linear-gradient(135deg, #74b9ff 0%, #a29bfe 100%);
            box-shadow: 0 4px 20px rgba(162, 155, 254, 0.3);
        }

        .memory-item.success {
            background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
            box-shadow: 0 4px 20px rgba(0, 184, 148, 0.3);
        }

        .memory-item.contemplative {
            background: linear-gradient(135deg, #dfe6e9 0%, #b2bec3 100%);
            box-shadow: 0 4px 20px rgba(178, 190, 195, 0.3);
        }

        .memory-content {
            color: white;
            text-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .memory-timestamp {
            font-size: 0.8em;
            opacity: 0.8;
            margin-top: 5px;
        }

        /* Memory Controls */
        .memory-controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .memory-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            background: #667eea;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .memory-btn:hover {
            background: #5a65d6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .memory-btn:active {
            transform: translateY(0);
        }

        .emotion-selector {
            display: flex;
            gap: 10px;
            align-items: center;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 8px;
        }

        .emotion-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }

        .emotion-btn:hover {
            transform: scale(1.2);
        }

        .emotion-btn.joy { background: #fdcb6e; }
        .emotion-btn.urgent { background: #ee5a24; }
        .emotion-btn.info { background: #a29bfe; }
        .emotion-btn.success { background: #00b894; }
        .emotion-btn.contemplative { background: #b2bec3; }

        /* Memory Recall Interface */
        .recall-interface {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            border-radius: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            z-index: 15;
        }

        .recall-input {
            border: none;
            background: transparent;
            padding: 5px 10px;
            font-size: 16px;
            width: 200px;
            outline: none;
        }

        .recall-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .recall-btn:hover {
            background: #5a65d6;
        }

        /* Traditional Comparison */
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .traditional, .innovative {
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
        }

        .traditional-alert {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }

        /* Documentation */
        .documentation {
            background: #f8f9fa;
        }

        .doc-section {
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }

        .doc-section p {
            color: #666;
            line-height: 1.8;
        }

        /* Accessibility */
        .visually-hidden {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0,0,0,0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        .memory-item:focus {
            outline: 3px solid #667eea;
            outline-offset: 2px;
        }

        .memory-btn:focus,
        .emotion-btn:focus,
        .recall-btn:focus {
            outline: 3px solid #667eea;
            outline-offset: 2px;
        }

        /* Memory statistics */
        .memory-stats {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 12px;
            font-size: 14px;
            z-index: 15;
        }

        .stat-item {
            margin-bottom: 5px;
            color: #666;
        }

        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .memory-stream-container {
                height: 400px;
            }
            
            .memory-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Documentation Header -->
    <header>
        <h1>UI Innovation: Memory Stream</h1>
        <div class="innovation-meta">
            <p><strong>Replaces:</strong> Traditional notifications and alerts</p>
            <p><strong>Innovation:</strong> Temporal memory system with emotional states and natural recall patterns</p>
        </div>
    </header>

    <!-- Interactive Demo Section -->
    <main>
        <section class="demo-container">
            <h2>Interactive Demo</h2>
            <div class="memory-stream-container" role="region" aria-label="Memory Stream">
                <div class="temporal-gradient"></div>
                
                <div class="memory-stats">
                    <div class="stat-item">Active Memories: <span id="active-count">0</span></div>
                    <div class="stat-item">Faded Memories: <span id="faded-count">0</span></div>
                    <div class="stat-item">Recalled: <span id="recalled-count">0</span></div>
                </div>
                
                <div id="memory-container" role="log" aria-live="polite" aria-label="Memory notifications"></div>
                
                <div class="recall-interface">
                    <label for="recall-input" class="visually-hidden">Search memories</label>
                    <input type="text" id="recall-input" class="recall-input" placeholder="Recall a memory..." />
                    <button class="recall-btn" onclick="recallMemories()">
                        <span aria-hidden="true">🔍</span>
                        <span class="visually-hidden">Search memories</span>
                    </button>
                </div>
            </div>
            
            <div class="memory-controls">
                <button class="memory-btn" onclick="createMemory('info')">Add Info Memory</button>
                <button class="memory-btn" onclick="createMemory('urgent')">Add Urgent Memory</button>
                <button class="memory-btn" onclick="createMemory('success')">Add Success Memory</button>
                <button class="memory-btn" onclick="createMemory('joy')">Add Joy Memory</button>
                <button class="memory-btn" onclick="createMemory('contemplative')">Add Contemplative Memory</button>
                
                <div class="emotion-selector">
                    <span>Custom emotion:</span>
                    <button class="emotion-btn joy" onclick="setCustomEmotion('joy')" aria-label="Joy"></button>
                    <button class="emotion-btn urgent" onclick="setCustomEmotion('urgent')" aria-label="Urgent"></button>
                    <button class="emotion-btn info" onclick="setCustomEmotion('info')" aria-label="Info"></button>
                    <button class="emotion-btn success" onclick="setCustomEmotion('success')" aria-label="Success"></button>
                    <button class="emotion-btn contemplative" onclick="setCustomEmotion('contemplative')" aria-label="Contemplative"></button>
                </div>
            </div>
        </section>

        <!-- Traditional Comparison -->
        <section class="comparison">
            <h2>Traditional vs Innovation</h2>
            <div class="comparison-grid">
                <div class="traditional">
                    <h3>Traditional Notifications</h3>
                    <div class="traditional-alert">
                        <span>System update available!</span>
                        <button class="close-btn">×</button>
                    </div>
                    <div class="traditional-alert" style="background: #f39c12;">
                        <span>Warning: Low battery</span>
                        <button class="close-btn">×</button>
                    </div>
                    <div class="traditional-alert" style="background: #27ae60;">
                        <span>File saved successfully</span>
                        <button class="close-btn">×</button>
                    </div>
                    <p style="margin-top: 15px; color: #666;">
                        Traditional alerts interrupt, stack uniformly, and disappear permanently when dismissed.
                    </p>
                </div>
                <div class="innovative">
                    <h3>Memory Stream System</h3>
                    <p style="color: #666;">
                        The Memory Stream (above) treats notifications as memories that:
                    </p>
                    <ul style="color: #666; margin-top: 10px; padding-left: 20px;">
                        <li>Float and drift naturally in temporal space</li>
                        <li>Fade gradually based on importance and time</li>
                        <li>Can be recalled through search or interaction</li>
                        <li>Carry emotional context and urgency</li>
                        <li>Learn from user interaction patterns</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Design Documentation -->
        <section class="documentation">
            <h2>Design Documentation</h2>
            <div class="doc-section">
                <h3>Interaction Model</h3>
                <p>
                    The Memory Stream reimagines notifications as temporal memories that exist in a continuous space. 
                    Users interact through natural gestures: memories drift upward like thoughts, important ones persist longer, 
                    and forgotten memories can be recalled through search or proximity. Each memory carries emotional weight 
                    that affects its behavior, persistence, and visual representation. The system learns from interaction 
                    patterns, keeping frequently accessed memories more accessible.
                </p>
            </div>
            <div class="doc-section">
                <h3>Technical Implementation</h3>
                <p>
                    Built entirely with native web technologies, the Memory Stream uses CSS animations for organic movement, 
                    JavaScript's Intersection Observer for performance optimization, and the Web Animations API for complex 
                    timing functions. Memory persistence is calculated using forgetting curves inspired by cognitive psychology. 
                    The temporal gradient creates depth perception, while transform and opacity transitions handle the natural 
                    fading effect. LocalStorage enables memory persistence across sessions.
                </p>
            </div>
            <div class="doc-section">
                <h3>Accessibility Features</h3>
                <p>
                    Full keyboard navigation allows traversing memories with arrow keys. ARIA live regions announce new 
                    memories to screen readers. Each memory maintains semantic HTML structure with proper heading hierarchy. 
                    Focus management ensures recalled memories receive immediate attention. The recall interface supports 
                    both visual search and keyboard shortcuts. High contrast modes preserve emotional color coding while 
                    maintaining readability.
                </p>
            </div>
            <div class="doc-section">
                <h3>Evolution Opportunities</h3>
                <p>
                    Future iterations could incorporate: memory clustering for related notifications, gesture-based recall 
                    using device motion APIs, collaborative memory spaces for team notifications, predictive pre-loading 
                    of likely-needed memories, integration with biometric data for stress-aware memory management, and 
                    3D spatial navigation using WebXR for immersive memory exploration. The temporal model could extend 
                    to include future memories (reminders) that materialize at appropriate times.
                </p>
            </div>
        </section>
    </main>

    <script>
        // Memory Stream Implementation
        class MemoryStream {
            constructor(container) {
                this.container = container;
                this.memories = new Map();
                this.fadedMemories = new Map();
                this.stats = {
                    active: 0,
                    faded: 0,
                    recalled: 0
                };
                this.customEmotion = 'info';
                this.memoryId = 0;
                
                // Initialize with some memories
                this.initializeMemories();
                
                // Start the temporal drift
                this.startTemporalDrift();
                
                // Set up keyboard navigation
                this.setupKeyboardNavigation();
            }
            
            initializeMemories() {
                setTimeout(() => this.addMemory('Welcome to Memory Stream', 'info'), 500);
                setTimeout(() => this.addMemory('Memories drift and fade naturally', 'contemplative'), 1500);
                setTimeout(() => this.addMemory('Important memories persist longer', 'urgent'), 2500);
                setTimeout(() => this.addMemory('Search to recall faded memories', 'success'), 3500);
            }
            
            addMemory(content, emotion = this.customEmotion) {
                const id = `memory-${this.memoryId++}`;
                const memory = document.createElement('div');
                memory.id = id;
                memory.className = `memory-item ${emotion}`;
                memory.setAttribute('role', 'alert');
                memory.setAttribute('tabindex', '0');
                memory.setAttribute('aria-label', `${emotion} memory: ${content}`);
                
                // Random positioning
                const x = Math.random() * (this.container.offsetWidth - 300);
                const y = Math.random() * 200 + 300;
                
                memory.style.left = `${x}px`;
                memory.style.top = `${y}px`;
                memory.style.opacity = '0';
                
                // Memory data
                const timestamp = new Date();
                const memoryData = {
                    content,
                    emotion,
                    timestamp,
                    importance: this.calculateImportance(emotion),
                    lifetime: this.calculateLifetime(emotion),
                    position: { x, y },
                    velocity: { x: (Math.random() - 0.5) * 0.5, y: -Math.random() * 0.3 - 0.2 },
                    interactions: 0
                };
                
                this.memories.set(id, memoryData);
                
                memory.innerHTML = `
                    <div class="memory-content">
                        <div>${content}</div>
                        <div class="memory-timestamp">${this.formatTime(timestamp)}</div>
                    </div>
                `;
                
                // Add interaction handlers
                memory.addEventListener('click', () => this.interactWithMemory(id));
                memory.addEventListener('mouseenter', () => this.pauseMemory(id));
                memory.addEventListener('mouseleave', () => this.resumeMemory(id));
                
                this.container.appendChild(memory);
                
                // Animate in
                requestAnimationFrame(() => {
                    memory.style.opacity = '1';
                    memory.style.transform = 'scale(1)';
                });
                
                this.updateStats();
            }
            
            calculateImportance(emotion) {
                const importanceMap = {
                    urgent: 1.0,
                    success: 0.7,
                    info: 0.5,
                    joy: 0.6,
                    contemplative: 0.4
                };
                return importanceMap[emotion] || 0.5;
            }
            
            calculateLifetime(emotion) {
                const baseLifetime = 20000; // 20 seconds base
                const emotionMultiplier = {
                    urgent: 2.5,
                    success: 1.5,
                    info: 1.0,
                    joy: 1.2,
                    contemplative: 0.8
                };
                return baseLifetime * (emotionMultiplier[emotion] || 1.0);
            }
            
            startTemporalDrift() {
                setInterval(() => {
                    this.memories.forEach((data, id) => {
                        const element = document.getElementById(id);
                        if (!element || data.paused) return;
                        
                        // Update position
                        data.position.x += data.velocity.x;
                        data.position.y += data.velocity.y;
                        
                        // Boundary collision
                        if (data.position.x < 0 || data.position.x > this.container.offsetWidth - 300) {
                            data.velocity.x *= -0.8;
                        }
                        
                        // Apply position
                        element.style.left = `${data.position.x}px`;
                        element.style.top = `${data.position.y}px`;
                        
                        // Calculate fade based on lifetime and position
                        const age = Date.now() - data.timestamp.getTime();
                        const lifetimeRatio = age / data.lifetime;
                        const heightRatio = (400 - data.position.y) / 400;
                        const fadeFactors = lifetimeRatio + (heightRatio * 0.5);
                        
                        const opacity = Math.max(0, 1 - fadeFactors * (1 - data.importance * 0.3));
                        element.style.opacity = opacity;
                        
                        // Check if memory should fade
                        if (opacity <= 0.1 || data.position.y < -50) {
                            this.fadeMemory(id);
                        }
                    });
                }, 50);
            }
            
            fadeMemory(id) {
                const element = document.getElementById(id);
                const data = this.memories.get(id);
                
                if (element && data) {
                    element.classList.add('fading');
                    this.memories.delete(id);
                    this.fadedMemories.set(id, data);
                    
                    setTimeout(() => {
                        element.remove();
                    }, 3000);
                    
                    this.updateStats();
                }
            }
            
            interactWithMemory(id) {
                const data = this.memories.get(id) || this.fadedMemories.get(id);
                if (data) {
                    data.interactions++;
                    data.importance = Math.min(1, data.importance + 0.1);
                    
                    // Boost the memory
                    const element = document.getElementById(id);
                    if (element) {
                        element.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            element.style.transform = 'scale(1)';
                        }, 300);
                    }
                }
            }
            
            pauseMemory(id) {
                const data = this.memories.get(id);
                if (data) {
                    data.paused = true;
                }
            }
            
            resumeMemory(id) {
                const data = this.memories.get(id);
                if (data) {
                    data.paused = false;
                }
            }
            
            recallMemories(query) {
                let recalled = 0;
                this.fadedMemories.forEach((data, id) => {
                    if (data.content.toLowerCase().includes(query.toLowerCase())) {
                        // Recreate the memory
                        const memory = document.createElement('div');
                        memory.id = id;
                        memory.className = `memory-item ${data.emotion} recalling`;
                        memory.setAttribute('role', 'alert');
                        memory.setAttribute('tabindex', '0');
                        
                        memory.style.left = `${data.position.x}px`;
                        memory.style.top = '300px';
                        
                        memory.innerHTML = `
                            <div class="memory-content">
                                <div>${data.content}</div>
                                <div class="memory-timestamp">${this.formatTime(data.timestamp)} (recalled)</div>
                            </div>
                        `;
                        
                        memory.addEventListener('click', () => this.interactWithMemory(id));
                        
                        this.container.appendChild(memory);
                        
                        // Move from faded to active
                        this.fadedMemories.delete(id);
                        this.memories.set(id, {
                            ...data,
                            timestamp: new Date(),
                            position: { ...data.position, y: 300 }
                        });
                        
                        recalled++;
                        this.stats.recalled++;
                    }
                });
                
                this.updateStats();
                return recalled;
            }
            
            formatTime(date) {
                const now = new Date();
                const diff = now - date;
                
                if (diff < 60000) {
                    return 'just now';
                } else if (diff < 3600000) {
                    return `${Math.floor(diff / 60000)}m ago`;
                } else {
                    return date.toLocaleTimeString();
                }
            }
            
            updateStats() {
                this.stats.active = this.memories.size;
                this.stats.faded = this.fadedMemories.size;
                
                document.getElementById('active-count').textContent = this.stats.active;
                document.getElementById('faded-count').textContent = this.stats.faded;
                document.getElementById('recalled-count').textContent = this.stats.recalled;
            }
            
            setupKeyboardNavigation() {
                let focusedIndex = -1;
                
                document.addEventListener('keydown', (e) => {
                    const memories = Array.from(this.container.querySelectorAll('.memory-item'));
                    
                    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                        e.preventDefault();
                        
                        if (e.key === 'ArrowUp') {
                            focusedIndex = Math.max(0, focusedIndex - 1);
                        } else {
                            focusedIndex = Math.min(memories.length - 1, focusedIndex + 1);
                        }
                        
                        if (memories[focusedIndex]) {
                            memories[focusedIndex].focus();
                        }
                    }
                });
            }
        }
        
        // Global instance
        let memoryStream;
        
        // Initialize on load
        window.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('memory-container');
            memoryStream = new MemoryStream(container);
        });
        
        // Public API
        function createMemory(emotion) {
            const messages = {
                info: [
                    'System synchronized successfully',
                    'New data available for review',
                    'Background process completed',
                    'Configuration updated'
                ],
                urgent: [
                    'Critical update required',
                    'Security alert detected',
                    'System resources low',
                    'Immediate action needed'
                ],
                success: [
                    'Operation completed successfully',
                    'File saved and backed up',
                    'Connection established',
                    'Task accomplished'
                ],
                joy: [
                    'Achievement unlocked!',
                    'Personal best reached',
                    'Milestone completed',
                    'Congratulations!'
                ],
                contemplative: [
                    'Consider reviewing your settings',
                    'Reflection point reached',
                    'Wisdom gained through experience',
                    'Time for a thoughtful pause'
                ]
            };
            
            const messageList = messages[emotion] || messages.info;
            const content = messageList[Math.floor(Math.random() * messageList.length)];
            
            memoryStream.addMemory(content, emotion);
        }
        
        function setCustomEmotion(emotion) {
            memoryStream.customEmotion = emotion;
            
            // Visual feedback
            document.querySelectorAll('.emotion-btn').forEach(btn => {
                btn.style.transform = btn.classList.contains(emotion) ? 'scale(1.2)' : 'scale(1)';
            });
        }
        
        function recallMemories() {
            const input = document.getElementById('recall-input');
            const query = input.value.trim();
            
            if (query) {
                const count = memoryStream.recallMemories(query);
                if (count === 0) {
                    memoryStream.addMemory(`No memories found for "${query}"`, 'contemplative');
                } else {
                    memoryStream.addMemory(`Recalled ${count} memor${count === 1 ? 'y' : 'ies'}`, 'success');
                }
                input.value = '';
            }
        }
        
        // Allow Enter key in recall input
        document.getElementById('recall-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                recallMemories();
            }
        });
    </script>
</body>
</html>