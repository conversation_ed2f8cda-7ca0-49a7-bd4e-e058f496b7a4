<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Innovation: MagneticField Selection</title>
    <style>
        /* Base Reset and Typography */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Header Styles */
        header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }

        .innovation-meta p {
            font-size: 1.1em;
            margin: 5px 0;
            color: #666;
        }

        /* Section Styles */
        section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #444;
        }

        h3 {
            font-size: 1.4em;
            margin-bottom: 15px;
            color: #555;
        }

        /* Magnetic Field Component Styles */
        .magnetic-field-container {
            position: relative;
            width: 100%;
            height: 600px;
            background: radial-gradient(ellipse at center, #f8f9ff 0%, #e8ebf5 100%);
            border-radius: 20px;
            overflow: hidden;
            cursor: crosshair;
            user-select: none;
        }

        /* Canvas for Field Lines */
        #fieldCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        /* Magnetic Particle Styles */
        .magnetic-particle {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            cursor: grab;
            transition: box-shadow 0.3s ease;
            z-index: 10;
        }

        .magnetic-particle:active {
            cursor: grabbing;
        }

        .magnetic-particle.north {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a6f 100%);
            box-shadow: 0 4px 20px rgba(238, 90, 111, 0.4);
        }

        .magnetic-particle.south {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a3aa 100%);
            box-shadow: 0 4px 20px rgba(78, 205, 196, 0.4);
        }

        .magnetic-particle.attracted {
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Selection Pole Styles */
        .selection-pole {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
            pointer-events: none;
            z-index: 5;
        }

        .selection-pole.selector-north {
            background: radial-gradient(circle, #ff4757 0%, #c44569 100%);
            box-shadow: 0 0 40px rgba(255, 71, 87, 0.6);
        }

        .selection-pole.selector-south {
            background: radial-gradient(circle, #26de81 0%, #20bf6b 100%);
            box-shadow: 0 0 40px rgba(38, 222, 129, 0.6);
        }

        /* Control Panel */
        .control-panel {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-group label {
            font-weight: 600;
            color: #666;
        }

        .control-button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .control-button:active {
            transform: translateY(0);
        }

        .field-strength-slider {
            width: 200px;
            height: 6px;
            -webkit-appearance: none;
            appearance: none;
            background: #ddd;
            border-radius: 3px;
            outline: none;
        }

        .field-strength-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #667eea;
            border-radius: 50%;
            cursor: pointer;
        }

        /* Selected Items Display */
        .selected-items {
            margin-top: 20px;
            padding: 15px;
            background: #f0f4ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .selected-items h4 {
            font-weight: 600;
            margin-bottom: 10px;
            color: #667eea;
        }

        .selected-items ul {
            list-style: none;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .selected-items li {
            padding: 5px 15px;
            background: white;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Comparison Grid */
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .comparison-grid > div {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        /* Traditional Checkboxes */
        .traditional-checkboxes {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .traditional-checkboxes label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }

        .traditional-checkboxes input[type="checkbox"] {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        /* Documentation Styles */
        .documentation {
            background: #f8f9fa;
        }

        .doc-section {
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }

        .doc-section p {
            color: #666;
            line-height: 1.8;
        }

        .doc-section ul {
            margin-top: 10px;
            margin-left: 20px;
            color: #666;
        }

        .doc-section li {
            margin-bottom: 8px;
        }

        /* Keyboard Instructions */
        .keyboard-instructions {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            font-size: 14px;
        }

        .keyboard-instructions strong {
            color: #856404;
        }

        /* Accessibility Announcements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Loading Animation */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            font-style: italic;
            color: #999;
        }
    </style>
</head>
<body>
    <!-- Documentation Header -->
    <header>
        <h1>UI Innovation: MagneticField Selection</h1>
        <div class="innovation-meta">
            <p><strong>Replaces:</strong> Traditional checkboxes and radio buttons</p>
            <p><strong>Innovation:</strong> Magnetic field-based selection using attraction and repulsion forces with visual field line rendering</p>
        </div>
    </header>

    <!-- Interactive Demo Section -->
    <main>
        <section class="demo-container">
            <h2>Interactive Demo</h2>
            <div class="magnetic-field-container" id="magneticField" role="application" aria-label="Magnetic field selection interface">
                <canvas id="fieldCanvas"></canvas>
                <!-- Selection poles will be added dynamically -->
                <!-- Magnetic particles will be added dynamically -->
            </div>
            
            <div class="control-panel">
                <div class="control-group">
                    <label>Field Strength</label>
                    <input type="range" class="field-strength-slider" id="fieldStrength" min="50" max="300" value="150" aria-label="Magnetic field strength">
                </div>
                <div class="control-group">
                    <label>Selection Mode</label>
                    <button class="control-button" id="toggleMode" aria-label="Toggle selection mode">Single Select</button>
                </div>
                <div class="control-group">
                    <label>Actions</label>
                    <button class="control-button" id="clearSelection" aria-label="Clear all selections">Clear Selection</button>
                </div>
                <div class="control-group">
                    <label>Field Visualization</label>
                    <button class="control-button" id="toggleField" aria-label="Toggle field visualization">Show Fields</button>
                </div>
            </div>

            <div class="selected-items">
                <h4>Selected Options:</h4>
                <ul id="selectedList" aria-live="polite">
                    <li>None selected</li>
                </ul>
            </div>

            <div class="keyboard-instructions">
                <strong>Keyboard Controls:</strong> Tab to navigate particles, Space to toggle selection, Arrow keys to move selected particle, Shift+Arrow for fine control
            </div>
        </section>

        <!-- Traditional Comparison -->
        <section class="comparison">
            <h2>Traditional vs Innovation</h2>
            <div class="comparison-grid">
                <div class="traditional">
                    <h3>Traditional Checkboxes</h3>
                    <div class="traditional-checkboxes">
                        <label>
                            <input type="checkbox" name="traditional" value="option1">
                            Option 1
                        </label>
                        <label>
                            <input type="checkbox" name="traditional" value="option2">
                            Option 2
                        </label>
                        <label>
                            <input type="checkbox" name="traditional" value="option3">
                            Option 3
                        </label>
                        <label>
                            <input type="checkbox" name="traditional" value="option4">
                            Option 4
                        </label>
                        <label>
                            <input type="checkbox" name="traditional" value="option5">
                            Option 5
                        </label>
                    </div>
                </div>
                <div class="innovative">
                    <h3>Magnetic Field Selection</h3>
                    <p>The demo above shows the innovative magnetic field approach where:</p>
                    <ul>
                        <li>Options are magnetic particles with poles</li>
                        <li>Selection uses magnetic attraction</li>
                        <li>Visual field lines show forces</li>
                        <li>Physics simulation creates natural interactions</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Design Documentation -->
        <section class="documentation">
            <h2>Design Documentation</h2>
            
            <div class="doc-section">
                <h3>Interaction Model</h3>
                <p>The MagneticField Selection interface transforms checkbox and radio button selection into a physics-based magnetic field interaction:</p>
                <ul>
                    <li><strong>Magnetic Particles:</strong> Each option is represented as a magnetic particle with north (red) and south (blue) poles</li>
                    <li><strong>Selection Poles:</strong> Fixed magnetic poles attract or repel particles based on polarity</li>
                    <li><strong>Drag Interaction:</strong> Users can drag particles through the magnetic field, feeling the forces</li>
                    <li><strong>Attraction Selection:</strong> When particles are attracted to selection poles, they become selected</li>
                    <li><strong>Field Visualization:</strong> Optional magnetic field lines show the invisible forces at play</li>
                    <li><strong>Multi-Select:</strong> Multiple particles can cluster around attraction poles for multi-selection</li>
                </ul>
            </div>
            
            <div class="doc-section">
                <h3>Technical Implementation</h3>
                <p>This component leverages native web technologies to create a realistic magnetic field simulation:</p>
                <ul>
                    <li><strong>Canvas API:</strong> Renders magnetic field lines using mathematical field equations</li>
                    <li><strong>Physics Engine:</strong> Custom JavaScript physics simulation for magnetic forces</li>
                    <li><strong>Drag & Drop:</strong> Native pointer events for smooth particle manipulation</li>
                    <li><strong>RequestAnimationFrame:</strong> 60fps animation loop for fluid motion</li>
                    <li><strong>CSS Transforms:</strong> Hardware-accelerated particle movement</li>
                    <li><strong>Inverse Square Law:</strong> Realistic magnetic force calculations based on distance</li>
                </ul>
            </div>
            
            <div class="doc-section">
                <h3>Accessibility Features</h3>
                <p>Despite the visual nature, the component maintains full accessibility:</p>
                <ul>
                    <li><strong>Keyboard Navigation:</strong> Tab through particles, Space to select, Arrow keys to move</li>
                    <li><strong>Screen Reader Support:</strong> ARIA labels and live regions announce selections</li>
                    <li><strong>Focus Indicators:</strong> Clear visual focus states for keyboard users</li>
                    <li><strong>Alternative Input:</strong> Works with mouse, touch, and keyboard equally well</li>
                    <li><strong>High Contrast:</strong> Strong color differentiation between poles</li>
                    <li><strong>Motion Control:</strong> Respects prefers-reduced-motion settings</li>
                </ul>
            </div>
            
            <div class="doc-section">
                <h3>Evolution Opportunities</h3>
                <p>Future enhancements could expand the magnetic metaphor:</p>
                <ul>
                    <li><strong>Electromagnetic Fields:</strong> Add electrical fields for more complex interactions</li>
                    <li><strong>Field Shaping:</strong> User-drawn field lines to create custom selection patterns</li>
                    <li><strong>Quantum States:</strong> Particles in superposition for probabilistic selection</li>
                    <li><strong>3D Fields:</strong> WebGL-based 3D magnetic field visualization</li>
                    <li><strong>Haptic Feedback:</strong> Vibration API for feeling magnetic forces</li>
                    <li><strong>Group Dynamics:</strong> Particles that influence each other's selection state</li>
                </ul>
            </div>
        </section>
    </main>

    <!-- Screen Reader Announcements -->
    <div aria-live="polite" aria-atomic="true" class="sr-only" id="announcements"></div>

    <script>
        // Magnetic Field Selection Component
        class MagneticFieldSelection {
            constructor(container) {
                this.container = container;
                this.canvas = document.getElementById('fieldCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.particles = [];
                this.selectionPoles = [];
                this.selected = new Set();
                this.isDragging = false;
                this.draggedParticle = null;
                this.fieldStrength = 150;
                this.showFieldLines = false;
                this.isMultiSelect = true;
                this.animationId = null;
                this.focusedIndex = -1;
                
                this.init();
            }
            
            init() {
                // Set canvas size
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());
                
                // Create selection poles
                this.createSelectionPoles();
                
                // Create magnetic particles for options
                this.createParticles();
                
                // Set up event listeners
                this.setupEventListeners();
                
                // Start animation loop
                this.animate();
                
                // Check for reduced motion preference
                this.respectReducedMotion();
            }
            
            resizeCanvas() {
                const rect = this.container.getBoundingClientRect();
                this.canvas.width = rect.width;
                this.canvas.height = rect.height;
            }
            
            createSelectionPoles() {
                // Create north pole (attractor)
                const northPole = document.createElement('div');
                northPole.className = 'selection-pole selector-north';
                northPole.textContent = 'SELECT';
                northPole.style.left = '100px';
                northPole.style.top = '50%';
                northPole.style.transform = 'translateY(-50%)';
                this.container.appendChild(northPole);
                
                this.selectionPoles.push({
                    element: northPole,
                    type: 'north',
                    x: 100,
                    y: this.canvas.height / 2,
                    strength: this.fieldStrength
                });
                
                // Create south pole (repeller)
                const southPole = document.createElement('div');
                southPole.className = 'selection-pole selector-south';
                southPole.textContent = 'DESELECT';
                southPole.style.right = '100px';
                southPole.style.top = '50%';
                southPole.style.transform = 'translateY(-50%)';
                this.container.appendChild(southPole);
                
                this.selectionPoles.push({
                    element: southPole,
                    type: 'south',
                    x: this.canvas.width - 100,
                    y: this.canvas.height / 2,
                    strength: this.fieldStrength
                });
            }
            
            createParticles() {
                const options = [
                    { id: 'opt1', label: 'Option 1', polarity: 'north' },
                    { id: 'opt2', label: 'Option 2', polarity: 'south' },
                    { id: 'opt3', label: 'Option 3', polarity: 'north' },
                    { id: 'opt4', label: 'Option 4', polarity: 'south' },
                    { id: 'opt5', label: 'Option 5', polarity: 'north' }
                ];
                
                options.forEach((option, index) => {
                    const particle = document.createElement('div');
                    particle.className = `magnetic-particle ${option.polarity}`;
                    particle.textContent = option.label;
                    particle.setAttribute('role', 'checkbox');
                    particle.setAttribute('aria-checked', 'false');
                    particle.setAttribute('aria-label', option.label);
                    particle.setAttribute('tabindex', index === 0 ? '0' : '-1');
                    particle.dataset.id = option.id;
                    particle.dataset.index = index;
                    
                    // Random initial position
                    const x = 200 + Math.random() * (this.canvas.width - 400);
                    const y = 100 + Math.random() * (this.canvas.height - 200);
                    
                    particle.style.left = x + 'px';
                    particle.style.top = y + 'px';
                    
                    this.container.appendChild(particle);
                    
                    this.particles.push({
                        element: particle,
                        id: option.id,
                        label: option.label,
                        polarity: option.polarity,
                        x: x + 30, // Center of particle
                        y: y + 30,
                        vx: 0,
                        vy: 0,
                        mass: 1,
                        charge: option.polarity === 'north' ? 1 : -1
                    });
                });
            }
            
            setupEventListeners() {
                // Mouse/touch events for dragging
                this.container.addEventListener('mousedown', (e) => this.handlePointerDown(e));
                this.container.addEventListener('touchstart', (e) => this.handlePointerDown(e.touches[0]));
                
                document.addEventListener('mousemove', (e) => this.handlePointerMove(e));
                document.addEventListener('touchmove', (e) => this.handlePointerMove(e.touches[0]));
                
                document.addEventListener('mouseup', () => this.handlePointerUp());
                document.addEventListener('touchend', () => this.handlePointerUp());
                
                // Keyboard events
                this.container.addEventListener('keydown', (e) => this.handleKeyDown(e));
                
                // Control panel events
                document.getElementById('fieldStrength').addEventListener('input', (e) => {
                    this.fieldStrength = parseInt(e.target.value);
                    this.updatePoleStrength();
                });
                
                document.getElementById('toggleMode').addEventListener('click', () => {
                    this.isMultiSelect = !this.isMultiSelect;
                    document.getElementById('toggleMode').textContent = 
                        this.isMultiSelect ? 'Multi Select' : 'Single Select';
                    if (!this.isMultiSelect && this.selected.size > 1) {
                        // Keep only the first selected item
                        const first = Array.from(this.selected)[0];
                        this.selected.clear();
                        this.selected.add(first);
                        this.updateSelection();
                    }
                });
                
                document.getElementById('clearSelection').addEventListener('click', () => {
                    this.clearSelection();
                });
                
                document.getElementById('toggleField').addEventListener('click', () => {
                    this.showFieldLines = !this.showFieldLines;
                    document.getElementById('toggleField').textContent = 
                        this.showFieldLines ? 'Hide Fields' : 'Show Fields';
                });
            }
            
            handlePointerDown(e) {
                const rect = this.container.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // Check if clicking on a particle
                for (let particle of this.particles) {
                    const dx = x - particle.x;
                    const dy = y - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 30) { // Particle radius
                        this.isDragging = true;
                        this.draggedParticle = particle;
                        particle.element.style.cursor = 'grabbing';
                        particle.element.style.zIndex = '20';
                        break;
                    }
                }
            }
            
            handlePointerMove(e) {
                if (!this.isDragging || !this.draggedParticle) return;
                
                const rect = this.container.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // Update particle position
                this.draggedParticle.x = Math.max(30, Math.min(this.canvas.width - 30, x));
                this.draggedParticle.y = Math.max(30, Math.min(this.canvas.height - 30, y));
                
                // Reset velocity when dragging
                this.draggedParticle.vx = 0;
                this.draggedParticle.vy = 0;
            }
            
            handlePointerUp() {
                if (this.draggedParticle) {
                    this.draggedParticle.element.style.cursor = 'grab';
                    this.draggedParticle.element.style.zIndex = '10';
                    this.draggedParticle = null;
                }
                this.isDragging = false;
            }
            
            handleKeyDown(e) {
                const currentParticle = this.particles[this.focusedIndex];
                if (!currentParticle) return;
                
                switch(e.key) {
                    case 'Tab':
                        e.preventDefault();
                        this.moveFocus(e.shiftKey ? -1 : 1);
                        break;
                        
                    case ' ':
                    case 'Enter':
                        e.preventDefault();
                        this.toggleParticleSelection(currentParticle);
                        break;
                        
                    case 'ArrowLeft':
                        e.preventDefault();
                        currentParticle.x -= e.shiftKey ? 5 : 20;
                        break;
                        
                    case 'ArrowRight':
                        e.preventDefault();
                        currentParticle.x += e.shiftKey ? 5 : 20;
                        break;
                        
                    case 'ArrowUp':
                        e.preventDefault();
                        currentParticle.y -= e.shiftKey ? 5 : 20;
                        break;
                        
                    case 'ArrowDown':
                        e.preventDefault();
                        currentParticle.y += e.shiftKey ? 5 : 20;
                        break;
                }
            }
            
            moveFocus(direction) {
                // Remove focus from current
                if (this.focusedIndex >= 0) {
                    this.particles[this.focusedIndex].element.setAttribute('tabindex', '-1');
                }
                
                // Calculate new focus index
                this.focusedIndex = (this.focusedIndex + direction + this.particles.length) % this.particles.length;
                
                // Set focus on new particle
                const particle = this.particles[this.focusedIndex];
                particle.element.setAttribute('tabindex', '0');
                particle.element.focus();
            }
            
            updatePoleStrength() {
                this.selectionPoles.forEach(pole => {
                    pole.strength = this.fieldStrength;
                });
            }
            
            calculateMagneticForce(particle, pole) {
                const dx = pole.x - particle.x;
                const dy = pole.y - particle.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 1) return { fx: 0, fy: 0 };
                
                // Magnetic force using inverse square law
                const force = (pole.strength * particle.charge) / (distance * distance);
                
                // Attraction/repulsion based on pole types
                const attraction = (pole.type === 'north' && particle.polarity === 'south') ||
                                 (pole.type === 'south' && particle.polarity === 'north') ? 1 : -1;
                
                const fx = attraction * force * (dx / distance);
                const fy = attraction * force * (dy / distance);
                
                return { fx, fy };
            }
            
            updatePhysics() {
                if (this.isDragging) return;
                
                const damping = 0.95;
                const maxVelocity = 5;
                
                this.particles.forEach(particle => {
                    let totalFx = 0;
                    let totalFy = 0;
                    
                    // Calculate forces from all poles
                    this.selectionPoles.forEach(pole => {
                        const force = this.calculateMagneticForce(particle, pole);
                        totalFx += force.fx;
                        totalFy += force.fy;
                    });
                    
                    // Add slight repulsion between particles
                    this.particles.forEach(other => {
                        if (other === particle) return;
                        
                        const dx = other.x - particle.x;
                        const dy = other.y - particle.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < 80 && distance > 0) {
                            const repulsion = 50 / (distance * distance);
                            totalFx -= repulsion * (dx / distance);
                            totalFy -= repulsion * (dy / distance);
                        }
                    });
                    
                    // Update velocity
                    particle.vx += totalFx / particle.mass;
                    particle.vy += totalFy / particle.mass;
                    
                    // Apply damping
                    particle.vx *= damping;
                    particle.vy *= damping;
                    
                    // Limit velocity
                    const velocity = Math.sqrt(particle.vx * particle.vx + particle.vy * particle.vy);
                    if (velocity > maxVelocity) {
                        particle.vx = (particle.vx / velocity) * maxVelocity;
                        particle.vy = (particle.vy / velocity) * maxVelocity;
                    }
                    
                    // Update position
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    // Boundary collision
                    if (particle.x < 30) {
                        particle.x = 30;
                        particle.vx *= -0.5;
                    } else if (particle.x > this.canvas.width - 30) {
                        particle.x = this.canvas.width - 30;
                        particle.vx *= -0.5;
                    }
                    
                    if (particle.y < 30) {
                        particle.y = 30;
                        particle.vy *= -0.5;
                    } else if (particle.y > this.canvas.height - 30) {
                        particle.y = this.canvas.height - 30;
                        particle.vy *= -0.5;
                    }
                    
                    // Update DOM position
                    particle.element.style.left = (particle.x - 30) + 'px';
                    particle.element.style.top = (particle.y - 30) + 'px';
                    
                    // Check selection based on proximity to selection pole
                    const selectionPole = this.selectionPoles[0]; // North pole for selection
                    const selectionDistance = Math.sqrt(
                        Math.pow(selectionPole.x - particle.x, 2) + 
                        Math.pow(selectionPole.y - particle.y, 2)
                    );
                    
                    if (selectionDistance < 100) {
                        if (!this.selected.has(particle.id)) {
                            if (!this.isMultiSelect) {
                                this.selected.clear();
                            }
                            this.selected.add(particle.id);
                            this.updateSelection();
                        }
                        particle.element.classList.add('attracted');
                    } else {
                        if (this.selected.has(particle.id) && selectionDistance > 150) {
                            this.selected.delete(particle.id);
                            this.updateSelection();
                        }
                        particle.element.classList.remove('attracted');
                    }
                });
            }
            
            drawFieldLines() {
                if (!this.showFieldLines) {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    return;
                }
                
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.strokeStyle = 'rgba(102, 126, 234, 0.2)';
                this.ctx.lineWidth = 1;
                
                // Draw field lines from poles
                this.selectionPoles.forEach(pole => {
                    const lineCount = 16;
                    for (let i = 0; i < lineCount; i++) {
                        const angle = (i / lineCount) * Math.PI * 2;
                        const startX = pole.x + Math.cos(angle) * 40;
                        const startY = pole.y + Math.sin(angle) * 40;
                        
                        this.drawFieldLine(startX, startY, pole);
                    }
                });
            }
            
            drawFieldLine(startX, startY, sourcePole) {
                this.ctx.beginPath();
                this.ctx.moveTo(startX, startY);
                
                let x = startX;
                let y = startY;
                const steps = 50;
                const stepSize = 5;
                
                for (let i = 0; i < steps; i++) {
                    let totalFx = 0;
                    let totalFy = 0;
                    
                    // Calculate field direction at this point
                    this.selectionPoles.forEach(pole => {
                        const dx = x - pole.x;
                        const dy = y - pole.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance > 1) {
                            const strength = pole.strength / (distance * distance);
                            const direction = pole === sourcePole ? 1 : -1;
                            totalFx += direction * strength * (dx / distance);
                            totalFy += direction * strength * (dy / distance);
                        }
                    });
                    
                    // Normalize and step
                    const magnitude = Math.sqrt(totalFx * totalFx + totalFy * totalFy);
                    if (magnitude > 0) {
                        x += (totalFx / magnitude) * stepSize;
                        y += (totalFy / magnitude) * stepSize;
                        this.ctx.lineTo(x, y);
                    }
                    
                    // Stop if out of bounds
                    if (x < 0 || x > this.canvas.width || y < 0 || y > this.canvas.height) break;
                }
                
                this.ctx.stroke();
            }
            
            toggleParticleSelection(particle) {
                if (this.selected.has(particle.id)) {
                    this.selected.delete(particle.id);
                } else {
                    if (!this.isMultiSelect) {
                        this.selected.clear();
                    }
                    this.selected.add(particle.id);
                }
                this.updateSelection();
                
                // Announce to screen readers
                const announcement = this.selected.has(particle.id) ? 
                    `${particle.label} selected` : `${particle.label} deselected`;
                this.announce(announcement);
            }
            
            updateSelection() {
                // Update ARIA states
                this.particles.forEach(particle => {
                    const isSelected = this.selected.has(particle.id);
                    particle.element.setAttribute('aria-checked', isSelected.toString());
                });
                
                // Update selected items display
                const selectedList = document.getElementById('selectedList');
                selectedList.innerHTML = '';
                
                if (this.selected.size === 0) {
                    const li = document.createElement('li');
                    li.textContent = 'None selected';
                    selectedList.appendChild(li);
                } else {
                    this.selected.forEach(id => {
                        const particle = this.particles.find(p => p.id === id);
                        if (particle) {
                            const li = document.createElement('li');
                            li.textContent = particle.label;
                            selectedList.appendChild(li);
                        }
                    });
                }
            }
            
            clearSelection() {
                this.selected.clear();
                this.updateSelection();
                this.announce('All selections cleared');
                
                // Move particles away from selection pole
                this.particles.forEach(particle => {
                    particle.vx = (Math.random() - 0.5) * 10;
                    particle.vy = (Math.random() - 0.5) * 10;
                });
            }
            
            announce(message) {
                const announcements = document.getElementById('announcements');
                announcements.textContent = message;
                setTimeout(() => {
                    announcements.textContent = '';
                }, 1000);
            }
            
            respectReducedMotion() {
                const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
                if (prefersReducedMotion) {
                    // Reduce animation intensity
                    this.particles.forEach(particle => {
                        particle.element.style.transition = 'none';
                    });
                }
            }
            
            animate() {
                this.updatePhysics();
                this.drawFieldLines();
                this.animationId = requestAnimationFrame(() => this.animate());
            }
            
            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
            }
        }
        
        // Initialize the component
        let magneticField = null;
        
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('magneticField');
            magneticField = new MagneticFieldSelection(container);
        });
        
        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            if (magneticField) {
                magneticField.destroy();
            }
        });
    </script>
</body>
</html>