<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Innovation: Quantum State Toggle</title>
    <style>
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #e0e0e0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        header {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00ffcc, #0099ff, #cc00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .innovation-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .innovation-meta p {
            background: rgba(255, 255, 255, 0.05);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        section {
            margin-bottom: 4rem;
            background: rgba(255, 255, 255, 0.02);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        h2 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: #00ffcc;
        }

        h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #0099ff;
        }

        /* Quantum Toggle Styles */
        .quantum-toggle-container {
            display: flex;
            flex-wrap: wrap;
            gap: 3rem;
            justify-content: center;
            margin: 2rem 0;
        }

        .quantum-toggle {
            position: relative;
            width: 200px;
            height: 200px;
            cursor: pointer;
            user-select: none;
        }

        .quantum-field {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
        }

        /* Probability Cloud */
        .probability-cloud {
            position: absolute;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, 
                rgba(0, 255, 204, 0.3) 0%, 
                rgba(0, 153, 255, 0.2) 40%, 
                rgba(204, 0, 255, 0.1) 70%, 
                transparent 100%);
            animation: quantumFluctuation 3s ease-in-out infinite;
            filter: blur(2px);
        }

        @keyframes quantumFluctuation {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(90deg); }
            50% { transform: scale(0.9) rotate(180deg); }
            75% { transform: scale(1.05) rotate(270deg); }
        }

        /* Wave Function */
        .wave-function {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .wave-path {
            stroke: rgba(0, 255, 204, 0.8);
            stroke-width: 2;
            fill: none;
            filter: drop-shadow(0 0 5px rgba(0, 255, 204, 0.5));
            animation: waveOscillation 2s linear infinite;
        }

        @keyframes waveOscillation {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -100; }
        }

        /* Quantum States */
        .quantum-state {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .state-on {
            background: radial-gradient(circle, #00ffcc, #0099ff);
            box-shadow: 0 0 30px rgba(0, 255, 204, 0.8);
        }

        .state-off {
            background: radial-gradient(circle, #666, #333);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        .state-superposition {
            background: radial-gradient(circle, 
                rgba(0, 255, 204, 0.5), 
                rgba(102, 102, 102, 0.5));
            box-shadow: 0 0 20px rgba(0, 255, 204, 0.4);
            animation: superpositionPulse 1s ease-in-out infinite;
        }

        @keyframes superpositionPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        /* Probability Display */
        .probability-display {
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            font-size: 0.9rem;
            color: #00ffcc;
            white-space: nowrap;
        }

        /* Measurement Effect */
        .collapse-wave {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px solid rgba(0, 255, 204, 0.8);
            animation: collapseAnimation 0.6s ease-out;
            pointer-events: none;
        }

        @keyframes collapseAnimation {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        /* Entanglement Line */
        .entanglement-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(204, 0, 255, 0.8), 
                transparent);
            transform-origin: left center;
            pointer-events: none;
            animation: entanglementPulse 2s ease-in-out infinite;
        }

        @keyframes entanglementPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        /* Labels */
        .toggle-label {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.9rem;
            color: #0099ff;
        }

        /* Traditional Toggle Styles */
        .traditional-toggle {
            display: inline-block;
            margin: 1rem;
        }

        .traditional-toggle input[type="checkbox"] {
            display: none;
        }

        .traditional-toggle-label {
            display: inline-block;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }

        .traditional-toggle-label::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }

        .traditional-toggle input[type="checkbox"]:checked + .traditional-toggle-label {
            background: #4CAF50;
        }

        .traditional-toggle input[type="checkbox"]:checked + .traditional-toggle-label::after {
            transform: translateX(30px);
        }

        /* Comparison Grid */
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        .comparison-grid > div {
            background: rgba(255, 255, 255, 0.03);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Documentation */
        .doc-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            border-left: 3px solid #00ffcc;
        }

        .doc-section p {
            margin-bottom: 0.5rem;
            line-height: 1.8;
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .quantum-toggle:focus {
            outline: 2px solid #00ffcc;
            outline-offset: 5px;
        }

        /* Control Panel */
        .control-panel {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-button {
            background: rgba(0, 255, 204, 0.2);
            border: 1px solid rgba(0, 255, 204, 0.5);
            color: #00ffcc;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .control-button:hover {
            background: rgba(0, 255, 204, 0.3);
            box-shadow: 0 0 10px rgba(0, 255, 204, 0.5);
        }
    </style>
</head>
<body>
    <!-- Documentation Header -->
    <header>
        <h1>UI Innovation: Quantum State Toggle</h1>
        <div class="innovation-meta">
            <p><strong>Replaces:</strong> Traditional binary toggles/switches</p>
            <p><strong>Innovation:</strong> Quantum superposition states with probability visualization</p>
        </div>
    </header>

    <!-- Interactive Demo Section -->
    <main>
        <section class="demo-container">
            <h2>Interactive Demo</h2>
            
            <div class="control-panel">
                <button class="control-button" onclick="measureAll()">Measure All States</button>
                <button class="control-button" onclick="entangleToggles()">Create Entanglement</button>
                <button class="control-button" onclick="resetToSuperposition()">Reset to Superposition</button>
            </div>
            
            <div class="quantum-toggle-container" id="quantumContainer">
                <!-- Quantum toggles will be dynamically created -->
            </div>
        </section>

        <!-- Traditional Comparison -->
        <section class="comparison">
            <h2>Traditional vs Innovation</h2>
            <div class="comparison-grid">
                <div class="traditional">
                    <h3>Traditional Toggle</h3>
                    <p>Binary states only (ON/OFF)</p>
                    <div style="text-align: center; margin: 2rem 0;">
                        <div class="traditional-toggle">
                            <input type="checkbox" id="trad1">
                            <label class="traditional-toggle-label" for="trad1"></label>
                        </div>
                        <div class="traditional-toggle">
                            <input type="checkbox" id="trad2">
                            <label class="traditional-toggle-label" for="trad2"></label>
                        </div>
                        <div class="traditional-toggle">
                            <input type="checkbox" id="trad3">
                            <label class="traditional-toggle-label" for="trad3"></label>
                        </div>
                    </div>
                    <p>• Immediate state change</p>
                    <p>• No uncertainty representation</p>
                    <p>• Independent operation</p>
                </div>
                <div class="innovative">
                    <h3>Quantum Toggle</h3>
                    <p>Superposition states with probability</p>
                    <p style="margin: 2rem 0; text-align: center;">
                        See interactive demo above ↑
                    </p>
                    <p>• States exist in superposition</p>
                    <p>• Probability wave visualization</p>
                    <p>• Quantum entanglement possible</p>
                    <p>• Measurement causes wave collapse</p>
                </div>
            </div>
        </section>

        <!-- Design Documentation -->
        <section class="documentation">
            <h2>Design Documentation</h2>
            
            <div class="doc-section">
                <h3>Interaction Model</h3>
                <p>Users interact with quantum toggles through observation and measurement. Unlike traditional binary switches, these toggles exist in superposition until measured.</p>
                <p>• <strong>Click/Tap:</strong> Performs a quantum measurement, collapsing the wave function to a definite state</p>
                <p>• <strong>Hover:</strong> Shows probability amplitudes without collapsing the state</p>
                <p>• <strong>Entanglement:</strong> Connected toggles affect each other instantaneously</p>
                <p>• <strong>Keyboard:</strong> Space/Enter to measure, Tab to navigate</p>
            </div>
            
            <div class="doc-section">
                <h3>Technical Implementation</h3>
                <p>Built using native web technologies to create quantum-inspired visualizations:</p>
                <p>• <strong>CSS Animations:</strong> Continuous wave functions and probability clouds</p>
                <p>• <strong>SVG Paths:</strong> Dynamic wave function visualization</p>
                <p>• <strong>JavaScript:</strong> Quantum state management and entanglement logic</p>
                <p>• <strong>CSS Custom Properties:</strong> Real-time probability updates</p>
                <p>• <strong>Transform & Filters:</strong> Superposition visual effects</p>
            </div>
            
            <div class="doc-section">
                <h3>Accessibility Features</h3>
                <p>• Full keyboard navigation with visible focus indicators</p>
                <p>• ARIA labels describing current quantum state and probability</p>
                <p>• Screen reader announcements for state changes</p>
                <p>• High contrast visual indicators for all states</p>
                <p>• Reduced motion option respects user preferences</p>
            </div>
            
            <div class="doc-section">
                <h3>Evolution Opportunities</h3>
                <p>• <strong>Multi-state Superposition:</strong> Beyond binary to n-dimensional quantum states</p>
                <p>• <strong>Quantum Gates:</strong> Implement quantum logic operations between toggles</p>
                <p>• <strong>Decoherence Effects:</strong> Environmental interaction causing gradual state collapse</p>
                <p>• <strong>Quantum Tunneling:</strong> Probability of spontaneous state changes</p>
                <p>• <strong>Many-Worlds Visualization:</strong> Show parallel universe states</p>
            </div>
        </section>
    </main>

    <script>
        // Quantum Toggle Implementation
        class QuantumToggle {
            constructor(id, label) {
                this.id = id;
                this.label = label;
                this.state = 'superposition';
                this.probability = { on: 0.5, off: 0.5 };
                this.entangled = [];
                this.element = null;
                this.isCollapsing = false;
            }

            createElement() {
                const container = document.createElement('div');
                container.className = 'quantum-toggle';
                container.tabIndex = 0;
                container.setAttribute('role', 'switch');
                container.setAttribute('aria-checked', 'mixed');
                container.setAttribute('aria-label', `${this.label} - Quantum state toggle`);
                
                container.innerHTML = `
                    <span class="sr-only">Quantum toggle: ${this.label}</span>
                    <div class="toggle-label">${this.label}</div>
                    <div class="quantum-field">
                        <div class="probability-cloud"></div>
                        <svg class="wave-function" viewBox="0 0 200 200">
                            <path class="wave-path" stroke-dasharray="5,5" />
                        </svg>
                        <div class="quantum-state state-superposition"></div>
                    </div>
                    <div class="probability-display">|ψ⟩ = √${this.probability.on.toFixed(2)}|1⟩ + √${this.probability.off.toFixed(2)}|0⟩</div>
                `;

                // Event listeners
                container.addEventListener('click', () => this.measure());
                container.addEventListener('keydown', (e) => {
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        this.measure();
                    }
                });

                this.element = container;
                this.updateWaveFunction();
                return container;
            }

            updateWaveFunction() {
                if (!this.element) return;
                
                const path = this.element.querySelector('.wave-path');
                const amplitude = this.state === 'superposition' ? 30 : 15;
                const frequency = this.state === 'superposition' ? 3 : 1;
                
                let d = 'M 0,100 ';
                for (let x = 0; x <= 200; x += 5) {
                    const y = 100 + amplitude * Math.sin((x / 200) * Math.PI * 2 * frequency) * 
                              (this.probability.on - this.probability.off);
                    d += `L ${x},${y} `;
                }
                
                path.setAttribute('d', d);
            }

            measure() {
                if (this.isCollapsing) return;
                this.isCollapsing = true;

                // Create collapse animation
                const collapseWave = document.createElement('div');
                collapseWave.className = 'collapse-wave';
                this.element.querySelector('.quantum-field').appendChild(collapseWave);

                // Determine collapsed state based on probability
                const random = Math.random();
                const newState = random < this.probability.on ? 'on' : 'off';
                
                setTimeout(() => {
                    this.setState(newState);
                    collapseWave.remove();
                    this.isCollapsing = false;
                    
                    // Affect entangled toggles
                    this.entangled.forEach(toggle => {
                        if (toggle.state === 'superposition') {
                            toggle.influenceFromEntangled(newState);
                        }
                    });
                }, 300);
            }

            setState(newState) {
                this.state = newState;
                const stateElement = this.element.querySelector('.quantum-state');
                
                stateElement.className = 'quantum-state';
                if (newState === 'on') {
                    stateElement.classList.add('state-on');
                    this.probability = { on: 1, off: 0 };
                    this.element.setAttribute('aria-checked', 'true');
                } else if (newState === 'off') {
                    stateElement.classList.add('state-off');
                    this.probability = { on: 0, off: 1 };
                    this.element.setAttribute('aria-checked', 'false');
                } else {
                    stateElement.classList.add('state-superposition');
                    this.element.setAttribute('aria-checked', 'mixed');
                }

                this.updateProbabilityDisplay();
                this.updateWaveFunction();
            }

            influenceFromEntangled(entangledState) {
                if (this.state !== 'superposition') return;
                
                // Quantum entanglement influence
                if (entangledState === 'on') {
                    this.probability.on = Math.min(0.8, this.probability.on + 0.2);
                    this.probability.off = 1 - this.probability.on;
                } else {
                    this.probability.off = Math.min(0.8, this.probability.off + 0.2);
                    this.probability.on = 1 - this.probability.off;
                }
                
                this.updateProbabilityDisplay();
                this.updateWaveFunction();
            }

            updateProbabilityDisplay() {
                const display = this.element.querySelector('.probability-display');
                if (this.state === 'superposition') {
                    display.textContent = `|ψ⟩ = √${this.probability.on.toFixed(2)}|1⟩ + √${this.probability.off.toFixed(2)}|0⟩`;
                } else if (this.state === 'on') {
                    display.textContent = '|ψ⟩ = |1⟩';
                } else {
                    display.textContent = '|ψ⟩ = |0⟩';
                }
            }

            resetToSuperposition() {
                this.state = 'superposition';
                this.probability = { on: 0.5, off: 0.5 };
                this.setState('superposition');
            }
        }

        // Initialize quantum toggles
        const toggles = [];
        const container = document.getElementById('quantumContainer');

        function initializeToggles() {
            const labels = ['Notifications', 'Dark Mode', 'Auto-Save'];
            
            labels.forEach((label, index) => {
                const toggle = new QuantumToggle(`quantum-${index}`, label);
                toggles.push(toggle);
                container.appendChild(toggle.createElement());
            });
        }

        function measureAll() {
            toggles.forEach(toggle => {
                if (toggle.state === 'superposition') {
                    setTimeout(() => toggle.measure(), Math.random() * 500);
                }
            });
        }

        function entangleToggles() {
            // Clear existing entanglements
            toggles.forEach(toggle => toggle.entangled = []);
            
            // Create entanglement between adjacent toggles
            for (let i = 0; i < toggles.length - 1; i++) {
                toggles[i].entangled.push(toggles[i + 1]);
                toggles[i + 1].entangled.push(toggles[i]);
                
                // Visual entanglement line
                const line = document.createElement('div');
                line.className = 'entanglement-line';
                line.style.width = '100px';
                line.style.left = `${150 + i * 230}px`;
                line.style.top = '100px';
                container.appendChild(line);
                
                setTimeout(() => line.remove(), 3000);
            }
        }

        function resetToSuperposition() {
            toggles.forEach(toggle => toggle.resetToSuperposition());
        }

        // Check for reduced motion preference
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        if (prefersReducedMotion) {
            document.documentElement.style.setProperty('--animation-duration', '0s');
        }

        // Initialize on load
        initializeToggles();
    </script>
</body>
</html>