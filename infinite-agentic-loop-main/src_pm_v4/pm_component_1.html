<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inteligentný Dokumentový Hub - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 600px;
        }

        .pm-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.3rem;
        }

        .filter-item:hover {
            background: #e2e8f0;
        }

        .filter-item.active {
            background: #4f46e5;
            color: white;
        }

        .filter-count {
            margin-left: auto;
            font-size: 0.8rem;
            background: #e2e8f0;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
        }

        .filter-item.active .filter-count {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .pm-data {
            padding: 1.5rem 2rem;
        }

        .search-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #4f46e5;
        }

        .ai-suggestions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .ai-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .suggestion-list {
            list-style: none;
        }

        .suggestion-item {
            padding: 0.3rem 0;
            color: #92400e;
            cursor: pointer;
        }

        .suggestion-item:hover {
            text-decoration: underline;
        }

        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .document-card {
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .document-card:hover {
            border-color: #4f46e5;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .doc-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .doc-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 1.1rem;
        }

        .pm-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-draft {
            background: #fef3c7;
            color: #92400e;
        }

        .status-review {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-outdated {
            background: #fee2e2;
            color: #991b1b;
        }

        .doc-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .doc-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .doc-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .document-card:hover .doc-actions {
            opacity: 1;
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            border-radius: 4px;
        }

        .btn-edit {
            background: #4f46e5;
            color: white;
        }

        .btn-version {
            background: #059669;
            color: white;
        }

        .btn-delete {
            background: #dc2626;
            color: white;
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .pm-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Inteligentný Dokumentový Hub - Projektový Manažment</h1>
        
        <div class="pm-component">
            <div class="pm-header">
                <h2>📁 Dokumentový Hub</h2>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="uploadDocument()">+ Nahrať Dokument</button>
                    <button class="btn btn-primary" onclick="generateTemplate()">🤖 Generovať Šablónu</button>
                </div>
            </div>
            
            <div class="pm-content">
                <div class="pm-sidebar">
                    <div class="sidebar-section">
                        <div class="sidebar-title">Kategórie Dokumentov</div>
                        <div class="filter-item active" data-category="all">
                            <span>Všetky dokumenty</span>
                            <span class="filter-count">24</span>
                        </div>
                        <div class="filter-item" data-category="plans">
                            <span>📋 Projektové plány</span>
                            <span class="filter-count">6</span>
                        </div>
                        <div class="filter-item" data-category="technical">
                            <span>⚙️ Technická dokumentácia</span>
                            <span class="filter-count">8</span>
                        </div>
                        <div class="filter-item" data-category="business">
                            <span>💼 Obchodná dokumentácia</span>
                            <span class="filter-count">5</span>
                        </div>
                        <div class="filter-item" data-category="process">
                            <span>🔄 Procesná dokumentácia</span>
                            <span class="filter-count">3</span>
                        </div>
                        <div class="filter-item" data-category="communication">
                            <span>💬 Komunikačná dokumentácia</span>
                            <span class="filter-count">2</span>
                        </div>
                    </div>
                    
                    <div class="sidebar-section">
                        <div class="sidebar-title">Stav Dokumentov</div>
                        <div class="filter-item" data-status="draft">
                            <span>📝 Návrh</span>
                            <span class="filter-count">8</span>
                        </div>
                        <div class="filter-item" data-status="review">
                            <span>👀 Na kontrole</span>
                            <span class="filter-count">6</span>
                        </div>
                        <div class="filter-item" data-status="approved">
                            <span>✅ Schválené</span>
                            <span class="filter-count">9</span>
                        </div>
                        <div class="filter-item" data-status="outdated">
                            <span>⚠️ Zastarané</span>
                            <span class="filter-count">1</span>
                        </div>
                    </div>
                </div>
                
                <div class="pm-data">
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="Vyhľadať dokumenty..." id="searchInput">
                        <button class="btn btn-primary" onclick="searchDocuments()">🔍 Hľadať</button>
                    </div>
                    
                    <div class="ai-suggestions">
                        <div class="ai-title">
                            🤖 AI Návrhy na Zlepšenie Dokumentácie
                        </div>
                        <ul class="suggestion-list">
                            <li class="suggestion-item">Chýba risk management plán pre projekt "E-shop Modernizácia"</li>
                            <li class="suggestion-item">Technická špecifikácia API potrebuje aktualizáciu verzií</li>
                            <li class="suggestion-item">Odporúčame vytvoriť testing checklist pre QA tím</li>
                        </ul>
                    </div>
                    
                    <div class="document-grid" id="documentGrid">
                        <!-- Dokumenty budú vygenerované JavaScriptom -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Simulované projektové dáta
        const projectDocuments = [
            {
                id: 1,
                title: "Projektový Plán - E-shop Modernizácia",
                category: "plans",
                status: "approved",
                lastModified: "2024-06-10",
                author: "Ján Novák",
                description: "Komplexný plán modernizácie e-shop platformy s časovým harmonogramom a míľnikmi."
            },
            {
                id: 2,
                title: "API Dokumentácia v3.2",
                category: "technical",
                status: "review",
                lastModified: "2024-06-12",
                author: "Mária Svobodová",
                description: "Technická dokumentácia REST API endpoints s príkladmi použitia."
            },
            {
                id: 3,
                title: "Obchodné Požiadavky - Mobile App",
                category: "business",
                status: "draft",
                lastModified: "2024-06-13",
                author: "Peter Kováč",
                description: "Detailné obchodné požiadavky pre vývoj mobilnej aplikácie."
            },
            {
                id: 4,
                title: "Testing Checklist",
                category: "process",
                status: "approved",
                lastModified: "2024-06-08",
                author: "Anna Horváthová",
                description: "Kontrolný zoznam pre testovanie funkcionalít pred produkčným nasadením."
            },
            {
                id: 5,
                title: "Meeting Notes - Sprint Planning",
                category: "communication",
                status: "approved",
                lastModified: "2024-06-11",
                author: "Tomáš Varga",
                description: "Zápisnica zo sprint planning meetingu s akčnými bodmi."
            },
            {
                id: 6,
                title: "Database Schema v2.1",
                category: "technical",
                status: "outdated",
                lastModified: "2024-05-15",
                author: "Lucia Nováková",
                description: "Schéma databázy - potrebuje aktualizáciu na najnovšiu verziu."
            }
        ];

        function initPMComponent() {
            renderDocuments(projectDocuments);
            setupEventListeners();
        }

        function renderDocuments(documents) {
            const grid = document.getElementById('documentGrid');
            grid.innerHTML = '';

            documents.forEach(doc => {
                const card = createDocumentCard(doc);
                grid.appendChild(card);
            });
        }

        function createDocumentCard(doc) {
            const card = document.createElement('div');
            card.className = 'document-card';
            card.innerHTML = `
                <div class="doc-header">
                    <div class="doc-title">${doc.title}</div>
                    <div class="pm-status status-${doc.status}">
                        ${getStatusText(doc.status)}
                    </div>
                </div>
                <div class="doc-meta">
                    <span>📅 ${doc.lastModified}</span>
                    <span>👤 ${doc.author}</span>
                </div>
                <div class="doc-description">${doc.description}</div>
                <div class="doc-actions">
                    <button class="btn btn-small btn-edit" onclick="editDocument(${doc.id})">Upraviť</button>
                    <button class="btn btn-small btn-version" onclick="viewVersions(${doc.id})">Verzie</button>
                    <button class="btn btn-small btn-delete" onclick="deleteDocument(${doc.id})">Zmazať</button>
                </div>
            `;
            return card;
        }

        function getStatusText(status) {
            const statusMap = {
                'draft': 'Návrh',
                'review': 'Na kontrole',
                'approved': 'Schválené',
                'outdated': 'Zastarané'
            };
            return statusMap[status] || status;
        }

        function setupEventListeners() {
            // Filter listeners
            document.querySelectorAll('.filter-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.filter-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    filterDocuments();
                });
            });

            // Search listener
            document.getElementById('searchInput').addEventListener('input', searchDocuments);
        }

        function filterDocuments() {
            const activeFilter = document.querySelector('.filter-item.active');
            const category = activeFilter.dataset.category;
            const status = activeFilter.dataset.status;
            
            let filtered = projectDocuments;
            
            if (category && category !== 'all') {
                filtered = filtered.filter(doc => doc.category === category);
            }
            
            if (status) {
                filtered = filtered.filter(doc => doc.status === status);
            }
            
            renderDocuments(filtered);
        }

        function searchDocuments() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const filtered = projectDocuments.filter(doc => 
                doc.title.toLowerCase().includes(query) ||
                doc.description.toLowerCase().includes(query) ||
                doc.author.toLowerCase().includes(query)
            );
            renderDocuments(filtered);
        }

        function uploadDocument() {
            alert('Funkcia nahrávania dokumentu - implementácia v produkčnej verzii');
        }

        function generateTemplate() {
            alert('AI generovanie šablóny na základe existujúcich dokumentov - implementácia v produkčnej verzii');
        }

        function editDocument(id) {
            alert(`Úprava dokumentu ID: ${id} - implementácia v produkčnej verzii`);
        }

        function viewVersions(id) {
            alert(`Zobrazenie verzií dokumentu ID: ${id} - implementácia v produkčnej verzii`);
        }

        function deleteDocument(id) {
            if (confirm('Naozaj chcete zmazať tento dokument?')) {
                alert(`Zmazanie dokumentu ID: ${id} - implementácia v produkčnej verzii`);
            }
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
