<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Project Assistant & Automation Hub - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #581c87 0%, #7c3aed 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #7c3aed 0%, #581c87 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .ai-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 400px 1fr 350px;
            min-height: 700px;
        }

        .ai-chat-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .ai-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .ai-status {
            text-align: center;
            margin-bottom: 2rem;
        }

        .status-text {
            color: #10b981;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .status-description {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            max-height: 400px;
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            gap: 0.75rem;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .ai-message-avatar {
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white;
        }

        .user-message-avatar {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
        }

        .message-content {
            background: #f8fafc;
            padding: 0.75rem 1rem;
            border-radius: 12px;
            max-width: 80%;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #7c3aed;
            color: white;
        }

        .message-time {
            font-size: 0.7rem;
            color: #9ca3af;
            margin-top: 0.25rem;
        }

        .chat-input {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 0.5rem;
        }

        .input-field {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            border-color: #7c3aed;
        }

        .send-btn {
            background: #7c3aed;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #6d28d9;
            transform: scale(1.05);
        }

        .main-automation {
            padding: 2rem;
            overflow-y: auto;
        }

        .automation-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-bottom-color: #7c3aed;
            color: #7c3aed;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .automation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .automation-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .automation-card:hover {
            border-color: #7c3aed;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .automation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .automation-title {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .automation-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active { background: #d1fae5; color: #065f46; }
        .status-inactive { background: #fee2e2; color: #991b1b; }
        .status-scheduled { background: #dbeafe; color: #1e40af; }

        .automation-description {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .automation-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
        }

        .metric-value {
            font-weight: 700;
            color: #7c3aed;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.7rem;
            color: #6b7280;
        }

        .automation-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            border-color: #7c3aed;
            color: #7c3aed;
        }

        .action-btn.primary {
            background: #7c3aed;
            color: white;
            border-color: #7c3aed;
        }

        .action-btn.primary:hover {
            background: #6d28d9;
        }

        .insights-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .ai-insights {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .insights-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .insight-item {
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .insight-item:hover {
            background: rgba(255,255,255,0.95);
            transform: translateX(3px);
        }

        .insight-text {
            color: #92400e;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quick-actions {
            margin-bottom: 2rem;
        }

        .quick-action-btn {
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quick-action-btn:hover {
            border-color: #7c3aed;
            background: #f3f4f6;
        }

        .automation-stats {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .stats-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #7c3aed;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 350px 1fr;
            }
            
            .insights-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .ai-chat-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>AI Project Assistant & Automation Hub - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>🤖 AI Project Assistant & Automation Hub</h2>
                <div class="ai-indicator">
                    🧠 AI Status: Active
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="createAutomation()">+ Nová Automatizácia</button>
                    <button class="btn btn-primary" onclick="aiAnalysis()">🔍 AI Analýza</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="ai-chat-sidebar">
                    <div class="sidebar-title">🤖 AI Asistent</div>

                    <div class="ai-avatar">🤖</div>

                    <div class="ai-status">
                        <div class="status-text">AI Asistent Online</div>
                        <div class="status-description">Pripravený pomôcť s projektovým manažmentom</div>
                    </div>

                    <div class="chat-container">
                        <div class="chat-messages" id="chatMessages">
                            <div class="message">
                                <div class="message-avatar ai-message-avatar">AI</div>
                                <div class="message-content">
                                    Dobrý deň! Som váš AI Project Assistant. Môžem vám pomôcť s:
                                    <br><br>
                                    • Analýzou projektového pokroku<br>
                                    • Optimalizáciou procesov<br>
                                    • Predikciou rizík<br>
                                    • Automatizáciou úloh<br>
                                    • Generovaním reportov
                                    <div class="message-time">Dnes 14:30</div>
                                </div>
                            </div>

                            <div class="message user">
                                <div class="message-avatar user-message-avatar">JN</div>
                                <div class="message-content">
                                    Aký je aktuálny stav projektu E-shop Modernizácia?
                                    <div class="message-time">Dnes 14:32</div>
                                </div>
                            </div>

                            <div class="message">
                                <div class="message-avatar ai-message-avatar">AI</div>
                                <div class="message-content">
                                    Projekt E-shop Modernizácia je na 65% dokončený:
                                    <br><br>
                                    ✅ Dokončené: Analýza, UI/UX dizajn<br>
                                    🔄 V priebehu: Backend (75%), Frontend (45%)<br>
                                    ⏳ Plánované: Testing, Deployment<br>
                                    <br>
                                    Odporúčam zamerať sa na frontend development pre dodržanie termínov.
                                    <div class="message-time">Dnes 14:33</div>
                                </div>
                            </div>
                        </div>

                        <div class="chat-input">
                            <input type="text" class="input-field" id="chatInput" placeholder="Napíšte správu AI asistentovi...">
                            <button class="send-btn" onclick="sendMessage()">📤</button>
                        </div>
                    </div>
                </div>

                <div class="main-automation">
                    <div class="automation-tabs">
                        <button class="tab-btn active" data-tab="workflows">🔄 Workflows</button>
                        <button class="tab-btn" data-tab="triggers">⚡ Triggery</button>
                        <button class="tab-btn" data-tab="integrations">🔗 Integrácie</button>
                        <button class="tab-btn" data-tab="ai-rules">🧠 AI Pravidlá</button>
                    </div>

                    <!-- Workflows Tab -->
                    <div class="tab-content active" id="workflows">
                        <div class="automation-grid">
                            <div class="automation-card">
                                <div class="automation-header">
                                    <div class="automation-title">
                                        📊 Automatické Status Reporty
                                    </div>
                                    <div class="automation-status status-active">Aktívne</div>
                                </div>
                                <div class="automation-description">
                                    Automatické generovanie a odosielanie týždenných status reportov stakeholderom
                                </div>
                                <div class="automation-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">24</div>
                                        <div class="metric-label">Reporty</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">100%</div>
                                        <div class="metric-label">Úspešnosť</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">5h</div>
                                        <div class="metric-label">Ušetrené</div>
                                    </div>
                                </div>
                                <div class="automation-actions">
                                    <button class="action-btn" onclick="editAutomation('reports')">Upraviť</button>
                                    <button class="action-btn" onclick="viewAutomationLogs('reports')">Logy</button>
                                    <button class="action-btn primary" onclick="runAutomation('reports')">Spustiť</button>
                                </div>
                            </div>

                            <div class="automation-card">
                                <div class="automation-header">
                                    <div class="automation-title">
                                        🎯 Smart Task Assignment
                                    </div>
                                    <div class="automation-status status-active">Aktívne</div>
                                </div>
                                <div class="automation-description">
                                    AI-powered automatické priraďovanie úloh na základe skillsetu a dostupnosti tímu
                                </div>
                                <div class="automation-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">156</div>
                                        <div class="metric-label">Úlohy</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">94%</div>
                                        <div class="metric-label">Presnosť</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">12h</div>
                                        <div class="metric-label">Ušetrené</div>
                                    </div>
                                </div>
                                <div class="automation-actions">
                                    <button class="action-btn" onclick="editAutomation('assignment')">Upraviť</button>
                                    <button class="action-btn" onclick="viewAutomationLogs('assignment')">Logy</button>
                                    <button class="action-btn primary" onclick="runAutomation('assignment')">Spustiť</button>
                                </div>
                            </div>

                            <div class="automation-card">
                                <div class="automation-header">
                                    <div class="automation-title">
                                        ⚠️ Risk Detection & Alerts
                                    </div>
                                    <div class="automation-status status-active">Aktívne</div>
                                </div>
                                <div class="automation-description">
                                    Kontinuálne monitorovanie projektových rizík s automatickými upozorneniami
                                </div>
                                <div class="automation-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">8</div>
                                        <div class="metric-label">Riziká</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">87%</div>
                                        <div class="metric-label">Predikcia</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">3</div>
                                        <div class="metric-label">Predídené</div>
                                    </div>
                                </div>
                                <div class="automation-actions">
                                    <button class="action-btn" onclick="editAutomation('risk')">Upraviť</button>
                                    <button class="action-btn" onclick="viewAutomationLogs('risk')">Logy</button>
                                    <button class="action-btn primary" onclick="runAutomation('risk')">Spustiť</button>
                                </div>
                            </div>

                            <div class="automation-card">
                                <div class="automation-header">
                                    <div class="automation-title">
                                        📅 Meeting Scheduler
                                    </div>
                                    <div class="automation-status status-scheduled">Naplánované</div>
                                </div>
                                <div class="automation-description">
                                    Automatické plánovanie meetingov na základe dostupnosti a priority
                                </div>
                                <div class="automation-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">42</div>
                                        <div class="metric-label">Meetingy</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">96%</div>
                                        <div class="metric-label">Účasť</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">8h</div>
                                        <div class="metric-label">Ušetrené</div>
                                    </div>
                                </div>
                                <div class="automation-actions">
                                    <button class="action-btn" onclick="editAutomation('meetings')">Upraviť</button>
                                    <button class="action-btn" onclick="viewAutomationLogs('meetings')">Logy</button>
                                    <button class="action-btn primary" onclick="runAutomation('meetings')">Spustiť</button>
                                </div>
                            </div>

                            <div class="automation-card">
                                <div class="automation-header">
                                    <div class="automation-title">
                                        💰 Budget Monitoring
                                    </div>
                                    <div class="automation-status status-inactive">Neaktívne</div>
                                </div>
                                <div class="automation-description">
                                    Automatické sledovanie rozpočtu s upozorneniami pri prekročení limitov
                                </div>
                                <div class="automation-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">0</div>
                                        <div class="metric-label">Kontroly</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">-</div>
                                        <div class="metric-label">Úspešnosť</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">0h</div>
                                        <div class="metric-label">Ušetrené</div>
                                    </div>
                                </div>
                                <div class="automation-actions">
                                    <button class="action-btn" onclick="editAutomation('budget')">Upraviť</button>
                                    <button class="action-btn" onclick="viewAutomationLogs('budget')">Logy</button>
                                    <button class="action-btn primary" onclick="activateAutomation('budget')">Aktivovať</button>
                                </div>
                            </div>

                            <div class="automation-card">
                                <div class="automation-header">
                                    <div class="automation-title">
                                        🚀 Deployment Pipeline
                                    </div>
                                    <div class="automation-status status-active">Aktívne</div>
                                </div>
                                <div class="automation-description">
                                    Automatické CI/CD pipeline s testovaním a deploymentom
                                </div>
                                <div class="automation-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">89</div>
                                        <div class="metric-label">Deploymenty</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">98%</div>
                                        <div class="metric-label">Úspešnosť</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">25h</div>
                                        <div class="metric-label">Ušetrené</div>
                                    </div>
                                </div>
                                <div class="automation-actions">
                                    <button class="action-btn" onclick="editAutomation('deployment')">Upraviť</button>
                                    <button class="action-btn" onclick="viewAutomationLogs('deployment')">Logy</button>
                                    <button class="action-btn primary" onclick="runAutomation('deployment')">Spustiť</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other tabs content would be here -->
                    <div class="tab-content" id="triggers">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>⚡ Automatické Triggery</h3>
                            <p>Konfigurácia triggerov pre automatické spúšťanie procesov</p>
                        </div>
                    </div>

                    <div class="tab-content" id="integrations">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>🔗 Externé Integrácie</h3>
                            <p>Prepojenie s externými nástrojmi a službami</p>
                        </div>
                    </div>

                    <div class="tab-content" id="ai-rules">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>🧠 AI Pravidlá a Logika</h3>
                            <p>Konfigurácia AI algoritmov a rozhodovacích pravidiel</p>
                        </div>
                    </div>
                </div>

                <div class="insights-sidebar">
                    <div class="sidebar-title">💡 AI Insights</div>

                    <div class="ai-insights">
                        <div class="insights-title">
                            🧠 Inteligentné Odporúčania
                        </div>

                        <div class="insight-item" onclick="applyInsight(1)">
                            <div class="insight-text">
                                Optimalizovať task assignment algoritmus môže zvýšiť efektívnosť o 15%
                            </div>
                        </div>

                        <div class="insight-item" onclick="applyInsight(2)">
                            <div class="insight-text">
                                Aktivovať budget monitoring pre predchádzanie prekročeniu rozpočtu
                            </div>
                        </div>

                        <div class="insight-item" onclick="applyInsight(3)">
                            <div class="insight-text">
                                Implementovať automatické code review môže znížiť bugy o 23%
                            </div>
                        </div>

                        <div class="insight-item" onclick="applyInsight(4)">
                            <div class="insight-text">
                                Pridať performance monitoring pre včasné odhalenie problémov
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">⚡ Rýchle Akcie</div>

                    <div class="quick-actions">
                        <button class="quick-action-btn" onclick="quickAction('optimize')">
                            🎯 Optimalizovať Všetky Procesy
                        </button>
                        <button class="quick-action-btn" onclick="quickAction('analyze')">
                            📊 Spustiť AI Analýzu
                        </button>
                        <button class="quick-action-btn" onclick="quickAction('predict')">
                            🔮 Predikcia Rizík
                        </button>
                        <button class="quick-action-btn" onclick="quickAction('automate')">
                            🤖 Navrhnúť Automatizácie
                        </button>
                        <button class="quick-action-btn" onclick="quickAction('report')">
                            📋 Generovať AI Report
                        </button>
                    </div>

                    <div class="sidebar-title">📊 Automation Stats</div>

                    <div class="automation-stats">
                        <div class="stats-title">Celková Efektívnosť</div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">6</div>
                                <div class="stat-label">Aktívne Automatizácie</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">53h</div>
                                <div class="stat-label">Ušetrený Čas</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">94%</div>
                                <div class="stat-label">Úspešnosť</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">€8,400</div>
                                <div class="stat-label">Úspory</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            setupTabs();
            setupChatInput();
            startAIMonitoring();
        }

        function setupTabs() {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                    this.classList.add('active');
                    document.getElementById(this.dataset.tab).classList.add('active');
                });
            });
        }

        function setupChatInput() {
            const input = document.getElementById('chatInput');
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }

        function startAIMonitoring() {
            // Simulácia AI monitoring
            setInterval(() => {
                updateAIInsights();
            }, 60000); // Každú minútu
        }

        function updateAIInsights() {
            console.log('AI insights aktualizované:', new Date().toLocaleTimeString());
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) return;

            // Pridať user message
            addMessage(message, 'user');
            input.value = '';

            // Simulovať AI odpoveď
            setTimeout(() => {
                const aiResponse = generateAIResponse(message);
                addMessage(aiResponse, 'ai');
            }, 1000);
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const time = new Date().toLocaleTimeString('sk-SK', { hour: '2-digit', minute: '2-digit' });

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const avatar = sender === 'ai' ? 'AI' : 'JN';
            const avatarClass = sender === 'ai' ? 'ai-message-avatar' : 'user-message-avatar';

            messageDiv.innerHTML = `
                <div class="message-avatar ${avatarClass}">${avatar}</div>
                <div class="message-content">
                    ${text}
                    <div class="message-time">Dnes ${time}</div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function generateAIResponse(userMessage) {
            const responses = {
                'pokrok': 'Projekt je na 65% dokončený. Backend development pokračuje dobre, frontend potrebuje viac pozornosti.',
                'rizika': 'Identifikoval som 3 potenciálne riziká: API integrácia, resource availability a timeline pressure.',
                'tím': 'Tím pracuje efektívne. Peter Kováč je momentálne preťažený, odporúčam redistribúciu úloh.',
                'rozpočet': 'Rozpočet je využitý na 78%. Zostáva €10,800, čo je v rámci plánu.',
                'automatizácia': 'Môžem navrhnúť automatizáciu pre status reporty, task assignment a risk monitoring.'
            };

            const lowerMessage = userMessage.toLowerCase();

            for (const [key, response] of Object.entries(responses)) {
                if (lowerMessage.includes(key)) {
                    return response;
                }
            }

            return 'Rozumiem vašej otázke. Môžete byť konkrétnejší? Môžem pomôcť s analýzou pokroku, rizík, tímu, rozpočtu alebo automatizáciou procesov.';
        }

        function createAutomation() {
            alert('🤖 Vytvorenie Novej Automatizácie\n\nDostupné typy:\n• Workflow automatizácia\n• Trigger-based akcie\n• AI-powered rozhodovanie\n• Integrácie s externými nástrojmi\n• Scheduled úlohy\n\nVyberte typ automatizácie a definujte pravidlá.\nAI asistent vám pomôže s optimálnou konfiguráciou.');
        }

        function aiAnalysis() {
            alert('🔍 Spúšťam Komplexnú AI Analýzu...\n\n• Project health assessment: ✓\n• Risk prediction modeling: ✓\n• Resource optimization analysis: ✓\n• Performance trend analysis: ✓\n• Automation opportunities: ✓\n\nKľúčové zistenia:\n📈 Projekt je na dobrej ceste\n⚠️ 3 potenciálne riziká identifikované\n🎯 5 možností optimalizácie\n🤖 8 návrhov na automatizáciu\n\nDetailný AI report bude vygenerovaný za 2 minúty.');
        }

        function editAutomation(type) {
            alert(`✏️ Úprava Automatizácie: ${type}\n\nMôžete upraviť:\n• Trigger podmienky\n• Akcie a kroky\n• Notifikácie\n• Časovanie\n• AI parametre\n\nEditor automatizácie sa otvorí v novom okne.`);
        }

        function viewAutomationLogs(type) {
            alert(`📋 Logy Automatizácie: ${type}\n\nPosledných 50 spustení:\n• Úspešné: 47\n• Neúspešné: 3\n• Priemerné trvanie: 2.3s\n• Posledné spustenie: Pred 15 min\n\nDetailné logy dostupné na stiahnutie.`);
        }

        function runAutomation(type) {
            alert(`🚀 Spúšťam Automatizáciu: ${type}\n\nAutomatizácia bola spustená manuálne.\nMonitorovanie v real-time...\nNotifikácia po dokončení: ✓`);
        }

        function activateAutomation(type) {
            alert(`✅ Aktivácia Automatizácie: ${type}\n\nAutomatizácia bola úspešne aktivovaná.\nMonitorovanie spustené.\nPrvé spustenie: Za 5 minút`);
        }

        function applyInsight(id) {
            const insights = {
                1: 'Optimalizácia task assignment algoritmu aplikovaná. Očakávané zlepšenie efektívnosti o 15%.',
                2: 'Budget monitoring aktivovaný. Automatické upozornenia nastavené na 80% a 95% využitia.',
                3: 'Automatické code review pravidlá implementované. Očakávané zníženie bugov o 23%.',
                4: 'Performance monitoring pridaný do CI/CD pipeline. Real-time alerting aktivovaný.'
            };

            alert(`💡 AI Insight Aplikovaný\n\n${insights[id]}\n\nZmeny budú aktívne do 5 minút.\nMonitorovanie výsledkov spustené.`);
        }

        function quickAction(action) {
            const actions = {
                'optimize': '🎯 Optimalizácia všetkých procesov spustená. Analýza 15 komponentov...',
                'analyze': '📊 AI analýza v priebehu. Spracovávam 1,247 dátových bodov...',
                'predict': '🔮 Risk prediction model spustený. Identifikujem potenciálne problémy...',
                'automate': '🤖 Hľadám možnosti automatizácie. Analyzujem workflow patterns...',
                'report': '📋 Generujem AI report. Komprimujem insights z posledných 30 dní...'
            };

            alert(`⚡ Rýchla Akcia\n\n${actions[action]}\n\nVýsledky budú dostupné za 2-5 minút.\nNotifikácia po dokončení: ✓`);
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
