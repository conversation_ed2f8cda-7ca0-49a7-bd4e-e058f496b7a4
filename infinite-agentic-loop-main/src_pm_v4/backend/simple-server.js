/**
 * PM Master System v4 - Simple Mock Server
 * Jednoduchý server pre demonštr<PERSON>ciu bez závislostí
 */

const http = require('http');
const url = require('url');

const PORT = 3001;

// Sample data
const sampleData = {
    projects: [
        {
            id: 1,
            name: 'E-shop Modernizácia',
            description: 'Komplexná modernizácia e-shop platformy',
            status: 'active',
            priority: 'high',
            progress: 67,
            budget: 150000,
            teamId: 1
        },
        {
            id: 2,
            name: 'Mobile App Development',
            description: 'Vývoj mobilnej aplikácie pre iOS a Android',
            status: 'planning',
            priority: 'medium',
            progress: 15,
            budget: 80000,
            teamId: 2
        }
    ],
    tasks: [
        {
            id: 1,
            projectId: 1,
            name: 'Frontend komponenty design',
            description: 'Návrh a implementácia React komponentov',
            status: 'in-progress',
            priority: 'high',
            assigneeId: 1,
            estimatedHours: 40,
            actualHours: 25
        }
    ],
    teams: [
        {
            id: 1,
            name: 'Frontend Development Team',
            description: '<PERSON><PERSON><PERSON> na frontend development',
            leaderId: 1,
            members: [1, 2, 3],
            capacity: 90
        }
    ],
    analytics: {
        overview: {
            totalProjects: 12,
            activeProjects: 8,
            completedProjects: 4,
            teamEfficiency: 87
        }
    }
};

// Helper functions
function sendJSON(res, data, status = 200) {
    res.writeHead(status, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end(JSON.stringify({ success: true, data }));
}

function sendError(res, message, status = 500) {
    res.writeHead(status, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
    });
    res.end(JSON.stringify({ success: false, error: message }));
}

// Request handler
function handleRequest(req, res) {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    console.log(`${new Date().toISOString()} - ${method} ${path}`);

    // Handle CORS preflight
    if (method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end();
        return;
    }

    // Health check
    if (path === '/api/v4/health') {
        sendJSON(res, {
            status: 'healthy',
            version: '4.0.0',
            timestamp: new Date(),
            uptime: process.uptime()
        });
        return;
    }

    // Projects API
    if (path === '/api/v4/projects') {
        if (method === 'GET') {
            sendJSON(res, sampleData.projects);
            return;
        }
    }

    // Single project
    if (path.match(/^\/api\/v4\/projects\/\d+$/)) {
        const projectId = parseInt(path.split('/').pop());
        const project = sampleData.projects.find(p => p.id === projectId);
        
        if (project) {
            sendJSON(res, project);
        } else {
            sendError(res, 'Project not found', 404);
        }
        return;
    }

    // Project health
    if (path.match(/^\/api\/v4\/projects\/\d+\/health$/)) {
        sendJSON(res, {
            overallHealth: 'good',
            score: 87,
            metrics: {
                schedule: { status: 'on-track', variance: 2 },
                budget: { status: 'under-budget', variance: -5 },
                quality: { status: 'excellent', score: 94 }
            }
        });
        return;
    }

    // Tasks API
    if (path === '/api/v4/tasks') {
        sendJSON(res, sampleData.tasks);
        return;
    }

    // Teams API
    if (path === '/api/v4/teams') {
        sendJSON(res, sampleData.teams);
        return;
    }

    // Analytics API
    if (path.startsWith('/api/v4/analytics')) {
        sendJSON(res, sampleData.analytics);
        return;
    }

    // AI API
    if (path === '/api/v4/ai/chat') {
        if (method === 'POST') {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });
            req.on('end', () => {
                try {
                    const data = JSON.parse(body);
                    const response = {
                        response: `Ďakujem za vašu otázku: "${data.message}". Som AI asistent pre projektový manažment a môžem vám pomôcť s analýzou projektov.`,
                        confidence: 0.95,
                        suggestions: [
                            'Analyzovať výkonnosť projektu',
                            'Predpovedať riziká',
                            'Optimalizovať zdroje'
                        ],
                        timestamp: new Date()
                    };
                    sendJSON(res, response);
                } catch (error) {
                    sendError(res, 'Invalid JSON', 400);
                }
            });
            return;
        }
    }

    // Default 404
    sendError(res, 'Endpoint not found', 404);
}

// Create server
const server = http.createServer(handleRequest);

server.listen(PORT, () => {
    console.log(`🚀 PM Master System v4 Simple API Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/v4/health`);
    console.log(`📚 API Base URL: http://localhost:${PORT}/api/v4`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Shutting down server gracefully...');
    server.close(() => {
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Shutting down server gracefully...');
    server.close(() => {
        process.exit(0);
    });
});
