<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Budget Tracker & Financial Analytics - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #92400e 0%, #d97706 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #d97706 0%, #92400e 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .budget-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
            padding: 2rem;
        }

        .budget-overview {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .budget-card {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            border: 1px solid #f59e0b;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .budget-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .budget-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        .budget-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .budget-amount {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #92400e;
        }

        .budget-label {
            color: #a16207;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .budget-progress {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.5);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-safe { background: #10b981; }
        .progress-warning { background: #f59e0b; }
        .progress-danger { background: #ef4444; }

        .budget-percentage {
            font-size: 0.9rem;
            font-weight: 600;
            color: #92400e;
        }

        .expense-breakdown {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .breakdown-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .expense-chart {
            height: 300px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-bottom: 1.5rem;
        }

        .chart-container {
            position: relative;
            width: 200px;
            height: 200px;
        }

        .donut-chart {
            transform: rotate(-90deg);
        }

        .donut-segment {
            fill: none;
            stroke-width: 30;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .donut-segment:hover {
            stroke-width: 35;
        }

        .chart-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .center-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }

        .center-label {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .expense-legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .legend-info {
            flex: 1;
        }

        .legend-label {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .legend-amount {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .financial-analytics {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .analytics-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .analytics-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .analytics-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .analytics-label {
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .analytics-trend {
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .trend-up { color: #ef4444; }
        .trend-down { color: #10b981; }
        .trend-stable { color: #6b7280; }

        .spending-timeline {
            height: 200px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: end;
            justify-content: space-around;
            padding: 1rem;
        }

        .timeline-bar {
            background: linear-gradient(to top, #d97706, #f59e0b);
            border-radius: 4px 4px 0 0;
            min-width: 20px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .timeline-bar:hover {
            background: linear-gradient(to top, #92400e, #d97706);
            transform: scaleY(1.05);
        }

        .bar-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            color: #6b7280;
            white-space: nowrap;
        }

        .bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            font-weight: 600;
            color: #1f2937;
        }

        .forecast-section {
            grid-column: 1 / -1;
            background: linear-gradient(135dg, #ecfdf5 0%, #d1fae5 100%);
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 2rem;
        }

        .forecast-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #065f46;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .forecast-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .forecast-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #a7f3d0;
        }

        .forecast-period {
            font-weight: 600;
            color: #047857;
            margin-bottom: 1rem;
        }

        .forecast-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .forecast-metric {
            text-align: center;
        }

        .forecast-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 0.25rem;
        }

        .forecast-label {
            font-size: 0.8rem;
            color: #047857;
        }

        .ai-insights {
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border-left: 4px solid #10b981;
        }

        .insights-title {
            font-weight: 600;
            color: #065f46;
            margin-bottom: 0.5rem;
        }

        .insights-text {
            color: #047857;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        @media (max-width: 1024px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .budget-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Budget Tracker & Financial Analytics - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>💰 Budget Tracker & Financial Analytics</h2>
                <div class="budget-indicator">
                    📊 Rozpočet Využitý: 78%
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="addExpense()">+ Pridať Výdavok</button>
                    <button class="btn btn-primary" onclick="generateFinancialReport()">📊 Finančný Report</button>
                </div>
            </div>

            <div class="pm-content">
                <!-- Budget Overview -->
                <div class="budget-overview">
                    <div class="budget-card">
                        <div class="budget-icon">💰</div>
                        <div class="budget-amount">€50,000</div>
                        <div class="budget-label">Celkový Rozpočet</div>
                        <div class="budget-progress">
                            <div class="progress-fill progress-warning" style="width: 78%;"></div>
                        </div>
                        <div class="budget-percentage">78% využité</div>
                    </div>

                    <div class="budget-card">
                        <div class="budget-icon">💸</div>
                        <div class="budget-amount">€39,200</div>
                        <div class="budget-label">Spotrebované</div>
                        <div class="budget-progress">
                            <div class="progress-fill progress-warning" style="width: 78%;"></div>
                        </div>
                        <div class="budget-percentage">+€2,400 tento týždeň</div>
                    </div>

                    <div class="budget-card">
                        <div class="budget-icon">💵</div>
                        <div class="budget-amount">€10,800</div>
                        <div class="budget-label">Zostáva</div>
                        <div class="budget-progress">
                            <div class="progress-fill progress-safe" style="width: 22%;"></div>
                        </div>
                        <div class="budget-percentage">22% rezerva</div>
                    </div>

                    <div class="budget-card">
                        <div class="budget-icon">📈</div>
                        <div class="budget-amount">€42,500</div>
                        <div class="budget-label">Predpokladané Náklady</div>
                        <div class="budget-progress">
                            <div class="progress-fill progress-safe" style="width: 85%;"></div>
                        </div>
                        <div class="budget-percentage">85% z rozpočtu</div>
                    </div>
                </div>

                <!-- Expense Breakdown -->
                <div class="expense-breakdown">
                    <div class="breakdown-title">
                        📊 Rozdelenie Výdavkov
                    </div>

                    <div class="expense-chart">
                        <div class="chart-container">
                            <svg class="donut-chart" width="200" height="200">
                                <circle class="donut-segment" cx="100" cy="100" r="80"
                                        stroke="#3b82f6" stroke-dasharray="125 314" stroke-dashoffset="0"></circle>
                                <circle class="donut-segment" cx="100" cy="100" r="80"
                                        stroke="#10b981" stroke-dasharray="94 314" stroke-dashoffset="-125"></circle>
                                <circle class="donut-segment" cx="100" cy="100" r="80"
                                        stroke="#f59e0b" stroke-dasharray="63 314" stroke-dashoffset="-219"></circle>
                                <circle class="donut-segment" cx="100" cy="100" r="80"
                                        stroke="#ef4444" stroke-dasharray="32 314" stroke-dashoffset="-282"></circle>
                            </svg>
                            <div class="chart-center">
                                <div class="center-amount">€39,200</div>
                                <div class="center-label">Celkom</div>
                            </div>
                        </div>
                    </div>

                    <div class="expense-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #3b82f6;"></div>
                            <div class="legend-info">
                                <div class="legend-label">Ľudské Zdroje</div>
                                <div class="legend-amount">€20,000 (51%)</div>
                            </div>
                        </div>

                        <div class="legend-item">
                            <div class="legend-color" style="background: #10b981;"></div>
                            <div class="legend-info">
                                <div class="legend-label">Technológie</div>
                                <div class="legend-amount">€15,000 (38%)</div>
                            </div>
                        </div>

                        <div class="legend-item">
                            <div class="legend-color" style="background: #f59e0b;"></div>
                            <div class="legend-info">
                                <div class="legend-label">Marketing</div>
                                <div class="legend-amount">€3,200 (8%)</div>
                            </div>
                        </div>

                        <div class="legend-item">
                            <div class="legend-color" style="background: #ef4444;"></div>
                            <div class="legend-info">
                                <div class="legend-label">Ostatné</div>
                                <div class="legend-amount">€1,000 (3%)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Analytics -->
                <div class="financial-analytics">
                    <div class="analytics-title">
                        📈 Finančná Analytika
                    </div>

                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <div class="analytics-value" style="color: #ef4444;">€3,250</div>
                            <div class="analytics-label">Týždenné Výdavky</div>
                            <div class="analytics-trend trend-up">
                                ↗ +15% vs minulý týždeň
                            </div>
                        </div>

                        <div class="analytics-card">
                            <div class="analytics-value" style="color: #10b981;">€1,950</div>
                            <div class="analytics-label">Priemerné Týždenné</div>
                            <div class="analytics-trend trend-down">
                                ↘ -8% vs plán
                            </div>
                        </div>

                        <div class="analytics-card">
                            <div class="analytics-value" style="color: #f59e0b;">€125</div>
                            <div class="analytics-label">Najväčší Výdavok</div>
                            <div class="analytics-trend trend-stable">
                                → Freelancer payment
                            </div>
                        </div>

                        <div class="analytics-card">
                            <div class="analytics-value" style="color: #3b82f6;">4.2</div>
                            <div class="analytics-label">Burn Rate (týždne)</div>
                            <div class="analytics-trend trend-down">
                                ↘ Zlepšenie o 0.8 týždňa
                            </div>
                        </div>
                    </div>

                    <div class="spending-timeline">
                        <div class="timeline-bar" style="height: 60%;">
                            <div class="bar-value">€2,400</div>
                            <div class="bar-label">T21</div>
                        </div>
                        <div class="timeline-bar" style="height: 80%;">
                            <div class="bar-value">€3,200</div>
                            <div class="bar-label">T22</div>
                        </div>
                        <div class="timeline-bar" style="height: 45%;">
                            <div class="bar-value">€1,800</div>
                            <div class="bar-label">T23</div>
                        </div>
                        <div class="timeline-bar" style="height: 90%;">
                            <div class="bar-value">€3,600</div>
                            <div class="bar-label">T24</div>
                        </div>
                        <div class="timeline-bar" style="height: 75%;">
                            <div class="bar-value">€3,000</div>
                            <div class="bar-label">T25</div>
                        </div>
                        <div class="timeline-bar" style="height: 85%;">
                            <div class="bar-value">€3,400</div>
                            <div class="bar-label">T26</div>
                        </div>
                        <div class="timeline-bar" style="height: 70%;">
                            <div class="bar-value">€2,800</div>
                            <div class="bar-label">T27</div>
                        </div>
                        <div class="timeline-bar" style="height: 95%;">
                            <div class="bar-value">€3,800</div>
                            <div class="bar-label">T28</div>
                        </div>
                    </div>
                </div>

                <!-- AI Forecast Section -->
                <div class="forecast-section">
                    <div class="forecast-title">
                        🤖 AI Finančný Forecast
                    </div>

                    <div class="forecast-grid">
                        <div class="forecast-card">
                            <div class="forecast-period">Nasledujúci Mesiac</div>
                            <div class="forecast-metrics">
                                <div class="forecast-metric">
                                    <div class="forecast-value">€12,500</div>
                                    <div class="forecast-label">Predpokladané Výdavky</div>
                                </div>
                                <div class="forecast-metric">
                                    <div class="forecast-value">89%</div>
                                    <div class="forecast-label">Presnosť Predikcie</div>
                                </div>
                            </div>
                            <div class="ai-insights">
                                <div class="insights-title">💡 AI Insights</div>
                                <div class="insights-text">
                                    Očakáva sa zvýšenie výdavkov o 15% kvôli dodatočným testovacím aktivitám.
                                </div>
                            </div>
                        </div>

                        <div class="forecast-card">
                            <div class="forecast-period">Koniec Projektu</div>
                            <div class="forecast-metrics">
                                <div class="forecast-metric">
                                    <div class="forecast-value">€47,800</div>
                                    <div class="forecast-label">Celkové Náklady</div>
                                </div>
                                <div class="forecast-metric">
                                    <div class="forecast-value">€2,200</div>
                                    <div class="forecast-label">Úspora</div>
                                </div>
                            </div>
                            <div class="ai-insights">
                                <div class="insights-title">📊 Prognóza</div>
                                <div class="insights-text">
                                    Projekt skončí 4.4% pod rozpočtom pri dodržaní aktuálneho tempa.
                                </div>
                            </div>
                        </div>

                        <div class="forecast-card">
                            <div class="forecast-period">Risk Analýza</div>
                            <div class="forecast-metrics">
                                <div class="forecast-metric">
                                    <div class="forecast-value">€3,500</div>
                                    <div class="forecast-label">Potenciálne Riziká</div>
                                </div>
                                <div class="forecast-metric">
                                    <div class="forecast-value">23%</div>
                                    <div class="forecast-label">Pravdepodobnosť</div>
                                </div>
                            </div>
                            <div class="ai-insights">
                                <div class="insights-title">⚠️ Upozornenie</div>
                                <div class="insights-text">
                                    Identifikované riziko prekročenia rozpočtu pri API integrácii.
                                </div>
                            </div>
                        </div>

                        <div class="forecast-card">
                            <div class="forecast-period">Optimalizácia</div>
                            <div class="forecast-metrics">
                                <div class="forecast-metric">
                                    <div class="forecast-value">€1,800</div>
                                    <div class="forecast-label">Možné Úspory</div>
                                </div>
                                <div class="forecast-metric">
                                    <div class="forecast-value">3</div>
                                    <div class="forecast-label">Odporúčania</div>
                                </div>
                            </div>
                            <div class="ai-insights">
                                <div class="insights-title">💡 Návrhy</div>
                                <div class="insights-text">
                                    Optimalizácia resource allocation môže ušetriť až €1,800.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            animateCharts();
            updateFinancialMetrics();
            startBudgetMonitoring();
        }

        function animateCharts() {
            // Animácia donut chart segmentov
            const segments = document.querySelectorAll('.donut-segment');
            segments.forEach((segment, index) => {
                setTimeout(() => {
                    segment.style.opacity = '1';
                    segment.style.strokeDasharray = segment.getAttribute('stroke-dasharray');
                }, index * 200);
            });

            // Animácia timeline bars
            const bars = document.querySelectorAll('.timeline-bar');
            bars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.opacity = '1';
                    bar.style.transform = 'scaleY(1)';
                }, index * 100);
            });
        }

        function updateFinancialMetrics() {
            // Simulácia aktualizácie finančných metrík
            console.log('Finančné metriky aktualizované:', new Date().toLocaleTimeString());
        }

        function startBudgetMonitoring() {
            // Simulácia real-time monitoring rozpočtu
            setInterval(() => {
                updateFinancialMetrics();
                checkBudgetAlerts();
            }, 60000); // Každú minútu
        }

        function checkBudgetAlerts() {
            // Simulácia kontroly rozpočtových upozornení
            const currentUtilization = 78;
            if (currentUtilization > 80) {
                showBudgetAlert();
            }
        }

        function showBudgetAlert() {
            alert('⚠️ Rozpočtové Upozornenie!\n\nVyužitie rozpočtu prekročilo 80%\n\nAktuálne využitie: 78%\nZostáva: €10,800\nOdhadovaný koniec: 4 týždne\n\nOdporúčame:\n• Prehodnotiť priority\n• Optimalizovať výdavky\n• Zvážiť dodatočný rozpočet');
        }

        function addExpense() {
            const expense = prompt('Zadajte nový výdavok:\n\nFormát: Suma,Kategória,Popis\nPríklad: 1500,Technológie,Server hosting');

            if (expense) {
                const [amount, category, description] = expense.split(',');
                if (amount && category && description) {
                    alert(`✅ Výdavok pridaný!\n\nSuma: €${amount}\nKategória: ${category}\nPopis: ${description}\n\n• Rozpočet aktualizovaný\n• Metriky prepočítané\n• Notifikácia odoslaná tímu`);
                } else {
                    alert('❌ Nesprávny formát! Použite: Suma,Kategória,Popis');
                }
            }
        }

        function generateFinancialReport() {
            alert('📊 Generujem Finančný Report...\n\n• Budget utilization analysis: ✓\n• Expense breakdown: ✓\n• Trend analysis: ✓\n• AI forecasting: ✓\n• Risk assessment: ✓\n\nReport obsahuje:\n✓ Executive summary\n✓ Detailed expense tracking\n✓ Budget vs actual analysis\n✓ Cash flow projections\n✓ Cost optimization recommendations\n\nReport exportovaný do Excel a PDF!');
        }

        // Interaktívne donut chart segmenty
        document.querySelectorAll('.donut-segment').forEach((segment, index) => {
            const categories = ['Ľudské Zdroje', 'Technológie', 'Marketing', 'Ostatné'];
            const amounts = ['€20,000', '€15,000', '€3,200', '€1,000'];
            const percentages = ['51%', '38%', '8%', '3%'];

            segment.addEventListener('click', function() {
                alert(`📊 Detail Kategórie: ${categories[index]}\n\nSuma: ${amounts[index]}\nPodiel: ${percentages[index]}\n\nBreakdown:\n• Plánované: ${amounts[index]}\n• Skutočné: ${amounts[index]}\n• Odchýlka: €0 (0%)\n• Trend: Stabilný`);
            });
        });

        // Interaktívne timeline bars
        document.querySelectorAll('.timeline-bar').forEach(bar => {
            bar.addEventListener('click', function() {
                const week = this.querySelector('.bar-label').textContent;
                const amount = this.querySelector('.bar-value').textContent;
                alert(`📈 Detail pre ${week}\n\nVýdavky: ${amount}\n\nBreakdown:\n• Ľudské zdroje: 60%\n• Technológie: 25%\n• Marketing: 10%\n• Ostatné: 5%\n\nKľúčové výdavky:\n• Freelancer payment\n• Software licenses\n• Marketing campaign`);
            });
        });

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>