<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Meeting Planner - Projektov<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            min-height: 700px;
        }

        .main-area {
            padding: 2rem;
        }

        .pm-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .ai-suggestions {
            background: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);
            border: 1px solid #a78bfa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .ai-title {
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .suggestion-item {
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            background: rgba(255,255,255,0.9);
            transform: translateX(5px);
        }

        .suggestion-title {
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 0.25rem;
        }

        .suggestion-desc {
            font-size: 0.9rem;
            color: #6b46c1;
        }

        .meeting-form {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #f59e0b;
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .attendees-section {
            margin-bottom: 1.5rem;
        }

        .attendee-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .attendee-tag {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .remove-attendee {
            cursor: pointer;
            color: #6b46c1;
            font-weight: bold;
        }

        .add-attendee {
            display: flex;
            gap: 0.5rem;
        }

        .availability-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .time-slot {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .time-slot.available {
            background: #d1fae5;
            border-color: #059669;
            color: #065f46;
        }

        .time-slot.busy {
            background: #fee2e2;
            border-color: #dc2626;
            color: #991b1b;
        }

        .time-slot.optimal {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
            font-weight: 600;
        }

        .agenda-builder {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .agenda-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .agenda-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .agenda-content {
            flex: 1;
        }

        .agenda-topic {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .agenda-duration {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .agenda-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            border-radius: 4px;
        }

        .btn-edit {
            background: #3b82f6;
            color: white;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .meeting-summary {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .summary-title {
            font-weight: 600;
            color: #065f46;
            margin-bottom: 1rem;
        }

        .summary-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #a7f3d0;
        }

        .summary-label {
            font-weight: 500;
            color: #047857;
        }

        .summary-value {
            color: #065f46;
        }

        .upcoming-meetings {
            margin-top: 2rem;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .meeting-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #f59e0b;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .meeting-time {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .meeting-title {
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .meeting-attendees {
            font-size: 0.8rem;
            color: #6b7280;
        }

        @media (max-width: 1024px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Smart Meeting Planner - Projektový Manažment</h1>
        
        <div class="pm-component">
            <div class="pm-header">
                <h2>🤝 Smart Meeting Planner</h2>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="scheduleRecurring()">🔄 Pravidelné Meetingy</button>
                    <button class="btn btn-primary" onclick="exportCalendar()">📅 Export do Kalendára</button>
                </div>
            </div>
            
            <div class="pm-content">
                <div class="main-area">
                    <div class="ai-suggestions">
                        <div class="ai-title">
                            🤖 AI Návrhy na Optimalizáciu Meetingov
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(1)">
                            <div class="suggestion-title">Optimálny čas pre Sprint Planning</div>
                            <div class="suggestion-desc">Utorok 10:00 - všetci účastníci dostupní, vysoká produktivita</div>
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(2)">
                            <div class="suggestion-title">Skrátiť Daily Standup</div>
                            <div class="suggestion-desc">Odporúčame 15 min namiesto 30 min na základe histórie</div>
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(3)">
                            <div class="suggestion-title">Pridať Code Review Session</div>
                            <div class="suggestion-desc">Chýba v harmonograme, kritické pre kvalitu kódu</div>
                        </div>
                    </div>

                    <div class="meeting-form">
                        <div class="form-title">📝 Naplánuj Nový Meeting</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Názov Meetingu</label>
                                <input type="text" class="form-input" id="meetingTitle" placeholder="Sprint Planning Meeting">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Typ Meetingu</label>
                                <select class="form-select" id="meetingType">
                                    <option value="planning">Sprint Planning</option>
                                    <option value="standup">Daily Standup</option>
                                    <option value="review">Sprint Review</option>
                                    <option value="retrospective">Retrospective</option>
                                    <option value="stakeholder">Stakeholder Meeting</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Dátum</label>
                                <input type="date" class="form-input" id="meetingDate" value="2024-06-20">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Trvanie (minúty)</label>
                                <input type="number" class="form-input" id="meetingDuration" value="60">
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">Popis a Ciele</label>
                                <textarea class="form-textarea" id="meetingDescription" placeholder="Definovanie úloh pre nasledujúci sprint, odhad story points..."></textarea>
                            </div>
                        </div>

                        <div class="attendees-section">
                            <label class="form-label">Účastníci</label>
                            <div class="attendee-list" id="attendeeList">
                                <div class="attendee-tag">
                                    Ján Novák <span class="remove-attendee" onclick="removeAttendee(this)">×</span>
                                </div>
                                <div class="attendee-tag">
                                    Mária Svobodová <span class="remove-attendee" onclick="removeAttendee(this)">×</span>
                                </div>
                                <div class="attendee-tag">
                                    Peter Kováč <span class="remove-attendee" onclick="removeAttendee(this)">×</span>
                                </div>
                            </div>
                            <div class="add-attendee">
                                <input type="text" class="form-input" id="newAttendee" placeholder="Pridať účastníka...">
                                <button class="btn btn-primary" onclick="addAttendee()">Pridať</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">🕐 Dostupnosť Účastníkov (Tento Týždeň)</label>
                            <div class="availability-grid" id="availabilityGrid">
                                <!-- Availability slots budú vygenerované JavaScriptom -->
                            </div>
                        </div>

                        <button class="btn btn-primary" onclick="createMeeting()" style="width: 100%; padding: 1rem; font-size: 1.1rem;">
                            🚀 Vytvoriť Meeting
                        </button>
                    </div>

                    <div class="agenda-builder">
                        <div class="agenda-title">📋 Agenda Builder</div>
                        <div id="agendaItems">
                            <div class="agenda-item">
                                <div class="agenda-content">
                                    <div class="agenda-topic">Úvod a ciele sprintu</div>
                                    <div class="agenda-duration">10 minút</div>
                                </div>
                                <div class="agenda-actions">
                                    <button class="btn btn-small btn-edit">Upraviť</button>
                                    <button class="btn btn-small btn-delete">Zmazať</button>
                                </div>
                            </div>
                            <div class="agenda-item">
                                <div class="agenda-content">
                                    <div class="agenda-topic">Review backlog items</div>
                                    <div class="agenda-duration">30 minút</div>
                                </div>
                                <div class="agenda-actions">
                                    <button class="btn btn-small btn-edit">Upraviť</button>
                                    <button class="btn btn-small btn-delete">Zmazať</button>
                                </div>
                            </div>
                            <div class="agenda-item">
                                <div class="agenda-content">
                                    <div class="agenda-topic">Story points estimation</div>
                                    <div class="agenda-duration">15 minút</div>
                                </div>
                                <div class="agenda-actions">
                                    <button class="btn btn-small btn-edit">Upraviť</button>
                                    <button class="btn btn-small btn-delete">Zmazať</button>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="addAgendaItem()">+ Pridať Bod Agendy</button>
                    </div>

                    <div class="meeting-summary">
                        <div class="summary-title">📊 Súhrn Meetingu</div>
                        <div class="summary-details">
                            <div class="summary-item">
                                <span class="summary-label">Celkové trvanie:</span>
                                <span class="summary-value">55 minút</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Počet účastníkov:</span>
                                <span class="summary-value">3 osoby</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Odhadované náklady:</span>
                                <span class="summary-value">€275</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Optimálny čas:</span>
                                <span class="summary-value">Utorok 10:00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="pm-sidebar">
                    <div class="sidebar-title">📅 Nadchádzajúce Meetingy</div>
                    
                    <div class="meeting-card">
                        <div class="meeting-time">Dnes 14:00 - 14:30</div>
                        <div class="meeting-title">Daily Standup</div>
                        <div class="meeting-attendees">5 účastníkov</div>
                    </div>
                    
                    <div class="meeting-card">
                        <div class="meeting-time">Zajtra 10:00 - 11:30</div>
                        <div class="meeting-title">Sprint Review</div>
                        <div class="meeting-attendees">8 účastníkov</div>
                    </div>
                    
                    <div class="meeting-card">
                        <div class="meeting-time">Piatok 15:00 - 16:00</div>
                        <div class="meeting-title">Stakeholder Update</div>
                        <div class="meeting-attendees">4 účastníci</div>
                    </div>

                    <div class="sidebar-title" style="margin-top: 2rem;">📈 Meeting Štatistiky</div>
                    
                    <div style="background: white; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Tento týždeň:</span>
                            <strong>12 meetingov</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Celkový čas:</span>
                            <strong>8.5 hodín</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>Efektívnosť:</span>
                            <strong style="color: #059669;">87%</strong>
                        </div>
                    </div>

                    <div style="background: white; border-radius: 8px; padding: 1rem;">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">🎯 AI Odporúčania</div>
                        <div style="font-size: 0.9rem; color: #6b7280; line-height: 1.4;">
                            • Skrátiť daily standupy o 5 min<br>
                            • Plánovať meetingy na dopoludnie<br>
                            • Pridať buffer čas medzi meetingmi
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            generateAvailabilityGrid();
            updateMeetingSummary();
        }

        function generateAvailabilityGrid() {
            const grid = document.getElementById('availabilityGrid');
            const days = ['Pon', 'Uto', 'Str', 'Štv', 'Pia', 'Sob', 'Ned'];
            const times = ['9:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00'];
            
            grid.innerHTML = '';
            
            // Simulácia dostupnosti
            const availability = [
                ['busy', 'available', 'available', 'available', 'busy', 'available', 'available'],
                ['available', 'optimal', 'available', 'busy', 'available', 'available', 'available'],
                ['available', 'available', 'busy', 'available', 'available', 'busy', 'available'],
                ['busy', 'available', 'available', 'optimal', 'available', 'available', 'busy'],
                ['available', 'available', 'available', 'available', 'busy', 'available', 'available']
            ];

            times.forEach((time, timeIndex) => {
                days.forEach((day, dayIndex) => {
                    const slot = document.createElement('div');
                    slot.className = `time-slot ${availability[timeIndex][dayIndex]}`;
                    slot.innerHTML = `${day}<br>${time}`;
                    slot.onclick = () => selectTimeSlot(slot);
                    grid.appendChild(slot);
                });
            });
        }

        function selectTimeSlot(slot) {
            document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));
            slot.classList.add('selected');
            slot.style.background = '#3b82f6';
            slot.style.color = 'white';
        }

        function addAttendee() {
            const input = document.getElementById('newAttendee');
            const name = input.value.trim();
            
            if (name) {
                const attendeeList = document.getElementById('attendeeList');
                const tag = document.createElement('div');
                tag.className = 'attendee-tag';
                tag.innerHTML = `${name} <span class="remove-attendee" onclick="removeAttendee(this)">×</span>`;
                attendeeList.appendChild(tag);
                input.value = '';
                updateMeetingSummary();
            }
        }

        function removeAttendee(element) {
            element.parentElement.remove();
            updateMeetingSummary();
        }

        function addAgendaItem() {
            const topic = prompt('Zadajte názov bodu agendy:');
            const duration = prompt('Zadajte trvanie v minútach:');
            
            if (topic && duration) {
                const agendaItems = document.getElementById('agendaItems');
                const item = document.createElement('div');
                item.className = 'agenda-item';
                item.innerHTML = `
                    <div class="agenda-content">
                        <div class="agenda-topic">${topic}</div>
                        <div class="agenda-duration">${duration} minút</div>
                    </div>
                    <div class="agenda-actions">
                        <button class="btn btn-small btn-edit">Upraviť</button>
                        <button class="btn btn-small btn-delete" onclick="this.parentElement.parentElement.remove(); updateMeetingSummary();">Zmazať</button>
                    </div>
                `;
                agendaItems.appendChild(item);
                updateMeetingSummary();
            }
        }

        function updateMeetingSummary() {
            const attendees = document.querySelectorAll('.attendee-tag').length;
            const agendaItems = document.querySelectorAll('.agenda-item').length;
            
            // Simulácia aktualizácie súhrnu
            document.querySelector('.summary-details').innerHTML = `
                <div class="summary-item">
                    <span class="summary-label">Celkové trvanie:</span>
                    <span class="summary-value">${agendaItems * 15} minút</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Počet účastníkov:</span>
                    <span class="summary-value">${attendees} osôb</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Odhadované náklady:</span>
                    <span class="summary-value">€${attendees * agendaItems * 25}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Optimálny čas:</span>
                    <span class="summary-value">Utorok 10:00</span>
                </div>
            `;
        }

        function applySuggestion(id) {
            const suggestions = {
                1: 'AI návrh aplikovaný: Meeting naplánovaný na utorok 10:00',
                2: 'AI návrh aplikovaný: Trvanie daily standup zmenené na 15 minút',
                3: 'AI návrh aplikovaný: Code review session pridaný do agendy'
            };
            alert(suggestions[id]);
        }

        function createMeeting() {
            const title = document.getElementById('meetingTitle').value;
            const date = document.getElementById('meetingDate').value;
            const duration = document.getElementById('meetingDuration').value;
            
            if (title && date && duration) {
                alert(`✅ Meeting "${title}" úspešne vytvorený!\n\nDátum: ${date}\nTrvanie: ${duration} minút\nPozvánky odoslané všetkým účastníkom\nKalendárové udalosti vytvorené`);
            } else {
                alert('Prosím vyplňte všetky povinné polia');
            }
        }

        function scheduleRecurring() {
            alert('🔄 Nastavenie pravidelných meetingov:\n\n• Daily Standup: Každý deň 9:00\n• Sprint Planning: Každé 2 týždne\n• Sprint Review: Každé 2 týždne\n• Retrospective: Každé 2 týždne\n\nPravidelné meetingy nastavené!');
        }

        function exportCalendar() {
            alert('📅 Export do kalendára:\n\n• Outlook kalendár: ✓\n• Google Calendar: ✓\n• iCal súbor: ✓\n\nVšetky meetingy exportované!');
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
