<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Pipeline & DevOps Manager - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #065f46 0%, #10b981 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #10b981 0%, #065f46 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .pipeline-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
            padding: 2rem;
        }

        .pipeline-overview {
            grid-column: 1 / -1;
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .overview-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .pipeline-flow {
            display: flex;
            align-items: center;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem 0;
        }

        .pipeline-stage {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            min-width: 200px;
            text-align: center;
            border: 2px solid #e5e7eb;
            position: relative;
            transition: all 0.3s ease;
        }

        .pipeline-stage:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .pipeline-stage.success {
            border-color: #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        }

        .pipeline-stage.running {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        }

        .pipeline-stage.failed {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        .pipeline-stage.pending {
            border-color: #d1d5db;
            background: #f9fafb;
        }

        .stage-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stage-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .stage-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .status-success { background: #d1fae5; color: #065f46; }
        .status-running { background: #dbeafe; color: #1e40af; }
        .status-failed { background: #fee2e2; color: #991b1b; }
        .status-pending { background: #f3f4f6; color: #6b7280; }

        .stage-duration {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .pipeline-arrow {
            font-size: 1.5rem;
            color: #10b981;
            flex-shrink: 0;
        }

        .environments-grid {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .environments-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .environment-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .environment-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .environment-item:hover {
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .env-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .env-name {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .env-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .env-healthy { background: #d1fae5; color: #065f46; }
        .env-deploying { background: #dbeafe; color: #1e40af; }
        .env-error { background: #fee2e2; color: #991b1b; }

        .env-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .env-detail {
            text-align: center;
        }

        .detail-value {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .detail-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .env-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            border-color: #10b981;
            color: #10b981;
        }

        .action-btn.primary {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .action-btn.primary:hover {
            background: #059669;
        }

        .monitoring-dashboard {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .monitoring-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .metric-trend {
            font-size: 0.8rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #6b7280; }

        .deployment-logs {
            background: #1f2937;
            border-radius: 8px;
            padding: 1rem;
            color: #e5e7eb;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            max-height: 200px;
            overflow-y: auto;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 0.25rem;
        }

        .log-timestamp {
            color: #9ca3af;
        }

        .log-info { color: #60a5fa; }
        .log-success { color: #34d399; }
        .log-warning { color: #fbbf24; }
        .log-error { color: #f87171; }

        .devops-tools {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border: 2px solid #8b5cf6;
            border-radius: 12px;
            padding: 2rem;
        }

        .tools-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .tool-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #c4b5fd;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            background: rgba(255,255,255,0.95);
            transform: translateY(-2px);
        }

        .tool-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .tool-name {
            font-weight: 600;
            color: #5b21b6;
        }

        .tool-status {
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .tool-active { background: #d1fae5; color: #065f46; }
        .tool-inactive { background: #fee2e2; color: #991b1b; }

        .tool-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .tool-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }

        .tool-metric {
            text-align: center;
            padding: 0.5rem;
            background: rgba(255,255,255,0.5);
            border-radius: 6px;
        }

        .tool-metric-value {
            font-weight: 600;
            color: #5b21b6;
        }

        .tool-metric-label {
            font-size: 0.7rem;
            color: #7c3aed;
        }

        @media (max-width: 1024px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .pipeline-flow {
                flex-direction: column;
                align-items: stretch;
            }

            .pipeline-arrow {
                transform: rotate(90deg);
                align-self: center;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Deployment Pipeline & DevOps Manager - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>🚀 Deployment Pipeline & DevOps Manager</h2>
                <div class="pipeline-indicator">
                    ✅ Pipeline Status: Healthy
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="triggerDeployment()">🚀 Deploy to Staging</button>
                    <button class="btn btn-primary" onclick="viewLogs()">📋 View Logs</button>
                </div>
            </div>

            <div class="pm-content">
                <!-- Pipeline Overview -->
                <div class="pipeline-overview">
                    <div class="overview-title">
                        🔄 CI/CD Pipeline - E-shop Modernizácia
                    </div>

                    <div class="pipeline-flow">
                        <div class="pipeline-stage success">
                            <div class="stage-icon">📝</div>
                            <div class="stage-name">Source</div>
                            <div class="stage-status status-success">Completed</div>
                            <div class="stage-duration">0.5s</div>
                        </div>

                        <div class="pipeline-arrow">→</div>

                        <div class="pipeline-stage success">
                            <div class="stage-icon">🔨</div>
                            <div class="stage-name">Build</div>
                            <div class="stage-status status-success">Completed</div>
                            <div class="stage-duration">3m 24s</div>
                        </div>

                        <div class="pipeline-arrow">→</div>

                        <div class="pipeline-stage success">
                            <div class="stage-icon">🧪</div>
                            <div class="stage-name">Test</div>
                            <div class="stage-status status-success">Completed</div>
                            <div class="stage-duration">2m 15s</div>
                        </div>

                        <div class="pipeline-arrow">→</div>

                        <div class="pipeline-stage running">
                            <div class="stage-icon">🔍</div>
                            <div class="stage-name">Security Scan</div>
                            <div class="stage-status status-running">Running</div>
                            <div class="stage-duration">1m 45s</div>
                        </div>

                        <div class="pipeline-arrow">→</div>

                        <div class="pipeline-stage pending">
                            <div class="stage-icon">📦</div>
                            <div class="stage-name">Package</div>
                            <div class="stage-status status-pending">Pending</div>
                            <div class="stage-duration">-</div>
                        </div>

                        <div class="pipeline-arrow">→</div>

                        <div class="pipeline-stage pending">
                            <div class="stage-icon">🚀</div>
                            <div class="stage-name">Deploy</div>
                            <div class="stage-status status-pending">Pending</div>
                            <div class="stage-duration">-</div>
                        </div>
                    </div>
                </div>

                <!-- Environments -->
                <div class="environments-grid">
                    <div class="environments-title">
                        🌍 Deployment Environments
                    </div>

                    <div class="environment-list">
                        <div class="environment-item">
                            <div class="env-header">
                                <div class="env-name">
                                    🔧 Development
                                </div>
                                <div class="env-status env-healthy">Healthy</div>
                            </div>
                            <div class="env-details">
                                <div class="env-detail">
                                    <div class="detail-value">v2.1.3</div>
                                    <div class="detail-label">Version</div>
                                </div>
                                <div class="env-detail">
                                    <div class="detail-value">99.8%</div>
                                    <div class="detail-label">Uptime</div>
                                </div>
                                <div class="env-detail">
                                    <div class="detail-value">45ms</div>
                                    <div class="detail-label">Response Time</div>
                                </div>
                            </div>
                            <div class="env-actions">
                                <button class="action-btn primary" onclick="deployToEnv('dev')">Deploy</button>
                                <button class="action-btn" onclick="viewEnvLogs('dev')">Logs</button>
                                <button class="action-btn" onclick="rollbackEnv('dev')">Rollback</button>
                            </div>
                        </div>

                        <div class="environment-item">
                            <div class="env-header">
                                <div class="env-name">
                                    🧪 Staging
                                </div>
                                <div class="env-status env-deploying">Deploying</div>
                            </div>
                            <div class="env-details">
                                <div class="env-detail">
                                    <div class="detail-value">v2.1.2</div>
                                    <div class="detail-label">Version</div>
                                </div>
                                <div class="env-detail">
                                    <div class="detail-value">97.5%</div>
                                    <div class="detail-label">Uptime</div>
                                </div>
                                <div class="env-detail">
                                    <div class="detail-value">78ms</div>
                                    <div class="detail-label">Response Time</div>
                                </div>
                            </div>
                            <div class="env-actions">
                                <button class="action-btn" disabled>Deploying...</button>
                                <button class="action-btn" onclick="viewEnvLogs('staging')">Logs</button>
                                <button class="action-btn" onclick="rollbackEnv('staging')">Rollback</button>
                            </div>
                        </div>

                        <div class="environment-item">
                            <div class="env-header">
                                <div class="env-name">
                                    🌐 Production
                                </div>
                                <div class="env-status env-healthy">Healthy</div>
                            </div>
                            <div class="env-details">
                                <div class="env-detail">
                                    <div class="detail-value">v2.1.1</div>
                                    <div class="detail-label">Version</div>
                                </div>
                                <div class="env-detail">
                                    <div class="detail-value">99.9%</div>
                                    <div class="detail-label">Uptime</div>
                                </div>
                                <div class="env-detail">
                                    <div class="detail-value">32ms</div>
                                    <div class="detail-label">Response Time</div>
                                </div>
                            </div>
                            <div class="env-actions">
                                <button class="action-btn primary" onclick="deployToEnv('prod')">Deploy</button>
                                <button class="action-btn" onclick="viewEnvLogs('prod')">Logs</button>
                                <button class="action-btn" onclick="rollbackEnv('prod')">Rollback</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monitoring Dashboard -->
                <div class="monitoring-dashboard">
                    <div class="monitoring-title">
                        📊 System Monitoring
                    </div>

                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value" style="color: #10b981;">99.8%</div>
                            <div class="metric-label">System Uptime</div>
                            <div class="metric-trend trend-up">
                                ↗ +0.2% za týždeň
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #3b82f6;">42ms</div>
                            <div class="metric-label">Avg Response Time</div>
                            <div class="metric-trend trend-down">
                                ↘ -8ms za týždeň
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #f59e0b;">2.3GB</div>
                            <div class="metric-label">Memory Usage</div>
                            <div class="metric-trend trend-stable">
                                → Stabilné
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #ef4444;">0</div>
                            <div class="metric-label">Critical Errors</div>
                            <div class="metric-trend trend-down">
                                ↘ -3 za týždeň
                            </div>
                        </div>
                    </div>

                    <div class="deployment-logs" id="deploymentLogs">
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:32:15]</span>
                            <span class="log-info">[INFO]</span> Starting deployment pipeline...
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:32:16]</span>
                            <span class="log-success">[SUCCESS]</span> Source code retrieved from Git repository
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:32:18]</span>
                            <span class="log-info">[INFO]</span> Installing dependencies...
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:34:42]</span>
                            <span class="log-success">[SUCCESS]</span> Build completed successfully
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:34:45]</span>
                            <span class="log-info">[INFO]</span> Running unit tests...
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:37:00]</span>
                            <span class="log-success">[SUCCESS]</span> All tests passed (142/142)
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:37:02]</span>
                            <span class="log-info">[INFO]</span> Starting security scan...
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:38:47]</span>
                            <span class="log-warning">[WARNING]</span> Minor vulnerability detected in dependency
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:38:48]</span>
                            <span class="log-info">[INFO]</span> Vulnerability patched automatically
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-06-18 14:38:50]</span>
                            <span class="log-success">[SUCCESS]</span> Security scan completed - No critical issues
                        </div>
                    </div>
                </div>

                <!-- DevOps Tools -->
                <div class="devops-tools">
                    <div class="tools-title">
                        🛠️ DevOps Tools & Integrations
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <div class="tool-header">
                                <div class="tool-name">GitHub Actions</div>
                                <div class="tool-status tool-active">Active</div>
                            </div>
                            <div class="tool-description">
                                CI/CD pipeline automation s automatickým testovaním a deploymentom
                            </div>
                            <div class="tool-metrics">
                                <div class="tool-metric">
                                    <div class="tool-metric-value">156</div>
                                    <div class="tool-metric-label">Workflows</div>
                                </div>
                                <div class="tool-metric">
                                    <div class="tool-metric-value">98.5%</div>
                                    <div class="tool-metric-label">Success Rate</div>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <div class="tool-header">
                                <div class="tool-name">Docker</div>
                                <div class="tool-status tool-active">Active</div>
                            </div>
                            <div class="tool-description">
                                Kontajnerizácia aplikácií pre konzistentné deployment prostredie
                            </div>
                            <div class="tool-metrics">
                                <div class="tool-metric">
                                    <div class="tool-metric-value">12</div>
                                    <div class="tool-metric-label">Images</div>
                                </div>
                                <div class="tool-metric">
                                    <div class="tool-metric-value">3.2GB</div>
                                    <div class="tool-metric-label">Total Size</div>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <div class="tool-header">
                                <div class="tool-name">Kubernetes</div>
                                <div class="tool-status tool-active">Active</div>
                            </div>
                            <div class="tool-description">
                                Orchestrácia kontajnerov a automatické škálovanie aplikácií
                            </div>
                            <div class="tool-metrics">
                                <div class="tool-metric">
                                    <div class="tool-metric-value">8</div>
                                    <div class="tool-metric-label">Pods</div>
                                </div>
                                <div class="tool-metric">
                                    <div class="tool-metric-value">3</div>
                                    <div class="tool-metric-label">Services</div>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <div class="tool-header">
                                <div class="tool-name">Prometheus</div>
                                <div class="tool-status tool-active">Active</div>
                            </div>
                            <div class="tool-description">
                                Monitoring a alerting systém pre real-time sledovanie metrík
                            </div>
                            <div class="tool-metrics">
                                <div class="tool-metric">
                                    <div class="tool-metric-value">47</div>
                                    <div class="tool-metric-label">Metrics</div>
                                </div>
                                <div class="tool-metric">
                                    <div class="tool-metric-value">5</div>
                                    <div class="tool-metric-label">Alerts</div>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <div class="tool-header">
                                <div class="tool-name">SonarQube</div>
                                <div class="tool-status tool-active">Active</div>
                            </div>
                            <div class="tool-description">
                                Analýza kvality kódu a detekcia bezpečnostných vulnerabilít
                            </div>
                            <div class="tool-metrics">
                                <div class="tool-metric">
                                    <div class="tool-metric-value">A</div>
                                    <div class="tool-metric-label">Quality Gate</div>
                                </div>
                                <div class="tool-metric">
                                    <div class="tool-metric-value">87%</div>
                                    <div class="tool-metric-label">Coverage</div>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <div class="tool-header">
                                <div class="tool-name">Terraform</div>
                                <div class="tool-status tool-inactive">Inactive</div>
                            </div>
                            <div class="tool-description">
                                Infrastructure as Code pre automatizované provisioning zdrojov
                            </div>
                            <div class="tool-metrics">
                                <div class="tool-metric">
                                    <div class="tool-metric-value">0</div>
                                    <div class="tool-metric-label">Resources</div>
                                </div>
                                <div class="tool-metric">
                                    <div class="tool-metric-value">-</div>
                                    <div class="tool-metric-label">State</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            updatePipelineStatus();
            startLogStreaming();
            monitorEnvironments();
        }

        function updatePipelineStatus() {
            // Simulácia aktualizácie pipeline statusu
            console.log('Pipeline status aktualizovaný:', new Date().toLocaleTimeString());
        }

        function startLogStreaming() {
            // Simulácia real-time log streaming
            setInterval(() => {
                addNewLogEntry();
            }, 10000); // Každých 10 sekúnd
        }

        function addNewLogEntry() {
            const logs = document.getElementById('deploymentLogs');
            const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
            const logTypes = ['INFO', 'SUCCESS', 'WARNING'];
            const logType = logTypes[Math.floor(Math.random() * logTypes.length)];
            const messages = [
                'Health check completed successfully',
                'Database connection verified',
                'Cache cleared and rebuilt',
                'SSL certificate renewed',
                'Backup completed successfully'
            ];
            const message = messages[Math.floor(Math.random() * messages.length)];

            const newLog = document.createElement('div');
            newLog.className = 'log-entry';
            newLog.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-${logType.toLowerCase()}">[${logType}]</span> ${message}
            `;

            logs.appendChild(newLog);
            logs.scrollTop = logs.scrollHeight;

            // Udržiavať maximálne 20 log entries
            while (logs.children.length > 20) {
                logs.removeChild(logs.firstChild);
            }
        }

        function monitorEnvironments() {
            // Simulácia monitoring environmentov
            setInterval(() => {
                updateEnvironmentMetrics();
            }, 30000); // Každých 30 sekúnd
        }

        function updateEnvironmentMetrics() {
            // Simulácia aktualizácie environment metrík
            console.log('Environment metrics aktualizované:', new Date().toLocaleTimeString());
        }

        function triggerDeployment() {
            alert('🚀 Spúšťam deployment do Staging...\n\n• Verifikácia kódu: ✓\n• Build process: ✓\n• Unit tests: ✓\n• Security scan: ✓\n\nDeployment kroky:\n1. Package aplikácie\n2. Database migrácie\n3. Rolling deployment\n4. Health checks\n5. Smoke tests\n\nOdhadovaný čas: 8 minút\nNotifikácia po dokončení: ✓');
        }

        function viewLogs() {
            alert('📋 Deployment Logs\n\nZobrazujem real-time logy z:\n• Build process\n• Test execution\n• Security scans\n• Deployment steps\n• Health checks\n\nLogy sú automaticky aktualizované každých 10 sekúnd.\nMožnosť filtrovania podľa typu: INFO, WARNING, ERROR\nExport logov do súboru: Dostupný');
        }

        function deployToEnv(env) {
            const envNames = {
                'dev': 'Development',
                'staging': 'Staging',
                'prod': 'Production'
            };

            const envName = envNames[env];
            const isProduction = env === 'prod';

            if (isProduction) {
                const confirm = window.confirm(`⚠️ PRODUCTION DEPLOYMENT\n\nNaozaj chcete deploynúť do Production?\n\nTáto akcia:\n• Ovplyvní živý systém\n• Bude viditeľná pre používateľov\n• Vyžaduje dodatočné schválenie\n\nPokračovať?`);

                if (!confirm) return;
            }

            alert(`🚀 Deployment do ${envName}\n\n• Environment: ${envName}\n• Version: v2.1.3\n• Strategy: ${isProduction ? 'Blue-Green' : 'Rolling'}\n• Rollback: Automatický pri zlyhaní\n\nKroky:\n1. Pre-deployment checks\n2. Database migrations\n3. Application deployment\n4. Post-deployment verification\n5. Health monitoring\n\nDeployment spustený!`);
        }

        function viewEnvLogs(env) {
            const envNames = {
                'dev': 'Development',
                'staging': 'Staging',
                'prod': 'Production'
            };

            alert(`📋 ${envNames[env]} Environment Logs\n\nPosledných 100 log entries:\n• Application logs\n• System metrics\n• Error reports\n• Performance data\n\nReal-time monitoring aktívny\nLog retention: 30 dní\nExport možný v JSON/CSV formáte`);
        }

        function rollbackEnv(env) {
            const envNames = {
                'dev': 'Development',
                'staging': 'Staging',
                'prod': 'Production'
            };

            const envName = envNames[env];
            const isProduction = env === 'prod';

            if (isProduction) {
                const confirm = window.confirm(`⚠️ PRODUCTION ROLLBACK\n\nNaozaj chcete vykonať rollback v Production?\n\nTáto akcia:\n• Vráti predchádzajúcu verziu\n• Môže ovplyvniť používateľov\n• Vyžaduje okamžité testovanie\n\nPokračovať?`);

                if (!confirm) return;
            }

            alert(`🔄 Rollback v ${envName}\n\n• Current version: v2.1.3\n• Rollback to: v2.1.2\n• Strategy: ${isProduction ? 'Immediate' : 'Gradual'}\n• Data migration: Automatická\n\nRollback kroky:\n1. Traffic redirection\n2. Version switch\n3. Database rollback\n4. Verification tests\n5. Monitoring activation\n\nRollback spustený!`);
        }

        // Pipeline stage click handlers
        document.querySelectorAll('.pipeline-stage').forEach(stage => {
            stage.addEventListener('click', function() {
                const stageName = this.querySelector('.stage-name').textContent;
                const stageStatus = this.querySelector('.stage-status').textContent;
                const stageDuration = this.querySelector('.stage-duration').textContent;

                alert(`📋 Pipeline Stage: ${stageName}\n\nStatus: ${stageStatus}\nTrvanie: ${stageDuration}\n\nDetaily:\n• Konfigurácia: Automatická\n• Retry policy: 3 pokusy\n• Timeout: 10 minút\n• Notifikácie: Email + Slack\n\nLogy a metriky dostupné v monitoring dashboarde.`);
            });
        });

        // Tool card click handlers
        document.querySelectorAll('.tool-card').forEach(card => {
            card.addEventListener('click', function() {
                const toolName = this.querySelector('.tool-name').textContent;
                const toolStatus = this.querySelector('.tool-status').textContent;
                const toolDesc = this.querySelector('.tool-description').textContent;

                alert(`🛠️ DevOps Tool: ${toolName}\n\nStatus: ${toolStatus}\nPopis: ${toolDesc}\n\nFunkcie:\n• Konfigurácia: Centralizovaná\n• Monitoring: Real-time\n• Integrácie: API + Webhooks\n• Dokumentácia: Dostupná\n\nPrístup k nástroju cez admin panel.`);
            });
        });

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>