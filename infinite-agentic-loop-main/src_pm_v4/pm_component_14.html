<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Portal & Feedback Manager - <PERSON>je<PERSON><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .client-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            min-height: 700px;
        }

        .client-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .client-info {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e5e7eb;
        }

        .client-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }

        .client-name {
            text-align: center;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .client-company {
            text-align: center;
            color: #6b7280;
            margin-bottom: 1rem;
        }

        .client-contact {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .project-overview {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }

        .overview-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 6px;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .main-portal {
            padding: 2rem;
            display: flex;
            flex-direction: column;
        }

        .portal-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-bottom-color: #3b82f6;
            color: #3b82f6;
        }

        .tab-content {
            display: none;
            flex: 1;
        }

        .tab-content.active {
            display: flex;
            flex-direction: column;
        }

        .project-status {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }

        .status-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .milestone-timeline {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .milestone-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            position: relative;
        }

        .milestone-item::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 0;
            bottom: 0;
            width: 4px;
            border-radius: 2px;
        }

        .milestone-item.completed::before { background: #10b981; }
        .milestone-item.current::before { background: #3b82f6; }
        .milestone-item.upcoming::before { background: #e5e7eb; }

        .milestone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .milestone-name {
            font-weight: 600;
            color: #1f2937;
        }

        .milestone-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-completed { background: #d1fae5; color: #065f46; }
        .status-current { background: #dbeafe; color: #1e40af; }
        .status-upcoming { background: #f3f4f6; color: #6b7280; }

        .milestone-description {
            color: #6b7280;
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }

        .milestone-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .progress-bar {
            flex: 1;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
            margin: 0 1rem;
        }

        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-completed { background: #10b981; }
        .progress-current { background: #3b82f6; }

        .feedback-form {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .form-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #3b82f6;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .rating-group {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .rating-star {
            font-size: 1.5rem;
            color: #d1d5db;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .rating-star.active {
            color: #fbbf24;
        }

        .rating-star:hover {
            color: #f59e0b;
        }

        .submit-btn {
            background: #3b82f6;
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        .feedback-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .recent-feedback {
            margin-bottom: 2rem;
        }

        .feedback-item {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
        }

        .feedback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .feedback-type {
            font-weight: 500;
            color: #1f2937;
            font-size: 0.9rem;
        }

        .feedback-date {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .feedback-rating {
            display: flex;
            gap: 0.2rem;
            margin-bottom: 0.5rem;
        }

        .feedback-star {
            font-size: 0.8rem;
            color: #fbbf24;
        }

        .feedback-text {
            font-size: 0.8rem;
            color: #6b7280;
            line-height: 1.4;
        }

        .satisfaction-meter {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .meter-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            text-align: center;
        }

        .meter-display {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        .meter-circle {
            transform: rotate(-90deg);
        }

        .meter-bg {
            fill: none;
            stroke: #e5e7eb;
            stroke-width: 8;
        }

        .meter-progress {
            fill: none;
            stroke: #10b981;
            stroke-width: 8;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }

        .meter-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .meter-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #10b981;
        }

        .meter-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 300px 1fr;
            }

            .feedback-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .client-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Client Portal & Feedback Manager - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>👤 Client Portal & Feedback Manager</h2>
                <div class="client-indicator">
                    😊 Spokojnosť Klienta: 92%
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="scheduleClientMeeting()">📅 Naplánovať Meeting</button>
                    <button class="btn btn-primary" onclick="generateClientReport()">📊 Client Report</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="client-sidebar">
                    <div class="sidebar-title">👤 Informácie o Klientovi</div>

                    <div class="client-info">
                        <div class="client-avatar">EM</div>
                        <div class="client-name">PhDr. Eva Marková</div>
                        <div class="client-company">TechnoShop s.r.o.</div>

                        <div class="client-contact">
                            <div class="contact-item">
                                <span>📧</span>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <span>📞</span>
                                <span>+*********** 456</span>
                            </div>
                            <div class="contact-item">
                                <span>🏢</span>
                                <span>Bratislava, Slovensko</span>
                            </div>
                            <div class="contact-item">
                                <span>🕒</span>
                                <span>GMT+1 (CET)</span>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">📊 Prehľad Projektu</div>

                    <div class="project-overview">
                        <div class="overview-title">E-shop Modernizácia</div>
                        <div class="overview-stats">
                            <div class="stat-item">
                                <div class="stat-value">65%</div>
                                <div class="stat-label">Dokončené</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">€39,200</div>
                                <div class="stat-label">Spotrebované</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">42</div>
                                <div class="stat-label">Dni Zostáva</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">5</div>
                                <div class="stat-label">Tím</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">📞 Posledný Kontakt</div>

                    <div style="background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e5e7eb;">
                        <div style="font-weight: 500; margin-bottom: 0.5rem;">Demo Session</div>
                        <div style="font-size: 0.9rem; color: #6b7280; margin-bottom: 0.5rem;">15.06.2024 - 14:00</div>
                        <div style="font-size: 0.8rem; color: #6b7280; line-height: 1.4;">
                            Prezentácia aktuálneho pokroku, pozitívny feedback na UI/UX dizajn.
                        </div>
                    </div>
                </div>

                <div class="main-portal">
                    <div class="portal-tabs">
                        <button class="tab-btn active" data-tab="status">📊 Status Projektu</button>
                        <button class="tab-btn" data-tab="feedback">💬 Feedback</button>
                        <button class="tab-btn" data-tab="documents">📄 Dokumenty</button>
                        <button class="tab-btn" data-tab="communication">📞 Komunikácia</button>
                    </div>

                    <!-- Status Tab -->
                    <div class="tab-content active" id="status">
                        <div class="project-status">
                            <div class="status-title">
                                🎯 Míľniky Projektu
                            </div>

                            <div class="milestone-timeline">
                                <div class="milestone-item completed">
                                    <div class="milestone-header">
                                        <div class="milestone-name">Analýza a Plánovanie</div>
                                        <div class="milestone-status status-completed">Dokončené</div>
                                    </div>
                                    <div class="milestone-description">
                                        Kompletná analýza požiadaviek, technická špecifikácia a projektový plán
                                    </div>
                                    <div class="milestone-progress">
                                        <span>01.05.2024</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill progress-completed" style="width: 100%;"></div>
                                        </div>
                                        <span>100%</span>
                                    </div>
                                </div>

                                <div class="milestone-item completed">
                                    <div class="milestone-header">
                                        <div class="milestone-name">UI/UX Dizajn</div>
                                        <div class="milestone-status status-completed">Dokončené</div>
                                    </div>
                                    <div class="milestone-description">
                                        Wireframes, mockupy a finálny dizajn všetkých stránok e-shopu
                                    </div>
                                    <div class="milestone-progress">
                                        <span>15.05.2024</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill progress-completed" style="width: 100%;"></div>
                                        </div>
                                        <span>100%</span>
                                    </div>
                                </div>

                                <div class="milestone-item current">
                                    <div class="milestone-header">
                                        <div class="milestone-name">Backend Development</div>
                                        <div class="milestone-status status-current">V Priebehu</div>
                                    </div>
                                    <div class="milestone-description">
                                        API endpoints, databáza, autentifikácia a integrácie s externými službami
                                    </div>
                                    <div class="milestone-progress">
                                        <span>01.06.2024</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill progress-current" style="width: 75%;"></div>
                                        </div>
                                        <span>75%</span>
                                    </div>
                                </div>

                                <div class="milestone-item current">
                                    <div class="milestone-header">
                                        <div class="milestone-name">Frontend Development</div>
                                        <div class="milestone-status status-current">V Priebehu</div>
                                    </div>
                                    <div class="milestone-description">
                                        Implementácia používateľského rozhrania a integrácia s backend API
                                    </div>
                                    <div class="milestone-progress">
                                        <span>10.06.2024</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill progress-current" style="width: 45%;"></div>
                                        </div>
                                        <span>45%</span>
                                    </div>
                                </div>

                                <div class="milestone-item upcoming">
                                    <div class="milestone-header">
                                        <div class="milestone-name">Testovanie a QA</div>
                                        <div class="milestone-status status-upcoming">Plánované</div>
                                    </div>
                                    <div class="milestone-description">
                                        Komplexné testovanie funkcionalít, performance a bezpečnosti
                                    </div>
                                    <div class="milestone-progress">
                                        <span>15.07.2024</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%;"></div>
                                        </div>
                                        <span>0%</span>
                                    </div>
                                </div>

                                <div class="milestone-item upcoming">
                                    <div class="milestone-header">
                                        <div class="milestone-name">Deployment a Launch</div>
                                        <div class="milestone-status status-upcoming">Plánované</div>
                                    </div>
                                    <div class="milestone-description">
                                        Nasadenie do produkcie, migrácia dát a oficiálne spustenie
                                    </div>
                                    <div class="milestone-progress">
                                        <span>31.08.2024</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%;"></div>
                                        </div>
                                        <span>0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feedback Tab -->
                    <div class="tab-content" id="feedback">
                        <div class="feedback-form">
                            <div class="form-title">💬 Váš Feedback</div>

                            <div class="form-group">
                                <label class="form-label">Typ Feedbacku</label>
                                <select class="form-select" id="feedbackType">
                                    <option value="general">Všeobecný feedback</option>
                                    <option value="feature">Požiadavka na funkciu</option>
                                    <option value="bug">Nahlásenie problému</option>
                                    <option value="design">Pripomienky k dizajnu</option>
                                    <option value="performance">Výkonnosť</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Hodnotenie Pokroku</label>
                                <div class="rating-group" id="progressRating">
                                    <span class="rating-star" data-rating="1">★</span>
                                    <span class="rating-star" data-rating="2">★</span>
                                    <span class="rating-star" data-rating="3">★</span>
                                    <span class="rating-star" data-rating="4">★</span>
                                    <span class="rating-star" data-rating="5">★</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Predmet</label>
                                <input type="text" class="form-input" id="feedbackSubject" placeholder="Stručný popis vášho feedbacku">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Detailný Popis</label>
                                <textarea class="form-textarea" id="feedbackDescription" placeholder="Popíšte detailne váš feedback, návrhy alebo pripomienky..."></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Priorita</label>
                                <select class="form-select" id="feedbackPriority">
                                    <option value="low">Nízka</option>
                                    <option value="medium" selected>Stredná</option>
                                    <option value="high">Vysoká</option>
                                    <option value="urgent">Urgentná</option>
                                </select>
                            </div>

                            <button class="submit-btn" onclick="submitFeedback()">📤 Odoslať Feedback</button>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div class="tab-content" id="documents">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>📄 Projektové Dokumenty</h3>
                            <p style="margin: 1rem 0;">Prístup k všetkým projektovým dokumentom a deliverables:</p>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;">
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="openDocument('spec')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📋</div>
                                    <div style="font-weight: 600;">Technická Špecifikácia</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Aktualizované: 10.06.2024</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="openDocument('design')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎨</div>
                                    <div style="font-weight: 600;">UI/UX Mockupy</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Aktualizované: 15.05.2024</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="openDocument('api')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔌</div>
                                    <div style="font-weight: 600;">API Dokumentácia</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Aktualizované: 18.06.2024</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="openDocument('testing')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🧪</div>
                                    <div style="font-weight: 600;">Test Plán</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Aktualizované: 12.06.2024</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Communication Tab -->
                    <div class="tab-content" id="communication">
                        <div style="padding: 2rem;">
                            <h3 style="margin-bottom: 2rem;">📞 Komunikačná História</h3>

                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <div style="font-weight: 600;">Demo Session</div>
                                        <div style="font-size: 0.8rem; color: #6b7280;">15.06.2024 14:00</div>
                                    </div>
                                    <div style="color: #6b7280; margin-bottom: 0.5rem;">Prezentácia aktuálneho pokroku</div>
                                    <div style="font-size: 0.8rem; color: #10b981;">✅ Pozitívny feedback na UI dizajn</div>
                                </div>

                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <div style="font-weight: 600;">Weekly Status Call</div>
                                        <div style="font-size: 0.8rem; color: #6b7280;">10.06.2024 10:00</div>
                                    </div>
                                    <div style="color: #6b7280; margin-bottom: 0.5rem;">Týždenný update o pokroku</div>
                                    <div style="font-size: 0.8rem; color: #3b82f6;">📋 Diskusia o API integrácii</div>
                                </div>

                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <div style="font-weight: 600;">Kick-off Meeting</div>
                                        <div style="font-size: 0.8rem; color: #6b7280;">01.05.2024 09:00</div>
                                    </div>
                                    <div style="color: #6b7280; margin-bottom: 0.5rem;">Úvodné stretnutie projektu</div>
                                    <div style="font-size: 0.8rem; color: #10b981;">🚀 Projekt oficiálne spustený</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feedback-sidebar">
                    <div class="sidebar-title">💬 Nedávny Feedback</div>

                    <div class="recent-feedback">
                        <div class="feedback-item">
                            <div class="feedback-header">
                                <div class="feedback-type">UI Dizajn</div>
                                <div class="feedback-date">15.06.2024</div>
                            </div>
                            <div class="feedback-rating">
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                            </div>
                            <div class="feedback-text">
                                Výborný dizajn, veľmi moderný a používateľsky prívetivý.
                            </div>
                        </div>

                        <div class="feedback-item">
                            <div class="feedback-header">
                                <div class="feedback-type">Funkcionalita</div>
                                <div class="feedback-date">10.06.2024</div>
                            </div>
                            <div class="feedback-rating">
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span style="color: #d1d5db;">★</span>
                            </div>
                            <div class="feedback-text">
                                Potrebujeme pridať možnosť bulk editácie produktov.
                            </div>
                        </div>

                        <div class="feedback-item">
                            <div class="feedback-header">
                                <div class="feedback-type">Performance</div>
                                <div class="feedback-date">05.06.2024</div>
                            </div>
                            <div class="feedback-rating">
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                                <span class="feedback-star">★</span>
                            </div>
                            <div class="feedback-text">
                                Rýchlosť načítania stránok je výrazne lepšia.
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">😊 Spokojnosť</div>

                    <div class="satisfaction-meter">
                        <div class="meter-title">Celková Spokojnosť</div>
                        <div class="meter-display">
                            <svg class="meter-circle" width="120" height="120">
                                <circle class="meter-bg" cx="60" cy="60" r="50"></circle>
                                <circle class="meter-progress" cx="60" cy="60" r="50"
                                        stroke-dasharray="289 314"></circle>
                            </svg>
                            <div class="meter-text">
                                <div class="meter-value">92%</div>
                                <div class="meter-label">Spokojnosť</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentRating = 0;

        function initPMComponent() {
            setupTabs();
            setupRatingSystem();
            updateSatisfactionMeter();
        }

        function setupTabs() {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                    this.classList.add('active');
                    document.getElementById(this.dataset.tab).classList.add('active');
                });
            });
        }

        function setupRatingSystem() {
            document.querySelectorAll('.rating-star').forEach(star => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    currentRating = rating;

                    document.querySelectorAll('.rating-star').forEach((s, index) => {
                        if (index < rating) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
            });
        }

        function updateSatisfactionMeter() {
            // Simulácia aktualizácie satisfaction meter
            const percentage = 92;
            const circumference = 2 * Math.PI * 50;
            const offset = circumference - (percentage / 100) * circumference;

            document.querySelector('.meter-progress').style.strokeDasharray = `${circumference - offset} ${circumference}`;
        }

        function submitFeedback() {
            const type = document.getElementById('feedbackType').value;
            const subject = document.getElementById('feedbackSubject').value;
            const description = document.getElementById('feedbackDescription').value;
            const priority = document.getElementById('feedbackPriority').value;

            if (!subject || !description) {
                alert('Prosím vyplňte predmet a popis feedbacku.');
                return;
            }

            alert(`📤 Feedback odoslaný!\n\nTyp: ${type}\nPredmet: ${subject}\nHodnotenie: ${currentRating}/5 ⭐\nPriorita: ${priority}\n\n✅ Váš feedback bol úspešne odoslaný\n📧 Potvrdenie odoslané na email\n⏰ Odpoveď očakávajte do 24 hodín\n🔔 Notifikácia odoslaná projektovému tímu`);

            // Vyčistenie formulára
            document.getElementById('feedbackSubject').value = '';
            document.getElementById('feedbackDescription').value = '';
            document.querySelectorAll('.rating-star').forEach(s => s.classList.remove('active'));
            currentRating = 0;
        }

        function scheduleClientMeeting() {
            alert('📅 Plánovanie Client Meetingu...\n\n• Dostupné termíny:\n  - Utorok 25.06.2024 14:00\n  - Streda 26.06.2024 10:00\n  - Piatok 28.06.2024 15:00\n\n• Typ meetingu: Demo Session\n• Trvanie: 60 minút\n• Platforma: Microsoft Teams\n\nPozvánka bude odoslaná po potvrdení termínu.');
        }

        function generateClientReport() {
            alert('📊 Generujem Client Report...\n\n• Project status summary: ✓\n• Milestone progress: ✓\n• Budget utilization: ✓\n• Team performance: ✓\n• Next steps: ✓\n\nReport obsahuje:\n✓ Executive summary\n✓ Visual progress charts\n✓ Detailed milestone breakdown\n✓ Risk assessment\n✓ Recommendations\n\nReport exportovaný do PDF a odoslaný na email!');
        }

        function openDocument(type) {
            const documents = {
                'spec': 'Technická Špecifikácia - E-shop Modernizácia\n\nObsahuje detailný popis všetkých funkcionalít, technických požiadaviek a implementačných detailov.',
                'design': 'UI/UX Mockupy - Všetky stránky e-shopu\n\nInteraktívne prototypy s kompletným user flow a responsive dizajnom.',
                'api': 'API Dokumentácia - REST endpoints\n\nKompletná dokumentácia všetkých API endpointov s príkladmi použitia.',
                'testing': 'Test Plán - QA stratégia\n\nDetailný plán testovania zahŕňajúci unit, integration a E2E testy.'
            };

            alert(`📄 Otváram dokument...\n\n${documents[type]}\n\n• Dokument sa otvorí v novom okne\n• Možnosť stiahnutia v PDF formáte\n• Posledná aktualizácia: Dnes\n• Prístup: Read-only`);
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>