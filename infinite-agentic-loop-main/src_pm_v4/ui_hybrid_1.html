<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brutalist Control Center - Hybrid UI Komponent</title>
    <meta name="description" content="Advanced hybrid UI komponent s architectural brutalism témou">

    <style>
        /* CSS Custom Properties pre Brutalist Theme */
        :root {
            --concrete-primary: #8B8680;
            --concrete-dark: #5A5751;
            --concrete-light: #B5B0A8;
            --concrete-accent: #D4CFC4;
            --steel-blue: #4A5568;
            --rust-orange: #ED8936;
            --warning-red: #E53E3E;
            --success-green: #38A169;

            --shadow-brutal: 8px 8px 0px rgba(0,0,0,0.3);
            --shadow-inset: inset 4px 4px 8px rgba(0,0,0,0.2);
            --border-thick: 4px solid var(--concrete-dark);
            --border-accent: 2px solid var(--rust-orange);

            --font-mono: 'Courier New', monospace;
            --font-sans: 'Aria<PERSON>', Arial, sans-serif;

            --transition-brutal: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --animation-duration: 0.3s;
        }

        /* Reset a Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-sans);
            background: linear-gradient(135deg, var(--concrete-primary) 0%, var(--concrete-dark) 100%);
            color: var(--concrete-accent);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Brutalist Background Texture */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(0,0,0,0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 20px 20px, 15px 15px;
            pointer-events: none;
            z-index: -1;
        }

        /* Main Container */
        .brutalist-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            grid-template-rows: auto 1fr auto;
            grid-template-areas:
                "nav header controls"
                "nav main controls"
                "nav footer controls";
            gap: 2rem;
            min-height: 100vh;
        }

        /* Navigation Panel */
        .nav-panel {
            grid-area: nav;
            background: var(--concrete-dark);
            border: var(--border-thick);
            box-shadow: var(--shadow-brutal);
            position: relative;
            overflow: hidden;
        }

        .nav-header {
            background: var(--steel-blue);
            color: white;
            padding: 1.5rem;
            font-size: 1.2rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 2px;
            border-bottom: var(--border-accent);
            position: relative;
        }

        .nav-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--rust-orange), transparent);
        }

        .nav-menu {
            list-style: none;
            padding: 0;
        }

        .nav-item {
            border-bottom: 2px solid var(--concrete-primary);
            position: relative;
            overflow: hidden;
        }

        .nav-link {
            display: block;
            padding: 1.2rem 1.5rem;
            color: var(--concrete-accent);
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: var(--transition-brutal);
            position: relative;
            z-index: 1;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--rust-orange);
            transition: var(--transition-brutal);
            z-index: -1;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            left: 0;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            transform: translateX(8px);
        }

        /* Header Section */
        .header-section {
            grid-area: header;
            background: var(--concrete-light);
            border: var(--border-thick);
            box-shadow: var(--shadow-brutal);
            padding: 2rem;
            position: relative;
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--concrete-dark);
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.1);
        }

        .header-subtitle {
            font-size: 1.1rem;
            color: var(--steel-blue);
            font-family: var(--font-mono);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Main Content Area */
        .main-content {
            grid-area: main;
            background: var(--concrete-accent);
            border: var(--border-thick);
            box-shadow: var(--shadow-brutal);
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        /* Data Display Grid */
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .data-card {
            background: var(--concrete-primary);
            border: 3px solid var(--concrete-dark);
            padding: 1.5rem;
            position: relative;
            transition: var(--transition-brutal);
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: var(--rust-orange);
            z-index: -1;
            opacity: 0;
            transition: var(--transition-brutal);
        }

        .data-card:hover::before {
            opacity: 1;
        }

        .data-card:hover {
            transform: translate(-4px, -4px);
            color: white;
        }

        .data-label {
            font-size: 0.9rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
            color: var(--steel-blue);
        }

        .data-value {
            font-size: 2rem;
            font-weight: 900;
            font-family: var(--font-mono);
            color: var(--concrete-dark);
        }

        /* Form Elements */
        .form-section {
            background: var(--concrete-dark);
            padding: 2rem;
            margin: 2rem 0;
            border: var(--border-accent);
            position: relative;
        }

        .form-title {
            color: var(--concrete-accent);
            font-size: 1.3rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--rust-orange);
            padding-bottom: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
            color: var(--concrete-accent);
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 3px solid var(--concrete-primary);
            background: var(--concrete-light);
            color: var(--concrete-dark);
            font-family: var(--font-mono);
            font-weight: 700;
            font-size: 1rem;
            transition: var(--transition-brutal);
            outline: none;
        }

        .form-input:focus {
            border-color: var(--rust-orange);
            box-shadow: var(--shadow-brutal);
            transform: translate(-2px, -2px);
        }

        .form-button {
            background: var(--steel-blue);
            color: white;
            border: 3px solid var(--concrete-dark);
            padding: 1rem 2rem;
            font-family: var(--font-sans);
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 2px;
            cursor: pointer;
            transition: var(--transition-brutal);
            position: relative;
            overflow: hidden;
        }

        .form-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--rust-orange);
            transition: var(--transition-brutal);
            z-index: -1;
        }

        .form-button:hover::before {
            left: 0;
        }

        .form-button:hover {
            transform: translate(-4px, -4px);
            box-shadow: var(--shadow-brutal);
        }

        .form-button:active {
            transform: translate(0, 0);
            box-shadow: none;
        }

        /* Controls Panel */
        .controls-panel {
            grid-area: controls;
            background: var(--concrete-dark);
            border: var(--border-thick);
            box-shadow: var(--shadow-brutal);
            padding: 2rem;
            position: relative;
        }

        .controls-title {
            color: var(--concrete-accent);
            font-size: 1.3rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 2rem;
            text-align: center;
            border-bottom: 2px solid var(--rust-orange);
            padding-bottom: 1rem;
        }

        /* Media Controls */
        .media-controls {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: var(--concrete-primary);
            border: 2px solid var(--steel-blue);
        }

        .media-title {
            font-size: 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
            color: var(--concrete-dark);
        }

        .media-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .media-btn {
            width: 50px;
            height: 50px;
            background: var(--steel-blue);
            border: 3px solid var(--concrete-dark);
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition-brutal);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .media-btn:hover {
            background: var(--rust-orange);
            transform: translate(-2px, -2px);
            box-shadow: 4px 4px 0px rgba(0,0,0,0.3);
        }

        .media-btn:active {
            transform: translate(0, 0);
            box-shadow: none;
        }

        .media-btn.playing {
            background: var(--success-green);
        }

        /* Volume Control */
        .volume-control {
            margin-top: 1rem;
        }

        .volume-slider {
            width: 100%;
            height: 8px;
            background: var(--concrete-light);
            border: 2px solid var(--concrete-dark);
            outline: none;
            cursor: pointer;
            position: relative;
        }

        .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--rust-orange);
            border: 2px solid var(--concrete-dark);
            cursor: pointer;
        }

        .volume-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--rust-orange);
            border: 2px solid var(--concrete-dark);
            cursor: pointer;
            border-radius: 0;
        }

        /* Interactive Widgets */
        .widget-section {
            margin-bottom: 2rem;
        }

        .widget-title {
            font-size: 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
            color: var(--concrete-accent);
        }

        /* Toggle Switch */
        .toggle-group {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .toggle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--concrete-primary);
            border: 2px solid var(--steel-blue);
        }

        .toggle-label {
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--concrete-dark);
            font-size: 0.9rem;
        }

        .toggle-switch {
            width: 60px;
            height: 30px;
            background: var(--concrete-light);
            border: 3px solid var(--concrete-dark);
            position: relative;
            cursor: pointer;
            transition: var(--transition-brutal);
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 20px;
            height: 20px;
            background: var(--steel-blue);
            transition: var(--transition-brutal);
        }

        .toggle-switch.active {
            background: var(--success-green);
        }

        .toggle-switch.active::before {
            left: 33px;
            background: white;
        }

        /* Progress Indicators */
        .progress-section {
            margin-bottom: 2rem;
        }

        .progress-item {
            margin-bottom: 1rem;
        }

        .progress-label {
            font-size: 0.9rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
            color: var(--concrete-accent);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--concrete-light);
            border: 3px solid var(--concrete-dark);
            position: relative;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--rust-orange), var(--success-green));
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 5px,
                rgba(255,255,255,0.1) 5px,
                rgba(255,255,255,0.1) 10px
            );
            animation: progress-stripes 1s linear infinite;
        }

        @keyframes progress-stripes {
            0% { transform: translateX(0); }
            100% { transform: translateX(20px); }
        }

        /* Footer */
        .footer-section {
            grid-area: footer;
            background: var(--steel-blue);
            border: var(--border-thick);
            box-shadow: var(--shadow-brutal);
            padding: 1.5rem 2rem;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-info {
            font-family: var(--font-mono);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .footer-status {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            background: var(--success-green);
            border: 2px solid white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .brutalist-container {
                grid-template-columns: 250px 1fr 300px;
                gap: 1.5rem;
            }
        }

        @media (max-width: 992px) {
            .brutalist-container {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "nav"
                    "main"
                    "controls"
                    "footer";
                padding: 1rem;
            }

            .nav-panel,
            .controls-panel {
                max-height: none;
            }

            .data-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .header-title {
                font-size: 2rem;
            }

            .data-grid {
                grid-template-columns: 1fr;
            }

            .media-buttons {
                flex-wrap: wrap;
            }

            .footer-section {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus Styles */
        .nav-link:focus,
        .form-input:focus,
        .form-button:focus,
        .media-btn:focus,
        .toggle-switch:focus {
            outline: 3px solid var(--rust-orange);
            outline-offset: 2px;
        }

        /* High Contrast Mode */
        @media (prefers-contrast: high) {
            :root {
                --concrete-primary: #000000;
                --concrete-dark: #ffffff;
                --concrete-light: #ffffff;
                --concrete-accent: #000000;
                --steel-blue: #0000ff;
                --rust-orange: #ff6600;
            }
        }
    </style>
</head>
<body>
    <!-- Main Container -->
    <div class="brutalist-container">
        <!-- Navigation Panel -->
        <nav class="nav-panel" role="navigation" aria-label="Hlavná navigácia">
            <div class="nav-header">
                <span role="img" aria-label="Ovládací panel">⚡</span> Control Center
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-section="dashboard"
                       aria-current="page">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="#analytics" class="nav-link" data-section="analytics">Analytics</a>
                </li>
                <li class="nav-item">
                    <a href="#monitoring" class="nav-link" data-section="monitoring">Monitoring</a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link" data-section="settings">Settings</a>
                </li>
                <li class="nav-item">
                    <a href="#reports" class="nav-link" data-section="reports">Reports</a>
                </li>
                <li class="nav-item">
                    <a href="#security" class="nav-link" data-section="security">Security</a>
                </li>
            </ul>
        </nav>

        <!-- Header Section -->
        <header class="header-section" role="banner">
            <h1 class="header-title">Brutalist Interface</h1>
            <p class="header-subtitle">Advanced Control System v2.1</p>
        </header>

        <!-- Main Content -->
        <main class="main-content" role="main">
            <!-- Data Display Grid -->
            <section class="data-grid" aria-label="Systémové metriky">
                <div class="data-card" tabindex="0" role="region" aria-label="CPU využitie">
                    <div class="data-label">CPU Usage</div>
                    <div class="data-value" id="cpu-value">67%</div>
                </div>
                <div class="data-card" tabindex="0" role="region" aria-label="Pamäť">
                    <div class="data-label">Memory</div>
                    <div class="data-value" id="memory-value">4.2GB</div>
                </div>
                <div class="data-card" tabindex="0" role="region" aria-label="Sieť">
                    <div class="data-label">Network</div>
                    <div class="data-value" id="network-value">125MB/s</div>
                </div>
                <div class="data-card" tabindex="0" role="region" aria-label="Úložisko">
                    <div class="data-label">Storage</div>
                    <div class="data-value" id="storage-value">78%</div>
                </div>
            </section>

            <!-- Form Section -->
            <section class="form-section" aria-label="Konfiguračný formulár">
                <h2 class="form-title">System Configuration</h2>
                <form id="config-form" novalidate>
                    <div class="form-group">
                        <label for="server-name" class="form-label">Server Name</label>
                        <input type="text" id="server-name" class="form-input"
                               value="BRUTALIST-SRV-01" required
                               aria-describedby="server-name-help">
                        <div id="server-name-help" class="sr-only">
                            Zadajte názov servera (povinné pole)
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="max-connections" class="form-label">Max Connections</label>
                        <input type="number" id="max-connections" class="form-input"
                               value="1000" min="1" max="10000" required>
                    </div>
                    <div class="form-group">
                        <label for="timeout" class="form-label">Timeout (seconds)</label>
                        <input type="number" id="timeout" class="form-input"
                               value="30" min="1" max="300" required>
                    </div>
                    <button type="submit" class="form-button">
                        Apply Configuration
                    </button>
                </form>
            </section>
        </main>

        <!-- Controls Panel -->
        <aside class="controls-panel" role="complementary" aria-label="Ovládacie prvky">
            <h2 class="controls-title">Controls</h2>

            <!-- Media Controls -->
            <section class="media-controls" aria-label="Mediálne ovládanie">
                <h3 class="media-title">Media Player</h3>
                <div class="media-buttons" role="group" aria-label="Ovládanie prehrávania">
                    <button class="media-btn" id="prev-btn" aria-label="Predchádzajúci">
                        <span aria-hidden="true">⏮</span>
                    </button>
                    <button class="media-btn" id="play-btn" aria-label="Prehrať/Pozastaviť">
                        <span aria-hidden="true">▶</span>
                    </button>
                    <button class="media-btn" id="next-btn" aria-label="Nasledujúci">
                        <span aria-hidden="true">⏭</span>
                    </button>
                    <button class="media-btn" id="stop-btn" aria-label="Zastaviť">
                        <span aria-hidden="true">⏹</span>
                    </button>
                </div>
                <div class="volume-control">
                    <label for="volume-slider" class="form-label">Volume</label>
                    <input type="range" id="volume-slider" class="volume-slider"
                           min="0" max="100" value="75"
                           aria-label="Hlasitosť" aria-valuemin="0" aria-valuemax="100" aria-valuenow="75">
                </div>
            </section>

            <!-- Interactive Widgets -->
            <section class="widget-section" aria-label="Interaktívne widgety">
                <h3 class="widget-title">System Toggles</h3>
                <div class="toggle-group" role="group" aria-label="Systémové prepínače">
                    <div class="toggle-item">
                        <span class="toggle-label">Auto Backup</span>
                        <button class="toggle-switch active" role="switch"
                                aria-checked="true" aria-label="Automatické zálohovanie"
                                data-toggle="auto-backup">
                        </button>
                    </div>
                    <div class="toggle-item">
                        <span class="toggle-label">Debug Mode</span>
                        <button class="toggle-switch" role="switch"
                                aria-checked="false" aria-label="Debug režim"
                                data-toggle="debug-mode">
                        </button>
                    </div>
                    <div class="toggle-item">
                        <span class="toggle-label">Notifications</span>
                        <button class="toggle-switch active" role="switch"
                                aria-checked="true" aria-label="Notifikácie"
                                data-toggle="notifications">
                        </button>
                    </div>
                    <div class="toggle-item">
                        <span class="toggle-label">Maintenance</span>
                        <button class="toggle-switch" role="switch"
                                aria-checked="false" aria-label="Údržbový režim"
                                data-toggle="maintenance">
                        </button>
                    </div>
                </div>
            </section>

            <!-- Progress Indicators -->
            <section class="progress-section" aria-label="Indikátory priebehu">
                <h3 class="widget-title">System Status</h3>
                <div class="progress-item">
                    <div class="progress-label">Database Sync</div>
                    <div class="progress-bar" role="progressbar" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                </div>
                <div class="progress-item">
                    <div class="progress-label">Cache Optimization</div>
                    <div class="progress-bar" role="progressbar" aria-valuenow="62" aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-fill" style="width: 62%"></div>
                    </div>
                </div>
                <div class="progress-item">
                    <div class="progress-label">Security Scan</div>
                    <div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-fill" style="width: 100%"></div>
                    </div>
                </div>
            </section>
        </aside>

        <!-- Footer -->
        <footer class="footer-section" role="contentinfo">
            <div class="footer-info">
                Brutalist Control Center © 2024
            </div>
            <div class="footer-status">
                <div class="status-indicator" aria-label="Systém online"></div>
                <span>System Online</span>
            </div>
        </footer>
    </div>

    <!-- Screen Reader Only Content -->
    <div class="sr-only">
        <h1>Brutalist Control Center - Prístupný ovládací panel</h1>
        <p>Tento ovládací panel poskytuje kompletné ovládanie systému s podporou klávesnice a čítačiek obrazovky.</p>
    </div>

    <script>
        // Brutalist Control Center JavaScript
        class BrutalistController {
            constructor() {
                this.isPlaying = false;
                this.currentSection = 'dashboard';
                this.systemData = {
                    cpu: 67,
                    memory: 4.2,
                    network: 125,
                    storage: 78
                };

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.startDataUpdates();
                this.setupKeyboardNavigation();
                this.setupIntersectionObserver();
                this.announceToScreenReader('Brutalist Control Center načítaný');
            }

            setupEventListeners() {
                // Navigation
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchSection(e.target.dataset.section);
                    });
                });

                // Media Controls
                document.getElementById('play-btn').addEventListener('click', () => {
                    this.togglePlayback();
                });

                document.getElementById('prev-btn').addEventListener('click', () => {
                    this.previousTrack();
                });

                document.getElementById('next-btn').addEventListener('click', () => {
                    this.nextTrack();
                });

                document.getElementById('stop-btn').addEventListener('click', () => {
                    this.stopPlayback();
                });

                // Volume Control
                const volumeSlider = document.getElementById('volume-slider');
                volumeSlider.addEventListener('input', (e) => {
                    this.updateVolume(e.target.value);
                });

                // Toggle Switches
                document.querySelectorAll('.toggle-switch').forEach(toggle => {
                    toggle.addEventListener('click', (e) => {
                        this.toggleSwitch(e.target);
                    });
                });

                // Form Submission
                document.getElementById('config-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.submitConfiguration();
                });

                // Data Cards Interaction
                document.querySelectorAll('.data-card').forEach(card => {
                    card.addEventListener('click', () => {
                        this.showDetailedMetrics(card);
                    });
                });
            }

            switchSection(section) {
                // Update navigation
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                    link.removeAttribute('aria-current');
                });

                const activeLink = document.querySelector(`[data-section="${section}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                    activeLink.setAttribute('aria-current', 'page');
                }

                this.currentSection = section;
                this.announceToScreenReader(`Prepnuté na sekciu ${section}`);

                // Simulate section content change
                this.updateSectionContent(section);
            }

            updateSectionContent(section) {
                const title = document.querySelector('.header-title');
                const subtitle = document.querySelector('.header-subtitle');

                const sectionData = {
                    dashboard: {
                        title: 'Brutalist Interface',
                        subtitle: 'Advanced Control System v2.1'
                    },
                    analytics: {
                        title: 'Analytics Center',
                        subtitle: 'Data Analysis & Insights'
                    },
                    monitoring: {
                        title: 'System Monitor',
                        subtitle: 'Real-time Performance Tracking'
                    },
                    settings: {
                        title: 'Configuration',
                        subtitle: 'System Settings & Preferences'
                    },
                    reports: {
                        title: 'Report Generator',
                        subtitle: 'Automated Reporting System'
                    },
                    security: {
                        title: 'Security Center',
                        subtitle: 'Access Control & Monitoring'
                    }
                };

                if (sectionData[section]) {
                    title.textContent = sectionData[section].title;
                    subtitle.textContent = sectionData[section].subtitle;
                }
            }

            togglePlayback() {
                const playBtn = document.getElementById('play-btn');
                const icon = playBtn.querySelector('span');

                this.isPlaying = !this.isPlaying;

                if (this.isPlaying) {
                    icon.textContent = '⏸';
                    playBtn.classList.add('playing');
                    playBtn.setAttribute('aria-label', 'Pozastaviť');
                    this.announceToScreenReader('Prehrávanie spustené');
                } else {
                    icon.textContent = '▶';
                    playBtn.classList.remove('playing');
                    playBtn.setAttribute('aria-label', 'Prehrať');
                    this.announceToScreenReader('Prehrávanie pozastavené');
                }
            }

            previousTrack() {
                this.announceToScreenReader('Predchádzajúca stopa');
                // Simulate track change
                this.showNotification('Previous Track', 'success');
            }

            nextTrack() {
                this.announceToScreenReader('Nasledujúca stopa');
                // Simulate track change
                this.showNotification('Next Track', 'success');
            }

            stopPlayback() {
                this.isPlaying = false;
                const playBtn = document.getElementById('play-btn');
                const icon = playBtn.querySelector('span');

                icon.textContent = '▶';
                playBtn.classList.remove('playing');
                playBtn.setAttribute('aria-label', 'Prehrať');
                this.announceToScreenReader('Prehrávanie zastavené');
            }

            updateVolume(value) {
                const slider = document.getElementById('volume-slider');
                slider.setAttribute('aria-valuenow', value);
                this.announceToScreenReader(`Hlasitosť nastavená na ${value}%`);
            }

            toggleSwitch(toggle) {
                const isActive = toggle.classList.contains('active');
                const newState = !isActive;

                toggle.classList.toggle('active');
                toggle.setAttribute('aria-checked', newState.toString());

                const toggleType = toggle.dataset.toggle;
                const action = newState ? 'zapnuté' : 'vypnuté';

                this.announceToScreenReader(`${toggleType} ${action}`);
                this.showNotification(`${toggleType} ${action}`, newState ? 'success' : 'warning');
            }

            submitConfiguration() {
                const form = document.getElementById('config-form');
                const formData = new FormData(form);

                // Simulate form submission
                this.showNotification('Configuration Updated', 'success');
                this.announceToScreenReader('Konfigurácia bola úspešne uložená');

                // Add visual feedback
                const submitBtn = form.querySelector('.form-button');
                const originalText = submitBtn.textContent;

                submitBtn.textContent = 'Saving...';
                submitBtn.disabled = true;

                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            }

            showDetailedMetrics(card) {
                const label = card.querySelector('.data-label').textContent;
                const value = card.querySelector('.data-value').textContent;

                this.announceToScreenReader(`Detailné metriky pre ${label}: ${value}`);
                this.showNotification(`${label}: ${value}`, 'info');
            }

            startDataUpdates() {
                setInterval(() => {
                    this.updateSystemData();
                }, 3000);
            }

            updateSystemData() {
                // Simulate real-time data updates
                this.systemData.cpu = Math.max(10, Math.min(95, this.systemData.cpu + (Math.random() - 0.5) * 10));
                this.systemData.memory = Math.max(1, Math.min(8, this.systemData.memory + (Math.random() - 0.5) * 0.5));
                this.systemData.network = Math.max(50, Math.min(200, this.systemData.network + (Math.random() - 0.5) * 20));
                this.systemData.storage = Math.max(50, Math.min(95, this.systemData.storage + (Math.random() - 0.5) * 5));

                // Update UI
                document.getElementById('cpu-value').textContent = `${Math.round(this.systemData.cpu)}%`;
                document.getElementById('memory-value').textContent = `${this.systemData.memory.toFixed(1)}GB`;
                document.getElementById('network-value').textContent = `${Math.round(this.systemData.network)}MB/s`;
                document.getElementById('storage-value').textContent = `${Math.round(this.systemData.storage)}%`;

                // Update progress bars
                this.updateProgressBars();
            }

            updateProgressBars() {
                const progressBars = document.querySelectorAll('.progress-bar');
                progressBars.forEach((bar, index) => {
                    const fill = bar.querySelector('.progress-fill');
                    const currentWidth = parseInt(fill.style.width);
                    const newWidth = Math.max(10, Math.min(100, currentWidth + (Math.random() - 0.5) * 10));

                    fill.style.width = `${newWidth}%`;
                    bar.setAttribute('aria-valuenow', newWidth);
                });
            }

            setupKeyboardNavigation() {
                // Enhanced keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.altKey && e.key >= '1' && e.key <= '6') {
                        e.preventDefault();
                        const sections = ['dashboard', 'analytics', 'monitoring', 'settings', 'reports', 'security'];
                        const sectionIndex = parseInt(e.key) - 1;
                        if (sections[sectionIndex]) {
                            this.switchSection(sections[sectionIndex]);
                        }
                    }

                    // Space bar for play/pause
                    if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'BUTTON') {
                        e.preventDefault();
                        this.togglePlayback();
                    }
                });

                // Focus management
                document.querySelectorAll('.data-card').forEach(card => {
                    card.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.showDetailedMetrics(card);
                        }
                    });
                });
            }

            setupIntersectionObserver() {
                // Lazy loading and scroll animations
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('fade-in');
                        }
                    });
                }, { threshold: 0.1 });

                document.querySelectorAll('.data-card, .form-section, .widget-section').forEach(el => {
                    observer.observe(el);
                });
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 2rem;
                    right: 2rem;
                    background: var(--${type === 'success' ? 'success-green' : type === 'warning' ? 'warning-red' : 'steel-blue'});
                    color: white;
                    padding: 1rem 2rem;
                    border: 3px solid var(--concrete-dark);
                    box-shadow: var(--shadow-brutal);
                    font-weight: 700;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    z-index: 10000;
                    animation: slideIn 0.3s ease;
                `;

                document.body.appendChild(notification);

                // Remove after 3 seconds
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            announceToScreenReader(message) {
                // Create live region for screen reader announcements
                let liveRegion = document.getElementById('live-region');
                if (!liveRegion) {
                    liveRegion = document.createElement('div');
                    liveRegion.id = 'live-region';
                    liveRegion.setAttribute('aria-live', 'polite');
                    liveRegion.setAttribute('aria-atomic', 'true');
                    liveRegion.style.cssText = `
                        position: absolute;
                        left: -10000px;
                        width: 1px;
                        height: 1px;
                        overflow: hidden;
                    `;
                    document.body.appendChild(liveRegion);
                }

                liveRegion.textContent = message;
            }
        }

        // Additional CSS animations
        const additionalStyles = document.createElement('style');
        additionalStyles.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .sr-only {
                position: absolute;
                left: -10000px;
                width: 1px;
                height: 1px;
                overflow: hidden;
            }

            /* Enhanced focus indicators */
            .data-card:focus {
                outline: 3px solid var(--rust-orange);
                outline-offset: 2px;
                transform: translate(-2px, -2px);
            }

            /* Loading states */
            .form-button:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none !important;
            }

            /* High contrast improvements */
            @media (prefers-contrast: high) {
                .data-card,
                .form-section,
                .media-controls,
                .widget-section {
                    border-width: 4px;
                }
            }
        `;
        document.head.appendChild(additionalStyles);

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            const controller = new BrutalistController();

            // Add global error handling
            window.addEventListener('error', (e) => {
                console.error('Application error:', e.error);
                controller.showNotification('System Error Detected', 'warning');
            });

            // Performance monitoring
            if ('performance' in window) {
                window.addEventListener('load', () => {
                    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                    console.log(`Brutalist Control Center loaded in ${loadTime}ms`);
                });
            }

            // Service Worker registration (if available)
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js').catch(err => {
                    console.log('Service Worker registration failed:', err);
                });
            }
        });
    </script>
</body>
</html>