{"name": "pm-master-system-v4", "version": "4.0.0", "description": "PM Master System v4 - Simplified for quick start", "main": "index.html", "scripts": {"start": "npm run serve", "serve": "http-server . -p 8000 -c-1 --cors", "dev": "concurrently \"npm run serve\" \"npm run api:dev\"", "api:dev": "cd backend && node server.js", "api:start": "cd backend && node server.js", "install-backend": "cd backend && npm install"}, "dependencies": {"http-server": "^14.1.1", "concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}