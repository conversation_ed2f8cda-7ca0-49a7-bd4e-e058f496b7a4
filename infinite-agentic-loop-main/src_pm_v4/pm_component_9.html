<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Analyzer & Metrics Dashboard - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #312e81 0%, #1e1b4b 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .performance-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
            padding: 2rem;
        }

        .metrics-overview {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .metric-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .metric-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .metric-trend {
            font-size: 0.8rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #6b7280; }

        .performance-charts {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            height: 300px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder {
            color: #9ca3af;
            font-size: 1.1rem;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            justify-content: space-around;
            height: 100%;
            padding: 2rem 1rem;
            width: 100%;
        }

        .chart-bar {
            background: linear-gradient(to top, #312e81, #6366f1);
            border-radius: 4px 4px 0 0;
            min-width: 30px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .chart-bar:hover {
            background: linear-gradient(to top, #1e1b4b, #4f46e5);
            transform: scaleY(1.05);
        }

        .bar-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: #6b7280;
            white-space: nowrap;
        }

        .bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            font-weight: 600;
            color: #1f2937;
        }

        .team-performance {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .team-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .team-member-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .team-member-card:hover {
            border-color: #312e81;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .member-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #312e81, #6366f1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .member-role {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
        }

        .metric-score {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-name {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .score-excellent { color: #10b981; }
        .score-good { color: #3b82f6; }
        .score-average { color: #f59e0b; }
        .score-poor { color: #ef4444; }

        .ai-insights {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 2rem;
        }

        .ai-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .insight-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid;
        }

        .insight-card.positive { border-left-color: #10b981; }
        .insight-card.warning { border-left-color: #f59e0b; }
        .insight-card.critical { border-left-color: #ef4444; }

        .insight-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .insight-title {
            font-weight: 600;
            color: #1f2937;
            flex: 1;
        }

        .insight-priority {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .priority-high { background: #fee2e2; color: #991b1b; }
        .priority-medium { background: #fef3c7; color: #92400e; }
        .priority-low { background: #d1fae5; color: #065f46; }

        .insight-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 0.75rem;
        }

        .insight-action {
            background: #f3f4f6;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
            color: #374151;
        }

        @media (max-width: 1024px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .metrics-overview {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Performance Analyzer & Metrics Dashboard - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>📊 Performance Analyzer & Metrics Dashboard</h2>
                <div class="performance-indicator">
                    📈 Celková Výkonnosť: 87%
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="generateAnalysis()">🔍 Hlboká Analýza</button>
                    <button class="btn btn-primary" onclick="exportMetrics()">📤 Export Metrík</button>
                </div>
            </div>

            <div class="pm-content">
                <!-- Metrics Overview -->
                <div class="metrics-overview">
                    <div class="metric-card">
                        <div class="metric-icon">⚡</div>
                        <div class="metric-value score-excellent">94%</div>
                        <div class="metric-label">Produktivita Tímu</div>
                        <div class="metric-trend trend-up">
                            ↗ +7% za týždeň
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🎯</div>
                        <div class="metric-value score-good">78%</div>
                        <div class="metric-label">Dodržanie Deadlines</div>
                        <div class="metric-trend trend-down">
                            ↘ -5% za týždeň
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">💰</div>
                        <div class="metric-value score-average">€45,200</div>
                        <div class="metric-label">Spotrebovaný Rozpočet</div>
                        <div class="metric-trend trend-up">
                            ↗ +€3,200 za týždeň
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🏆</div>
                        <div class="metric-value score-excellent">92%</div>
                        <div class="metric-label">Kvalita Deliverables</div>
                        <div class="metric-trend trend-up">
                            ↗ +4% za týždeň
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">⏱️</div>
                        <div class="metric-value score-good">2.3h</div>
                        <div class="metric-label">Priemerný Response Time</div>
                        <div class="metric-trend trend-down">
                            ↘ -0.4h za týždeň
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🔄</div>
                        <div class="metric-value score-poor">15</div>
                        <div class="metric-label">Aktívne Blockers</div>
                        <div class="metric-trend trend-up">
                            ↗ +3 za týždeň
                        </div>
                    </div>
                </div>

                <!-- Performance Charts -->
                <div class="performance-charts">
                    <div class="chart-title">
                        📈 Týždenná Výkonnosť - Posledných 8 Týždňov
                    </div>
                    <div class="chart-container">
                        <div class="chart-bars">
                            <div class="chart-bar" style="height: 60%;">
                                <div class="bar-value">72%</div>
                                <div class="bar-label">T18</div>
                            </div>
                            <div class="chart-bar" style="height: 75%;">
                                <div class="bar-value">78%</div>
                                <div class="bar-label">T19</div>
                            </div>
                            <div class="chart-bar" style="height: 85%;">
                                <div class="bar-value">85%</div>
                                <div class="bar-label">T20</div>
                            </div>
                            <div class="chart-bar" style="height: 70%;">
                                <div class="bar-value">74%</div>
                                <div class="bar-label">T21</div>
                            </div>
                            <div class="chart-bar" style="height: 90%;">
                                <div class="bar-value">89%</div>
                                <div class="bar-label">T22</div>
                            </div>
                            <div class="chart-bar" style="height: 95%;">
                                <div class="bar-value">94%</div>
                                <div class="bar-label">T23</div>
                            </div>
                            <div class="chart-bar" style="height: 88%;">
                                <div class="bar-value">87%</div>
                                <div class="bar-label">T24</div>
                            </div>
                            <div class="chart-bar" style="height: 92%;">
                                <div class="bar-value">91%</div>
                                <div class="bar-label">T25</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team Performance -->
                <div class="team-performance">
                    <div class="team-title">
                        👥 Individuálna Výkonnosť Tímu
                    </div>
                    <div class="team-grid">
                        <div class="team-member-card">
                            <div class="member-header">
                                <div class="member-avatar">JN</div>
                                <div class="member-info">
                                    <div class="member-name">Ján Novák</div>
                                    <div class="member-role">Project Manager</div>
                                </div>
                            </div>
                            <div class="performance-metrics">
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">96%</div>
                                    <div class="metric-name">Efektívnosť</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-good">85%</div>
                                    <div class="metric-name">Komunikácia</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">92%</div>
                                    <div class="metric-name">Dodržanie Termínov</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-average">78%</div>
                                    <div class="metric-name">Work-Life Balance</div>
                                </div>
                            </div>
                        </div>

                        <div class="team-member-card">
                            <div class="member-header">
                                <div class="member-avatar">MS</div>
                                <div class="member-info">
                                    <div class="member-name">Mária Svobodová</div>
                                    <div class="member-role">UI/UX Designer</div>
                                </div>
                            </div>
                            <div class="performance-metrics">
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">94%</div>
                                    <div class="metric-name">Kreativita</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">91%</div>
                                    <div class="metric-name">Kvalita Práce</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-good">88%</div>
                                    <div class="metric-name">Dodržanie Termínov</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">95%</div>
                                    <div class="metric-name">Spokojnosť</div>
                                </div>
                            </div>
                        </div>

                        <div class="team-member-card">
                            <div class="member-header">
                                <div class="member-avatar">PK</div>
                                <div class="member-info">
                                    <div class="member-name">Peter Kováč</div>
                                    <div class="member-role">Backend Developer</div>
                                </div>
                            </div>
                            <div class="performance-metrics">
                                <div class="metric-item">
                                    <div class="metric-score score-good">87%</div>
                                    <div class="metric-name">Code Quality</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-average">76%</div>
                                    <div class="metric-name">Produktivita</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-poor">65%</div>
                                    <div class="metric-name">Dodržanie Termínov</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-average">72%</div>
                                    <div class="metric-name">Kolaborácia</div>
                                </div>
                            </div>
                        </div>

                        <div class="team-member-card">
                            <div class="member-header">
                                <div class="member-avatar">AH</div>
                                <div class="member-info">
                                    <div class="member-name">Anna Horváthová</div>
                                    <div class="member-role">QA Tester</div>
                                </div>
                            </div>
                            <div class="performance-metrics">
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">98%</div>
                                    <div class="metric-name">Presnosť</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">93%</div>
                                    <div class="metric-name">Efektívnosť</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-excellent">96%</div>
                                    <div class="metric-name">Dodržanie Termínov</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-score score-good">89%</div>
                                    <div class="metric-name">Dokumentácia</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Insights -->
                <div class="ai-insights">
                    <div class="ai-title">
                        🤖 AI Performance Insights
                    </div>
                    <div class="insights-grid">
                        <div class="insight-card positive">
                            <div class="insight-header">
                                <div class="insight-title">Výborná Tímová Dynamika</div>
                                <div class="insight-priority priority-low">Nízka</div>
                            </div>
                            <div class="insight-description">
                                Tím vykazuje vynikajúcu kolaboráciu a komunikáciu. Produktivita vzrástla o 15% za posledný mesiac.
                            </div>
                            <div class="insight-action">
                                <strong>Odporúčanie:</strong> Pokračovať v súčasnom prístupe a zdieľať best practices s ostatnými tímami.
                            </div>
                        </div>

                        <div class="insight-card warning">
                            <div class="insight-header">
                                <div class="insight-title">Bottleneck v Backend Vývoji</div>
                                <div class="insight-priority priority-medium">Stredná</div>
                            </div>
                            <div class="insight-description">
                                Peter Kováč vykazuje zníženie produktivity o 12%. Identifikované problémy s komplexnosťou úloh.
                            </div>
                            <div class="insight-action">
                                <strong>Odporúčanie:</strong> Rozdeliť komplexné úlohy na menšie časti a poskytnúť dodatočný support.
                            </div>
                        </div>

                        <div class="insight-card critical">
                            <div class="insight-header">
                                <div class="insight-title">Rastúci Počet Blockers</div>
                                <div class="insight-priority priority-high">Vysoká</div>
                            </div>
                            <div class="insight-description">
                                Počet aktívnych blockers vzrástol o 50% za posledný týždeň. Hlavné príčiny: API dependencies a resource conflicts.
                            </div>
                            <div class="insight-action">
                                <strong>Odporúčanie:</strong> Okamžite zvolať blocker resolution meeting a implementovať preventívne opatrenia.
                            </div>
                        </div>

                        <div class="insight-card positive">
                            <div class="insight-header">
                                <div class="insight-title">Zlepšenie Kvality Kódu</div>
                                <div class="insight-priority priority-low">Nízka</div>
                            </div>
                            <div class="insight-description">
                                Code review proces vykazuje 23% zlepšenie v detekcii bugov. Zníženie production issues o 18%.
                            </div>
                            <div class="insight-action">
                                <strong>Odporúčanie:</strong> Rozšíriť code review best practices na všetky komponenty projektu.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            animateCharts();
            updateMetrics();
            startRealTimeUpdates();
        }

        function animateCharts() {
            // Animácia chart bars
            const bars = document.querySelectorAll('.chart-bar');
            bars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.opacity = '1';
                    bar.style.transform = 'scaleY(1)';
                }, index * 100);
            });
        }

        function updateMetrics() {
            // Simulácia aktualizácie metrík
            console.log('Metriky aktualizované:', new Date().toLocaleTimeString());
        }

        function startRealTimeUpdates() {
            // Simulácia real-time aktualizácií každých 30 sekúnd
            setInterval(() => {
                updateMetrics();
                simulateMetricChanges();
            }, 30000);
        }

        function simulateMetricChanges() {
            // Simulácia zmien v metrikách pre demo účely
            const metricValues = document.querySelectorAll('.metric-value');
            metricValues.forEach(value => {
                if (Math.random() < 0.1) { // 10% šanca na zmenu
                    const currentText = value.textContent;
                    if (currentText.includes('%')) {
                        const currentValue = parseInt(currentText);
                        const change = Math.floor(Math.random() * 6) - 3; // -3 až +3
                        const newValue = Math.max(0, Math.min(100, currentValue + change));
                        value.textContent = newValue + '%';

                        // Aktualizácia farby na základe hodnoty
                        value.className = value.className.replace(/score-\w+/, '');
                        if (newValue >= 90) value.classList.add('score-excellent');
                        else if (newValue >= 75) value.classList.add('score-good');
                        else if (newValue >= 60) value.classList.add('score-average');
                        else value.classList.add('score-poor');
                    }
                }
            });
        }

        function generateAnalysis() {
            alert('🔍 Spúšťam hlbokú performance analýzu...\n\n• Machine learning analýza trendov: ✓\n• Behavioral pattern recognition: ✓\n• Predictive performance modeling: ✓\n• Cross-team comparison analysis: ✓\n\nKľúčové zistenia:\n📈 Produktivita tímu rastie o 2.3% týždenne\n⚠️ Identifikované 3 performance bottlenecks\n🎯 Predpokladané zlepšenie o 15% v nasledujúcom mesiaci\n💡 Generované 8 actionable recommendations\n\nDetailný report exportovaný!');
        }

        function exportMetrics() {
            alert('📤 Export Performance Metrík...\n\n• Generujem executive dashboard: ✓\n• Vytváram trend analýzy: ✓\n• Komprimujem raw data: ✓\n• Pripravujem visualizations: ✓\n\nExportované súbory:\n📊 Executive_Dashboard.pdf (2.1 MB)\n📈 Performance_Trends.xlsx (4.3 MB)\n📋 Raw_Metrics_Data.csv (1.8 MB)\n🎨 Charts_and_Graphs.pptx (5.2 MB)\n\nSťahovanie začína...');
        }

        // Interaktívne chart bars
        document.querySelectorAll('.chart-bar').forEach(bar => {
            bar.addEventListener('click', function() {
                const week = this.querySelector('.bar-label').textContent;
                const value = this.querySelector('.bar-value').textContent;
                alert(`📊 Detail pre ${week}\n\nVýkonnosť: ${value}\n\nBreakdown:\n• Produktivita: 89%\n• Kvalita: 94%\n• Dodržanie termínov: 76%\n• Tímová spolupráca: 91%\n\nKľúčové udalosti:\n• Sprint planning meeting\n• Code review session\n• Client presentation`);
            });
        });

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>