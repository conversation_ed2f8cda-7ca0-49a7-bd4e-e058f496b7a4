<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Master Dashboard & Control Center - Projektov<PERSON>ž<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1800px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .system-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto auto auto;
            gap: 2rem;
            padding: 2rem;
        }

        .overview-cards {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .overview-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .overview-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .card-projects::before { background: linear-gradient(90deg, #3b82f6, #1e40af); }
        .card-tasks::before { background: linear-gradient(90deg, #10b981, #059669); }
        .card-team::before { background: linear-gradient(90deg, #f59e0b, #d97706); }
        .card-budget::before { background: linear-gradient(90deg, #ef4444, #dc2626); }
        .card-quality::before { background: linear-gradient(90deg, #8b5cf6, #7c3aed); }
        .card-performance::before { background: linear-gradient(90deg, #06b6d4, #0891b2); }

        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .card-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #1f2937;
        }

        .card-label {
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .card-trend {
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #6b7280; }

        .component-grid {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .grid-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .component-tiles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .component-tile {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .component-tile:hover {
            border-color: #334155;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .tile-icon {
            font-size: 2rem;
            margin-bottom: 0.75rem;
        }

        .tile-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .tile-description {
            color: #6b7280;
            font-size: 0.8rem;
            line-height: 1.3;
        }

        .tile-status {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-active { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }

        .system-health {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .health-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .health-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .health-metric {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .metric-circle {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 0.75rem;
        }

        .circle-bg {
            fill: none;
            stroke: #e5e7eb;
            stroke-width: 6;
        }

        .circle-progress {
            fill: none;
            stroke-width: 6;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }

        .circle-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 700;
            color: #1f2937;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }

        .activity-feed {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .feed-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-left: 3px solid;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            transform: translateX(3px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .activity-item.success { border-left-color: #10b981; }
        .activity-item.warning { border-left-color: #f59e0b; }
        .activity-item.error { border-left-color: #ef4444; }
        .activity-item.info { border-left-color: #3b82f6; }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .activity-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.9rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #9ca3af;
        }

        .activity-description {
            color: #6b7280;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .quick-actions {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border: 2px solid #8b5cf6;
            border-radius: 12px;
            padding: 2rem;
        }

        .actions-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #c4b5fd;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .action-card:hover {
            background: rgba(255,255,255,0.95);
            transform: translateY(-2px);
            border-color: #8b5cf6;
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.75rem;
            color: #7c3aed;
        }

        .action-name {
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 0.5rem;
        }

        .action-description {
            color: #7c3aed;
            font-size: 0.8rem;
            line-height: 1.3;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .overview-cards {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Master Dashboard & Control Center - Projektový Manažment v4</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>🎛️ Master Dashboard & Control Center</h2>
                <div class="system-indicator">
                    🟢 Systém: Operational
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="systemOverview()">📊 System Overview</button>
                    <button class="btn btn-primary" onclick="emergencyMode()">🚨 Emergency Mode</button>
                </div>
            </div>

            <div class="pm-content">
                <!-- Overview Cards -->
                <div class="overview-cards">
                    <div class="overview-card card-projects">
                        <div class="card-icon">📁</div>
                        <div class="card-value">12</div>
                        <div class="card-label">Aktívne Projekty</div>
                        <div class="card-trend trend-up">
                            ↗ +2 za mesiac
                        </div>
                    </div>

                    <div class="overview-card card-tasks">
                        <div class="card-icon">✅</div>
                        <div class="card-value">347</div>
                        <div class="card-label">Dokončené Úlohy</div>
                        <div class="card-trend trend-up">
                            ↗ +23 za týždeň
                        </div>
                    </div>

                    <div class="overview-card card-team">
                        <div class="card-icon">👥</div>
                        <div class="card-value">28</div>
                        <div class="card-label">Členovia Tímu</div>
                        <div class="card-trend trend-stable">
                            → Stabilné
                        </div>
                    </div>

                    <div class="overview-card card-budget">
                        <div class="card-icon">💰</div>
                        <div class="card-value">€245K</div>
                        <div class="card-label">Celkový Rozpočet</div>
                        <div class="card-trend trend-up">
                            ↗ 78% využité
                        </div>
                    </div>

                    <div class="overview-card card-quality">
                        <div class="card-icon">🏆</div>
                        <div class="card-value">94%</div>
                        <div class="card-label">Kvalita Kódu</div>
                        <div class="card-trend trend-up">
                            ↗ +3% za mesiac
                        </div>
                    </div>

                    <div class="overview-card card-performance">
                        <div class="card-icon">⚡</div>
                        <div class="card-value">97%</div>
                        <div class="card-label">System Uptime</div>
                        <div class="card-trend trend-stable">
                            → Excelentné
                        </div>
                    </div>
                </div>

                <!-- PM Components Grid -->
                <div class="component-grid">
                    <div class="grid-title">
                        🧩 PM Komponenty (1-8)
                    </div>
                    <div class="component-tiles">
                        <div class="component-tile" onclick="openComponent(1)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">📚</div>
                            <div class="tile-name">Dokumentový Hub</div>
                            <div class="tile-description">AI kategorizácia a správa dokumentov</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(2)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">📊</div>
                            <div class="tile-name">Gantt Plánovač</div>
                            <div class="tile-description">Adaptívne projektové plánovanie</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(3)">
                            <div class="tile-status status-warning"></div>
                            <div class="tile-icon">💚</div>
                            <div class="tile-name">Project Health</div>
                            <div class="tile-description">Real-time monitoring zdravia projektu</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(4)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">📅</div>
                            <div class="tile-name">Meeting Planner</div>
                            <div class="tile-description">Smart plánovanie stretnutí</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(5)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">📄</div>
                            <div class="tile-name">Template Generátor</div>
                            <div class="tile-description">AI-powered šablóny</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(6)">
                            <div class="tile-status status-warning"></div>
                            <div class="tile-icon">⚠️</div>
                            <div class="tile-name">Risk Predictor</div>
                            <div class="tile-description">Predikcia a mitigation rizík</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(7)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">👤</div>
                            <div class="tile-name">Resource Optimizer</div>
                            <div class="tile-description">Optimalizácia ľudských zdrojov</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(8)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">📖</div>
                            <div class="tile-name">Knowledge Base</div>
                            <div class="tile-description">Wiki a knowledge management</div>
                        </div>
                    </div>
                </div>

                <!-- PM Components Grid 2 -->
                <div class="component-grid">
                    <div class="grid-title">
                        🧩 PM Komponenty (9-16)
                    </div>
                    <div class="component-tiles">
                        <div class="component-tile" onclick="openComponent(9)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">📈</div>
                            <div class="tile-name">Performance Analyzer</div>
                            <div class="tile-description">Analýza výkonnosti tímu</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(10)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">💬</div>
                            <div class="tile-name">Stakeholder Hub</div>
                            <div class="tile-description">Komunikácia so stakeholdermi</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(11)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">🧪</div>
                            <div class="tile-name">QA & Testing</div>
                            <div class="tile-description">Quality assurance management</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(12)">
                            <div class="tile-status status-warning"></div>
                            <div class="tile-icon">💰</div>
                            <div class="tile-name">Budget Tracker</div>
                            <div class="tile-description">Finančná analytika a forecasting</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(13)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">🏃‍♂️</div>
                            <div class="tile-name">Sprint Manager</div>
                            <div class="tile-description">Agile/Scrum board management</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(14)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">👤</div>
                            <div class="tile-name">Client Portal</div>
                            <div class="tile-description">Client feedback a komunikácia</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(15)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">🚀</div>
                            <div class="tile-name">DevOps Pipeline</div>
                            <div class="tile-description">Deployment a CI/CD management</div>
                        </div>

                        <div class="component-tile" onclick="openComponent(16)">
                            <div class="tile-status status-active"></div>
                            <div class="tile-icon">🤖</div>
                            <div class="tile-name">AI Assistant</div>
                            <div class="tile-description">Automatizácia a AI insights</div>
                        </div>
                    </div>
                </div>

                <!-- System Health -->
                <div class="system-health">
                    <div class="health-title">
                        💚 Zdravie Systému
                    </div>
                    <div class="health-metrics">
                        <div class="health-metric">
                            <div class="metric-circle">
                                <svg width="80" height="80">
                                    <circle class="circle-bg" cx="40" cy="40" r="35"></circle>
                                    <circle class="circle-progress" cx="40" cy="40" r="35"
                                            stroke="#10b981" stroke-dasharray="197 220"></circle>
                                </svg>
                                <div class="circle-text">97%</div>
                            </div>
                            <div class="metric-label">System Uptime</div>
                        </div>

                        <div class="health-metric">
                            <div class="metric-circle">
                                <svg width="80" height="80">
                                    <circle class="circle-bg" cx="40" cy="40" r="35"></circle>
                                    <circle class="circle-progress" cx="40" cy="40" r="35"
                                            stroke="#3b82f6" stroke-dasharray="176 220"></circle>
                                </svg>
                                <div class="circle-text">85%</div>
                            </div>
                            <div class="metric-label">Performance</div>
                        </div>

                        <div class="health-metric">
                            <div class="metric-circle">
                                <svg width="80" height="80">
                                    <circle class="circle-bg" cx="40" cy="40" r="35"></circle>
                                    <circle class="circle-progress" cx="40" cy="40" r="35"
                                            stroke="#f59e0b" stroke-dasharray="154 220"></circle>
                                </svg>
                                <div class="circle-text">72%</div>
                            </div>
                            <div class="metric-label">Resource Usage</div>
                        </div>

                        <div class="health-metric">
                            <div class="metric-circle">
                                <svg width="80" height="80">
                                    <circle class="circle-bg" cx="40" cy="40" r="35"></circle>
                                    <circle class="circle-progress" cx="40" cy="40" r="35"
                                            stroke="#8b5cf6" stroke-dasharray="209 220"></circle>
                                </svg>
                                <div class="circle-text">94%</div>
                            </div>
                            <div class="metric-label">Security Score</div>
                        </div>
                    </div>
                </div>

                <!-- Activity Feed -->
                <div class="activity-feed">
                    <div class="feed-title">
                        🔔 Nedávna Aktivita
                    </div>
                    <div class="activity-list">
                        <div class="activity-item success">
                            <div class="activity-header">
                                <div class="activity-title">Deployment úspešný</div>
                                <div class="activity-time">Pred 5 min</div>
                            </div>
                            <div class="activity-description">
                                E-shop Modernizácia v2.1.3 nasadená do staging prostredia
                            </div>
                        </div>

                        <div class="activity-item info">
                            <div class="activity-header">
                                <div class="activity-title">Sprint dokončený</div>
                                <div class="activity-time">Pred 15 min</div>
                            </div>
                            <div class="activity-description">
                                Sprint 3 ukončený s 87% úspešnosťou, 23/26 story points dokončených
                            </div>
                        </div>

                        <div class="activity-item warning">
                            <div class="activity-header">
                                <div class="activity-title">Budget upozornenie</div>
                                <div class="activity-time">Pred 1 hodinou</div>
                            </div>
                            <div class="activity-description">
                                Projekt E-shop prekročil 80% rozpočtu, zostáva €10,800
                            </div>
                        </div>

                        <div class="activity-item success">
                            <div class="activity-header">
                                <div class="activity-title">AI analýza dokončená</div>
                                <div class="activity-time">Pred 2 hodinami</div>
                            </div>
                            <div class="activity-description">
                                Performance analýza identifikovala 5 možností optimalizácie
                            </div>
                        </div>

                        <div class="activity-item error">
                            <div class="activity-header">
                                <div class="activity-title">Test zlyhal</div>
                                <div class="activity-time">Pred 3 hodinami</div>
                            </div>
                            <div class="activity-description">
                                Integration test "payment_processing" zlyhal, vyžaduje pozornosť
                            </div>
                        </div>

                        <div class="activity-item info">
                            <div class="activity-header">
                                <div class="activity-title">Nový člen tímu</div>
                                <div class="activity-time">Dnes ráno</div>
                            </div>
                            <div class="activity-description">
                                Lukáš Novotný pridaný ako Frontend Developer do projektu
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <div class="actions-title">
                        ⚡ Rýchle Akcie
                    </div>
                    <div class="actions-grid">
                        <div class="action-card" onclick="quickAction('backup')">
                            <div class="action-icon">💾</div>
                            <div class="action-name">System Backup</div>
                            <div class="action-description">Vytvoriť kompletný backup všetkých dát</div>
                        </div>

                        <div class="action-card" onclick="quickAction('health-check')">
                            <div class="action-icon">🏥</div>
                            <div class="action-name">Health Check</div>
                            <div class="action-description">Spustiť kompletnú diagnostiku systému</div>
                        </div>

                        <div class="action-card" onclick="quickAction('ai-analysis')">
                            <div class="action-icon">🧠</div>
                            <div class="action-name">AI Analýza</div>
                            <div class="action-description">Generovať AI insights pre všetky projekty</div>
                        </div>

                        <div class="action-card" onclick="quickAction('export-data')">
                            <div class="action-icon">📤</div>
                            <div class="action-name">Export Dát</div>
                            <div class="action-description">Exportovať všetky projektové dáta</div>
                        </div>

                        <div class="action-card" onclick="quickAction('security-scan')">
                            <div class="action-icon">🔒</div>
                            <div class="action-name">Security Scan</div>
                            <div class="action-description">Spustiť bezpečnostný audit systému</div>
                        </div>

                        <div class="action-card" onclick="quickAction('performance-optimize')">
                            <div class="action-icon">⚡</div>
                            <div class="action-name">Optimalizácia</div>
                            <div class="action-description">Optimalizovať výkonnosť systému</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            updateSystemMetrics();
            startRealTimeMonitoring();
            loadComponentStatuses();
        }

        function updateSystemMetrics() {
            // Simulácia aktualizácie system metrík
            console.log('System metrics aktualizované:', new Date().toLocaleTimeString());
        }

        function startRealTimeMonitoring() {
            // Real-time monitoring všetkých komponentov
            setInterval(() => {
                updateSystemMetrics();
                checkComponentHealth();
                updateActivityFeed();
            }, 30000); // Každých 30 sekúnd
        }

        function loadComponentStatuses() {
            // Načítanie statusov všetkých PM komponentov
            console.log('Component statuses načítané');
        }

        function checkComponentHealth() {
            // Kontrola zdravia všetkých komponentov
            const components = document.querySelectorAll('.component-tile');
            components.forEach((component, index) => {
                const status = component.querySelector('.tile-status');
                // Simulácia random status changes
                if (Math.random() < 0.05) { // 5% šanca na zmenu
                    const statuses = ['status-active', 'status-warning', 'status-error'];
                    const currentStatus = status.className.split(' ')[1];
                    const newStatus = statuses[Math.floor(Math.random() * statuses.length)];
                    status.className = `tile-status ${newStatus}`;
                }
            });
        }

        function updateActivityFeed() {
            // Simulácia pridania novej aktivity
            if (Math.random() < 0.1) { // 10% šanca na novú aktivitu
                addNewActivity();
            }
        }

        function addNewActivity() {
            const activities = [
                { type: 'success', title: 'Automatizácia spustená', desc: 'Status report automaticky odoslaný stakeholderom' },
                { type: 'info', title: 'Nová úloha vytvorená', desc: 'AI asistent vytvoril optimalizačnú úlohu' },
                { type: 'warning', title: 'Resource upozornenie', desc: 'Server load prekročil 80% kapacity' },
                { type: 'success', title: 'Backup dokončený', desc: 'Automatický backup úspešne vytvorený' }
            ];

            const activity = activities[Math.floor(Math.random() * activities.length)];
            const activityList = document.querySelector('.activity-list');
            const time = new Date().toLocaleTimeString('sk-SK', { hour: '2-digit', minute: '2-digit' });

            const newActivity = document.createElement('div');
            newActivity.className = `activity-item ${activity.type}`;
            newActivity.innerHTML = `
                <div class="activity-header">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">Pred chvíľou</div>
                </div>
                <div class="activity-description">${activity.desc}</div>
            `;

            activityList.insertBefore(newActivity, activityList.firstChild);

            // Udržiavať maximálne 10 aktivít
            while (activityList.children.length > 10) {
                activityList.removeChild(activityList.lastChild);
            }
        }

        function systemOverview() {
            alert('📊 System Overview\n\n🟢 Status: Operational\n📊 Komponenty: 16/16 aktívne\n👥 Používatelia: 28 online\n💾 Storage: 78% využité\n🔒 Security: Všetky kontroly OK\n⚡ Performance: Optimálna\n\n📈 Kľúčové metriky:\n• Uptime: 99.7%\n• Response time: 45ms\n• Error rate: 0.02%\n• Throughput: 1,247 req/min\n\nPosledná údržba: 15.06.2024\nĎalšia plánovaná: 30.06.2024');
        }

        function emergencyMode() {
            const confirm = window.confirm('🚨 EMERGENCY MODE\n\nTáto akcia aktivuje núdzový režim:\n\n• Všetky nekritické procesy budú pozastavené\n• Priorita pre kritické operácie\n• Automatické notifikácie administrátorom\n• Zvýšené monitorovanie\n\nNaozaj chcete aktivovať emergency mode?');

            if (confirm) {
                alert('🚨 EMERGENCY MODE AKTIVOVANÝ\n\n• Systém prepnutý do núdzového režimu\n• Kritické procesy majú prioritu\n• Administrátori notifikovaní\n• Enhanced monitoring aktívny\n\nPre deaktiváciu kontaktujte system admin.');
            }
        }

        function openComponent(componentId) {
            const componentNames = {
                1: 'Inteligentný Dokumentový Hub',
                2: 'Adaptívny Gantt Plánovač',
                3: 'Project Health Dashboard',
                4: 'Smart Meeting Planner',
                5: 'Template Generátor',
                6: 'Risk Predictor & Mitigation',
                7: 'Resource Optimizer',
                8: 'Knowledge Base Builder',
                9: 'Performance Analyzer',
                10: 'Stakeholder Communication Hub',
                11: 'Quality Assurance & Testing',
                12: 'Budget Tracker & Financial Analytics',
                13: 'Agile Sprint Manager & Scrum Board',
                14: 'Client Portal & Feedback Manager',
                15: 'Deployment Pipeline & DevOps',
                16: 'AI Project Assistant & Automation'
            };

            const componentName = componentNames[componentId];
            alert(`🧩 Otváram komponent: ${componentName}\n\nKomponent #${componentId}\nStatus: Aktívny\nPosledná aktualizácia: Dnes\n\nKomponent sa otvorí v novom okne...\n\n📁 Súbor: pm_component_${componentId}.html`);
        }

        function quickAction(action) {
            const actions = {
                'backup': '💾 System Backup spustený...\n\nZálohujú sa:\n• Projektové dáta\n• Konfigurácie\n• User settings\n• Logy a metriky\n\nOdhadovaný čas: 15 minút',
                'health-check': '🏥 Health Check spustený...\n\nKontrolujem:\n• System resources\n• Database integrity\n• Network connectivity\n• Security status\n\nVýsledky za 3 minúty',
                'ai-analysis': '🧠 AI Analýza spustená...\n\nAnalyzujem:\n• Project performance\n• Resource utilization\n• Risk factors\n• Optimization opportunities\n\nAI report za 5 minút',
                'export-data': '📤 Export dát spustený...\n\nExportujem:\n• Všetky projekty\n• Task history\n• Team metrics\n• Financial data\n\nSúbory budú dostupné za 10 minút',
                'security-scan': '🔒 Security Scan spustený...\n\nKontrolujem:\n• Vulnerability assessment\n• Access permissions\n• Data encryption\n• Audit logs\n\nSecurity report za 8 minút',
                'performance-optimize': '⚡ Optimalizácia spustená...\n\nOptimalizujem:\n• Database queries\n• Cache management\n• Resource allocation\n• System processes\n\nZlepšenie za 5 minút'
            };

            alert(`⚡ Rýchla Akcia\n\n${actions[action]}\n\nNotifikácia po dokončení: ✓\nProgress tracking: Aktívny`);
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
