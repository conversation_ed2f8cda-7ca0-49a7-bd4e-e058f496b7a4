<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptívny Gantt Plánovač - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .project-selector {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            outline: none;
        }

        .pm-content {
            display: grid;
            grid-template-rows: auto 1fr;
            height: 700px;
        }

        .timeline-controls {
            background: #f8fafc;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .view-controls {
            display: flex;
            gap: 0.5rem;
        }

        .view-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-btn.active {
            background: #059669;
            color: white;
            border-color: #059669;
        }

        .ai-optimizer {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ai-optimizer:hover {
            background: #fde68a;
        }

        .gantt-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            overflow: hidden;
        }

        .task-list {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            overflow-y: auto;
        }

        .task-header {
            background: #e2e8f0;
            padding: 1rem;
            font-weight: 600;
            border-bottom: 1px solid #cbd5e1;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .task-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .task-item:hover {
            background: #e2e8f0;
        }

        .task-priority {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .priority-high { background: #dc2626; }
        .priority-medium { background: #f59e0b; }
        .priority-low { background: #059669; }

        .task-name {
            flex: 1;
            font-weight: 500;
        }

        .task-duration {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .timeline-area {
            overflow-x: auto;
            overflow-y: auto;
            position: relative;
        }

        .timeline-header {
            background: #e2e8f0;
            height: 60px;
            display: flex;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 1px solid #cbd5e1;
        }

        .timeline-month {
            min-width: 200px;
            padding: 0.5rem;
            text-align: center;
            font-weight: 600;
            border-right: 1px solid #cbd5e1;
        }

        .timeline-weeks {
            display: flex;
            font-size: 0.8rem;
            color: #6b7280;
        }

        .timeline-week {
            min-width: 50px;
            padding: 0.25rem;
            text-align: center;
            border-right: 1px solid #d1d5db;
        }

        .gantt-bars {
            position: relative;
            min-height: 400px;
        }

        .gantt-row {
            height: 45px;
            border-bottom: 1px solid #e2e8f0;
            position: relative;
        }

        .gantt-bar {
            position: absolute;
            height: 30px;
            top: 7px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 0 0.5rem;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .gantt-bar:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .bar-planning { background: #3b82f6; }
        .bar-development { background: #8b5cf6; }
        .bar-testing { background: #f59e0b; }
        .bar-deployment { background: #059669; }
        .bar-review { background: #ef4444; }

        .milestone {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 16px solid #dc2626;
            top: 14px;
            cursor: pointer;
        }

        .milestone::after {
            content: attr(data-title);
            position: absolute;
            top: 20px;
            left: -50px;
            width: 100px;
            text-align: center;
            font-size: 0.7rem;
            color: #dc2626;
            font-weight: 600;
        }

        .dependency-line {
            position: absolute;
            border-top: 2px solid #6b7280;
            z-index: 5;
        }

        .resource-indicator {
            position: absolute;
            top: -5px;
            right: 5px;
            background: #dc2626;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-overlay {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            pointer-events: none;
        }

        .pm-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .status-ontrack { background: #d1fae5; color: #065f46; }
        .status-atrisk { background: #fef3c7; color: #92400e; }
        .status-delayed { background: #fee2e2; color: #991b1b; }

        @media (max-width: 1024px) {
            .gantt-container {
                grid-template-columns: 250px 1fr;
            }
            
            .timeline-month {
                min-width: 150px;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Adaptívny Gantt Plánovač - Projektový Manažment</h1>
        
        <div class="pm-component">
            <div class="pm-header">
                <h2>📊 Gantt Plánovač</h2>
                <div class="pm-actions">
                    <select class="project-selector" id="projectSelector">
                        <option value="eshop">E-shop Modernizácia</option>
                        <option value="mobile">Mobile App Development</option>
                        <option value="api">API Integration Project</option>
                    </select>
                    <button class="btn btn-primary" onclick="addTask()">+ Pridať Úlohu</button>
                    <button class="btn btn-primary" onclick="exportPlan()">📤 Export</button>
                </div>
            </div>
            
            <div class="pm-content">
                <div class="timeline-controls">
                    <div class="view-controls">
                        <button class="view-btn active" data-view="months">Mesiace</button>
                        <button class="view-btn" data-view="weeks">Týždne</button>
                        <button class="view-btn" data-view="days">Dni</button>
                    </div>
                    
                    <div class="ai-optimizer" onclick="optimizeSchedule()">
                        🤖 AI Optimalizácia Harmonogramu
                    </div>
                    
                    <div class="project-status">
                        Projekt: <span class="pm-status status-atrisk">Riziko Omeškania</span>
                    </div>
                </div>
                
                <div class="gantt-container">
                    <div class="task-list">
                        <div class="task-header">Úlohy Projektu</div>
                        <div id="taskList">
                            <!-- Úlohy budú vygenerované JavaScriptom -->
                        </div>
                    </div>
                    
                    <div class="timeline-area">
                        <div class="timeline-header" id="timelineHeader">
                            <!-- Timeline header bude vygenerovaný JavaScriptom -->
                        </div>
                        <div class="gantt-bars" id="ganttBars">
                            <!-- Gantt bars budú vygenerované JavaScriptom -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Simulované projektové dáta
        const projectData = {
            eshop: {
                name: "E-shop Modernizácia",
                startDate: "2024-06-01",
                endDate: "2024-12-31",
                tasks: [
                    {
                        id: 1,
                        name: "Analýza požiadaviek",
                        start: "2024-06-01",
                        duration: 14,
                        progress: 100,
                        priority: "high",
                        type: "planning",
                        assignee: "Ján Novák",
                        dependencies: []
                    },
                    {
                        id: 2,
                        name: "UI/UX Dizajn",
                        start: "2024-06-15",
                        duration: 21,
                        progress: 85,
                        priority: "high",
                        type: "planning",
                        assignee: "Mária Svobodová",
                        dependencies: [1]
                    },
                    {
                        id: 3,
                        name: "Backend Development",
                        start: "2024-07-01",
                        duration: 45,
                        progress: 60,
                        priority: "high",
                        type: "development",
                        assignee: "Peter Kováč",
                        dependencies: [1]
                    },
                    {
                        id: 4,
                        name: "Frontend Development",
                        start: "2024-07-15",
                        duration: 35,
                        progress: 40,
                        priority: "high",
                        type: "development",
                        assignee: "Anna Horváthová",
                        dependencies: [2]
                    },
                    {
                        id: 5,
                        name: "API Integrácia",
                        start: "2024-08-15",
                        duration: 14,
                        progress: 20,
                        priority: "medium",
                        type: "development",
                        assignee: "Tomáš Varga",
                        dependencies: [3, 4]
                    },
                    {
                        id: 6,
                        name: "Testovanie",
                        start: "2024-09-01",
                        duration: 21,
                        progress: 0,
                        priority: "high",
                        type: "testing",
                        assignee: "Lucia Nováková",
                        dependencies: [5]
                    },
                    {
                        id: 7,
                        name: "Produkčné Nasadenie",
                        start: "2024-09-25",
                        duration: 7,
                        progress: 0,
                        priority: "high",
                        type: "deployment",
                        assignee: "Ján Novák",
                        dependencies: [6]
                    }
                ],
                milestones: [
                    { date: "2024-06-30", title: "Analýza Dokončená" },
                    { date: "2024-08-31", title: "Development Dokončený" },
                    { date: "2024-10-01", title: "Go-Live" }
                ]
            }
        };

        let currentProject = 'eshop';
        let currentView = 'months';

        function initPMComponent() {
            renderTaskList();
            renderTimeline();
            setupEventListeners();
        }

        function renderTaskList() {
            const taskList = document.getElementById('taskList');
            const project = projectData[currentProject];
            
            taskList.innerHTML = '';
            
            project.tasks.forEach(task => {
                const taskItem = document.createElement('div');
                taskItem.className = 'task-item';
                taskItem.innerHTML = `
                    <div class="task-priority priority-${task.priority}"></div>
                    <div class="task-name">${task.name}</div>
                    <div class="task-duration">${task.duration}d</div>
                `;
                taskList.appendChild(taskItem);
            });
        }

        function renderTimeline() {
            renderTimelineHeader();
            renderGanttBars();
        }

        function renderTimelineHeader() {
            const header = document.getElementById('timelineHeader');
            header.innerHTML = '';
            
            // Simulácia mesiacov
            const months = ['Jún 2024', 'Júl 2024', 'August 2024', 'September 2024', 'Október 2024'];
            
            months.forEach(month => {
                const monthDiv = document.createElement('div');
                monthDiv.className = 'timeline-month';
                monthDiv.innerHTML = `
                    <div>${month}</div>
                    <div class="timeline-weeks">
                        <div class="timeline-week">T1</div>
                        <div class="timeline-week">T2</div>
                        <div class="timeline-week">T3</div>
                        <div class="timeline-week">T4</div>
                    </div>
                `;
                header.appendChild(monthDiv);
            });
        }

        function renderGanttBars() {
            const barsContainer = document.getElementById('ganttBars');
            const project = projectData[currentProject];
            
            barsContainer.innerHTML = '';
            
            project.tasks.forEach((task, index) => {
                const row = document.createElement('div');
                row.className = 'gantt-row';
                
                const bar = document.createElement('div');
                bar.className = `gantt-bar bar-${task.type}`;
                bar.style.left = calculatePosition(task.start) + 'px';
                bar.style.width = calculateWidth(task.duration) + 'px';
                bar.innerHTML = `
                    ${task.name}
                    <div class="progress-overlay" style="width: ${task.progress}%"></div>
                `;
                
                if (task.progress < 50 && new Date(task.start) < new Date()) {
                    bar.innerHTML += '<div class="resource-indicator">!</div>';
                }
                
                row.appendChild(bar);
                barsContainer.appendChild(row);
            });
            
            // Pridanie míľnikov
            project.milestones.forEach(milestone => {
                const milestoneEl = document.createElement('div');
                milestoneEl.className = 'milestone';
                milestoneEl.style.left = calculatePosition(milestone.date) + 'px';
                milestoneEl.setAttribute('data-title', milestone.title);
                barsContainer.appendChild(milestoneEl);
            });
        }

        function calculatePosition(date) {
            // Simulácia pozície na základe dátumu
            const startDate = new Date('2024-06-01');
            const taskDate = new Date(date);
            const daysDiff = (taskDate - startDate) / (1000 * 60 * 60 * 24);
            return daysDiff * 8; // 8px na deň
        }

        function calculateWidth(duration) {
            return duration * 8; // 8px na deň
        }

        function setupEventListeners() {
            // View controls
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentView = this.dataset.view;
                    renderTimeline();
                });
            });

            // Project selector
            document.getElementById('projectSelector').addEventListener('change', function() {
                currentProject = this.value;
                renderTaskList();
                renderTimeline();
            });
        }

        function addTask() {
            alert('Pridanie novej úlohy - implementácia v produkčnej verzii');
        }

        function exportPlan() {
            alert('Export projektového plánu do PDF/Excel - implementácia v produkčnej verzii');
        }

        function optimizeSchedule() {
            alert('🤖 AI Optimalizácia:\n\n• Detekované prekrývanie zdrojov v týždni 32\n• Odporúčame presunúť "API Integráciu" o 3 dni\n• Možná úspora času: 5 dní\n• Zníženie rizika omeškania o 23%\n\nAplikovať optimalizáciu?');
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
