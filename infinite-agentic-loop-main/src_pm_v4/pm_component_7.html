<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resource Optimizer & Capacity Planner - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #3730a3 0%, #1e40af 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .capacity-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            min-height: 700px;
        }

        .resource-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .resource-list {
            margin-bottom: 2rem;
        }

        .resource-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .resource-item:hover {
            border-color: #3730a3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .resource-item.selected {
            border-color: #3730a3;
            background: #ede9fe;
        }

        .resource-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .resource-name {
            font-weight: 600;
            color: #1f2937;
        }

        .resource-role {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .capacity-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .capacity-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .capacity-optimal { background: #10b981; }
        .capacity-high { background: #f59e0b; }
        .capacity-overloaded { background: #ef4444; }

        .capacity-text {
            font-size: 0.8rem;
            color: #6b7280;
            display: flex;
            justify-content: space-between;
        }

        .main-area {
            padding: 2rem;
            overflow-y: auto;
        }

        .ai-optimizer {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .ai-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .optimization-suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .suggestion-card {
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-card:hover {
            background: rgba(255,255,255,0.95);
            transform: translateY(-2px);
        }

        .suggestion-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 0.5rem;
        }

        .suggestion-desc {
            font-size: 0.9rem;
            color: #7c2d12;
            margin-bottom: 0.5rem;
        }

        .suggestion-impact {
            font-size: 0.8rem;
            color: #a16207;
            font-weight: 500;
        }

        .capacity-timeline {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }

        .timeline-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .timeline-controls {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .timeline-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .timeline-btn.active {
            background: #3730a3;
            color: white;
            border-color: #3730a3;
        }

        .timeline-grid {
            display: grid;
            grid-template-columns: 150px repeat(12, 1fr);
            gap: 1px;
            background: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .timeline-header {
            background: #374151;
            color: white;
            padding: 0.75rem 0.5rem;
            text-align: center;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .timeline-row {
            background: white;
            padding: 0.75rem 0.5rem;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .timeline-cell {
            background: white;
            padding: 0.5rem;
            position: relative;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .allocation-bar {
            width: 100%;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
            color: white;
        }

        .allocation-0 { background: #f3f4f6; color: #6b7280; }
        .allocation-25 { background: #10b981; }
        .allocation-50 { background: #3b82f6; }
        .allocation-75 { background: #f59e0b; }
        .allocation-100 { background: #ef4444; }

        .optimization-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .optimization-metrics {
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .metric-trend {
            font-size: 0.8rem;
            margin-top: 0.25rem;
            font-weight: 500;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }

        .quick-actions {
            margin-bottom: 2rem;
        }

        .action-btn {
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .action-btn:hover {
            border-color: #3730a3;
            background: #ede9fe;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 300px 1fr;
            }

            .optimization-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .resource-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Resource Optimizer & Capacity Planner - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>⚡ Resource Optimizer & Capacity Planner</h2>
                <div class="capacity-indicator">
                    📊 Celková Kapacita: 87%
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="optimizeResources()">🤖 Auto-Optimalizácia</button>
                    <button class="btn btn-primary" onclick="generateForecast()">📈 Forecast</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="resource-sidebar">
                    <div class="sidebar-title">👥 Tímové Zdroje</div>

                    <div class="resource-list">
                        <div class="resource-item selected" data-resource="jan-novak">
                            <div class="resource-header">
                                <div class="resource-name">Ján Novák</div>
                                <div style="font-size: 0.8rem; color: #ef4444;">110%</div>
                            </div>
                            <div class="resource-role">Project Manager</div>
                            <div class="capacity-bar">
                                <div class="capacity-fill capacity-overloaded" style="width: 110%;"></div>
                            </div>
                            <div class="capacity-text">
                                <span>44h/40h týždenne</span>
                                <span>Preťažený</span>
                            </div>
                        </div>

                        <div class="resource-item" data-resource="maria-svobodova">
                            <div class="resource-header">
                                <div class="resource-name">Mária Svobodová</div>
                                <div style="font-size: 0.8rem; color: #10b981;">75%</div>
                            </div>
                            <div class="resource-role">UI/UX Designer</div>
                            <div class="capacity-bar">
                                <div class="capacity-fill capacity-optimal" style="width: 75%;"></div>
                            </div>
                            <div class="capacity-text">
                                <span>30h/40h týždenne</span>
                                <span>Optimálne</span>
                            </div>
                        </div>

                        <div class="resource-item" data-resource="peter-kovac">
                            <div class="resource-header">
                                <div class="resource-name">Peter Kováč</div>
                                <div style="font-size: 0.8rem; color: #f59e0b;">95%</div>
                            </div>
                            <div class="resource-role">Backend Developer</div>
                            <div class="capacity-bar">
                                <div class="capacity-fill capacity-high" style="width: 95%;"></div>
                            </div>
                            <div class="capacity-text">
                                <span>38h/40h týždenne</span>
                                <span>Vysoké</span>
                            </div>
                        </div>

                        <div class="resource-item" data-resource="anna-horvathova">
                            <div class="resource-header">
                                <div class="resource-name">Anna Horváthová</div>
                                <div style="font-size: 0.8rem; color: #10b981;">60%</div>
                            </div>
                            <div class="resource-role">QA Tester</div>
                            <div class="capacity-bar">
                                <div class="capacity-fill capacity-optimal" style="width: 60%;"></div>
                            </div>
                            <div class="capacity-text">
                                <span>24h/40h týždenne</span>
                                <span>Dostupná</span>
                            </div>
                        </div>

                        <div class="resource-item" data-resource="tomas-varga">
                            <div class="resource-header">
                                <div class="resource-name">Tomáš Varga</div>
                                <div style="font-size: 0.8rem; color: #f59e0b;">85%</div>
                            </div>
                            <div class="resource-role">Frontend Developer</div>
                            <div class="capacity-bar">
                                <div class="capacity-fill capacity-high" style="width: 85%;"></div>
                            </div>
                            <div class="capacity-text">
                                <span>34h/40h týždenne</span>
                                <span>Vysoké</span>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">📋 Projekty</div>
                    <div style="background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e5e7eb;">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">E-shop Modernizácia</div>
                        <div style="font-size: 0.8rem; color: #6b7280; margin-bottom: 0.5rem;">5 členov tímu</div>
                        <div style="font-size: 0.8rem; color: #6b7280;">Deadline: 30.09.2024</div>
                    </div>
                </div>

                <div class="main-area">
                    <div class="ai-optimizer">
                        <div class="ai-title">
                            🤖 AI Resource Optimizer
                        </div>

                        <div class="optimization-suggestions">
                            <div class="suggestion-card" onclick="applySuggestion(1)">
                                <div class="suggestion-title">Redistribúcia Úloh</div>
                                <div class="suggestion-desc">
                                    Presunúť 2 úlohy z Jána Nováka na Annu Horváthovu
                                </div>
                                <div class="suggestion-impact">
                                    💡 Úspora: 8 hodín, Zlepšenie: 15%
                                </div>
                            </div>

                            <div class="suggestion-card" onclick="applySuggestion(2)">
                                <div class="suggestion-title">Paralelizácia Práce</div>
                                <div class="suggestion-desc">
                                    Spustiť UI testing súčasne s backend vývojom
                                </div>
                                <div class="suggestion-impact">
                                    ⚡ Úspora: 5 dní, Efektívnosť: +20%
                                </div>
                            </div>

                            <div class="suggestion-card" onclick="applySuggestion(3)">
                                <div class="suggestion-title">Externý Support</div>
                                <div class="suggestion-desc">
                                    Najať freelance developera na 2 týždne
                                </div>
                                <div class="suggestion-impact">
                                    📈 Kapacita: +25%, Náklady: €3,200
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="capacity-timeline">
                        <div class="timeline-title">
                            📅 Kapacitný Timeline - Nasledujúce 3 Mesiace
                        </div>

                        <div class="timeline-controls">
                            <button class="timeline-btn active" data-view="weeks">Týždne</button>
                            <button class="timeline-btn" data-view="months">Mesiace</button>
                            <button class="timeline-btn" data-view="quarters">Kvartály</button>
                        </div>

                        <div class="timeline-grid" id="timelineGrid">
                            <div class="timeline-header">Člen Tímu</div>
                            <div class="timeline-header">T25</div>
                            <div class="timeline-header">T26</div>
                            <div class="timeline-header">T27</div>
                            <div class="timeline-header">T28</div>
                            <div class="timeline-header">T29</div>
                            <div class="timeline-header">T30</div>
                            <div class="timeline-header">T31</div>
                            <div class="timeline-header">T32</div>
                            <div class="timeline-header">T33</div>
                            <div class="timeline-header">T34</div>
                            <div class="timeline-header">T35</div>
                            <div class="timeline-header">T36</div>

                            <div class="timeline-row">Ján Novák</div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-100">100%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-100">110%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">85%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">80%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-50">70%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-50">65%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-25">40%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-25">35%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>

                            <div class="timeline-row">Mária Svobodová</div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">75%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">80%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-50">60%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-25">30%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>

                            <div class="timeline-row">Peter Kováč</div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">95%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-100">100%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-100">100%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">90%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-75">85%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-50">70%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-50">60%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-25">40%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                            <div class="timeline-cell"><div class="allocation-bar allocation-0">0%</div></div>
                        </div>
                    </div>
                </div>

                <div class="optimization-sidebar">
                    <div class="sidebar-title">📊 Optimalizačné Metriky</div>

                    <div class="optimization-metrics">
                        <div class="metric-card">
                            <div class="metric-value" style="color: #ef4444;">87%</div>
                            <div class="metric-label">Celková Kapacita</div>
                            <div class="metric-trend trend-up">↗ +12% za týždeň</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #f59e0b;">2.3</div>
                            <div class="metric-label">Preťažených Zdrojov</div>
                            <div class="metric-trend trend-down">↘ -0.7 za týždeň</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #10b981;">€2,400</div>
                            <div class="metric-label">Potenciálne Úspory</div>
                            <div class="metric-trend trend-up">↗ +€400 za týždeň</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #3730a3;">94%</div>
                            <div class="metric-label">Efektívnosť Tímu</div>
                            <div class="metric-trend trend-up">↗ +3% za týždeň</div>
                        </div>
                    </div>

                    <div class="sidebar-title">⚡ Rýchle Akcie</div>

                    <div class="quick-actions">
                        <button class="action-btn" onclick="balanceWorkload()">
                            ⚖️ Vyvážiť Pracovnú Záťaž
                        </button>
                        <button class="action-btn" onclick="findAvailableResources()">
                            🔍 Nájsť Dostupné Zdroje
                        </button>
                        <button class="action-btn" onclick="scheduleBreaks()">
                            ☕ Naplánovať Prestávky
                        </button>
                        <button class="action-btn" onclick="requestAdditionalResources()">
                            ➕ Požiadať o Ďalšie Zdroje
                        </button>
                        <button class="action-btn" onclick="createCapacityReport()">
                            📋 Vytvoriť Kapacitný Report
                        </button>
                    </div>

                    <div class="sidebar-title">🎯 AI Insights</div>

                    <div style="background: white; border-radius: 8px; padding: 1rem; font-size: 0.9rem; line-height: 1.4; color: #6b7280;">
                        <strong style="color: #1f2937;">Odporúčania:</strong><br><br>
                        • Ján Novák je preťažený - redistribuovať úlohy<br>
                        • Anna má voľnú kapacitu - pridať testing úlohy<br>
                        • Optimálny čas na hiring: nasledujúci mesiac<br>
                        • Predpokladané úspory: €2,400/mesiac
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedResource = 'jan-novak';

        function initPMComponent() {
            setupEventListeners();
            updateResourceDetails();
        }

        function setupEventListeners() {
            // Resource selection
            document.querySelectorAll('.resource-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.resource-item').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedResource = this.dataset.resource;
                    updateResourceDetails();
                });
            });

            // Timeline view controls
            document.querySelectorAll('.timeline-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.timeline-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    updateTimelineView(this.dataset.view);
                });
            });
        }

        function updateResourceDetails() {
            console.log('Aktualizujem detaily pre:', selectedResource);
            // Simulácia aktualizácie detailov vybraného zdroja
        }

        function updateTimelineView(view) {
            console.log('Prepínam timeline view na:', view);
            // Simulácia prepnutia pohľadu timeline
        }

        function optimizeResources() {
            alert('🤖 AI Resource Optimizer spustený!\n\n• Analýza aktuálnej kapacity: ✓\n• Identifikácia bottlenecks: ✓\n• Generovanie optimalizačných návrhov: ✓\n\nNájdené optimalizácie:\n✓ Redistribúcia 5 úloh\n✓ Paralelizácia 3 procesov\n✓ Úspora 12 hodín týždenne\n✓ Zlepšenie efektívnosti o 18%\n\nAplikovať optimalizácie?');
        }

        function generateForecast() {
            alert('📈 Generujem Capacity Forecast...\n\n• Analýza historických dát: ✓\n• Machine learning predikcie: ✓\n• Seasonal adjustments: ✓\n\nForecast na nasledujúce 3 mesiace:\n📊 Júl: 92% kapacita\n📊 August: 78% kapacita\n📊 September: 85% kapacita\n\nKritické obdobia:\n⚠️ Týždeň 28-30: Vysoká záťaž\n✅ Týždeň 35-40: Optimálna kapacita\n\nReport exportovaný do Excel!');
        }

        function applySuggestion(id) {
            const suggestions = {
                1: 'Redistribúcia úloh aplikovaná! Ján Novák: 110% → 95%, Anna Horváthová: 60% → 75%',
                2: 'Paralelizácia nastavená! UI testing začne súčasne s backend vývojom.',
                3: 'Požiadavka na externého developera odoslaná HR oddeleniu.'
            };
            alert('✅ ' + suggestions[id]);
        }

        function balanceWorkload() {
            alert('⚖️ Vyvažujem pracovnú záťaž...\n\n• Identifikované preťažené zdroje: 2\n• Dostupná kapacita: 35 hodín\n• Navrhované presuny: 5 úloh\n\nVýsledok:\n✓ Všetci členovia tímu pod 90% kapacity\n✓ Zlepšenie work-life balance\n✓ Zníženie rizika burnout\n\nZmeny aplikované!');
        }

        function findAvailableResources() {
            alert('🔍 Hľadám dostupné zdroje...\n\nInterne dostupné:\n• Anna Horváthová: 16h/týždeň\n• Mária Svobodová: 10h/týždeň\n\nExterne dostupné:\n• 3 freelance developeri\n• 2 QA testeri\n• 1 UI/UX designer\n\nOdporúčam kontaktovať Annu pre dodatočné úlohy.');
        }

        function scheduleBreaks() {
            alert('☕ Plánovanie prestávok...\n\n• Ján Novák: 2-týždňová dovolenka v auguste\n• Peter Kováč: 1 týždeň v júli\n• Tím building event: 15. júla\n\nKapacita počas prestávok:\n📉 Týždeň 29: -40% kapacita\n📉 Týždeň 33-34: -25% kapacita\n\nMitigation plány vytvorené!');
        }

        function requestAdditionalResources() {
            alert('➕ Požiadavka o ďalšie zdroje...\n\nOdporúčané pozície:\n• 1x Senior Backend Developer\n• 1x QA Automation Engineer\n\nOdôvodnenie:\n• Aktuálne preťaženie: 87%\n• Rastúci scope projektov\n• Potreba expertise v automation\n\nPožiadavka odoslaná manažmentu!');
        }

        function createCapacityReport() {
            alert('📋 Vytváram Capacity Report...\n\n• Current utilization: 87%\n• Bottlenecks identified: 2\n• Optimization opportunities: 5\n• Forecast accuracy: 94%\n\nReport obsahuje:\n✓ Executive summary\n✓ Resource utilization charts\n✓ Optimization recommendations\n✓ 3-month forecast\n\nReport exportovaný do PDF!');
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>