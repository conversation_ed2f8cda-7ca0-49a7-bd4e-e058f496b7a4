<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template <PERSON>rátor - Projektový <PERSON>ž<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #065f46 0%, #047857 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 350px 1fr 400px;
            min-height: 700px;
        }

        .template-categories {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .category-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .category-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }

        .category-item:hover {
            background: #e2e8f0;
        }

        .category-item.active {
            background: #6366f1;
            color: white;
        }

        .category-icon {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .category-count {
            margin-left: auto;
            background: #e5e7eb;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.8rem;
        }

        .category-item.active .category-count {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .main-area {
            padding: 2rem;
            overflow-y: auto;
        }

        .ai-generator {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .ai-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .generator-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 500;
            color: #92400e;
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            background: rgba(255,255,255,0.8);
            outline: none;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #d97706;
            background: white;
        }

        .generate-btn {
            background: #f59e0b;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .generate-btn:hover {
            background: #d97706;
            transform: translateY(-2px);
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .template-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .template-card:hover {
            border-color: #6366f1;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .template-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 1.1rem;
        }

        .template-type {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .template-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1rem;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #9ca3af;
            margin-bottom: 1rem;
        }

        .template-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-use {
            background: #10b981;
            color: white;
        }

        .btn-preview {
            background: #3b82f6;
            color: white;
        }

        .btn-edit {
            background: #f59e0b;
            color: white;
        }

        .pm-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .recent-templates {
            list-style: none;
        }

        .recent-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-left: 3px solid #6366f1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recent-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .recent-title {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .recent-date {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #6366f1;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .template-preview {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .preview-content {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
            color: #374151;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 300px 1fr;
            }
            
            .pm-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .template-categories {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Template Generátor - Projektový Manažment</h1>
        
        <div class="pm-component">
            <div class="pm-header">
                <h2>📄 Template Generátor</h2>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="importTemplate()">📥 Import Template</button>
                    <button class="btn btn-primary" onclick="exportTemplates()">📤 Export Všetko</button>
                </div>
            </div>
            
            <div class="pm-content">
                <div class="template-categories">
                    <div class="category-title">Kategórie Šablón</div>
                    
                    <div class="category-item active" data-category="all">
                        <span class="category-icon">📋</span>
                        <span>Všetky šablóny</span>
                        <span class="category-count">24</span>
                    </div>
                    
                    <div class="category-item" data-category="planning">
                        <span class="category-icon">📊</span>
                        <span>Plánovanie</span>
                        <span class="category-count">8</span>
                    </div>
                    
                    <div class="category-item" data-category="documentation">
                        <span class="category-icon">📝</span>
                        <span>Dokumentácia</span>
                        <span class="category-count">6</span>
                    </div>
                    
                    <div class="category-item" data-category="meetings">
                        <span class="category-icon">🤝</span>
                        <span>Meetingy</span>
                        <span class="category-count">4</span>
                    </div>
                    
                    <div class="category-item" data-category="reports">
                        <span class="category-icon">📈</span>
                        <span>Reporty</span>
                        <span class="category-count">3</span>
                    </div>
                    
                    <div class="category-item" data-category="processes">
                        <span class="category-icon">🔄</span>
                        <span>Procesy</span>
                        <span class="category-count">3</span>
                    </div>
                </div>

                <div class="main-area">
                    <div class="ai-generator">
                        <div class="ai-title">
                            🤖 AI Template Generátor
                        </div>
                        
                        <div class="generator-form">
                            <div class="form-group">
                                <label class="form-label">Typ Projektu</label>
                                <select class="form-select" id="projectType">
                                    <option value="web">Web Development</option>
                                    <option value="mobile">Mobile App</option>
                                    <option value="infrastructure">IT Infraštruktúra</option>
                                    <option value="marketing">Marketing Kampaň</option>
                                    <option value="research">Výskum a Vývoj</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Metodológia</label>
                                <select class="form-select" id="methodology">
                                    <option value="agile">Agile/Scrum</option>
                                    <option value="waterfall">Waterfall</option>
                                    <option value="kanban">Kanban</option>
                                    <option value="hybrid">Hybrid</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Veľkosť Tímu</label>
                                <select class="form-select" id="teamSize">
                                    <option value="small">Malý (2-5 ľudí)</option>
                                    <option value="medium">Stredný (6-15 ľudí)</option>
                                    <option value="large">Veľký (16+ ľudí)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Trvanie Projektu</label>
                                <select class="form-select" id="duration">
                                    <option value="short">Krátky (1-3 mesiace)</option>
                                    <option value="medium">Stredný (3-12 mesiacov)</option>
                                    <option value="long">Dlhý (12+ mesiacov)</option>
                                </select>
                            </div>
                            
                            <div class="form-group full-width">
                                <label class="form-label">Špecifické Požiadavky</label>
                                <textarea class="form-textarea" id="requirements" rows="3" placeholder="Opíšte špecifické potreby vašeho projektu..."></textarea>
                            </div>
                        </div>
                        
                        <button class="generate-btn" onclick="generateTemplates()">
                            🚀 Generovať Prispôsobené Šablóny
                        </button>
                    </div>

                    <div class="template-grid" id="templateGrid">
                        <!-- Templates budú vygenerované JavaScriptom -->
                    </div>
                </div>

                <div class="pm-sidebar">
                    <div class="sidebar-section">
                        <div class="sidebar-title">📊 Štatistiky</div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">24</div>
                                <div class="stat-label">Dostupné Šablóny</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">156</div>
                                <div class="stat-label">Použitia Tento Mesiac</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">8</div>
                                <div class="stat-label">Vlastné Šablóny</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">92%</div>
                                <div class="stat-label">Spokojnosť</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <div class="sidebar-title">🕒 Nedávno Použité</div>
                        <div class="recent-templates">
                            <div class="recent-item" onclick="useTemplate('sprint-planning')">
                                <div class="recent-title">Sprint Planning Template</div>
                                <div class="recent-date">Pred 2 hodinami</div>
                            </div>
                            <div class="recent-item" onclick="useTemplate('risk-assessment')">
                                <div class="recent-title">Risk Assessment</div>
                                <div class="recent-date">Včera</div>
                            </div>
                            <div class="recent-item" onclick="useTemplate('project-charter')">
                                <div class="recent-title">Project Charter</div>
                                <div class="recent-date">Pred 3 dňami</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <div class="sidebar-title">🎯 AI Odporúčania</div>
                        <div style="background: white; border-radius: 8px; padding: 1rem; font-size: 0.9rem; line-height: 1.4; color: #6b7280;">
                            Na základe vašich projektov odporúčame:
                            <br><br>
                            • Retrospective Meeting Template<br>
                            • API Documentation Template<br>
                            • User Story Template
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Simulované template dáta
        const templateData = {
            planning: [
                {
                    id: 1,
                    title: "Sprint Planning Template",
                    type: "Plánovanie",
                    description: "Komplexná šablóna pre plánovanie sprintov s user stories, acceptance criteria a estimation.",
                    category: "planning",
                    lastUsed: "2024-06-15",
                    usage: 45,
                    preview: "# Sprint Planning\n\n## Sprint Goal\n[Definujte cieľ sprintu]\n\n## User Stories\n- [ ] Story 1\n- [ ] Story 2\n\n## Acceptance Criteria\n..."
                },
                {
                    id: 2,
                    title: "Project Charter",
                    type: "Dokumentácia",
                    description: "Oficiálny dokument definujúci projekt, jeho ciele, scope a kľúčových stakeholderov.",
                    category: "planning",
                    lastUsed: "2024-06-12",
                    usage: 32,
                    preview: "# Project Charter\n\n## Project Overview\n[Popis projektu]\n\n## Objectives\n1. Objective 1\n2. Objective 2\n\n## Scope\n..."
                }
            ],
            documentation: [
                {
                    id: 3,
                    title: "API Documentation",
                    type: "Technická",
                    description: "Štandardizovaná šablóna pre dokumentáciu REST API s endpoints, parametrami a príkladmi.",
                    category: "documentation",
                    lastUsed: "2024-06-14",
                    usage: 28,
                    preview: "# API Documentation\n\n## Endpoints\n\n### GET /api/users\nReturns list of users\n\n**Parameters:**\n- limit (optional)\n..."
                }
            ],
            meetings: [
                {
                    id: 4,
                    title: "Retrospective Meeting",
                    type: "Meeting",
                    description: "Šablóna pre retrospective meetings s What went well, What didn't, Action items.",
                    category: "meetings",
                    lastUsed: "2024-06-13",
                    usage: 67,
                    preview: "# Sprint Retrospective\n\n## What went well?\n- Item 1\n- Item 2\n\n## What didn't go well?\n- Issue 1\n\n## Action Items\n..."
                }
            ]
        };

        let currentCategory = 'all';

        function initPMComponent() {
            renderTemplates();
            setupEventListeners();
        }

        function renderTemplates() {
            const grid = document.getElementById('templateGrid');
            grid.innerHTML = '';

            let templates = [];
            if (currentCategory === 'all') {
                templates = Object.values(templateData).flat();
            } else {
                templates = templateData[currentCategory] || [];
            }

            templates.forEach(template => {
                const card = createTemplateCard(template);
                grid.appendChild(card);
            });
        }

        function createTemplateCard(template) {
            const card = document.createElement('div');
            card.className = 'template-card';
            card.innerHTML = `
                <div class="template-header">
                    <div class="template-title">${template.title}</div>
                    <div class="template-type">${template.type}</div>
                </div>
                <div class="template-description">${template.description}</div>
                <div class="template-meta">
                    <span>Použité: ${template.usage}×</span>
                    <span>Naposledy: ${template.lastUsed}</span>
                </div>
                <div class="template-actions">
                    <button class="btn-small btn-use" onclick="useTemplate('${template.id}')">Použiť</button>
                    <button class="btn-small btn-preview" onclick="previewTemplate('${template.id}')">Náhľad</button>
                    <button class="btn-small btn-edit" onclick="editTemplate('${template.id}')">Upraviť</button>
                </div>
                <div class="template-preview" id="preview-${template.id}" style="display: none;">
                    <div class="preview-content">${template.preview}</div>
                </div>
            `;
            return card;
        }

        function setupEventListeners() {
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.category-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    currentCategory = this.dataset.category;
                    renderTemplates();
                });
            });
        }

        function generateTemplates() {
            const projectType = document.getElementById('projectType').value;
            const methodology = document.getElementById('methodology').value;
            const teamSize = document.getElementById('teamSize').value;
            const duration = document.getElementById('duration').value;
            const requirements = document.getElementById('requirements').value;

            alert(`🤖 AI Generátor vytvoril prispôsobené šablóny!\n\nNa základe vašich parametrov:\n• Typ: ${projectType}\n• Metodológia: ${methodology}\n• Tím: ${teamSize}\n• Trvanie: ${duration}\n\nVygenerované šablóny:\n✓ Prispôsobený Project Charter\n✓ Sprint Planning Template\n✓ Risk Management Plan\n✓ Communication Matrix\n✓ Quality Assurance Checklist\n\nŠablóny sú pripravené na použitie!`);
        }

        function useTemplate(templateId) {
            alert(`✅ Šablóna "${templateId}" bola použitá!\n\nNový dokument vytvorený v projektovom workspace.\nMôžete začať s editáciou a prispôsobením obsahu.`);
        }

        function previewTemplate(templateId) {
            const preview = document.getElementById(`preview-${templateId}`);
            if (preview.style.display === 'none') {
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        }

        function editTemplate(templateId) {
            alert(`✏️ Editácia šablóny "${templateId}"\n\nOtváram template editor...\n\nMôžete upraviť:\n• Obsah a štruktúru\n• Formátovanie\n• Premenné a placeholders\n• Validačné pravidlá`);
        }

        function importTemplate() {
            alert('📥 Import Template:\n\nPodporované formáty:\n• Word (.docx)\n• Excel (.xlsx)\n• Markdown (.md)\n• JSON (.json)\n\nVyberte súbor na import...');
        }

        function exportTemplates() {
            alert('📤 Export Všetkých Šablón:\n\n• ZIP archív vytvorený\n• Obsahuje 24 šablón\n• Formáty: Word, Excel, Markdown\n• Veľkosť: 2.4 MB\n\nSťahovanie začína...');
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
