/**
 * PM Master System v4 - Sample Data Generator
 * Generuje realistické sample dáta pre demonštráciu systému
 */

class PMSampleDataGenerator {
    constructor() {
        this.currentId = 1000;
        this.users = [];
        this.teams = [];
        this.projects = [];
        this.tasks = [];
    }

    // Generate unique ID
    generateId() {
        return ++this.currentId;
    }

    // Generate random date within range
    randomDate(start, end) {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }

    // Generate random element from array
    randomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    // Generate random number within range
    randomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Generate sample users
    generateUsers() {
        const names = [
            '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>rb<PERSON>', '<PERSON><PERSON>vid <PERSON>rejčí'
        ];

        const roles = ['admin', 'executive', 'manager', 'developer', 'client'];
        const skills = [
            'JavaScript', 'Python', 'Java', 'React', 'Angular', 'Vue.j<PERSON>', '<PERSON>de.js',
            'Project Management', 'Agile', 'Scrum', 'DevOps', 'UI/UX Design',
            'Data Analysis', 'Machine Learning', 'Cloud Computing', 'Cybersecurity'
        ];

        this.users = names.map((name, index) => ({
            id: this.generateId(),
            name,
            email: name.toLowerCase().replace(/\s+/g, '.').replace(/[áäâ]/g, 'a')
                .replace(/[éěê]/g, 'e').replace(/[íî]/g, 'i').replace(/[óôö]/g, 'o')
                .replace(/[úůü]/g, 'u').replace(/[ý]/g, 'y').replace(/[č]/g, 'c')
                .replace(/[ď]/g, 'd').replace(/[ň]/g, 'n').replace(/[ř]/g, 'r')
                .replace(/[š]/g, 's').replace(/[ť]/g, 't').replace(/[ž]/g, 'z') + '@company.sk',
            role: index < 2 ? 'admin' : index < 4 ? 'executive' : index < 8 ? 'manager' : 'developer',
            skills: this.randomElement([
                skills.slice(0, 3),
                skills.slice(2, 6),
                skills.slice(4, 8),
                skills.slice(6, 10)
            ]),
            availability: this.randomNumber(70, 100),
            hourlyRate: this.randomNumber(25, 85),
            createdAt: this.randomDate(new Date(2023, 0, 1), new Date()),
            updatedAt: new Date()
        }));

        return this.users;
    }

    // Generate sample teams
    generateTeams() {
        const teamNames = [
            'Frontend Development Team',
            'Backend Development Team',
            'DevOps & Infrastructure Team',
            'UI/UX Design Team',
            'Quality Assurance Team',
            'Data Analytics Team',
            'Project Management Office'
        ];

        this.teams = teamNames.map((name, index) => {
            const teamMembers = this.users
                .filter(() => Math.random() > 0.6)
                .slice(0, this.randomNumber(3, 6))
                .map(user => user.id);

            const leader = teamMembers[0] || this.users[index]?.id;

            return {
                id: this.generateId(),
                name,
                description: `Profesionálny tím zameraný na ${name.toLowerCase()}`,
                leaderId: leader,
                members: teamMembers,
                skills: this.users
                    .filter(user => teamMembers.includes(user.id))
                    .flatMap(user => user.skills)
                    .filter((skill, index, arr) => arr.indexOf(skill) === index),
                capacity: this.randomNumber(80, 95),
                createdAt: this.randomDate(new Date(2023, 0, 1), new Date()),
                updatedAt: new Date()
            };
        });

        return this.teams;
    }

    // Generate sample projects
    generateProjects() {
        const projectNames = [
            'E-shop Modernizácia',
            'CRM Systém Upgrade',
            'Mobile App Development',
            'Data Analytics Platform',
            'Cloud Migration Project',
            'AI Chatbot Implementation',
            'Security Audit & Compliance',
            'Performance Optimization',
            'API Gateway Development',
            'Customer Portal Redesign'
        ];

        const statuses = ['planning', 'active', 'on-hold', 'completed'];
        const priorities = ['low', 'medium', 'high', 'critical'];

        this.projects = projectNames.map((name, index) => {
            const startDate = this.randomDate(new Date(2023, 6, 1), new Date());
            const endDate = new Date(startDate.getTime() + this.randomNumber(30, 180) * 24 * 60 * 60 * 1000);
            const status = this.randomElement(statuses);
            const progress = status === 'completed' ? 100 : 
                           status === 'planning' ? this.randomNumber(0, 20) :
                           status === 'active' ? this.randomNumber(20, 80) :
                           this.randomNumber(10, 60);

            return {
                id: this.generateId(),
                name,
                description: `Komplexný projekt ${name.toLowerCase()} s moderními technológiami a best practices.`,
                status,
                priority: this.randomElement(priorities),
                startDate,
                endDate,
                budget: this.randomNumber(50000, 500000),
                teamId: this.teams[index % this.teams.length]?.id || this.teams[0].id,
                progress,
                createdAt: this.randomDate(new Date(2023, 0, 1), startDate),
                updatedAt: new Date()
            };
        });

        return this.projects;
    }

    // Generate sample tasks
    generateTasks() {
        const taskTemplates = [
            'Analýza požiadaviek',
            'Návrh architektúry',
            'Database design',
            'Frontend implementácia',
            'Backend API development',
            'Unit testing',
            'Integration testing',
            'UI/UX design',
            'Code review',
            'Deployment preparation',
            'Documentation',
            'Performance testing',
            'Security audit',
            'Bug fixing',
            'Feature enhancement'
        ];

        const statuses = ['todo', 'in-progress', 'review', 'done', 'blocked'];
        const priorities = ['low', 'medium', 'high', 'critical'];

        this.tasks = [];

        this.projects.forEach(project => {
            const taskCount = this.randomNumber(8, 15);
            const projectTeam = this.teams.find(team => team.id === project.teamId);
            
            for (let i = 0; i < taskCount; i++) {
                const taskName = this.randomElement(taskTemplates);
                const status = this.randomElement(statuses);
                const estimatedHours = this.randomNumber(4, 40);
                const actualHours = status === 'done' ? this.randomNumber(estimatedHours * 0.8, estimatedHours * 1.2) :
                                 status === 'in-progress' ? this.randomNumber(0, estimatedHours * 0.7) : 0;

                this.tasks.push({
                    id: this.generateId(),
                    projectId: project.id,
                    name: `${taskName} - ${project.name}`,
                    description: `Detailná implementácia úlohy ${taskName.toLowerCase()} pre projekt ${project.name}.`,
                    status,
                    priority: this.randomElement(priorities),
                    assigneeId: projectTeam ? this.randomElement(projectTeam.members) : null,
                    estimatedHours,
                    actualHours,
                    dueDate: this.randomDate(project.startDate, project.endDate),
                    tags: this.randomElement([
                        ['frontend', 'react'],
                        ['backend', 'api'],
                        ['testing', 'qa'],
                        ['design', 'ui'],
                        ['devops', 'deployment'],
                        ['analysis', 'documentation']
                    ]),
                    createdAt: this.randomDate(project.startDate, new Date()),
                    updatedAt: new Date()
                });
            }
        });

        return this.tasks;
    }

    // Generate analytics data
    generateAnalytics() {
        const now = new Date();
        const analytics = {
            overview: {
                totalProjects: this.projects.length,
                activeProjects: this.projects.filter(p => p.status === 'active').length,
                completedProjects: this.projects.filter(p => p.status === 'completed').length,
                totalTasks: this.tasks.length,
                completedTasks: this.tasks.filter(t => t.status === 'done').length,
                totalTeamMembers: this.users.length,
                totalBudget: this.projects.reduce((sum, p) => sum + p.budget, 0),
                lastUpdated: now
            },
            
            performance: {
                projectSuccessRate: 94.7,
                teamEfficiency: 87,
                onTimeDelivery: 96,
                budgetVariance: 4.2,
                qualityScore: 94,
                clientSatisfaction: 4.8,
                lastCalculated: now
            },
            
            trends: {
                monthly: Array.from({ length: 12 }, (_, i) => ({
                    month: new Date(2024, i, 1).toLocaleDateString('sk-SK', { month: 'short' }),
                    projects: this.randomNumber(8, 15),
                    tasks: this.randomNumber(120, 200),
                    efficiency: this.randomNumber(80, 95)
                })),
                weekly: Array.from({ length: 4 }, (_, i) => ({
                    week: `Týždeň ${i + 1}`,
                    productivity: this.randomNumber(85, 98),
                    quality: this.randomNumber(90, 99),
                    satisfaction: this.randomNumber(4.5, 5.0)
                }))
            },
            
            resources: {
                utilization: {
                    development: 85,
                    testing: 78,
                    design: 92,
                    management: 88
                },
                availability: this.teams.map(team => ({
                    teamId: team.id,
                    teamName: team.name,
                    capacity: team.capacity,
                    utilization: this.randomNumber(70, 95),
                    efficiency: this.randomNumber(80, 98)
                }))
            }
        };

        return analytics;
    }

    // Generate system settings
    generateSettings() {
        return {
            system: {
                version: '4.0.0',
                environment: 'production',
                maintenanceMode: false,
                lastBackup: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
                nextMaintenance: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next week
            },
            
            notifications: {
                email: true,
                push: true,
                slack: true,
                frequency: 'daily',
                types: ['project_updates', 'task_assignments', 'deadlines', 'system_alerts']
            },
            
            integrations: {
                jira: { enabled: true, status: 'connected', lastSync: new Date() },
                slack: { enabled: true, status: 'connected', lastSync: new Date() },
                github: { enabled: true, status: 'connected', lastSync: new Date() },
                office365: { enabled: true, status: 'syncing', lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000) },
                sap: { enabled: false, status: 'error', lastSync: null },
                tableau: { enabled: true, status: 'connected', lastSync: new Date() }
            },
            
            ai: {
                enabled: true,
                provider: 'openai',
                model: 'gpt-4',
                features: {
                    documentAnalysis: true,
                    riskPrediction: true,
                    resourceOptimization: true,
                    performanceInsights: true,
                    budgetForecasting: true,
                    templateGeneration: true
                },
                usage: {
                    requestsToday: this.randomNumber(150, 300),
                    requestsThisMonth: this.randomNumber(3000, 6000),
                    averageResponseTime: this.randomNumber(800, 1500)
                }
            }
        };
    }

    // Generate all sample data
    generateAllData() {
        console.log('🎲 Generating sample data...');
        
        const users = this.generateUsers();
        const teams = this.generateTeams();
        const projects = this.generateProjects();
        const tasks = this.generateTasks();
        const analytics = this.generateAnalytics();
        const settings = this.generateSettings();

        const sampleData = {
            users,
            teams,
            projects,
            tasks,
            analytics,
            settings,
            metadata: {
                generated: new Date(),
                version: '4.0.0',
                totalRecords: users.length + teams.length + projects.length + tasks.length
            }
        };

        console.log(`✅ Generated ${sampleData.metadata.totalRecords} sample records`);
        return sampleData;
    }

    // Load sample data into data manager
    async loadIntoDataManager() {
        const data = this.generateAllData();
        
        try {
            // Load users
            for (const user of data.users) {
                await PMDataManager.create('users', user);
            }
            
            // Load teams
            for (const team of data.teams) {
                await PMDataManager.create('teams', team);
            }
            
            // Load projects
            for (const project of data.projects) {
                await PMDataManager.create('projects', project);
            }
            
            // Load tasks
            for (const task of data.tasks) {
                await PMDataManager.create('tasks', task);
            }
            
            // Store analytics and settings directly
            PMDataManager.storage.set('analytics', data.analytics);
            PMDataManager.storage.set('settings', data.settings);
            
            console.log('✅ Sample data loaded into Data Manager');
            
            // Emit event
            PMEventBus.emit('sampleData.loaded', {
                totalRecords: data.metadata.totalRecords,
                timestamp: new Date()
            });
            
            return { success: true, data: data.metadata };
        } catch (error) {
            console.error('❌ Failed to load sample data:', error);
            return { success: false, error: error.message };
        }
    }

    // Clear and reload sample data
    async reloadSampleData() {
        console.log('🔄 Reloading sample data...');
        
        // Clear existing data
        PMDataManager.clearAllData();
        
        // Load new sample data
        return await this.loadIntoDataManager();
    }
}

// Initialize sample data generator
window.PMSampleDataGenerator = new PMSampleDataGenerator();

// Auto-load sample data if no data exists
document.addEventListener('DOMContentLoaded', async () => {
    // Check if data already exists
    const existingProjects = await PMDataManager.read('projects');
    
    if (!existingProjects.success || existingProjects.data.length === 0) {
        console.log('📊 No existing data found, loading sample data...');
        await PMSampleDataGenerator.loadIntoDataManager();
    } else {
        console.log(`📊 Found ${existingProjects.data.length} existing projects, skipping sample data load`);
    }
});

console.log('🎲 PM Sample Data Generator Initialized - v4.0.0');
