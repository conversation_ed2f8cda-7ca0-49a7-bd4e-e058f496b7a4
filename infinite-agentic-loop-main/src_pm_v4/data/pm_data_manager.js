/**
 * PM Master System v4 - Data Persistence Manager
 * Centralizovaný manažment dát s offline/online synchronizáciou
 */

// Data validation schemas
const DataSchemas = {
    project: {
        id: { type: 'number', required: true },
        name: { type: 'string', required: true, minLength: 3 },
        description: { type: 'string', required: false },
        status: { type: 'string', enum: ['planning', 'active', 'on-hold', 'completed', 'cancelled'] },
        priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
        startDate: { type: 'date', required: true },
        endDate: { type: 'date', required: true },
        budget: { type: 'number', min: 0 },
        teamId: { type: 'number', required: true },
        progress: { type: 'number', min: 0, max: 100 },
        createdAt: { type: 'date', required: true },
        updatedAt: { type: 'date', required: true }
    },
    
    task: {
        id: { type: 'number', required: true },
        projectId: { type: 'number', required: true },
        name: { type: 'string', required: true, minLength: 3 },
        description: { type: 'string', required: false },
        status: { type: 'string', enum: ['todo', 'in-progress', 'review', 'done', 'blocked'] },
        priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
        assigneeId: { type: 'number', required: false },
        estimatedHours: { type: 'number', min: 0 },
        actualHours: { type: 'number', min: 0 },
        dueDate: { type: 'date', required: false },
        tags: { type: 'array', items: { type: 'string' } },
        createdAt: { type: 'date', required: true },
        updatedAt: { type: 'date', required: true }
    },
    
    team: {
        id: { type: 'number', required: true },
        name: { type: 'string', required: true, minLength: 3 },
        description: { type: 'string', required: false },
        leaderId: { type: 'number', required: true },
        members: { type: 'array', items: { type: 'number' } },
        skills: { type: 'array', items: { type: 'string' } },
        capacity: { type: 'number', min: 0, max: 100 },
        createdAt: { type: 'date', required: true },
        updatedAt: { type: 'date', required: true }
    },
    
    user: {
        id: { type: 'number', required: true },
        name: { type: 'string', required: true, minLength: 2 },
        email: { type: 'string', required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
        role: { type: 'string', enum: ['admin', 'executive', 'manager', 'developer', 'client'] },
        skills: { type: 'array', items: { type: 'string' } },
        availability: { type: 'number', min: 0, max: 100 },
        hourlyRate: { type: 'number', min: 0 },
        createdAt: { type: 'date', required: true },
        updatedAt: { type: 'date', required: true }
    }
};

// Data Validator
class PMDataValidator {
    static validate(data, schema) {
        const errors = [];
        
        for (const [field, rules] of Object.entries(schema)) {
            const value = data[field];
            
            // Required field check
            if (rules.required && (value === undefined || value === null)) {
                errors.push(`Field '${field}' is required`);
                continue;
            }
            
            // Skip validation if field is not required and not present
            if (!rules.required && (value === undefined || value === null)) {
                continue;
            }
            
            // Type validation
            if (rules.type && !this.validateType(value, rules.type)) {
                errors.push(`Field '${field}' must be of type ${rules.type}`);
                continue;
            }
            
            // String validations
            if (rules.type === 'string') {
                if (rules.minLength && value.length < rules.minLength) {
                    errors.push(`Field '${field}' must be at least ${rules.minLength} characters long`);
                }
                if (rules.maxLength && value.length > rules.maxLength) {
                    errors.push(`Field '${field}' must be at most ${rules.maxLength} characters long`);
                }
                if (rules.pattern && !rules.pattern.test(value)) {
                    errors.push(`Field '${field}' does not match required pattern`);
                }
            }
            
            // Number validations
            if (rules.type === 'number') {
                if (rules.min !== undefined && value < rules.min) {
                    errors.push(`Field '${field}' must be at least ${rules.min}`);
                }
                if (rules.max !== undefined && value > rules.max) {
                    errors.push(`Field '${field}' must be at most ${rules.max}`);
                }
            }
            
            // Enum validation
            if (rules.enum && !rules.enum.includes(value)) {
                errors.push(`Field '${field}' must be one of: ${rules.enum.join(', ')}`);
            }
            
            // Array validation
            if (rules.type === 'array') {
                if (!Array.isArray(value)) {
                    errors.push(`Field '${field}' must be an array`);
                } else if (rules.items) {
                    value.forEach((item, index) => {
                        if (!this.validateType(item, rules.items.type)) {
                            errors.push(`Field '${field}[${index}]' must be of type ${rules.items.type}`);
                        }
                    });
                }
            }
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    static validateType(value, type) {
        switch (type) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'boolean':
                return typeof value === 'boolean';
            case 'date':
                return value instanceof Date || !isNaN(Date.parse(value));
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return true;
        }
    }
}

// Local Storage Manager
class PMLocalStorage {
    constructor(prefix = 'pm_v4_') {
        this.prefix = prefix;
        this.compressionEnabled = true;
    }
    
    // Set data with optional compression
    set(key, data, options = {}) {
        try {
            const fullKey = this.prefix + key;
            const timestamp = Date.now();
            
            const payload = {
                data,
                timestamp,
                version: PMSystemConfig.version,
                compressed: false,
                ...options.metadata
            };
            
            let serialized = JSON.stringify(payload);
            
            // Compress large data
            if (this.compressionEnabled && serialized.length > 10000) {
                serialized = this.compress(serialized);
                payload.compressed = true;
            }
            
            localStorage.setItem(fullKey, serialized);
            
            // Emit storage event
            PMEventBus.emit('storage.set', { key, data, timestamp });
            
            return true;
        } catch (error) {
            console.error(`LocalStorage set error for key ${key}:`, error);
            return false;
        }
    }
    
    // Get data with decompression
    get(key, defaultValue = null) {
        try {
            const fullKey = this.prefix + key;
            const stored = localStorage.getItem(fullKey);
            
            if (!stored) {
                return defaultValue;
            }
            
            let payload = JSON.parse(stored);
            
            // Decompress if needed
            if (payload.compressed) {
                const decompressed = this.decompress(stored);
                payload = JSON.parse(decompressed);
            }
            
            // Check version compatibility
            if (payload.version && payload.version !== PMSystemConfig.version) {
                console.warn(`Version mismatch for key ${key}: stored ${payload.version}, current ${PMSystemConfig.version}`);
            }
            
            return payload.data;
        } catch (error) {
            console.error(`LocalStorage get error for key ${key}:`, error);
            return defaultValue;
        }
    }
    
    // Remove data
    remove(key) {
        try {
            const fullKey = this.prefix + key;
            localStorage.removeItem(fullKey);
            PMEventBus.emit('storage.remove', { key });
            return true;
        } catch (error) {
            console.error(`LocalStorage remove error for key ${key}:`, error);
            return false;
        }
    }
    
    // Clear all PM data
    clear() {
        try {
            const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
            keys.forEach(key => localStorage.removeItem(key));
            PMEventBus.emit('storage.clear', { count: keys.length });
            return true;
        } catch (error) {
            console.error('LocalStorage clear error:', error);
            return false;
        }
    }
    
    // Get all keys
    keys() {
        return Object.keys(localStorage)
            .filter(key => key.startsWith(this.prefix))
            .map(key => key.substring(this.prefix.length));
    }
    
    // Get storage usage
    getUsage() {
        let totalSize = 0;
        const keys = this.keys();
        
        keys.forEach(key => {
            const data = localStorage.getItem(this.prefix + key);
            totalSize += data ? data.length : 0;
        });
        
        return {
            keys: keys.length,
            sizeBytes: totalSize,
            sizeMB: (totalSize / 1024 / 1024).toFixed(2)
        };
    }
    
    // Simple compression (base64 encoding for now)
    compress(data) {
        try {
            return btoa(unescape(encodeURIComponent(data)));
        } catch (error) {
            console.warn('Compression failed, using original data');
            return data;
        }
    }
    
    // Simple decompression
    decompress(data) {
        try {
            return decodeURIComponent(escape(atob(data)));
        } catch (error) {
            console.warn('Decompression failed, using original data');
            return data;
        }
    }
}

// Main Data Manager
class PMDataManager {
    constructor() {
        this.storage = new PMLocalStorage();
        this.cache = new Map();
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.lastSyncTime = null;
        
        // Setup online/offline detection
        this.setupNetworkDetection();
        
        // Initialize data collections
        this.initializeCollections();
        
        console.log('📊 PM Data Manager initialized');
    }
    
    // Initialize data collections
    initializeCollections() {
        const collections = ['projects', 'tasks', 'teams', 'users', 'analytics', 'settings'];
        
        collections.forEach(collection => {
            if (!this.storage.get(collection)) {
                this.storage.set(collection, []);
            }
        });
    }
    
    // Setup network detection
    setupNetworkDetection() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            PMEventBus.emit('network.online');
            this.processSyncQueue();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            PMEventBus.emit('network.offline');
        });
    }
    
    // Create new record
    async create(collection, data) {
        try {
            // Validate data
            const schema = DataSchemas[collection.slice(0, -1)]; // Remove 's' from collection name
            if (schema) {
                const validation = PMDataValidator.validate(data, schema);
                if (!validation.valid) {
                    throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
                }
            }
            
            // Add metadata
            const now = new Date();
            const record = {
                ...data,
                id: data.id || this.generateId(),
                createdAt: now,
                updatedAt: now
            };
            
            // Get existing records
            const records = this.storage.get(collection, []);
            
            // Check for duplicate ID
            if (records.find(r => r.id === record.id)) {
                throw new Error(`Record with ID ${record.id} already exists`);
            }
            
            // Add record
            records.push(record);
            this.storage.set(collection, records);
            
            // Update cache
            this.cache.set(`${collection}_${record.id}`, record);
            
            // Add to sync queue if online
            if (this.isOnline) {
                this.addToSyncQueue('create', collection, record);
            }
            
            // Emit event
            PMEventBus.emit(`data.${collection}.created`, { record });
            
            return { success: true, data: record };
        } catch (error) {
            console.error(`Create error for ${collection}:`, error);
            return { success: false, error: error.message };
        }
    }
    
    // Read records
    async read(collection, filters = {}) {
        try {
            const records = this.storage.get(collection, []);
            
            // Apply filters
            let filteredRecords = records;
            
            if (Object.keys(filters).length > 0) {
                filteredRecords = records.filter(record => {
                    return Object.entries(filters).every(([key, value]) => {
                        if (Array.isArray(value)) {
                            return value.includes(record[key]);
                        }
                        return record[key] === value;
                    });
                });
            }
            
            return { success: true, data: filteredRecords };
        } catch (error) {
            console.error(`Read error for ${collection}:`, error);
            return { success: false, error: error.message };
        }
    }
    
    // Read single record by ID
    async readById(collection, id) {
        try {
            // Check cache first
            const cacheKey = `${collection}_${id}`;
            if (this.cache.has(cacheKey)) {
                return { success: true, data: this.cache.get(cacheKey) };
            }
            
            const records = this.storage.get(collection, []);
            const record = records.find(r => r.id === id);
            
            if (!record) {
                return { success: false, error: 'Record not found' };
            }
            
            // Update cache
            this.cache.set(cacheKey, record);
            
            return { success: true, data: record };
        } catch (error) {
            console.error(`ReadById error for ${collection}:`, error);
            return { success: false, error: error.message };
        }
    }
    
    // Update record
    async update(collection, id, data) {
        try {
            const records = this.storage.get(collection, []);
            const index = records.findIndex(r => r.id === id);
            
            if (index === -1) {
                return { success: false, error: 'Record not found' };
            }
            
            // Validate data
            const schema = DataSchemas[collection.slice(0, -1)];
            if (schema) {
                const mergedData = { ...records[index], ...data };
                const validation = PMDataValidator.validate(mergedData, schema);
                if (!validation.valid) {
                    throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
                }
            }
            
            // Update record
            const updatedRecord = {
                ...records[index],
                ...data,
                updatedAt: new Date()
            };
            
            records[index] = updatedRecord;
            this.storage.set(collection, records);
            
            // Update cache
            this.cache.set(`${collection}_${id}`, updatedRecord);
            
            // Add to sync queue
            if (this.isOnline) {
                this.addToSyncQueue('update', collection, updatedRecord);
            }
            
            // Emit event
            PMEventBus.emit(`data.${collection}.updated`, { record: updatedRecord });
            
            return { success: true, data: updatedRecord };
        } catch (error) {
            console.error(`Update error for ${collection}:`, error);
            return { success: false, error: error.message };
        }
    }
    
    // Delete record
    async delete(collection, id) {
        try {
            const records = this.storage.get(collection, []);
            const index = records.findIndex(r => r.id === id);
            
            if (index === -1) {
                return { success: false, error: 'Record not found' };
            }
            
            const deletedRecord = records[index];
            records.splice(index, 1);
            this.storage.set(collection, records);
            
            // Remove from cache
            this.cache.delete(`${collection}_${id}`);
            
            // Add to sync queue
            if (this.isOnline) {
                this.addToSyncQueue('delete', collection, { id });
            }
            
            // Emit event
            PMEventBus.emit(`data.${collection}.deleted`, { record: deletedRecord });
            
            return { success: true, data: deletedRecord };
        } catch (error) {
            console.error(`Delete error for ${collection}:`, error);
            return { success: false, error: error.message };
        }
    }
    
    // Generate unique ID
    generateId() {
        return Date.now() + Math.floor(Math.random() * 1000);
    }
    
    // Add operation to sync queue
    addToSyncQueue(operation, collection, data) {
        this.syncQueue.push({
            id: this.generateId(),
            operation,
            collection,
            data,
            timestamp: new Date(),
            retries: 0
        });
    }
    
    // Process sync queue
    async processSyncQueue() {
        if (!this.isOnline || this.syncQueue.length === 0) {
            return;
        }
        
        console.log(`📤 Processing sync queue: ${this.syncQueue.length} operations`);
        
        const operations = [...this.syncQueue];
        this.syncQueue = [];
        
        for (const operation of operations) {
            try {
                // Simulate API call (will be replaced with real API)
                await this.syncToServer(operation);
                console.log(`✅ Synced: ${operation.operation} ${operation.collection}`);
            } catch (error) {
                console.error(`❌ Sync failed: ${operation.operation} ${operation.collection}`, error);
                
                // Retry logic
                operation.retries++;
                if (operation.retries < 3) {
                    this.syncQueue.push(operation);
                }
            }
        }
        
        this.lastSyncTime = new Date();
        PMEventBus.emit('data.synced', { timestamp: this.lastSyncTime });
    }
    
    // Sync operation to server (mock for now)
    async syncToServer(operation) {
        // This will be replaced with real API calls
        return new Promise((resolve) => {
            setTimeout(resolve, 100); // Simulate network delay
        });
    }
    
    // Get storage statistics
    getStorageStats() {
        const usage = this.storage.getUsage();
        const cacheSize = this.cache.size;
        const queueSize = this.syncQueue.length;
        
        return {
            ...usage,
            cacheEntries: cacheSize,
            pendingSync: queueSize,
            isOnline: this.isOnline,
            lastSync: this.lastSyncTime
        };
    }
    
    // Clear all data
    clearAllData() {
        this.storage.clear();
        this.cache.clear();
        this.syncQueue = [];
        this.initializeCollections();
        PMEventBus.emit('data.cleared');
    }
}

// Export to global scope
window.PMDataManager = new PMDataManager();
window.PMDataValidator = PMDataValidator;
window.PMLocalStorage = PMLocalStorage;
window.DataSchemas = DataSchemas;

console.log('💾 PM Data Persistence Layer Initialized - v4.0.0');
