<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quality Assurance & Testing Manager - Proje<PERSON><PERSON><PERSON>žment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f766e 0%, #0d9488 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .quality-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            min-height: 700px;
        }

        .test-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .test-suite-list {
            margin-bottom: 2rem;
        }

        .test-suite-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-suite-item:hover {
            border-color: #0d9488;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .test-suite-item.selected {
            border-color: #0d9488;
            background: #f0fdfa;
        }

        .suite-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .suite-name {
            font-weight: 600;
            color: #1f2937;
        }

        .suite-status {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-passed { background: #d1fae5; color: #065f46; }
        .status-failed { background: #fee2e2; color: #991b1b; }
        .status-running { background: #dbeafe; color: #1e40af; }
        .status-pending { background: #f3f4f6; color: #6b7280; }

        .suite-progress {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-passed { background: #10b981; }
        .progress-failed { background: #ef4444; }
        .progress-running { background: #3b82f6; }

        .suite-stats {
            font-size: 0.8rem;
            color: #6b7280;
            display: flex;
            justify-content: space-between;
        }

        .main-testing {
            padding: 2rem;
            display: flex;
            flex-direction: column;
        }

        .testing-controls {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }

        .controls-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .control-select, .control-input {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .control-select:focus, .control-input:focus {
            border-color: #0d9488;
        }

        .run-tests-btn {
            background: #0d9488;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .run-tests-btn:hover {
            background: #0f766e;
            transform: translateY(-2px);
        }

        .test-results {
            flex: 1;
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
        }

        .results-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-case-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .test-case {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .test-case:hover {
            border-color: #0d9488;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .case-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .case-name {
            font-weight: 500;
            color: #1f2937;
        }

        .case-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .case-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .case-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #9ca3af;
        }

        .case-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .test-case:hover .case-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 0.25rem 0.75rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            border-color: #0d9488;
            color: #0d9488;
        }

        .qa-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .qa-metrics {
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .metric-trend {
            font-size: 0.8rem;
            margin-top: 0.25rem;
            font-weight: 500;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #6b7280; }

        .coverage-chart {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }

        .chart-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            text-align: center;
        }

        .coverage-ring {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        .coverage-ring svg {
            transform: rotate(-90deg);
        }

        .coverage-ring-bg {
            fill: none;
            stroke: #e5e7eb;
            stroke-width: 8;
        }

        .coverage-ring-progress {
            fill: none;
            stroke: #0d9488;
            stroke-width: 8;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }

        .coverage-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .coverage-percentage {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0d9488;
        }

        .coverage-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .bug-tracker {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .bug-item {
            padding: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .bug-item:hover {
            background: #f9fafb;
        }

        .bug-item:last-child {
            border-bottom: none;
        }

        .bug-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .bug-title {
            font-weight: 500;
            color: #1f2937;
            font-size: 0.9rem;
        }

        .bug-severity {
            padding: 0.2rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .severity-critical { background: #fee2e2; color: #991b1b; }
        .severity-high { background: #fef3c7; color: #92400e; }
        .severity-medium { background: #dbeafe; color: #1e40af; }
        .severity-low { background: #d1fae5; color: #065f46; }

        .bug-description {
            font-size: 0.8rem;
            color: #6b7280;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 250px 1fr;
            }

            .qa-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .test-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Quality Assurance & Testing Manager - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>🧪 Quality Assurance & Testing Manager</h2>
                <div class="quality-indicator">
                    ✅ Test Coverage: 87%
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="runAllTests()">🚀 Spustiť Všetky Testy</button>
                    <button class="btn btn-primary" onclick="generateQAReport()">📊 QA Report</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="test-sidebar">
                    <div class="sidebar-title">🧪 Test Suites</div>

                    <div class="test-suite-list">
                        <div class="test-suite-item selected" data-suite="unit-tests">
                            <div class="suite-header">
                                <div class="suite-name">Unit Tests</div>
                                <div class="suite-status status-passed">Passed</div>
                            </div>
                            <div class="suite-progress">
                                <div class="progress-fill progress-passed" style="width: 95%;"></div>
                            </div>
                            <div class="suite-stats">
                                <span>142/150 testov</span>
                                <span>95% úspešnosť</span>
                            </div>
                        </div>

                        <div class="test-suite-item" data-suite="integration-tests">
                            <div class="suite-header">
                                <div class="suite-name">Integration Tests</div>
                                <div class="suite-status status-running">Running</div>
                            </div>
                            <div class="suite-progress">
                                <div class="progress-fill progress-running" style="width: 60%;"></div>
                            </div>
                            <div class="suite-stats">
                                <span>36/60 testov</span>
                                <span>60% dokončené</span>
                            </div>
                        </div>

                        <div class="test-suite-item" data-suite="e2e-tests">
                            <div class="suite-header">
                                <div class="suite-name">E2E Tests</div>
                                <div class="suite-status status-failed">Failed</div>
                            </div>
                            <div class="suite-progress">
                                <div class="progress-fill progress-failed" style="width: 75%;"></div>
                            </div>
                            <div class="suite-stats">
                                <span>18/24 testov</span>
                                <span>3 zlyhania</span>
                            </div>
                        </div>

                        <div class="test-suite-item" data-suite="performance-tests">
                            <div class="suite-header">
                                <div class="suite-name">Performance Tests</div>
                                <div class="suite-status status-pending">Pending</div>
                            </div>
                            <div class="suite-progress">
                                <div class="progress-fill" style="width: 0%;"></div>
                            </div>
                            <div class="suite-stats">
                                <span>0/15 testov</span>
                                <span>Čaká na spustenie</span>
                            </div>
                        </div>

                        <div class="test-suite-item" data-suite="security-tests">
                            <div class="suite-header">
                                <div class="suite-name">Security Tests</div>
                                <div class="suite-status status-passed">Passed</div>
                            </div>
                            <div class="suite-progress">
                                <div class="progress-fill progress-passed" style="width: 100%;"></div>
                            </div>
                            <div class="suite-stats">
                                <span>28/28 testov</span>
                                <span>100% úspešnosť</span>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">🎯 Test Kategórie</div>
                    <div style="background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e5e7eb;">
                        <div style="font-weight: 500; margin-bottom: 0.5rem;">Aktívne Kategórie:</div>
                        <div style="font-size: 0.9rem; color: #6b7280; line-height: 1.4;">
                            • Frontend Components<br>
                            • API Endpoints<br>
                            • Database Operations<br>
                            • User Workflows<br>
                            • Security Validations
                        </div>
                    </div>
                </div>

                <div class="main-testing">
                    <div class="testing-controls">
                        <div class="controls-title">
                            ⚙️ Testing Configuration
                        </div>

                        <div class="controls-grid">
                            <div class="control-group">
                                <div class="control-label">Test Environment</div>
                                <select class="control-select" id="testEnvironment">
                                    <option value="development">Development</option>
                                    <option value="staging" selected>Staging</option>
                                    <option value="production">Production</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <div class="control-label">Browser</div>
                                <select class="control-select" id="browser">
                                    <option value="chrome" selected>Chrome</option>
                                    <option value="firefox">Firefox</option>
                                    <option value="safari">Safari</option>
                                    <option value="edge">Edge</option>
                                    <option value="all">Všetky Browsery</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <div class="control-label">Test Type</div>
                                <select class="control-select" id="testType">
                                    <option value="smoke">Smoke Tests</option>
                                    <option value="regression" selected>Regression Tests</option>
                                    <option value="full">Full Test Suite</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <div class="control-label">Parallel Execution</div>
                                <select class="control-select" id="parallelExecution">
                                    <option value="1">Sequential</option>
                                    <option value="2">2 Threads</option>
                                    <option value="4" selected>4 Threads</option>
                                    <option value="8">8 Threads</option>
                                </select>
                            </div>
                        </div>

                        <button class="run-tests-btn" onclick="runSelectedTests()">
                            🚀 Spustiť Vybrané Testy
                        </button>
                    </div>

                    <div class="test-results">
                        <div class="results-title">
                            📋 Test Results - Unit Tests
                        </div>

                        <div class="test-case-list" id="testCaseList">
                            <div class="test-case">
                                <div class="case-header">
                                    <div class="case-name">test_user_authentication</div>
                                    <div class="case-status status-passed">Passed</div>
                                </div>
                                <div class="case-description">
                                    Overuje správnosť autentifikácie používateľa s platnými prihlasovacími údajmi
                                </div>
                                <div class="case-details">
                                    <span>Trvanie: 0.245s</span>
                                    <span>Posledné spustenie: Dnes 14:32</span>
                                </div>
                                <div class="case-actions">
                                    <button class="action-btn">Spustiť</button>
                                    <button class="action-btn">Debug</button>
                                    <button class="action-btn">História</button>
                                </div>
                            </div>

                            <div class="test-case">
                                <div class="case-header">
                                    <div class="case-name">test_product_search</div>
                                    <div class="case-status status-passed">Passed</div>
                                </div>
                                <div class="case-description">
                                    Testuje funkcionalitu vyhľadávania produktov s rôznymi filtrami
                                </div>
                                <div class="case-details">
                                    <span>Trvanie: 1.123s</span>
                                    <span>Posledné spustenie: Dnes 14:32</span>
                                </div>
                                <div class="case-actions">
                                    <button class="action-btn">Spustiť</button>
                                    <button class="action-btn">Debug</button>
                                    <button class="action-btn">História</button>
                                </div>
                            </div>

                            <div class="test-case">
                                <div class="case-header">
                                    <div class="case-name">test_shopping_cart</div>
                                    <div class="case-status status-failed">Failed</div>
                                </div>
                                <div class="case-description">
                                    Overuje pridávanie a odoberanie produktov z nákupného košíka
                                </div>
                                <div class="case-details">
                                    <span>Trvanie: 2.456s</span>
                                    <span>Error: AssertionError at line 45</span>
                                </div>
                                <div class="case-actions">
                                    <button class="action-btn">Spustiť</button>
                                    <button class="action-btn">Debug</button>
                                    <button class="action-btn">História</button>
                                </div>
                            </div>

                            <div class="test-case">
                                <div class="case-header">
                                    <div class="case-name">test_payment_processing</div>
                                    <div class="case-status status-passed">Passed</div>
                                </div>
                                <div class="case-description">
                                    Testuje spracovanie platieb cez rôzne platobné brány
                                </div>
                                <div class="case-details">
                                    <span>Trvanie: 3.789s</span>
                                    <span>Posledné spustenie: Dnes 14:31</span>
                                </div>
                                <div class="case-actions">
                                    <button class="action-btn">Spustiť</button>
                                    <button class="action-btn">Debug</button>
                                    <button class="action-btn">História</button>
                                </div>
                            </div>

                            <div class="test-case">
                                <div class="case-header">
                                    <div class="case-name">test_order_confirmation</div>
                                    <div class="case-status status-running">Running</div>
                                </div>
                                <div class="case-description">
                                    Overuje správnosť generovania a odosielania potvrdení objednávok
                                </div>
                                <div class="case-details">
                                    <span>Trvanie: 1.234s (running)</span>
                                    <span>Spustené: Dnes 14:33</span>
                                </div>
                                <div class="case-actions">
                                    <button class="action-btn">Stop</button>
                                    <button class="action-btn">Debug</button>
                                    <button class="action-btn">História</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qa-sidebar">
                    <div class="sidebar-title">📊 QA Metriky</div>

                    <div class="qa-metrics">
                        <div class="metric-card">
                            <div class="metric-value" style="color: #0d9488;">87%</div>
                            <div class="metric-label">Test Coverage</div>
                            <div class="metric-trend trend-up">↗ +3% za týždeň</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #10b981;">234</div>
                            <div class="metric-label">Passed Tests</div>
                            <div class="metric-trend trend-up">↗ +12 za týždeň</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #ef4444;">8</div>
                            <div class="metric-label">Failed Tests</div>
                            <div class="metric-trend trend-down">↘ -3 za týždeň</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-value" style="color: #f59e0b;">2.3s</div>
                            <div class="metric-label">Avg Test Duration</div>
                            <div class="metric-trend trend-stable">→ Bez zmeny</div>
                        </div>
                    </div>

                    <div class="coverage-chart">
                        <div class="chart-title">Code Coverage</div>
                        <div class="coverage-ring">
                            <svg width="120" height="120">
                                <circle class="coverage-ring-bg" cx="60" cy="60" r="50"></circle>
                                <circle class="coverage-ring-progress" cx="60" cy="60" r="50"
                                        stroke-dasharray="274 314"></circle>
                            </svg>
                            <div class="coverage-text">
                                <div class="coverage-percentage">87%</div>
                                <div class="coverage-label">Coverage</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">🐛 Aktívne Bugy</div>

                    <div class="bug-tracker">
                        <div class="bug-item" onclick="viewBug(1)">
                            <div class="bug-header">
                                <div class="bug-title">Shopping cart calculation error</div>
                                <div class="bug-severity severity-high">High</div>
                            </div>
                            <div class="bug-description">
                                Nesprávny výpočet DPH pri zľavách
                            </div>
                        </div>

                        <div class="bug-item" onclick="viewBug(2)">
                            <div class="bug-header">
                                <div class="bug-title">Login form validation</div>
                                <div class="bug-severity severity-medium">Medium</div>
                            </div>
                            <div class="bug-description">
                                Chýba validácia pre špeciálne znaky
                            </div>
                        </div>

                        <div class="bug-item" onclick="viewBug(3)">
                            <div class="bug-header">
                                <div class="bug-title">Mobile responsive issue</div>
                                <div class="bug-severity severity-low">Low</div>
                            </div>
                            <div class="bug-description">
                                Menu sa nezobrazuje správne na iOS
                            </div>
                        </div>

                        <div class="bug-item" onclick="viewBug(4)">
                            <div class="bug-header">
                                <div class="bug-title">Database connection timeout</div>
                                <div class="bug-severity severity-critical">Critical</div>
                            </div>
                            <div class="bug-description">
                                Sporadické timeouty pri vysokej záťaži
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedTestSuite = 'unit-tests';

        function initPMComponent() {
            setupEventListeners();
            updateTestResults();
            startTestMonitoring();
        }

        function setupEventListeners() {
            // Test suite selection
            document.querySelectorAll('.test-suite-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.test-suite-item').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedTestSuite = this.dataset.suite;
                    updateTestResults();
                });
            });

            // Test case actions
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const action = this.textContent;
                    const testCase = this.closest('.test-case').querySelector('.case-name').textContent;
                    handleTestAction(action, testCase);
                });
            });
        }

        function updateTestResults() {
            console.log('Aktualizujem výsledky pre test suite:', selectedTestSuite);
            // V produkčnej verzii by sa tu načítali špecifické výsledky
        }

        function startTestMonitoring() {
            // Simulácia real-time monitoring testov
            setInterval(() => {
                updateRunningTests();
            }, 5000);
        }

        function updateRunningTests() {
            // Simulácia aktualizácie bežiacich testov
            const runningTests = document.querySelectorAll('.status-running');
            runningTests.forEach(test => {
                if (Math.random() < 0.3) { // 30% šanca na dokončenie
                    const isSuccess = Math.random() < 0.8; // 80% šanca na úspech
                    test.className = isSuccess ? 'case-status status-passed' : 'case-status status-failed';
                    test.textContent = isSuccess ? 'Passed' : 'Failed';
                }
            });
        }

        function runAllTests() {
            alert('🚀 Spúšťam všetky test suites...\n\n• Unit Tests: ✓ Spustené\n• Integration Tests: ✓ Spustené\n• E2E Tests: ✓ Spustené\n• Performance Tests: ✓ Spustené\n• Security Tests: ✓ Spustené\n\nCelkový čas: ~15 minút\nParalelné spustenie: 4 threads\nNotifikácia po dokončení: ✓');
        }

        function runSelectedTests() {
            const environment = document.getElementById('testEnvironment').value;
            const browser = document.getElementById('browser').value;
            const testType = document.getElementById('testType').value;
            const parallel = document.getElementById('parallelExecution').value;

            alert(`🧪 Spúšťam vybrané testy...\n\nKonfigurácia:\n• Environment: ${environment}\n• Browser: ${browser}\n• Test Type: ${testType}\n• Parallel Threads: ${parallel}\n\nTest suite: ${selectedTestSuite}\nOdhadovaný čas: 8 minút\n\nTesty spustené!`);
        }

        function generateQAReport() {
            alert('📊 Generujem QA Report...\n\n• Test execution summary: ✓\n• Coverage analysis: ✓\n• Bug tracking report: ✓\n• Performance metrics: ✓\n• Trend analysis: ✓\n\nReport obsahuje:\n✓ Executive summary\n✓ Detailed test results\n✓ Code coverage maps\n✓ Bug severity analysis\n✓ Recommendations\n\nReport exportovaný do PDF!');
        }

        function handleTestAction(action, testCase) {
            switch(action) {
                case 'Spustiť':
                    alert(`▶️ Spúšťam test: ${testCase}`);
                    break;
                case 'Debug':
                    alert(`🔍 Debug mode pre test: ${testCase}\n\nOtváram debugger s breakpoints...\nStack trace dostupný\nVariable inspection aktivovaný`);
                    break;
                case 'História':
                    alert(`📜 História testu: ${testCase}\n\n• Posledných 10 spustení\n• Success rate: 92%\n• Priemerné trvanie: 1.2s\n• Posledná zmena: Pred 3 dňami`);
                    break;
                case 'Stop':
                    alert(`⏹️ Zastavujem test: ${testCase}`);
                    break;
            }
        }

        function viewBug(bugId) {
            const bugDetails = {
                1: 'Bug #001: Shopping cart calculation error\n\nPopis: Nesprávny výpočet DPH pri aplikácii zľavových kódov\nSeverity: High\nStatus: Open\nAssigned: Peter Kováč\nCreated: 12.06.2024\nSteps to reproduce: 1. Pridaj produkt do košíka 2. Aplikuj zľavový kód 3. Skontroluj DPH',
                2: 'Bug #002: Login form validation\n\nPopis: Chýba validácia pre špeciálne znaky v hesle\nSeverity: Medium\nStatus: In Progress\nAssigned: Anna Horváthová\nCreated: 14.06.2024',
                3: 'Bug #003: Mobile responsive issue\n\nPopis: Navigačné menu sa nezobrazuje správne na iOS Safari\nSeverity: Low\nStatus: Open\nAssigned: Mária Svobodová\nCreated: 15.06.2024',
                4: 'Bug #004: Database connection timeout\n\nPopis: Sporadické timeouty pri vysokej záťaži\nSeverity: Critical\nStatus: Open\nAssigned: Peter Kováč\nCreated: 16.06.2024'
            };

            alert(bugDetails[bugId] || 'Bug detail nenájdený');
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>