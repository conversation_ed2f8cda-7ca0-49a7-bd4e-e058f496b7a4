<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Integration & API Management - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #7c2d12 0%, #dc2626 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1800px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #dc2626 0%, #7c2d12 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .integration-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            min-height: 700px;
        }

        .integrations-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .integration-list {
            margin-bottom: 2rem;
        }

        .integration-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .integration-item:hover {
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .integration-item.active {
            border-color: #dc2626;
            background: #fef2f2;
        }

        .integration-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .integration-name {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .integration-status {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-connected { background: #d1fae5; color: #065f46; }
        .status-disconnected { background: #fee2e2; color: #991b1b; }
        .status-syncing { background: #dbeafe; color: #1e40af; }
        .status-error { background: #fef3c7; color: #92400e; }

        .integration-description {
            color: #6b7280;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .integration-metrics {
            display: flex;
            justify-content: space-between;
            font-size: 0.7rem;
            color: #9ca3af;
        }

        .main-api-management {
            padding: 2rem;
            overflow-y: auto;
        }

        .api-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-bottom-color: #dc2626;
            color: #dc2626;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .api-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .api-card:hover {
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .api-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .api-title {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .api-version {
            padding: 0.25rem 0.5rem;
            background: #dc2626;
            color: white;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .api-description {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .api-endpoints {
            margin-bottom: 1rem;
        }

        .endpoint-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
        }

        .endpoint-method {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.7rem;
            min-width: 50px;
            text-align: center;
        }

        .method-get { background: #d1fae5; color: #065f46; }
        .method-post { background: #dbeafe; color: #1e40af; }
        .method-put { background: #fef3c7; color: #92400e; }
        .method-delete { background: #fee2e2; color: #991b1b; }

        .endpoint-path {
            color: #374151;
            font-family: 'Courier New', monospace;
        }

        .api-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
        }

        .metric-value {
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.7rem;
            color: #6b7280;
        }

        .api-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            border-color: #dc2626;
            color: #dc2626;
        }

        .action-btn.primary {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }

        .action-btn.primary:hover {
            background: #b91c1c;
        }

        .monitoring-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .monitoring-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .health-status {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 1rem;
        }

        .health-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .health-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .health-healthy { background: #10b981; }
        .health-warning { background: #f59e0b; }
        .health-error { background: #ef4444; }

        .health-text {
            font-weight: 500;
            color: #1f2937;
        }

        .health-details {
            font-size: 0.8rem;
            color: #6b7280;
            line-height: 1.3;
        }

        .performance-metrics {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .metric-card {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 6px;
        }

        .metric-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 0.25rem;
        }

        .metric-text {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .recent-activity {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            padding: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            background: #f9fafb;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .activity-title {
            font-weight: 500;
            color: #1f2937;
            font-size: 0.8rem;
        }

        .activity-time {
            font-size: 0.7rem;
            color: #9ca3af;
        }

        .activity-description {
            font-size: 0.7rem;
            color: #6b7280;
            line-height: 1.3;
        }

        .enterprise-tools {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #dc2626;
            border-radius: 12px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .tools-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #7c2d12;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .tool-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #fca5a5;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            background: rgba(255,255,255,0.95);
            transform: translateY(-2px);
        }

        .tool-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .tool-name {
            font-weight: 600;
            color: #7c2d12;
        }

        .tool-status {
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .tool-active { background: #d1fae5; color: #065f46; }
        .tool-inactive { background: #fee2e2; color: #991b1b; }

        .tool-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .tool-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .feature-tag {
            background: rgba(255,255,255,0.9);
            color: #7c2d12;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 300px 1fr;
            }
            
            .monitoring-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .integrations-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
