<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Integration & API Management - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #7c2d12 0%, #dc2626 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1800px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #dc2626 0%, #7c2d12 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .integration-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            min-height: 700px;
        }

        .integrations-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .integration-list {
            margin-bottom: 2rem;
        }

        .integration-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .integration-item:hover {
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .integration-item.active {
            border-color: #dc2626;
            background: #fef2f2;
        }

        .integration-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .integration-name {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .integration-status {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-connected { background: #d1fae5; color: #065f46; }
        .status-disconnected { background: #fee2e2; color: #991b1b; }
        .status-syncing { background: #dbeafe; color: #1e40af; }
        .status-error { background: #fef3c7; color: #92400e; }

        .integration-description {
            color: #6b7280;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .integration-metrics {
            display: flex;
            justify-content: space-between;
            font-size: 0.7rem;
            color: #9ca3af;
        }

        .main-api-management {
            padding: 2rem;
            overflow-y: auto;
        }

        .api-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-bottom-color: #dc2626;
            color: #dc2626;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .api-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .api-card:hover {
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .api-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .api-title {
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .api-version {
            padding: 0.25rem 0.5rem;
            background: #dc2626;
            color: white;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .api-description {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .api-endpoints {
            margin-bottom: 1rem;
        }

        .endpoint-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
        }

        .endpoint-method {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.7rem;
            min-width: 50px;
            text-align: center;
        }

        .method-get { background: #d1fae5; color: #065f46; }
        .method-post { background: #dbeafe; color: #1e40af; }
        .method-put { background: #fef3c7; color: #92400e; }
        .method-delete { background: #fee2e2; color: #991b1b; }

        .endpoint-path {
            color: #374151;
            font-family: 'Courier New', monospace;
        }

        .api-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
        }

        .metric-value {
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.7rem;
            color: #6b7280;
        }

        .api-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            border-color: #dc2626;
            color: #dc2626;
        }

        .action-btn.primary {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }

        .action-btn.primary:hover {
            background: #b91c1c;
        }

        .monitoring-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .monitoring-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .health-status {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 1rem;
        }

        .health-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .health-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .health-healthy { background: #10b981; }
        .health-warning { background: #f59e0b; }
        .health-error { background: #ef4444; }

        .health-text {
            font-weight: 500;
            color: #1f2937;
        }

        .health-details {
            font-size: 0.8rem;
            color: #6b7280;
            line-height: 1.3;
        }

        .performance-metrics {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .metric-card {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 6px;
        }

        .metric-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 0.25rem;
        }

        .metric-text {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .recent-activity {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            padding: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            background: #f9fafb;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .activity-title {
            font-weight: 500;
            color: #1f2937;
            font-size: 0.8rem;
        }

        .activity-time {
            font-size: 0.7rem;
            color: #9ca3af;
        }

        .activity-description {
            font-size: 0.7rem;
            color: #6b7280;
            line-height: 1.3;
        }

        .enterprise-tools {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #dc2626;
            border-radius: 12px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .tools-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #7c2d12;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .tool-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #fca5a5;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            background: rgba(255,255,255,0.95);
            transform: translateY(-2px);
        }

        .tool-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .tool-name {
            font-weight: 600;
            color: #7c2d12;
        }

        .tool-status {
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .tool-active { background: #d1fae5; color: #065f46; }
        .tool-inactive { background: #fee2e2; color: #991b1b; }

        .tool-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .tool-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .feature-tag {
            background: rgba(255,255,255,0.9);
            color: #7c2d12;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 300px 1fr;
            }
            
            .monitoring-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .integrations-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Enterprise Integration & API Management - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>🔗 Enterprise Integration & API Management</h2>
                <div class="integration-indicator">
                    🟢 Integrácie: 12/15 Active
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="addIntegration()">+ Nová Integrácia</button>
                    <button class="btn btn-primary" onclick="apiDocumentation()">📚 API Docs</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="integrations-sidebar">
                    <div class="sidebar-title">🔗 Aktívne Integrácie</div>

                    <div class="integration-list">
                        <div class="integration-item active">
                            <div class="integration-header">
                                <div class="integration-name">
                                    🏢 Jira Software
                                </div>
                                <div class="integration-status status-connected">Connected</div>
                            </div>
                            <div class="integration-description">
                                Project tracking a issue management integrácia
                            </div>
                            <div class="integration-metrics">
                                <span>1,247 sync</span>
                                <span>99.8% uptime</span>
                            </div>
                        </div>

                        <div class="integration-item">
                            <div class="integration-header">
                                <div class="integration-name">
                                    💬 Slack
                                </div>
                                <div class="integration-status status-connected">Connected</div>
                            </div>
                            <div class="integration-description">
                                Team komunikácia a notifikácie
                            </div>
                            <div class="integration-metrics">
                                <span>2,156 messages</span>
                                <span>100% uptime</span>
                            </div>
                        </div>

                        <div class="integration-item">
                            <div class="integration-header">
                                <div class="integration-name">
                                    📧 Microsoft 365
                                </div>
                                <div class="integration-status status-syncing">Syncing</div>
                            </div>
                            <div class="integration-description">
                                Email, kalendár a dokumenty
                            </div>
                            <div class="integration-metrics">
                                <span>856 items</span>
                                <span>97.2% uptime</span>
                            </div>
                        </div>

                        <div class="integration-item">
                            <div class="integration-header">
                                <div class="integration-name">
                                    🐙 GitHub
                                </div>
                                <div class="integration-status status-connected">Connected</div>
                            </div>
                            <div class="integration-description">
                                Source code management a CI/CD
                            </div>
                            <div class="integration-metrics">
                                <span>342 commits</span>
                                <span>99.5% uptime</span>
                            </div>
                        </div>

                        <div class="integration-item">
                            <div class="integration-header">
                                <div class="integration-name">
                                    💰 SAP ERP
                                </div>
                                <div class="integration-status status-error">Error</div>
                            </div>
                            <div class="integration-description">
                                Enterprise resource planning
                            </div>
                            <div class="integration-metrics">
                                <span>Last sync: 2h ago</span>
                                <span>Auth expired</span>
                            </div>
                        </div>

                        <div class="integration-item">
                            <div class="integration-header">
                                <div class="integration-name">
                                    📊 Tableau
                                </div>
                                <div class="integration-status status-connected">Connected</div>
                            </div>
                            <div class="integration-description">
                                Business intelligence a reporting
                            </div>
                            <div class="integration-metrics">
                                <span>45 dashboards</span>
                                <span>98.9% uptime</span>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">⚙️ Konfigurácia</div>

                    <div style="background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e5e7eb;">
                        <div style="font-weight: 500; margin-bottom: 0.75rem;">Sync Settings:</div>
                        <div style="font-size: 0.9rem; color: #6b7280; line-height: 1.4;">
                            • Real-time sync: Enabled<br>
                            • Batch processing: Every 15min<br>
                            • Error retry: 3 attempts<br>
                            • Webhook timeout: 30s<br>
                            • Rate limiting: 1000/hour
                        </div>
                    </div>
                </div>

                <div class="main-api-management">
                    <div class="api-tabs">
                        <button class="tab-btn active" data-tab="endpoints">🔌 API Endpoints</button>
                        <button class="tab-btn" data-tab="webhooks">🪝 Webhooks</button>
                        <button class="tab-btn" data-tab="authentication">🔐 Authentication</button>
                        <button class="tab-btn" data-tab="monitoring">📊 Monitoring</button>
                    </div>

                    <!-- API Endpoints Tab -->
                    <div class="tab-content active" id="endpoints">
                        <div class="api-grid">
                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title">
                                        📋 Projects API
                                    </div>
                                    <div class="api-version">v2.1</div>
                                </div>
                                <div class="api-description">
                                    Kompletné API pre správu projektov, úloh a tímov s CRUD operáciami
                                </div>
                                <div class="api-endpoints">
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-get">GET</div>
                                        <div class="endpoint-path">/api/v2/projects</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-post">POST</div>
                                        <div class="endpoint-path">/api/v2/projects</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-put">PUT</div>
                                        <div class="endpoint-path">/api/v2/projects/{id}</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-delete">DEL</div>
                                        <div class="endpoint-path">/api/v2/projects/{id}</div>
                                    </div>
                                </div>
                                <div class="api-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">1,247</div>
                                        <div class="metric-label">Requests/day</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">45ms</div>
                                        <div class="metric-label">Avg Response</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">99.8%</div>
                                        <div class="metric-label">Uptime</div>
                                    </div>
                                </div>
                                <div class="api-actions">
                                    <button class="action-btn" onclick="testAPI('projects')">Test</button>
                                    <button class="action-btn" onclick="viewDocs('projects')">Docs</button>
                                    <button class="action-btn primary" onclick="manageAPI('projects')">Manage</button>
                                </div>
                            </div>

                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title">
                                        👥 Teams API
                                    </div>
                                    <div class="api-version">v1.8</div>
                                </div>
                                <div class="api-description">
                                    API pre správu tímov, členov a ich rolí v projektoch
                                </div>
                                <div class="api-endpoints">
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-get">GET</div>
                                        <div class="endpoint-path">/api/v1/teams</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-post">POST</div>
                                        <div class="endpoint-path">/api/v1/teams/{id}/members</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-put">PUT</div>
                                        <div class="endpoint-path">/api/v1/teams/{id}/roles</div>
                                    </div>
                                </div>
                                <div class="api-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">856</div>
                                        <div class="metric-label">Requests/day</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">32ms</div>
                                        <div class="metric-label">Avg Response</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">99.9%</div>
                                        <div class="metric-label">Uptime</div>
                                    </div>
                                </div>
                                <div class="api-actions">
                                    <button class="action-btn" onclick="testAPI('teams')">Test</button>
                                    <button class="action-btn" onclick="viewDocs('teams')">Docs</button>
                                    <button class="action-btn primary" onclick="manageAPI('teams')">Manage</button>
                                </div>
                            </div>

                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title">
                                        📊 Analytics API
                                    </div>
                                    <div class="api-version">v3.0</div>
                                </div>
                                <div class="api-description">
                                    Pokročilé analytics a reporting API s real-time metrikami
                                </div>
                                <div class="api-endpoints">
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-get">GET</div>
                                        <div class="endpoint-path">/api/v3/analytics/metrics</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-get">GET</div>
                                        <div class="endpoint-path">/api/v3/analytics/reports</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-post">POST</div>
                                        <div class="endpoint-path">/api/v3/analytics/custom</div>
                                    </div>
                                </div>
                                <div class="api-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">2,134</div>
                                        <div class="metric-label">Requests/day</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">78ms</div>
                                        <div class="metric-label">Avg Response</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">99.5%</div>
                                        <div class="metric-label">Uptime</div>
                                    </div>
                                </div>
                                <div class="api-actions">
                                    <button class="action-btn" onclick="testAPI('analytics')">Test</button>
                                    <button class="action-btn" onclick="viewDocs('analytics')">Docs</button>
                                    <button class="action-btn primary" onclick="manageAPI('analytics')">Manage</button>
                                </div>
                            </div>

                            <div class="api-card">
                                <div class="api-header">
                                    <div class="api-title">
                                        🔔 Notifications API
                                    </div>
                                    <div class="api-version">v1.5</div>
                                </div>
                                <div class="api-description">
                                    Centralizované notifikácie cez email, SMS a push notifications
                                </div>
                                <div class="api-endpoints">
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-post">POST</div>
                                        <div class="endpoint-path">/api/v1/notifications/send</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-get">GET</div>
                                        <div class="endpoint-path">/api/v1/notifications/status</div>
                                    </div>
                                    <div class="endpoint-item">
                                        <div class="endpoint-method method-put">PUT</div>
                                        <div class="endpoint-path">/api/v1/notifications/preferences</div>
                                    </div>
                                </div>
                                <div class="api-metrics">
                                    <div class="metric-item">
                                        <div class="metric-value">3,456</div>
                                        <div class="metric-label">Requests/day</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">23ms</div>
                                        <div class="metric-label">Avg Response</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">99.7%</div>
                                        <div class="metric-label">Uptime</div>
                                    </div>
                                </div>
                                <div class="api-actions">
                                    <button class="action-btn" onclick="testAPI('notifications')">Test</button>
                                    <button class="action-btn" onclick="viewDocs('notifications')">Docs</button>
                                    <button class="action-btn primary" onclick="manageAPI('notifications')">Manage</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other tabs content -->
                    <div class="tab-content" id="webhooks">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>🪝 Webhook Management</h3>
                            <p>Konfigurácia a správa webhooks pre real-time integrácie</p>
                        </div>
                    </div>

                    <div class="tab-content" id="authentication">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>🔐 API Authentication</h3>
                            <p>Správa API kľúčov, OAuth tokens a bezpečnostných nastavení</p>
                        </div>
                    </div>

                    <div class="tab-content" id="monitoring">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>📊 API Monitoring</h3>
                            <p>Real-time monitoring API performance a usage analytics</p>
                        </div>
                    </div>
                </div>

                <div class="monitoring-sidebar">
                    <div class="monitoring-section">
                        <div class="section-title">💚 System Health</div>

                        <div class="health-status">
                            <div class="health-indicator">
                                <div class="health-dot health-healthy"></div>
                                <div class="health-text">API Gateway</div>
                            </div>
                            <div class="health-details">
                                Všetky služby operačné, response time 45ms
                            </div>
                        </div>

                        <div class="health-status">
                            <div class="health-indicator">
                                <div class="health-dot health-warning"></div>
                                <div class="health-text">Database</div>
                            </div>
                            <div class="health-details">
                                Vysoké využitie CPU (78%), monitoring aktívny
                            </div>
                        </div>

                        <div class="health-status">
                            <div class="health-indicator">
                                <div class="health-dot health-healthy"></div>
                                <div class="health-text">Cache Layer</div>
                            </div>
                            <div class="health-details">
                                Redis cluster stabilný, hit rate 94%
                            </div>
                        </div>
                    </div>

                    <div class="monitoring-section">
                        <div class="section-title">📊 Performance</div>

                        <div class="performance-metrics">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-number">45ms</div>
                                    <div class="metric-text">Avg Response</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-number">1,247</div>
                                    <div class="metric-text">Requests/min</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-number">99.8%</div>
                                    <div class="metric-text">Uptime</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-number">0.02%</div>
                                    <div class="metric-text">Error Rate</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="monitoring-section">
                        <div class="section-title">🔔 Recent Activity</div>

                        <div class="recent-activity">
                            <div class="activity-item">
                                <div class="activity-header">
                                    <div class="activity-title">API Key Generated</div>
                                    <div class="activity-time">Pred 5 min</div>
                                </div>
                                <div class="activity-description">
                                    Nový API kľúč vytvorený pre Jira integráciu
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-header">
                                    <div class="activity-title">Webhook Triggered</div>
                                    <div class="activity-time">Pred 12 min</div>
                                </div>
                                <div class="activity-description">
                                    GitHub webhook spustený pre nový commit
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-header">
                                    <div class="activity-title">Rate Limit Warning</div>
                                    <div class="activity-time">Pred 1 hodinou</div>
                                </div>
                                <div class="activity-description">
                                    SAP ERP integrácia dosiahla 80% rate limitu
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-header">
                                    <div class="activity-title">Sync Completed</div>
                                    <div class="activity-time">Pred 2 hodinami</div>
                                </div>
                                <div class="activity-description">
                                    Microsoft 365 sync dokončený (856 items)
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-header">
                                    <div class="activity-title">New Integration</div>
                                    <div class="activity-time">Dnes ráno</div>
                                </div>
                                <div class="activity-description">
                                    Tableau integrácia úspešne nakonfigurovaná
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enterprise Tools Section -->
            <div class="enterprise-tools">
                <div class="tools-title">
                    🏢 Enterprise Integration Tools
                </div>
                <div class="tools-grid">
                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-name">API Gateway</div>
                            <div class="tool-status tool-active">Active</div>
                        </div>
                        <div class="tool-description">
                            Centralizovaná správa všetkých API endpoints s rate limiting a security
                        </div>
                        <div class="tool-features">
                            <span class="feature-tag">Rate Limiting</span>
                            <span class="feature-tag">Authentication</span>
                            <span class="feature-tag">Monitoring</span>
                            <span class="feature-tag">Caching</span>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-name">Message Queue</div>
                            <div class="tool-status tool-active">Active</div>
                        </div>
                        <div class="tool-description">
                            Asynchronné spracovanie správ a event-driven architektúra
                        </div>
                        <div class="tool-features">
                            <span class="feature-tag">Redis</span>
                            <span class="feature-tag">RabbitMQ</span>
                            <span class="feature-tag">Event Sourcing</span>
                            <span class="feature-tag">Dead Letter Queue</span>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-name">Data Transformation</div>
                            <div class="tool-status tool-active">Active</div>
                        </div>
                        <div class="tool-description">
                            ETL procesy pre transformáciu dát medzi rôznymi systémami
                        </div>
                        <div class="tool-features">
                            <span class="feature-tag">JSON Mapping</span>
                            <span class="feature-tag">XML Transform</span>
                            <span class="feature-tag">Data Validation</span>
                            <span class="feature-tag">Schema Registry</span>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-name">Security Layer</div>
                            <div class="tool-status tool-active">Active</div>
                        </div>
                        <div class="tool-description">
                            Komplexná bezpečnosť s OAuth2, JWT tokens a encryption
                        </div>
                        <div class="tool-features">
                            <span class="feature-tag">OAuth2</span>
                            <span class="feature-tag">JWT</span>
                            <span class="feature-tag">TLS/SSL</span>
                            <span class="feature-tag">API Keys</span>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-name">Monitoring & Alerts</div>
                            <div class="tool-status tool-active">Active</div>
                        </div>
                        <div class="tool-description">
                            Real-time monitoring s automatickými alertmi a dashboards
                        </div>
                        <div class="tool-features">
                            <span class="feature-tag">Prometheus</span>
                            <span class="feature-tag">Grafana</span>
                            <span class="feature-tag">Alertmanager</span>
                            <span class="feature-tag">Log Aggregation</span>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-name">Service Mesh</div>
                            <div class="tool-status tool-inactive">Inactive</div>
                        </div>
                        <div class="tool-description">
                            Mikroslužby komunikácia s load balancing a service discovery
                        </div>
                        <div class="tool-features">
                            <span class="feature-tag">Istio</span>
                            <span class="feature-tag">Load Balancing</span>
                            <span class="feature-tag">Circuit Breaker</span>
                            <span class="feature-tag">Tracing</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            setupTabs();
            loadIntegrations();
            startMonitoring();
        }

        function setupTabs() {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                    this.classList.add('active');
                    document.getElementById(this.dataset.tab).classList.add('active');
                });
            });
        }

        function loadIntegrations() {
            // Načítanie statusov integrácií
            console.log('Integrácie načítané');
        }

        function startMonitoring() {
            // Real-time monitoring API performance
            setInterval(() => {
                updateMetrics();
                checkIntegrationHealth();
            }, 30000); // Každých 30 sekúnd
        }

        function updateMetrics() {
            // Simulácia aktualizácie metrík
            console.log('API metrics aktualizované:', new Date().toLocaleTimeString());
        }

        function checkIntegrationHealth() {
            // Kontrola zdravia integrácií
            const integrations = document.querySelectorAll('.integration-item');
            integrations.forEach(integration => {
                // Simulácia random status changes
                if (Math.random() < 0.02) { // 2% šanca na zmenu
                    const status = integration.querySelector('.integration-status');
                    const statuses = ['status-connected', 'status-syncing', 'status-error'];
                    const currentStatus = status.className.split(' ')[1];
                    const newStatus = statuses[Math.floor(Math.random() * statuses.length)];
                    status.className = `integration-status ${newStatus}`;
                    status.textContent = newStatus.replace('status-', '').replace('-', ' ');
                }
            });
        }

        function addIntegration() {
            alert('🔗 Pridanie Novej Integrácie\n\nDostupné integrácie:\n• CRM systémy (Salesforce, HubSpot)\n• Communication tools (Teams, Discord)\n• Development tools (GitLab, Bitbucket)\n• Analytics platforms (Google Analytics)\n• Cloud services (AWS, Azure, GCP)\n\nVyberte typ integrácie a postupujte podľa sprievodcu konfiguráciou.');
        }

        function apiDocumentation() {
            alert('📚 API Dokumentácia\n\nInteraktívna dokumentácia obsahuje:\n\n• Swagger/OpenAPI špecifikácie\n• Live API testing interface\n• Code examples (cURL, JavaScript, Python)\n• Authentication guides\n• Rate limiting information\n• Error codes reference\n\nDokumentácia sa otvorí v novom okne s možnosťou testovania API calls.');
        }

        function testAPI(apiName) {
            alert(`🧪 Testovanie ${apiName} API\n\nSpúšťam test suite:\n• Endpoint availability\n• Response time measurement\n• Data validation\n• Error handling\n• Rate limiting\n\nTest results budú dostupné za 30 sekúnd.\nDetailné logy v monitoring sekcii.`);
        }

        function viewDocs(apiName) {
            alert(`📖 ${apiName} API Dokumentácia\n\nOtváram dokumentáciu:\n• Endpoint specifications\n• Request/response examples\n• Authentication requirements\n• Rate limits\n• Error codes\n\nInteraktívne testovanie dostupné v Swagger UI.`);
        }

        function manageAPI(apiName) {
            alert(`⚙️ Správa ${apiName} API\n\nDostupné akcie:\n• Konfigurácia endpoints\n• Rate limiting nastavenia\n• Authentication management\n• Monitoring alerts\n• Version management\n• Deprecation planning\n\nAdmin panel sa otvorí v novom okne.`);
        }

        // Integration item click handlers
        document.querySelectorAll('.integration-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.integration-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');

                const name = this.querySelector('.integration-name').textContent.trim();
                const status = this.querySelector('.integration-status').textContent;
                const description = this.querySelector('.integration-description').textContent;

                console.log(`Selected integration: ${name} (${status})`);
            });
        });

        // Activity item click handlers
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('.activity-title').textContent;
                const time = this.querySelector('.activity-time').textContent;
                const description = this.querySelector('.activity-description').textContent;

                alert(`🔔 Activity Detail\n\n${title}\n${time}\n\n${description}\n\nDetailed logs dostupné v monitoring dashboarde.`);
            });
        });

        // Tool card click handlers
        document.querySelectorAll('.tool-card').forEach(card => {
            card.addEventListener('click', function() {
                const toolName = this.querySelector('.tool-name').textContent;
                const toolStatus = this.querySelector('.tool-status').textContent;
                const toolDesc = this.querySelector('.tool-description').textContent;

                alert(`🛠️ Enterprise Tool: ${toolName}\n\nStatus: ${toolStatus}\nPopis: ${toolDesc}\n\nFunkcie:\n• Konfigurácia\n• Monitoring\n• Logs & Analytics\n• Performance tuning\n\nAdmin interface dostupný pre authorized users.`);
            });
        });

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>