<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agile Sprint Manager & Scrum Board - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sprint-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 700px;
        }

        .sprint-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .sprint-info {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e5e7eb;
        }

        .sprint-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .sprint-dates {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .sprint-progress {
            margin-bottom: 1rem;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #374151;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #7c3aed, #a855f7);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .sprint-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 6px;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #7c3aed;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .team-velocity {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }

        .velocity-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .velocity-chart {
            height: 100px;
            display: flex;
            align-items: end;
            justify-content: space-around;
            background: #f8fafc;
            border-radius: 6px;
            padding: 0.5rem;
        }

        .velocity-bar {
            background: linear-gradient(to top, #7c3aed, #a855f7);
            border-radius: 2px 2px 0 0;
            min-width: 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .velocity-bar:hover {
            background: linear-gradient(to top, #6d28d9, #9333ea);
        }

        .bar-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            color: #6b7280;
        }

        .scrum-board {
            padding: 2rem;
            overflow-x: auto;
        }

        .board-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .board-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
        }

        .board-controls {
            display: flex;
            gap: 1rem;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            outline: none;
        }

        .add-task-btn {
            background: #7c3aed;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .add-task-btn:hover {
            background: #6d28d9;
        }

        .kanban-board {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            min-height: 500px;
        }

        .kanban-column {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }

        .column-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .column-title {
            font-weight: 600;
            color: #1f2937;
        }

        .column-count {
            background: #7c3aed;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .task-list {
            min-height: 400px;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .task-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .task-card:hover {
            border-color: #7c3aed;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .task-card.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .task-priority {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .priority-high { background: #ef4444; }
        .priority-medium { background: #f59e0b; }
        .priority-low { background: #10b981; }

        .task-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
            padding-right: 1rem;
        }

        .task-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }

        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #9ca3af;
        }

        .task-assignee {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .assignee-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.7rem;
        }

        .task-points {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-weight: 600;
        }

        .task-tags {
            display: flex;
            gap: 0.25rem;
            margin-top: 0.5rem;
            flex-wrap: wrap;
        }

        .task-tag {
            background: #f3f4f6;
            color: #374151;
            padding: 0.2rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .tag-frontend { background: #dbeafe; color: #1e40af; }
        .tag-backend { background: #dcfce7; color: #166534; }
        .tag-design { background: #fef3c7; color: #92400e; }
        .tag-testing { background: #fee2e2; color: #991b1b; }

        .drop-zone {
            min-height: 50px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 0.9rem;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .drop-zone.drag-over {
            border-color: #7c3aed;
            background: #f3f4f6;
            color: #7c3aed;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 250px 1fr;
            }

            .kanban-board {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .sprint-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }

            .kanban-board {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Agile Sprint Manager & Scrum Board - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>🏃‍♂️ Agile Sprint Manager & Scrum Board</h2>
                <div class="sprint-indicator">
                    🚀 Sprint 3 - Deň 8/14
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="startSprint()">▶️ Štart Sprint</button>
                    <button class="btn btn-primary" onclick="sprintReview()">📊 Sprint Review</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="sprint-sidebar">
                    <div class="sidebar-title">🏃‍♂️ Aktuálny Sprint</div>

                    <div class="sprint-info">
                        <div class="sprint-name">Sprint 3: E-shop Core Features</div>
                        <div class="sprint-dates">17.06.2024 - 30.06.2024</div>

                        <div class="sprint-progress">
                            <div class="progress-label">
                                <span>Pokrok</span>
                                <span>57%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 57%;"></div>
                            </div>
                        </div>

                        <div class="sprint-stats">
                            <div class="stat-item">
                                <div class="stat-value">23</div>
                                <div class="stat-label">Story Points</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">13</div>
                                <div class="stat-label">Dokončené</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">8</div>
                                <div class="stat-label">V Priebehu</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">2</div>
                                <div class="stat-label">Čakajúce</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">📈 Team Velocity</div>

                    <div class="team-velocity">
                        <div class="velocity-title">Posledných 6 Sprintov</div>
                        <div class="velocity-chart">
                            <div class="velocity-bar" style="height: 60%;" title="Sprint 1: 18 SP">
                                <div class="bar-label">S1</div>
                            </div>
                            <div class="velocity-bar" style="height: 80%;" title="Sprint 2: 24 SP">
                                <div class="bar-label">S2</div>
                            </div>
                            <div class="velocity-bar" style="height: 75%;" title="Sprint 3: 23 SP">
                                <div class="bar-label">S3</div>
                            </div>
                            <div class="velocity-bar" style="height: 90%;" title="Sprint 4: 27 SP">
                                <div class="bar-label">S4</div>
                            </div>
                            <div class="velocity-bar" style="height: 85%;" title="Sprint 5: 26 SP">
                                <div class="bar-label">S5</div>
                            </div>
                            <div class="velocity-bar" style="height: 95%;" title="Sprint 6: 29 SP">
                                <div class="bar-label">S6</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">👥 Tím</div>

                    <div style="background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e5e7eb;">
                        <div style="font-weight: 500; margin-bottom: 0.75rem;">Členovia Tímu:</div>
                        <div style="font-size: 0.9rem; color: #6b7280; line-height: 1.6;">
                            • Ján Novák (Scrum Master)<br>
                            • Peter Kováč (Developer)<br>
                            • Mária Svobodová (Designer)<br>
                            • Anna Horváthová (QA)<br>
                            • Tomáš Varga (Developer)
                        </div>
                    </div>
                </div>

                <div class="scrum-board">
                    <div class="board-header">
                        <div class="board-title">🏃‍♂️ Scrum Board - Sprint 3</div>
                        <div class="board-controls">
                            <select class="filter-select" id="assigneeFilter">
                                <option value="all">Všetci členovia</option>
                                <option value="jan">Ján Novák</option>
                                <option value="peter">Peter Kováč</option>
                                <option value="maria">Mária Svobodová</option>
                                <option value="anna">Anna Horváthová</option>
                                <option value="tomas">Tomáš Varga</option>
                            </select>
                            <select class="filter-select" id="priorityFilter">
                                <option value="all">Všetky priority</option>
                                <option value="high">Vysoká</option>
                                <option value="medium">Stredná</option>
                                <option value="low">Nízka</option>
                            </select>
                            <button class="add-task-btn" onclick="addNewTask()">+ Pridať Úlohu</button>
                        </div>
                    </div>

                    <div class="kanban-board">
                        <!-- To Do Column -->
                        <div class="kanban-column" data-status="todo">
                            <div class="column-header">
                                <div class="column-title">📋 To Do</div>
                                <div class="column-count">2</div>
                            </div>
                            <div class="task-list" id="todoTasks">
                                <div class="task-card" draggable="true" data-task-id="1">
                                    <div class="task-priority priority-high"></div>
                                    <div class="task-title">Implementovať platobný systém</div>
                                    <div class="task-description">
                                        Integrácia s PayPal a Stripe API pre spracovanie platieb
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-backend">Backend</span>
                                        <span class="task-tag tag-frontend">Frontend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">PK</div>
                                            <span>Peter Kováč</span>
                                        </div>
                                        <div class="task-points">8 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="2">
                                    <div class="task-priority priority-medium"></div>
                                    <div class="task-title">Optimalizovať databázové dotazy</div>
                                    <div class="task-description">
                                        Zlepšiť výkonnosť produktového katalógu
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-backend">Backend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">TV</div>
                                            <span>Tomáš Varga</span>
                                        </div>
                                        <div class="task-points">5 SP</div>
                                    </div>
                                </div>
                            </div>
                            <div class="drop-zone">Presuňte úlohu sem</div>
                        </div>

                        <!-- In Progress Column -->
                        <div class="kanban-column" data-status="inprogress">
                            <div class="column-header">
                                <div class="column-title">🔄 In Progress</div>
                                <div class="column-count">3</div>
                            </div>
                            <div class="task-list" id="inprogressTasks">
                                <div class="task-card" draggable="true" data-task-id="3">
                                    <div class="task-priority priority-high"></div>
                                    <div class="task-title">Responzívny dizajn košíka</div>
                                    <div class="task-description">
                                        Prispôsobiť nákupný košík pre mobilné zariadenia
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-design">Design</span>
                                        <span class="task-tag tag-frontend">Frontend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">MS</div>
                                            <span>Mária Svobodová</span>
                                        </div>
                                        <div class="task-points">3 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="4">
                                    <div class="task-priority priority-medium"></div>
                                    <div class="task-title">API pre používateľské profily</div>
                                    <div class="task-description">
                                        CRUD operácie pre správu používateľských účtov
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-backend">Backend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">PK</div>
                                            <span>Peter Kováč</span>
                                        </div>
                                        <div class="task-points">5 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="5">
                                    <div class="task-priority priority-low"></div>
                                    <div class="task-title">Testovanie registrácie</div>
                                    <div class="task-description">
                                        Automatizované testy pre registračný proces
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-testing">Testing</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">AH</div>
                                            <span>Anna Horváthová</span>
                                        </div>
                                        <div class="task-points">2 SP</div>
                                    </div>
                                </div>
                            </div>
                            <div class="drop-zone">Presuňte úlohu sem</div>
                        </div>

                        <!-- Review Column -->
                        <div class="kanban-column" data-status="review">
                            <div class="column-header">
                                <div class="column-title">👀 Review</div>
                                <div class="column-count">2</div>
                            </div>
                            <div class="task-list" id="reviewTasks">
                                <div class="task-card" draggable="true" data-task-id="6">
                                    <div class="task-priority priority-high"></div>
                                    <div class="task-title">Produktový katalóg</div>
                                    <div class="task-description">
                                        Frontend komponenty pre zobrazenie produktov
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-frontend">Frontend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">TV</div>
                                            <span>Tomáš Varga</span>
                                        </div>
                                        <div class="task-points">8 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="7">
                                    <div class="task-priority priority-medium"></div>
                                    <div class="task-title">Vyhľadávanie produktov</div>
                                    <div class="task-description">
                                        Elasticsearch integrácia s filtrami
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-backend">Backend</span>
                                        <span class="task-tag tag-frontend">Frontend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">PK</div>
                                            <span>Peter Kováč</span>
                                        </div>
                                        <div class="task-points">5 SP</div>
                                    </div>
                                </div>
                            </div>
                            <div class="drop-zone">Presuňte úlohu sem</div>
                        </div>

                        <!-- Done Column -->
                        <div class="kanban-column" data-status="done">
                            <div class="column-header">
                                <div class="column-title">✅ Done</div>
                                <div class="column-count">4</div>
                            </div>
                            <div class="task-list" id="doneTasks">
                                <div class="task-card" draggable="true" data-task-id="8">
                                    <div class="task-priority priority-high"></div>
                                    <div class="task-title">Používateľská autentifikácia</div>
                                    <div class="task-description">
                                        Login/logout funkcionalita s JWT tokenmi
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-backend">Backend</span>
                                        <span class="task-tag tag-frontend">Frontend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">PK</div>
                                            <span>Peter Kováč</span>
                                        </div>
                                        <div class="task-points">8 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="9">
                                    <div class="task-priority priority-medium"></div>
                                    <div class="task-title">Hlavná navigácia</div>
                                    <div class="task-description">
                                        Responzívne menu s kategóriami produktov
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-design">Design</span>
                                        <span class="task-tag tag-frontend">Frontend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">MS</div>
                                            <span>Mária Svobodová</span>
                                        </div>
                                        <div class="task-points">3 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="10">
                                    <div class="task-priority priority-low"></div>
                                    <div class="task-title">Databázová schéma</div>
                                    <div class="task-description">
                                        Návrh a implementácia DB štruktúry
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-backend">Backend</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">PK</div>
                                            <span>Peter Kováč</span>
                                        </div>
                                        <div class="task-points">5 SP</div>
                                    </div>
                                </div>

                                <div class="task-card" draggable="true" data-task-id="11">
                                    <div class="task-priority priority-medium"></div>
                                    <div class="task-title">Unit testy pre API</div>
                                    <div class="task-description">
                                        Pokrytie základných API endpointov
                                    </div>
                                    <div class="task-tags">
                                        <span class="task-tag tag-testing">Testing</span>
                                    </div>
                                    <div class="task-meta">
                                        <div class="task-assignee">
                                            <div class="assignee-avatar">AH</div>
                                            <span>Anna Horváthová</span>
                                        </div>
                                        <div class="task-points">3 SP</div>
                                    </div>
                                </div>
                            </div>
                            <div class="drop-zone">Presuňte úlohu sem</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let draggedTask = null;

        function initPMComponent() {
            setupDragAndDrop();
            setupFilters();
            updateSprintMetrics();
        }

        function setupDragAndDrop() {
            // Drag start
            document.querySelectorAll('.task-card').forEach(card => {
                card.addEventListener('dragstart', function(e) {
                    draggedTask = this;
                    this.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                });

                card.addEventListener('dragend', function() {
                    this.classList.remove('dragging');
                    draggedTask = null;
                });
            });

            // Drop zones
            document.querySelectorAll('.kanban-column').forEach(column => {
                column.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    this.querySelector('.drop-zone').classList.add('drag-over');
                });

                column.addEventListener('dragleave', function() {
                    this.querySelector('.drop-zone').classList.remove('drag-over');
                });

                column.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.querySelector('.drop-zone').classList.remove('drag-over');

                    if (draggedTask) {
                        const taskList = this.querySelector('.task-list');
                        taskList.appendChild(draggedTask);

                        const newStatus = this.dataset.status;
                        const taskId = draggedTask.dataset.taskId;
                        updateTaskStatus(taskId, newStatus);
                        updateColumnCounts();
                        updateSprintMetrics();
                    }
                });
            });
        }

        function setupFilters() {
            document.getElementById('assigneeFilter').addEventListener('change', filterTasks);
            document.getElementById('priorityFilter').addEventListener('change', filterTasks);
        }

        function filterTasks() {
            const assigneeFilter = document.getElementById('assigneeFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;

            document.querySelectorAll('.task-card').forEach(card => {
                let showCard = true;

                // Filter by assignee
                if (assigneeFilter !== 'all') {
                    const assignee = card.querySelector('.task-assignee span').textContent.toLowerCase();
                    if (!assignee.includes(assigneeFilter)) {
                        showCard = false;
                    }
                }

                // Filter by priority
                if (priorityFilter !== 'all') {
                    const priority = card.querySelector('.task-priority');
                    if (!priority.classList.contains(`priority-${priorityFilter}`)) {
                        showCard = false;
                    }
                }

                card.style.display = showCard ? 'block' : 'none';
            });

            updateColumnCounts();
        }

        function updateTaskStatus(taskId, newStatus) {
            console.log(`Task ${taskId} moved to ${newStatus}`);
            // V produkčnej verzii by sa tu aktualizoval stav v databáze
        }

        function updateColumnCounts() {
            document.querySelectorAll('.kanban-column').forEach(column => {
                const visibleTasks = column.querySelectorAll('.task-card:not([style*="display: none"])').length;
                column.querySelector('.column-count').textContent = visibleTasks;
            });
        }

        function updateSprintMetrics() {
            // Simulácia aktualizácie sprint metrík
            const totalTasks = document.querySelectorAll('.task-card').length;
            const doneTasks = document.querySelectorAll('#doneTasks .task-card').length;
            const progress = Math.round((doneTasks / totalTasks) * 100);

            document.querySelector('.progress-fill').style.width = progress + '%';
            document.querySelector('.progress-label span:last-child').textContent = progress + '%';
        }

        function startSprint() {
            alert('🚀 Štartuje nový Sprint!\n\n• Sprint Goal: Dokončiť core e-shop funkcionalitu\n• Trvanie: 2 týždne\n• Story Points: 23\n• Tím: 5 členov\n\nDaily standups: Každý deň 9:00\nSprint Review: 30.06.2024\nSprint Retrospective: 30.06.2024\n\nŠprintový cieľ nastavený!');
        }

        function sprintReview() {
            alert('📊 Sprint Review - Sprint 3\n\n📈 Výsledky:\n• Dokončené Story Points: 13/23\n• Velocity: 13 SP\n• Úspešnosť: 57%\n\n✅ Dokončené úlohy:\n• Používateľská autentifikácia (8 SP)\n• Hlavná navigácia (3 SP)\n• Databázová schéma (5 SP)\n• Unit testy pre API (3 SP)\n\n🔄 Nedokončené úlohy:\n• Platobný systém (8 SP)\n• Responzívny dizajn košíka (3 SP)\n\nOdporúčania pre ďalší sprint:\n• Znížiť commitment na 20 SP\n• Viac času na code review\n• Lepšia koordinácia medzi frontend/backend');
        }

        function addNewTask() {
            const taskTitle = prompt('Zadajte názov novej úlohy:');
            if (taskTitle) {
                alert(`✅ Nová úloha vytvorená!\n\nNázov: ${taskTitle}\nStav: To Do\nPriorita: Stredná\nStory Points: 3\n\n• Úloha pridaná do backlogu\n• Notifikácia odoslaná tímu\n• Sprint board aktualizovaný`);
            }
        }

        // Task card click handler
        document.querySelectorAll('.task-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (e.target.closest('.task-card') && !e.target.closest('.dragging')) {
                    const taskTitle = this.querySelector('.task-title').textContent;
                    const taskDescription = this.querySelector('.task-description').textContent;
                    const assignee = this.querySelector('.task-assignee span').textContent;
                    const points = this.querySelector('.task-points').textContent;

                    alert(`📋 Detail úlohy\n\nNázov: ${taskTitle}\nPopis: ${taskDescription}\nPriradené: ${assignee}\nStory Points: ${points}\n\nAkcie:\n• Editovať úlohu\n• Pridať komentár\n• Zmeniť prioritu\n• Pridať subtask\n• Logovať čas`);
                }
            });
        });

        // Velocity chart interaction
        document.querySelectorAll('.velocity-bar').forEach(bar => {
            bar.addEventListener('click', function() {
                const title = this.getAttribute('title');
                alert(`📊 ${title}\n\nDetaily sprintu:\n• Plánované: 25 SP\n• Dokončené: ${title.split(': ')[1]}\n• Úspešnosť: ${Math.round((parseInt(title.split(': ')[1]) / 25) * 100)}%\n• Trvanie: 14 dní\n• Počet úloh: 12\n\nKľúčové metriky:\n• Burn-down rate: Optimálny\n• Team satisfaction: 8.5/10\n• Code quality: 92%`);
            });
        });

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>