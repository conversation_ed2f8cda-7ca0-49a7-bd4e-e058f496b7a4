<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Base Builder & Wiki Manager - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #047857 0%, #059669 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            min-width: 300px;
        }

        .search-input {
            background: none;
            border: none;
            color: white;
            outline: none;
            flex: 1;
            padding: 0.25rem 0.5rem;
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            min-height: 700px;
        }

        .wiki-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .wiki-tree {
            list-style: none;
        }

        .tree-item {
            margin-bottom: 0.5rem;
        }

        .tree-node {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tree-node:hover {
            background: #e2e8f0;
        }

        .tree-node.active {
            background: #047857;
            color: white;
        }

        .tree-icon {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .tree-label {
            flex: 1;
            font-size: 0.9rem;
        }

        .tree-children {
            margin-left: 1.5rem;
            margin-top: 0.5rem;
        }

        .main-editor {
            padding: 2rem;
            display: flex;
            flex-direction: column;
        }

        .editor-toolbar {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .toolbar-btn {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .toolbar-btn:hover {
            background: #e5e7eb;
        }

        .toolbar-btn.active {
            background: #047857;
            color: white;
            border-color: #047857;
        }

        .editor-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            flex: 1;
            min-height: 500px;
        }

        .editor-pane {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .pane-header {
            background: #f8fafc;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
        }

        .editor-textarea {
            width: 100%;
            height: 100%;
            border: none;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            resize: none;
            outline: none;
        }

        .preview-content {
            padding: 1rem;
            height: 100%;
            overflow-y: auto;
            line-height: 1.6;
        }

        .preview-content h1 {
            color: #1f2937;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .preview-content h2 {
            color: #374151;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
            font-size: 1.2rem;
        }

        .preview-content h3 {
            color: #4b5563;
            margin-bottom: 0.5rem;
            margin-top: 1rem;
            font-size: 1rem;
        }

        .preview-content p {
            margin-bottom: 1rem;
            color: #6b7280;
        }

        .preview-content ul, .preview-content ol {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
            color: #6b7280;
        }

        .preview-content code {
            background: #f3f4f6;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        .preview-content pre {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .ai-assistant {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .assistant-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .ai-suggestions {
            background: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);
            border: 1px solid #a78bfa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .ai-title {
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .suggestion-item {
            background: rgba(255,255,255,0.7);
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .suggestion-item:hover {
            background: rgba(255,255,255,0.9);
            transform: translateX(3px);
        }

        .recent-changes {
            margin-bottom: 1.5rem;
        }

        .change-item {
            background: white;
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid #047857;
            font-size: 0.9rem;
        }

        .change-title {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .change-meta {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .wiki-stats {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #047857;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 250px 1fr;
            }

            .ai-assistant {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .wiki-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }

            .editor-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Knowledge Base Builder & Wiki Manager - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>📚 Knowledge Base Builder & Wiki Manager</h2>
                <div class="search-bar">
                    <span>🔍</span>
                    <input type="text" class="search-input" placeholder="Hľadať v knowledge base..." id="searchInput">
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="createNewPage()">+ Nová Stránka</button>
                    <button class="btn btn-primary" onclick="exportWiki()">📤 Export Wiki</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="wiki-sidebar">
                    <div class="sidebar-section">
                        <div class="sidebar-title">📖 Wiki Štruktúra</div>
                        <ul class="wiki-tree">
                            <li class="tree-item">
                                <div class="tree-node active" data-page="home">
                                    <span class="tree-icon">🏠</span>
                                    <span class="tree-label">Domovská Stránka</span>
                                </div>
                            </li>
                            <li class="tree-item">
                                <div class="tree-node" data-page="project-overview">
                                    <span class="tree-icon">📋</span>
                                    <span class="tree-label">Prehľad Projektu</span>
                                </div>
                                <ul class="tree-children">
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="project-goals">
                                            <span class="tree-icon">🎯</span>
                                            <span class="tree-label">Ciele Projektu</span>
                                        </div>
                                    </li>
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="stakeholders">
                                            <span class="tree-icon">👥</span>
                                            <span class="tree-label">Stakeholderi</span>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            <li class="tree-item">
                                <div class="tree-node" data-page="technical-docs">
                                    <span class="tree-icon">⚙️</span>
                                    <span class="tree-label">Technická Dokumentácia</span>
                                </div>
                                <ul class="tree-children">
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="architecture">
                                            <span class="tree-icon">🏗️</span>
                                            <span class="tree-label">Architektúra</span>
                                        </div>
                                    </li>
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="api-docs">
                                            <span class="tree-icon">🔌</span>
                                            <span class="tree-label">API Dokumentácia</span>
                                        </div>
                                    </li>
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="deployment">
                                            <span class="tree-icon">🚀</span>
                                            <span class="tree-label">Deployment Guide</span>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            <li class="tree-item">
                                <div class="tree-node" data-page="processes">
                                    <span class="tree-icon">🔄</span>
                                    <span class="tree-label">Procesy a Postupy</span>
                                </div>
                                <ul class="tree-children">
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="development-workflow">
                                            <span class="tree-icon">💻</span>
                                            <span class="tree-label">Development Workflow</span>
                                        </div>
                                    </li>
                                    <li class="tree-item">
                                        <div class="tree-node" data-page="testing-procedures">
                                            <span class="tree-icon">🧪</span>
                                            <span class="tree-label">Testing Procedures</span>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            <li class="tree-item">
                                <div class="tree-node" data-page="meeting-notes">
                                    <span class="tree-icon">📝</span>
                                    <span class="tree-label">Zápisnice z Meetingov</span>
                                </div>
                            </li>
                            <li class="tree-item">
                                <div class="tree-node" data-page="lessons-learned">
                                    <span class="tree-icon">💡</span>
                                    <span class="tree-label">Lessons Learned</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="main-editor">
                    <div class="editor-toolbar">
                        <button class="toolbar-btn active" data-action="bold" title="Tučné">B</button>
                        <button class="toolbar-btn" data-action="italic" title="Kurzíva">I</button>
                        <button class="toolbar-btn" data-action="heading" title="Nadpis">H</button>
                        <button class="toolbar-btn" data-action="list" title="Zoznam">•</button>
                        <button class="toolbar-btn" data-action="link" title="Odkaz">🔗</button>
                        <button class="toolbar-btn" data-action="image" title="Obrázok">🖼️</button>
                        <button class="toolbar-btn" data-action="code" title="Kód">&lt;/&gt;</button>
                        <button class="toolbar-btn" data-action="table" title="Tabuľka">⊞</button>
                        <div style="margin-left: auto; display: flex; gap: 0.5rem;">
                            <button class="toolbar-btn" onclick="saveContent()">💾 Uložiť</button>
                            <button class="toolbar-btn" onclick="previewMode()">👁️ Náhľad</button>
                        </div>
                    </div>

                    <div class="editor-container">
                        <div class="editor-pane">
                            <div class="pane-header">📝 Markdown Editor</div>
                            <textarea class="editor-textarea" id="markdownEditor" placeholder="Začnite písať vašu dokumentáciu v Markdown formáte...">
# E-shop Modernizácia - Domovská Stránka

## Prehľad Projektu

Tento projekt sa zameriava na kompletnú modernizáciu existujúceho e-shop systému s cieľom zlepšiť používateľský zážitok, výkonnosť a škálovateľnosť.

### Kľúčové Ciele

- **Zlepšenie výkonnosti** - Zníženie loading time o 50%
- **Modernizácia UI/UX** - Responzívny dizajn pre všetky zariadenia
- **API Integration** - Integrácia s externými službami
- **Security Enhancement** - Implementácia najnovších bezpečnostných štandardov

### Tím Projektu

| Meno | Pozícia | Zodpovednosť |
|------|---------|--------------|
| Ján Novák | Project Manager | Celkové riadenie projektu |
| Mária Svobodová | UI/UX Designer | Dizajn a používateľský zážitok |
| Peter Kováč | Backend Developer | Server-side development |
| Anna Horváthová | QA Tester | Testovanie a quality assurance |

### Aktuálny Stav

- ✅ Analýza požiadaviek dokončená
- ✅ UI/UX dizajn schválený
- 🔄 Backend development v priebehu (60%)
- ⏳ Frontend development plánovaný
- ⏳ Testing fáza plánovaná

### Dôležité Odkazy

- [Projektový Plán](project-plan.md)
- [Technická Špecifikácia](technical-spec.md)
- [Meeting Notes](meeting-notes/)
- [Risk Register](risk-register.md)

---

*Posledná aktualizácia: 15.06.2024 - Ján Novák*</textarea>
                        </div>

                        <div class="editor-pane">
                            <div class="pane-header">👁️ Live Preview</div>
                            <div class="preview-content" id="previewContent">
                                <!-- Preview content bude vygenerovaný JavaScriptom -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ai-assistant">
                    <div class="assistant-title">🤖 AI Asistent</div>

                    <div class="ai-suggestions">
                        <div class="ai-title">
                            💡 Návrhy na Zlepšenie
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(1)">
                            Pridať sekciu "Quick Start Guide"
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(2)">
                            Vytvoriť FAQ sekciu
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(3)">
                            Pridať troubleshooting guide
                        </div>
                        <div class="suggestion-item" onclick="applySuggestion(4)">
                            Vytvoriť glossary pojmov
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <div class="sidebar-title">🕒 Nedávne Zmeny</div>
                        <div class="recent-changes">
                            <div class="change-item">
                                <div class="change-title">API Dokumentácia aktualizovaná</div>
                                <div class="change-meta">Pred 2 hodinami - Peter Kováč</div>
                            </div>
                            <div class="change-item">
                                <div class="change-title">Nová stránka: Testing Procedures</div>
                                <div class="change-meta">Včera - Anna Horváthová</div>
                            </div>
                            <div class="change-item">
                                <div class="change-title">Deployment Guide rozšírený</div>
                                <div class="change-meta">Pred 2 dňami - Ján Novák</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <div class="sidebar-title">📊 Wiki Štatistiky</div>
                        <div class="wiki-stats">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">24</div>
                                    <div class="stat-label">Stránky</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">156</div>
                                    <div class="stat-label">Úpravy</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">5</div>
                                    <div class="stat-label">Prispievatelia</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">89%</div>
                                    <div class="stat-label">Pokrytie</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <div class="sidebar-title">🎯 Odporúčania</div>
                        <div style="background: white; border-radius: 8px; padding: 1rem; font-size: 0.9rem; line-height: 1.4; color: #6b7280;">
                            <strong style="color: #1f2937;">AI Insights:</strong><br><br>
                            • Chýba dokumentácia pre error handling<br>
                            • Odporúčame pridať code examples<br>
                            • Aktualizovať screenshots v UI guide<br>
                            • Vytvoriť onboarding checklist
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentPage = 'home';
        let isPreviewMode = false;

        function initPMComponent() {
            setupEventListeners();
            updatePreview();
            loadPageContent();
        }

        function setupEventListeners() {
            // Wiki tree navigation
            document.querySelectorAll('.tree-node').forEach(node => {
                node.addEventListener('click', function() {
                    document.querySelectorAll('.tree-node').forEach(n => n.classList.remove('active'));
                    this.classList.add('active');
                    currentPage = this.dataset.page;
                    loadPageContent();
                });
            });

            // Toolbar actions
            document.querySelectorAll('.toolbar-btn[data-action]').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.dataset.action;
                    applyFormatting(action);
                });
            });

            // Live preview update
            document.getElementById('markdownEditor').addEventListener('input', updatePreview);

            // Search functionality
            document.getElementById('searchInput').addEventListener('input', searchWiki);
        }

        function loadPageContent() {
            const editor = document.getElementById('markdownEditor');

            // Simulácia načítania obsahu stránky
            const pageContent = {
                'home': editor.value, // Už je nastavený
                'project-overview': '# Prehľad Projektu\n\n## Úvod\nDetailný prehľad projektu E-shop Modernizácia...',
                'technical-docs': '# Technická Dokumentácia\n\n## Architektúra\nSystemová architektúra...',
                'api-docs': '# API Dokumentácia\n\n## Endpoints\n\n### GET /api/products\nVráti zoznam produktov...'
            };

            if (pageContent[currentPage]) {
                editor.value = pageContent[currentPage];
                updatePreview();
            }
        }

        function updatePreview() {
            const markdown = document.getElementById('markdownEditor').value;
            const preview = document.getElementById('previewContent');

            // Jednoduchá Markdown to HTML konverzia
            let html = markdown
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
                .replace(/\*(.*)\*/gim, '<em>$1</em>')
                .replace(/`(.*?)`/gim, '<code>$1</code>')
                .replace(/^\- (.*$)/gim, '<li>$1</li>')
                .replace(/\n\n/gim, '</p><p>')
                .replace(/\n/gim, '<br>');

            // Wrap paragraphs
            html = '<p>' + html + '</p>';

            // Fix lists
            html = html.replace(/(<li>.*<\/li>)/gim, '<ul>$1</ul>');

            preview.innerHTML = html;
        }

        function applyFormatting(action) {
            const editor = document.getElementById('markdownEditor');
            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            const selectedText = editor.value.substring(start, end);

            let replacement = '';

            switch(action) {
                case 'bold':
                    replacement = `**${selectedText}**`;
                    break;
                case 'italic':
                    replacement = `*${selectedText}*`;
                    break;
                case 'heading':
                    replacement = `## ${selectedText}`;
                    break;
                case 'list':
                    replacement = `- ${selectedText}`;
                    break;
                case 'link':
                    replacement = `[${selectedText}](url)`;
                    break;
                case 'code':
                    replacement = `\`${selectedText}\``;
                    break;
                case 'table':
                    replacement = `| Stĺpec 1 | Stĺpec 2 |\n|----------|----------|\n| ${selectedText} | hodnota |`;
                    break;
            }

            editor.value = editor.value.substring(0, start) + replacement + editor.value.substring(end);
            updatePreview();
        }

        function searchWiki() {
            const query = document.getElementById('searchInput').value.toLowerCase();

            if (query.length > 2) {
                // Simulácia vyhľadávania
                console.log('Hľadám:', query);
                // V produkčnej verzii by sa tu implementovalo skutočné vyhľadávanie
            }
        }

        function createNewPage() {
            const pageName = prompt('Zadajte názov novej stránky:');
            if (pageName) {
                alert(`✅ Nová stránka "${pageName}" vytvorená!\n\nStránka bola pridaná do wiki štruktúry.\nMôžete začať s editáciou obsahu.`);
            }
        }

        function saveContent() {
            alert('💾 Obsah uložený!\n\n• Automatické verziovanie: ✓\n• Backup vytvorený: ✓\n• Zmeny synchronizované: ✓\n• Notifikácie odoslané tímu: ✓\n\nStránka je aktualizovaná a dostupná pre všetkých členov tímu.');
        }

        function previewMode() {
            isPreviewMode = !isPreviewMode;
            const container = document.querySelector('.editor-container');

            if (isPreviewMode) {
                container.style.gridTemplateColumns = '1fr';
                document.querySelector('.editor-pane').style.display = 'none';
            } else {
                container.style.gridTemplateColumns = '1fr 1fr';
                document.querySelector('.editor-pane').style.display = 'block';
            }
        }

        function exportWiki() {
            alert('📤 Export Wiki...\n\n• Generujem PDF dokumentáciu: ✓\n• Vytváram HTML export: ✓\n• Archivujem obrázky: ✓\n• Komprimujem súbory: ✓\n\nWiki exportovaná!\nFormáty: PDF (12.4 MB), HTML (8.7 MB)\nSťahovanie začína...');
        }

        function applySuggestion(id) {
            const suggestions = {
                1: '# Quick Start Guide\n\n## Prvé Kroky\n1. Klonujte repository\n2. Nainštalujte dependencies\n3. Spustite development server',
                2: '# FAQ\n\n## Často Kladené Otázky\n\n**Q: Ako spustiť projekt lokálne?**\nA: Použite príkaz `npm start`',
                3: '# Troubleshooting\n\n## Časté Problémy\n\n### Build Error\nSkontrolujte verziu Node.js',
                4: '# Glossary\n\n## Pojmy\n\n**API** - Application Programming Interface\n**CI/CD** - Continuous Integration/Deployment'
            };

            const editor = document.getElementById('markdownEditor');
            editor.value += '\n\n' + suggestions[id];
            updatePreview();
            alert('✅ AI návrh aplikovaný! Obsah bol pridaný do editora.');
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>