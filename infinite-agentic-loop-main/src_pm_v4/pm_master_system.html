<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PM Master System v4 - Integrovaný Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .master-header {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1800px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .system-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(255,255,255,0.1);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .main-container {
            margin-top: 80px;
            display: flex;
            min-height: calc(100vh - 80px);
        }

        .sidebar {
            width: 280px;
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255,255,255,0.1);
            padding: 2rem 0;
            overflow-y: auto;
            position: fixed;
            height: calc(100vh - 80px);
            z-index: 100;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .section-title {
            color: #94a3b8;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 1.5rem;
            margin-bottom: 1rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: #e2e8f0;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #3b82f6;
            color: white;
        }

        .nav-item.active {
            background: rgba(59, 130, 246, 0.2);
            border-left-color: #3b82f6;
            color: white;
        }

        .nav-icon {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .nav-text {
            font-weight: 500;
        }

        .component-badge {
            background: #3b82f6;
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin-left: auto;
        }

        .content-area {
            flex: 1;
            margin-left: 280px;
            background: #f8fafc;
            min-height: calc(100vh - 80px);
        }

        .component-frame {
            width: 100%;
            height: calc(100vh - 80px);
            border: none;
            background: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(248, 250, 252, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .quick-actions {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 200;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.6);
        }

        .fab-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 1rem;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .fab-menu.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .fab-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
            color: #374151;
        }

        .fab-item:hover {
            background: #f3f4f6;
        }

        .breadcrumb {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .breadcrumb-separator {
            color: #d1d5db;
        }

        .system-notifications {
            position: fixed;
            top: 100px;
            right: 2rem;
            z-index: 300;
            max-width: 400px;
        }

        .notification {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-left: 4px solid #3b82f6;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { border-left-color: #10b981; }
        .notification.warning { border-left-color: #f59e0b; }
        .notification.error { border-left-color: #ef4444; }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .notification-title {
            font-weight: 600;
            color: #1f2937;
        }

        .notification-close {
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .notification-message {
            color: #6b7280;
            font-size: 0.9rem;
        }

        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .content-area {
                margin-left: 0;
            }
            
            .mobile-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 99;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .mobile-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
    </style>
</head>
<body>
    <!-- Master Header -->
    <header class="master-header">
        <div class="header-content">
            <div class="system-logo">
                <div class="logo-icon">🎯</div>
                <div>PM Master System v4</div>
            </div>

            <div class="system-status">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>20 Komponentov Online</span>
                </div>
                <div class="status-indicator">
                    <span>⚡ AI Engine Active</span>
                </div>
                <div class="user-profile" onclick="toggleUserMenu()">
                    <div class="user-avatar">JN</div>
                    <span>Ján Novák</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileSidebar()"></div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-section">
                <div class="section-title">Executive Level</div>
                <div class="nav-item" data-component="20" onclick="loadComponent(20)">
                    <div class="nav-icon">👑</div>
                    <div class="nav-text">Strategic Portfolio</div>
                    <div class="component-badge">20</div>
                </div>
                <div class="nav-item" data-component="17" onclick="loadComponent(17)">
                    <div class="nav-icon">🎛️</div>
                    <div class="nav-text">Master Dashboard</div>
                    <div class="component-badge">17</div>
                </div>
                <div class="nav-item" data-component="18" onclick="loadComponent(18)">
                    <div class="nav-icon">📊</div>
                    <div class="nav-text">Advanced Analytics</div>
                    <div class="component-badge">18</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="section-title">Enterprise Level</div>
                <div class="nav-item" data-component="19" onclick="loadComponent(19)">
                    <div class="nav-icon">🔗</div>
                    <div class="nav-text">Enterprise Integration</div>
                    <div class="component-badge">19</div>
                </div>
                <div class="nav-item" data-component="16" onclick="loadComponent(16)">
                    <div class="nav-icon">🤖</div>
                    <div class="nav-text">AI Assistant</div>
                    <div class="component-badge">16</div>
                </div>
                <div class="nav-item" data-component="15" onclick="loadComponent(15)">
                    <div class="nav-icon">🚀</div>
                    <div class="nav-text">DevOps Pipeline</div>
                    <div class="component-badge">15</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="section-title">Management Level</div>
                <div class="nav-item" data-component="12" onclick="loadComponent(12)">
                    <div class="nav-icon">💰</div>
                    <div class="nav-text">Budget Tracker</div>
                    <div class="component-badge">12</div>
                </div>
                <div class="nav-item" data-component="10" onclick="loadComponent(10)">
                    <div class="nav-icon">💬</div>
                    <div class="nav-text">Stakeholder Hub</div>
                    <div class="component-badge">10</div>
                </div>
                <div class="nav-item" data-component="9" onclick="loadComponent(9)">
                    <div class="nav-icon">📈</div>
                    <div class="nav-text">Performance Analyzer</div>
                    <div class="component-badge">9</div>
                </div>
                <div class="nav-item" data-component="6" onclick="loadComponent(6)">
                    <div class="nav-icon">⚠️</div>
                    <div class="nav-text">Risk Predictor</div>
                    <div class="component-badge">6</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="section-title">Operational Level</div>
                <div class="nav-item" data-component="13" onclick="loadComponent(13)">
                    <div class="nav-icon">🏃‍♂️</div>
                    <div class="nav-text">Sprint Manager</div>
                    <div class="component-badge">13</div>
                </div>
                <div class="nav-item" data-component="11" onclick="loadComponent(11)">
                    <div class="nav-icon">🧪</div>
                    <div class="nav-text">QA & Testing</div>
                    <div class="component-badge">11</div>
                </div>
                <div class="nav-item" data-component="7" onclick="loadComponent(7)">
                    <div class="nav-icon">👤</div>
                    <div class="nav-text">Resource Optimizer</div>
                    <div class="component-badge">7</div>
                </div>
                <div class="nav-item" data-component="3" onclick="loadComponent(3)">
                    <div class="nav-icon">💚</div>
                    <div class="nav-text">Project Health</div>
                    <div class="component-badge">3</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="section-title">Project Level</div>
                <div class="nav-item" data-component="2" onclick="loadComponent(2)">
                    <div class="nav-icon">📊</div>
                    <div class="nav-text">Gantt Plánovač</div>
                    <div class="component-badge">2</div>
                </div>
                <div class="nav-item" data-component="4" onclick="loadComponent(4)">
                    <div class="nav-icon">📅</div>
                    <div class="nav-text">Meeting Planner</div>
                    <div class="component-badge">4</div>
                </div>
                <div class="nav-item" data-component="14" onclick="loadComponent(14)">
                    <div class="nav-icon">👤</div>
                    <div class="nav-text">Client Portal</div>
                    <div class="component-badge">14</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="section-title">Knowledge Level</div>
                <div class="nav-item" data-component="1" onclick="loadComponent(1)">
                    <div class="nav-icon">📚</div>
                    <div class="nav-text">Dokumentový Hub</div>
                    <div class="component-badge">1</div>
                </div>
                <div class="nav-item" data-component="8" onclick="loadComponent(8)">
                    <div class="nav-icon">📖</div>
                    <div class="nav-text">Knowledge Base</div>
                    <div class="component-badge">8</div>
                </div>
                <div class="nav-item" data-component="5" onclick="loadComponent(5)">
                    <div class="nav-icon">📄</div>
                    <div class="nav-text">Template Generátor</div>
                    <div class="component-badge">5</div>
                </div>
            </div>
        </nav>

        <!-- Content Area -->
        <main class="content-area">
            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <div class="breadcrumb-item">
                    <span>🏠</span>
                    <span>PM Master System</span>
                </div>
                <div class="breadcrumb-separator">›</div>
                <div class="breadcrumb-item" id="currentBreadcrumb">
                    <span>Master Dashboard</span>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
            </div>

            <!-- Component Frame -->
            <iframe class="component-frame" id="componentFrame" src="pm_component_17.html"></iframe>
        </main>
    </div>

    <!-- Quick Actions FAB -->
    <div class="quick-actions">
        <button class="fab" onclick="toggleFabMenu()">⚡</button>
        <div class="fab-menu" id="fabMenu">
            <div class="fab-item" onclick="quickAction('ai-analysis')">
                <span>🧠</span>
                <span>AI Analýza</span>
            </div>
            <div class="fab-item" onclick="quickAction('create-project')">
                <span>➕</span>
                <span>Nový Projekt</span>
            </div>
            <div class="fab-item" onclick="quickAction('system-health')">
                <span>💚</span>
                <span>System Health</span>
            </div>
            <div class="fab-item" onclick="quickAction('export-data')">
                <span>📤</span>
                <span>Export Dát</span>
            </div>
            <div class="fab-item" onclick="quickAction('settings')">
                <span>⚙️</span>
                <span>Nastavenia</span>
            </div>
        </div>
    </div>

    <!-- System Notifications -->
    <div class="system-notifications" id="notifications"></div>

    <script>
        // Global state management
        const PMSystem = {
            currentComponent: 17,
            components: {
                1: { name: 'Inteligentný Dokumentový Hub', file: 'pm_component_1.html', category: 'Knowledge' },
                2: { name: 'Adaptívny Gantt Plánovač', file: 'pm_component_2.html', category: 'Project' },
                3: { name: 'Project Health Dashboard', file: 'pm_component_3.html', category: 'Operational' },
                4: { name: 'Smart Meeting Planner', file: 'pm_component_4.html', category: 'Project' },
                5: { name: 'Template Generátor', file: 'pm_component_5.html', category: 'Knowledge' },
                6: { name: 'Risk Predictor & Mitigation', file: 'pm_component_6.html', category: 'Management' },
                7: { name: 'Resource Optimizer', file: 'pm_component_7.html', category: 'Operational' },
                8: { name: 'Knowledge Base Builder', file: 'pm_component_8.html', category: 'Knowledge' },
                9: { name: 'Performance Analyzer', file: 'pm_component_9.html', category: 'Management' },
                10: { name: 'Stakeholder Communication Hub', file: 'pm_component_10.html', category: 'Management' },
                11: { name: 'Quality Assurance & Testing', file: 'pm_component_11.html', category: 'Operational' },
                12: { name: 'Budget Tracker & Financial Analytics', file: 'pm_component_12.html', category: 'Management' },
                13: { name: 'Agile Sprint Manager & Scrum Board', file: 'pm_component_13.html', category: 'Operational' },
                14: { name: 'Client Portal & Feedback Manager', file: 'pm_component_14.html', category: 'Project' },
                15: { name: 'Deployment Pipeline & DevOps', file: 'pm_component_15.html', category: 'Enterprise' },
                16: { name: 'AI Project Assistant & Automation', file: 'pm_component_16.html', category: 'Enterprise' },
                17: { name: 'Master Dashboard & Control Center', file: 'pm_component_17.html', category: 'Executive' },
                18: { name: 'Advanced Analytics & Business Intelligence', file: 'pm_component_18.html', category: 'Executive' },
                19: { name: 'Enterprise Integration & API Management', file: 'pm_component_19.html', category: 'Enterprise' },
                20: { name: 'Strategic Portfolio Management & Executive Dashboard', file: 'pm_component_20.html', category: 'Executive' }
            },
            notifications: [],
            fabMenuOpen: false,
            userMenuOpen: false
        };

        // Initialize system
        function initPMSystem() {
            console.log('🎯 PM Master System v4 Initialized');

            // Set initial active nav item
            updateActiveNavItem(PMSystem.currentComponent);

            // Start system monitoring
            startSystemMonitoring();

            // Show welcome notification
            showNotification('success', 'System Ready', 'PM Master System v4 úspešne načítaný s 20 komponentmi');

            // Load initial component
            loadComponent(17, false);
        }

        // Component loading
        function loadComponent(componentId, showLoading = true) {
            const component = PMSystem.components[componentId];
            if (!component) {
                showNotification('error', 'Chyba', `Komponent ${componentId} neexistuje`);
                return;
            }

            if (showLoading) {
                showLoadingOverlay();
            }

            // Update current component
            PMSystem.currentComponent = componentId;

            // Update navigation
            updateActiveNavItem(componentId);

            // Update breadcrumb
            updateBreadcrumb(component.name);

            // Load component in iframe
            const iframe = document.getElementById('componentFrame');
            iframe.src = component.file;

            // Hide loading after delay
            setTimeout(() => {
                hideLoadingOverlay();
                showNotification('info', 'Komponent Načítaný', `${component.name} je pripravený na použitie`);
            }, showLoading ? 1000 : 100);

            // Close mobile sidebar if open
            closeMobileSidebar();

            console.log(`📊 Loaded component ${componentId}: ${component.name}`);
        }

        // Navigation management
        function updateActiveNavItem(componentId) {
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to current component
            const activeItem = document.querySelector(`[data-component="${componentId}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }

        function updateBreadcrumb(componentName) {
            const breadcrumb = document.getElementById('currentBreadcrumb');
            breadcrumb.innerHTML = `<span>${componentName}</span>`;
        }

        // Loading overlay
        function showLoadingOverlay() {
            document.getElementById('loadingOverlay').classList.add('active');
        }

        function hideLoadingOverlay() {
            document.getElementById('loadingOverlay').classList.remove('active');
        }

        // FAB menu
        function toggleFabMenu() {
            PMSystem.fabMenuOpen = !PMSystem.fabMenuOpen;
            const fabMenu = document.getElementById('fabMenu');
            fabMenu.classList.toggle('active', PMSystem.fabMenuOpen);
        }

        function quickAction(action) {
            toggleFabMenu(); // Close menu

            const actions = {
                'ai-analysis': () => {
                    showNotification('info', 'AI Analýza', 'Spúšťam komplexnú AI analýzu všetkých projektov...');
                    setTimeout(() => {
                        showNotification('success', 'AI Analýza Dokončená', 'Identifikované 3 optimalizačné príležitosti');
                    }, 3000);
                },
                'create-project': () => {
                    showNotification('info', 'Nový Projekt', 'Otváram wizard pre vytvorenie nového projektu...');
                    loadComponent(2); // Load Gantt Planner
                },
                'system-health': () => {
                    showNotification('success', 'System Health', 'Všetky komponenty fungujú optimálne (99.8% uptime)');
                    loadComponent(17); // Load Master Dashboard
                },
                'export-data': () => {
                    showNotification('info', 'Export Dát', 'Generujem export všetkých projektových dát...');
                    setTimeout(() => {
                        showNotification('success', 'Export Dokončený', 'Dáta exportované do PM_Data_Export.xlsx');
                    }, 2000);
                },
                'settings': () => {
                    showNotification('info', 'Nastavenia', 'Otváram systémové nastavenia...');
                }
            };

            if (actions[action]) {
                actions[action]();
            }
        }

        // Notification system
        function showNotification(type, title, message) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-header">
                    <div class="notification-title">${title}</div>
                    <button class="notification-close" onclick="closeNotification(this)">×</button>
                </div>
                <div class="notification-message">${message}</div>
            `;

            const container = document.getElementById('notifications');
            container.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    closeNotification(notification.querySelector('.notification-close'));
                }
            }, 5000);

            PMSystem.notifications.push(notification);
        }

        function closeNotification(button) {
            const notification = button.closest('.notification');
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }

        // Mobile sidebar
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            sidebar.classList.toggle('mobile-open');
            overlay.classList.toggle('active');
        }

        function closeMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            sidebar.classList.remove('mobile-open');
            overlay.classList.remove('active');
        }

        // User menu
        function toggleUserMenu() {
            PMSystem.userMenuOpen = !PMSystem.userMenuOpen;
            if (PMSystem.userMenuOpen) {
                showNotification('info', 'User Menu', 'Profile settings, logout a system preferences');
            }
        }

        // System monitoring
        function startSystemMonitoring() {
            setInterval(() => {
                // Simulate system health checks
                const healthCheck = Math.random();
                if (healthCheck < 0.05) { // 5% chance of warning
                    showNotification('warning', 'System Warning', 'Vysoké využitie CPU detekované v komponente Analytics');
                }
            }, 30000); // Every 30 seconds

            console.log('🔍 System monitoring started');
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + number keys for quick component switching
            if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const componentId = parseInt(e.key);
                if (PMSystem.components[componentId]) {
                    loadComponent(componentId);
                }
            }

            // Escape to close menus
            if (e.key === 'Escape') {
                if (PMSystem.fabMenuOpen) toggleFabMenu();
                closeMobileSidebar();
            }
        });

        // Close menus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.quick-actions') && PMSystem.fabMenuOpen) {
                toggleFabMenu();
            }
        });

        // Initialize system when DOM is loaded
        document.addEventListener('DOMContentLoaded', initPMSystem);
    </script>
</body>
</html>
