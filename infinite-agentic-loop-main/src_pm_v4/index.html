<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PM Master System v4 - Kompletný Projektový Manažment</title>
    <meta name="description" content="Integrovaný projektový manažment systém s 20 AI-powered komponentmi">
    <meta name="keywords" content="project management, AI, automation, Slovak, PM system">
    <meta name="author" content="PM Master System v4">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="pm_system_config.js" as="script">
    <link rel="preload" href="pm_api_layer.js" as="script">
    <link rel="preload" href="pm_component_bridge.js" as="script">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
            color: white;
            overflow: hidden;
        }

        .splash-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .splash-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .splash-logo {
            font-size: 4rem;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        .splash-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-align: center;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .splash-subtitle {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 3rem;
            text-align: center;
        }

        .splash-progress {
            width: 300px;
            height: 4px;
            background: rgba(255,255,255,0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .splash-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            border-radius: 2px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .splash-status {
            color: #94a3b8;
            font-size: 0.9rem;
            text-align: center;
            min-height: 1.5rem;
        }

        .component-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1rem;
            margin-top: 2rem;
            max-width: 600px;
        }

        .component-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            opacity: 0.3;
        }

        .component-icon.loaded {
            opacity: 1;
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            transform: scale(1.1);
        }

        .system-info {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #64748b;
            font-size: 0.8rem;
        }

        .error-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.1rem;
            color: #fca5a5;
            margin-bottom: 2rem;
            text-align: center;
            max-width: 600px;
        }

        .error-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.2);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Loading animations */
        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body>
    <!-- Splash Screen -->
    <div class="splash-screen" id="splashScreen">
        <div class="splash-logo">🎯</div>
        <div class="splash-title">PM Master System v4</div>
        <div class="splash-subtitle">Integrovaný Projektový Manažment s AI</div>
        
        <div class="splash-progress">
            <div class="splash-progress-bar" id="progressBar"></div>
        </div>
        <div class="splash-status" id="statusText">Inicializujem systém<span class="loading-dots"></span></div>
        
        <div class="component-grid" id="componentGrid">
            <!-- Component icons will be populated by JavaScript -->
        </div>
        
        <div class="system-info">
            <div>PM Master System v4.0.0</div>
            <div>20 Integrovaných Komponentov | AI-Powered | Enterprise Ready</div>
            <div>© 2024 PM Master System. Všetky práva vyhradené.</div>
        </div>
    </div>

    <!-- Error Screen -->
    <div class="error-screen" id="errorScreen">
        <div class="error-icon">⚠️</div>
        <div class="error-title">Chyba pri načítaní systému</div>
        <div class="error-message" id="errorMessage">
            Nastala neočakávaná chyba pri inicializácii PM Master System v4.
            Skúste obnoviť stránku alebo kontaktujte administrátora.
        </div>
        <div class="error-actions">
            <button class="btn btn-primary" onclick="location.reload()">🔄 Obnoviť stránku</button>
            <button class="btn btn-secondary" onclick="showSystemInfo()">ℹ️ Systémové info</button>
        </div>
    </div>

    <!-- System Scripts -->
    <script src="pm_system_config.js"></script>
    <script src="pm_api_layer.js"></script>
    <script src="pm_component_bridge.js"></script>

    <script>
        // System Initialization Manager
        class PMSystemInitializer {
            constructor() {
                this.loadingSteps = [
                    { name: 'Načítavam konfiguráciu', duration: 500 },
                    { name: 'Inicializujem API layer', duration: 300 },
                    { name: 'Nastavujem component bridge', duration: 400 },
                    { name: 'Registrujem komponenty', duration: 600 },
                    { name: 'Kontrolujem závislosti', duration: 300 },
                    { name: 'Načítavam AI engine', duration: 800 },
                    { name: 'Pripájam integrácie', duration: 400 },
                    { name: 'Spúšťam monitoring', duration: 200 },
                    { name: 'Finalizujem systém', duration: 300 }
                ];
                
                this.currentStep = 0;
                this.totalSteps = this.loadingSteps.length;
                this.startTime = Date.now();
            }

            async initialize() {
                try {
                    console.log('🚀 Starting PM Master System v4 initialization...');
                    
                    // Create component icons
                    this.createComponentIcons();
                    
                    // Execute loading steps
                    for (let i = 0; i < this.loadingSteps.length; i++) {
                        await this.executeStep(i);
                    }
                    
                    // Final system check
                    await this.finalSystemCheck();
                    
                    // Launch main system
                    await this.launchMainSystem();
                    
                } catch (error) {
                    console.error('❌ System initialization failed:', error);
                    this.showError(error);
                }
            }

            createComponentIcons() {
                const grid = document.getElementById('componentGrid');
                const components = Object.values(PMSystemConfig.components);
                
                // Show first 20 components (all of them)
                components.slice(0, 20).forEach((comp, index) => {
                    const icon = document.createElement('div');
                    icon.className = 'component-icon';
                    icon.innerHTML = comp.icon;
                    icon.title = comp.name;
                    icon.id = `comp-icon-${comp.id}`;
                    grid.appendChild(icon);
                });
            }

            async executeStep(stepIndex) {
                const step = this.loadingSteps[stepIndex];
                this.currentStep = stepIndex + 1;
                
                // Update status
                document.getElementById('statusText').innerHTML = 
                    `${step.name}<span class="loading-dots"></span>`;
                
                // Update progress
                const progress = (this.currentStep / this.totalSteps) * 100;
                document.getElementById('progressBar').style.width = `${progress}%`;
                
                // Simulate loading time
                await this.delay(step.duration);
                
                // Mark some components as loaded
                if (stepIndex >= 3) {
                    const componentsToLoad = Math.floor((stepIndex - 2) * 3);
                    for (let i = 1; i <= Math.min(componentsToLoad, 20); i++) {
                        const icon = document.getElementById(`comp-icon-${i}`);
                        if (icon && !icon.classList.contains('loaded')) {
                            icon.classList.add('loaded');
                            await this.delay(50);
                        }
                    }
                }
                
                console.log(`✅ Step ${this.currentStep}/${this.totalSteps}: ${step.name}`);
            }

            async finalSystemCheck() {
                document.getElementById('statusText').innerHTML = 
                    'Vykonávam finálnu kontrolu systému<span class="loading-dots"></span>';
                
                // Check if all required globals are available
                const requiredGlobals = [
                    'PMSystemConfig',
                    'PMAPI',
                    'PMEventBus',
                    'PMDataStore',
                    'PMComponentRegistry'
                ];
                
                for (const global of requiredGlobals) {
                    if (typeof window[global] === 'undefined') {
                        throw new Error(`Required global ${global} not found`);
                    }
                }
                
                // Mark all remaining components as loaded
                for (let i = 1; i <= 20; i++) {
                    const icon = document.getElementById(`comp-icon-${i}`);
                    if (icon) {
                        icon.classList.add('loaded');
                    }
                }
                
                await this.delay(500);
                
                const loadTime = Date.now() - this.startTime;
                console.log(`🎯 PM Master System v4 initialized successfully in ${loadTime}ms`);
            }

            async launchMainSystem() {
                document.getElementById('statusText').innerHTML = 
                    'Spúšťam hlavný systém<span class="loading-dots"></span>';
                
                await this.delay(500);
                
                // Hide splash screen
                document.getElementById('splashScreen').classList.add('hidden');
                
                // Load main system after splash screen fades
                setTimeout(() => {
                    window.location.href = 'pm_master_system.html';
                }, 500);
            }

            showError(error) {
                document.getElementById('errorMessage').textContent = 
                    `Chyba: ${error.message || error}`;
                document.getElementById('errorScreen').style.display = 'flex';
                document.getElementById('splashScreen').style.display = 'none';
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            if (window.initializer) {
                window.initializer.showError(event.error);
            }
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            if (window.initializer) {
                window.initializer.showError(event.reason);
            }
        });

        // System info function
        function showSystemInfo() {
            const info = {
                version: '4.0.0',
                buildDate: '2024-06-15',
                components: 20,
                browser: navigator.userAgent,
                screen: `${screen.width}x${screen.height}`,
                memory: navigator.deviceMemory || 'Unknown',
                connection: navigator.connection?.effectiveType || 'Unknown'
            };
            
            alert(`PM Master System v4 - Systémové Informácie\n\n` +
                  `Verzia: ${info.version}\n` +
                  `Build: ${info.buildDate}\n` +
                  `Komponenty: ${info.components}\n` +
                  `Prehliadač: ${info.browser}\n` +
                  `Rozlíšenie: ${info.screen}\n` +
                  `Pamäť: ${info.memory} GB\n` +
                  `Pripojenie: ${info.connection}`);
        }

        // Performance monitoring
        function startPerformanceMonitoring() {
            if ('performance' in window) {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'navigation') {
                            console.log(`📊 Page load time: ${entry.loadEventEnd - entry.loadEventStart}ms`);
                        }
                    }
                });
                
                observer.observe({ entryTypes: ['navigation'] });
            }
        }

        // Initialize system when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 PM Master System v4 - Starting initialization...');
            
            // Start performance monitoring
            startPerformanceMonitoring();
            
            // Create and start initializer
            window.initializer = new PMSystemInitializer();
            
            // Small delay to show splash screen
            setTimeout(() => {
                window.initializer.initialize();
            }, 1000);
        });

        // Prevent context menu and F12 in production
        if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
            document.addEventListener('contextmenu', e => e.preventDefault());
            document.addEventListener('keydown', e => {
                if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                    e.preventDefault();
                }
            });
        }
    </script>
</body>
</html>
