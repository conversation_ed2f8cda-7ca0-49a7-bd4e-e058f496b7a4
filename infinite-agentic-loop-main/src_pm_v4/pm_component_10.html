<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stakeholder Communication Hub - Projektov<PERSON> Man<PERSON>žment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #7c2d12 0%, #dc2626 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .communication-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            min-height: 700px;
        }

        .stakeholder-sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .stakeholder-list {
            margin-bottom: 2rem;
        }

        .stakeholder-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stakeholder-item:hover {
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .stakeholder-item.selected {
            border-color: #dc2626;
            background: #fef2f2;
        }

        .stakeholder-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .stakeholder-name {
            font-weight: 600;
            color: #1f2937;
        }

        .stakeholder-status {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-active { background: #d1fae5; color: #065f46; }
        .status-busy { background: #fef3c7; color: #92400e; }
        .status-offline { background: #f3f4f6; color: #6b7280; }

        .stakeholder-role {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .stakeholder-contact {
            display: flex;
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .contact-method {
            padding: 0.2rem 0.5rem;
            background: #e5e7eb;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .contact-method:hover {
            background: #dc2626;
            color: white;
        }

        .main-communication {
            padding: 2rem;
            display: flex;
            flex-direction: column;
        }

        .communication-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-bottom-color: #dc2626;
            color: #dc2626;
        }

        .tab-content {
            display: none;
            flex: 1;
        }

        .tab-content.active {
            display: flex;
            flex-direction: column;
        }

        .message-composer {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }

        .composer-header {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .composer-field {
            flex: 1;
        }

        .field-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .field-input, .field-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .field-input:focus, .field-select:focus {
            border-color: #dc2626;
        }

        .message-textarea {
            width: 100%;
            min-height: 120px;
            padding: 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            resize: vertical;
            outline: none;
            font-family: inherit;
            margin-bottom: 1rem;
        }

        .message-textarea:focus {
            border-color: #dc2626;
        }

        .composer-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .message-options {
            display: flex;
            gap: 1rem;
        }

        .option-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .send-btn {
            background: #dc2626;
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #b91c1c;
            transform: translateY(-2px);
        }

        .message-history {
            flex: 1;
            overflow-y: auto;
        }

        .message-item {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .message-item:hover {
            border-color: #dc2626;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .message-from {
            font-weight: 600;
            color: #1f2937;
        }

        .message-date {
            font-size: 0.8rem;
            color: #6b7280;
        }

        .message-subject {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .message-preview {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .message-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message-item:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 0.25rem 0.75rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            border-color: #dc2626;
            color: #dc2626;
        }

        .activity-sidebar {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .activity-feed {
            margin-bottom: 2rem;
        }

        .activity-item {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-left: 3px solid #dc2626;
            font-size: 0.9rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .activity-text {
            color: #374151;
        }

        .quick-stats {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 300px 1fr;
            }

            .activity-sidebar {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .pm-content {
                grid-template-columns: 1fr;
            }

            .stakeholder-sidebar {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Stakeholder Communication Hub - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>💬 Stakeholder Communication Hub</h2>
                <div class="communication-status">
                    📡 5 Aktívnych Konverzácií
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="scheduleUpdate()">📅 Naplánovať Update</button>
                    <button class="btn btn-primary" onclick="generateReport()">📊 Status Report</button>
                </div>
            </div>

            <div class="pm-content">
                <div class="stakeholder-sidebar">
                    <div class="sidebar-title">👥 Stakeholderi</div>

                    <div class="stakeholder-list">
                        <div class="stakeholder-item selected" data-stakeholder="ceo">
                            <div class="stakeholder-header">
                                <div class="stakeholder-name">Ing. Martin Novotný</div>
                                <div class="stakeholder-status status-active">Online</div>
                            </div>
                            <div class="stakeholder-role">CEO & Founder</div>
                            <div class="stakeholder-contact">
                                <div class="contact-method" title="Email">📧</div>
                                <div class="contact-method" title="Teams">💬</div>
                                <div class="contact-method" title="Telefón">📞</div>
                            </div>
                        </div>

                        <div class="stakeholder-item" data-stakeholder="cto">
                            <div class="stakeholder-header">
                                <div class="stakeholder-name">Mgr. Jana Krejčová</div>
                                <div class="stakeholder-status status-busy">Zaneprázdnená</div>
                            </div>
                            <div class="stakeholder-role">CTO</div>
                            <div class="stakeholder-contact">
                                <div class="contact-method" title="Email">📧</div>
                                <div class="contact-method" title="Slack">💬</div>
                            </div>
                        </div>

                        <div class="stakeholder-item" data-stakeholder="product-owner">
                            <div class="stakeholder-header">
                                <div class="stakeholder-name">Bc. Tomáš Svoboda</div>
                                <div class="stakeholder-status status-active">Online</div>
                            </div>
                            <div class="stakeholder-role">Product Owner</div>
                            <div class="stakeholder-contact">
                                <div class="contact-method" title="Email">📧</div>
                                <div class="contact-method" title="Jira">📋</div>
                                <div class="contact-method" title="Teams">💬</div>
                            </div>
                        </div>

                        <div class="stakeholder-item" data-stakeholder="client">
                            <div class="stakeholder-header">
                                <div class="stakeholder-name">PhDr. Eva Marková</div>
                                <div class="stakeholder-status status-offline">Offline</div>
                            </div>
                            <div class="stakeholder-role">Client Representative</div>
                            <div class="stakeholder-contact">
                                <div class="contact-method" title="Email">📧</div>
                                <div class="contact-method" title="Telefón">📞</div>
                            </div>
                        </div>

                        <div class="stakeholder-item" data-stakeholder="sponsor">
                            <div class="stakeholder-header">
                                <div class="stakeholder-name">Ing. Pavel Dvořák</div>
                                <div class="stakeholder-status status-active">Online</div>
                            </div>
                            <div class="stakeholder-role">Project Sponsor</div>
                            <div class="stakeholder-contact">
                                <div class="contact-method" title="Email">📧</div>
                                <div class="contact-method" title="Telefón">📞</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-title">📋 Komunikačné Kanály</div>
                    <div style="background: white; border-radius: 8px; padding: 1rem; border: 1px solid #e5e7eb;">
                        <div style="font-weight: 500; margin-bottom: 0.5rem;">Aktívne Kanály:</div>
                        <div style="font-size: 0.9rem; color: #6b7280; line-height: 1.4;">
                            • Email Updates (Týždenne)<br>
                            • Teams Meetings (Bi-weekly)<br>
                            • Status Dashboard (24/7)<br>
                            • Emergency Hotline
                        </div>
                    </div>
                </div>

                <div class="main-communication">
                    <div class="communication-tabs">
                        <button class="tab-btn active" data-tab="compose">✍️ Nová Správa</button>
                        <button class="tab-btn" data-tab="history">📬 História</button>
                        <button class="tab-btn" data-tab="templates">📄 Šablóny</button>
                        <button class="tab-btn" data-tab="analytics">📊 Analytika</button>
                    </div>

                    <!-- Compose Tab -->
                    <div class="tab-content active" id="compose">
                        <div class="message-composer">
                            <div class="composer-header">
                                <div class="composer-field">
                                    <div class="field-label">Príjemca</div>
                                    <select class="field-select" id="recipient">
                                        <option value="ceo">Ing. Martin Novotný (CEO)</option>
                                        <option value="cto">Mgr. Jana Krejčová (CTO)</option>
                                        <option value="product-owner">Bc. Tomáš Svoboda (PO)</option>
                                        <option value="client">PhDr. Eva Marková (Client)</option>
                                        <option value="all">Všetci stakeholderi</option>
                                    </select>
                                </div>
                                <div class="composer-field">
                                    <div class="field-label">Typ Správy</div>
                                    <select class="field-select" id="messageType">
                                        <option value="status">Status Update</option>
                                        <option value="milestone">Milestone Report</option>
                                        <option value="issue">Issue Alert</option>
                                        <option value="request">Request for Approval</option>
                                        <option value="meeting">Meeting Invitation</option>
                                    </select>
                                </div>
                                <div class="composer-field">
                                    <div class="field-label">Priorita</div>
                                    <select class="field-select" id="priority">
                                        <option value="low">Nízka</option>
                                        <option value="normal" selected>Normálna</option>
                                        <option value="high">Vysoká</option>
                                        <option value="urgent">Urgentná</option>
                                    </select>
                                </div>
                            </div>

                            <div class="composer-field">
                                <div class="field-label">Predmet</div>
                                <input type="text" class="field-input" id="subject" placeholder="E-shop Modernizácia - Týždenný Status Update">
                            </div>

                            <div class="composer-field" style="margin-top: 1rem;">
                                <div class="field-label">Správa</div>
                                <textarea class="message-textarea" id="messageContent" placeholder="Vážený/á [Meno],

Rád by som Vás informoval o aktuálnom stave projektu E-shop Modernizácia:

📊 CELKOVÝ POKROK: 65% dokončené

✅ DOKONČENÉ ÚLOHY:
• Analýza požiadaviek (100%)
• UI/UX dizajn (100%)
• Backend API development (80%)

🔄 AKTUÁLNE PRÁCE:
• Frontend development (60%)
• Database optimization (45%)
• Third-party integrations (30%)

📅 MÍĽNIKY:
• Beta verzia: 30.06.2024 (na trase)
• User testing: 15.07.2024 (na trase)
• Production launch: 31.08.2024 (na trase)

⚠️ RIZIKÁ A PROBLÉMY:
• API integrácia s externým dodávateľom môže mať 1-týždňové omeškanie
• Potrebujeme schválenie finálneho dizajnu do 20.06.2024

💰 ROZPOČET:
• Spotrebované: €45,200 z €50,000 (90.4%)
• Zostáva: €4,800

📞 ĎALŠIE KROKY:
• Týždenný review meeting - Piatok 14:00
• Demo session pre stakeholderov - 25.06.2024

S pozdravom,
Ján Novák
Project Manager"></textarea>
                            </div>

                            <div class="composer-actions">
                                <div class="message-options">
                                    <label class="option-checkbox">
                                        <input type="checkbox" id="requestResponse">
                                        <span>Požiadať o odpoveď</span>
                                    </label>
                                    <label class="option-checkbox">
                                        <input type="checkbox" id="trackReading">
                                        <span>Sledovať prečítanie</span>
                                    </label>
                                    <label class="option-checkbox">
                                        <input type="checkbox" id="scheduleDelivery">
                                        <span>Naplánovať odoslanie</span>
                                    </label>
                                </div>
                                <button class="send-btn" onclick="sendMessage()">📤 Odoslať Správu</button>
                            </div>
                        </div>
                    </div>

                    <!-- History Tab -->
                    <div class="tab-content" id="history">
                        <div class="message-history">
                            <div class="message-item">
                                <div class="message-header">
                                    <div class="message-from">Ing. Martin Novotný (CEO)</div>
                                    <div class="message-date">Dnes 10:30</div>
                                </div>
                                <div class="message-subject">Re: E-shop Modernizácia - Týždenný Status</div>
                                <div class="message-preview">
                                    Ďakujem za update. Môžeme si dohodnúť krátky call ohľadom API integrácie? Chcel by som pochopiť potenciálne riziká...
                                </div>
                                <div class="message-actions">
                                    <button class="action-btn">Odpovedať</button>
                                    <button class="action-btn">Preposlať</button>
                                    <button class="action-btn">Archivovať</button>
                                </div>
                            </div>

                            <div class="message-item">
                                <div class="message-header">
                                    <div class="message-from">Bc. Tomáš Svoboda (PO)</div>
                                    <div class="message-date">Včera 16:45</div>
                                </div>
                                <div class="message-subject">Schválenie UI Changes</div>
                                <div class="message-preview">
                                    Ahoj Ján, pozrel som si najnovšie UI mockupy. Celkovo vyzerajú skvele, mám len pár drobných pripomienok k checkout procesu...
                                </div>
                                <div class="message-actions">
                                    <button class="action-btn">Odpovedať</button>
                                    <button class="action-btn">Preposlať</button>
                                    <button class="action-btn">Archivovať</button>
                                </div>
                            </div>

                            <div class="message-item">
                                <div class="message-header">
                                    <div class="message-from">PhDr. Eva Marková (Client)</div>
                                    <div class="message-date">Pred 2 dňami</div>
                                </div>
                                <div class="message-subject">Demo Session Feedback</div>
                                <div class="message-preview">
                                    Dobrý deň, chcela by som sa poďakovať za včerajšiu demo session. Tím odvádzal skvelú prácu. Máme však niekoľko požiadaviek na zmeny...
                                </div>
                                <div class="message-actions">
                                    <button class="action-btn">Odpovedať</button>
                                    <button class="action-btn">Preposlať</button>
                                    <button class="action-btn">Archivovať</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Tab -->
                    <div class="tab-content" id="templates">
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <h3>📄 Komunikačné Šablóny</h3>
                            <p style="margin: 1rem 0;">Vyberte šablónu pre rýchle vytvorenie správy:</p>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;">
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="loadTemplate('status')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                                    <div style="font-weight: 600;">Status Update</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="loadTemplate('milestone')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎯</div>
                                    <div style="font-weight: 600;">Milestone Report</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="loadTemplate('issue')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">⚠️</div>
                                    <div style="font-weight: 600;">Issue Alert</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; cursor: pointer;" onclick="loadTemplate('meeting')">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📅</div>
                                    <div style="font-weight: 600;">Meeting Invite</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div class="tab-content" id="analytics">
                        <div style="padding: 2rem;">
                            <h3 style="margin-bottom: 2rem;">📊 Komunikačná Analytika</h3>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #dc2626;">156</div>
                                    <div style="font-size: 0.9rem; color: #6b7280;">Odoslané Správy</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #dc2626;">94%</div>
                                    <div style="font-size: 0.9rem; color: #6b7280;">Response Rate</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #dc2626;">2.3h</div>
                                    <div style="font-size: 0.9rem; color: #6b7280;">Avg Response Time</div>
                                </div>
                                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #dc2626;">87%</div>
                                    <div style="font-size: 0.9rem; color: #6b7280;">Satisfaction Score</div>
                                </div>
                            </div>

                            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                                <h4 style="margin-bottom: 1rem;">📈 Komunikačné Trendy</h4>
                                <div style="color: #6b7280; line-height: 1.6;">
                                    • Najaktívnejší stakeholder: Ing. Martin Novotný (CEO)<br>
                                    • Najčastejší typ správy: Status Updates (45%)<br>
                                    • Peak komunikačný čas: Utorok 10:00-12:00<br>
                                    • Priemerná dĺžka správy: 247 slov<br>
                                    • Najrýchlejšia odpoveď: Bc. Tomáš Svoboda (23 min)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="activity-sidebar">
                    <div class="sidebar-title">🔔 Nedávna Aktivita</div>

                    <div class="activity-feed">
                        <div class="activity-item">
                            <div class="activity-time">Pred 15 min</div>
                            <div class="activity-text">Ing. Martin Novotný prečítal status update</div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-time">Pred 1 hodinou</div>
                            <div class="activity-text">Odoslaný týždenný report všetkým stakeholderom</div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-time">Pred 2 hodinami</div>
                            <div class="activity-text">Bc. Tomáš Svoboda schválil UI changes</div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-time">Včera</div>
                            <div class="activity-text">Naplánovaný meeting s PhDr. Eva Marková</div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-time">Pred 2 dňami</div>
                            <div class="activity-text">Demo session feedback od klienta</div>
                        </div>
                    </div>

                    <div class="sidebar-title">📊 Rýchle Štatistiky</div>

                    <div class="quick-stats">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">5</div>
                                <div class="stat-label">Aktívni Stakeholderi</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">12</div>
                                <div class="stat-label">Správy Tento Týždeň</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">3</div>
                                <div class="stat-label">Čakajúce Odpovede</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">1</div>
                                <div class="stat-label">Naplánované Meetingy</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedStakeholder = 'ceo';

        function initPMComponent() {
            setupEventListeners();
            updateStakeholderInfo();
        }

        function setupEventListeners() {
            // Stakeholder selection
            document.querySelectorAll('.stakeholder-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.stakeholder-item').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedStakeholder = this.dataset.stakeholder;
                    updateStakeholderInfo();
                });
            });

            // Tab switching
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                    this.classList.add('active');
                    document.getElementById(this.dataset.tab).classList.add('active');
                });
            });

            // Contact method clicks
            document.querySelectorAll('.contact-method').forEach(method => {
                method.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const type = this.getAttribute('title');
                    alert(`Otváram ${type} komunikáciu so stakeholderom...`);
                });
            });
        }

        function updateStakeholderInfo() {
            console.log('Aktualizujem info pre stakeholdera:', selectedStakeholder);
            // V produkčnej verzii by sa tu načítali špecifické informácie o stakeholderovi
        }

        function sendMessage() {
            const recipient = document.getElementById('recipient').value;
            const messageType = document.getElementById('messageType').value;
            const priority = document.getElementById('priority').value;
            const subject = document.getElementById('subject').value;
            const content = document.getElementById('messageContent').value;

            if (!subject || !content) {
                alert('Prosím vyplňte predmet a obsah správy.');
                return;
            }

            alert(`📤 Správa odoslaná!\n\nPríjemca: ${recipient}\nTyp: ${messageType}\nPriorita: ${priority}\n\n✅ Správa bola úspešne doručená\n📊 Tracking aktivovaný\n🔔 Notifikácia odoslaná`);

            // Vyčistenie formulára
            document.getElementById('subject').value = '';
            document.getElementById('messageContent').value = '';
        }

        function scheduleUpdate() {
            alert('📅 Plánovanie automatických updates...\n\n• Týždenný status report: Každý piatok 16:00\n• Milestone notifications: Pri dokončení\n• Risk alerts: Okamžite pri detekcii\n• Budget updates: Každý mesiac\n\nAutomatické updates nastavené!');
        }

        function generateReport() {
            alert('📊 Generujem Stakeholder Communication Report...\n\n• Komunikačná aktivita: ✓\n• Response rates: ✓\n• Satisfaction metrics: ✓\n• Engagement analysis: ✓\n\nReport obsahuje:\n✓ Executive summary\n✓ Communication timeline\n✓ Stakeholder engagement metrics\n✓ Recommendations\n\nReport exportovaný do PDF!');
        }

        function loadTemplate(type) {
            const templates = {
                'status': {
                    subject: 'E-shop Modernizácia - Týždenný Status Update',
                    content: 'Vážený/á [Meno],\n\nRád by som Vás informoval o aktuálnom stave projektu...'
                },
                'milestone': {
                    subject: 'Míľnik Dokončený - [Názov Míľnika]',
                    content: 'Dobrý deň,\n\ns radosťou Vám oznamujem dokončenie dôležitého míľnika...'
                },
                'issue': {
                    subject: 'URGENT: Problém Vyžaduje Pozornosť',
                    content: 'Vážený/á [Meno],\n\nidentifikovali sme problém, ktorý vyžaduje Vašu okamžitú pozornosť...'
                },
                'meeting': {
                    subject: 'Pozvánka na Meeting - [Téma]',
                    content: 'Dobrý deň,\n\nrád by som Vás pozval na meeting ohľadom...'
                }
            };

            if (templates[type]) {
                document.getElementById('subject').value = templates[type].subject;
                document.getElementById('messageContent').value = templates[type].content;

                // Prepnúť na compose tab
                document.querySelector('.tab-btn[data-tab="compose"]').click();

                alert(`✅ Šablóna "${type}" načítaná! Môžete upraviť obsah podľa potreby.`);
            }
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>