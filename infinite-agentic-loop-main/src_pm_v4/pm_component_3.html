<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Health Dashboard - Projektový Man<PERSON>žment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .refresh-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .pm-content {
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
        }

        .dashboard-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .health-overview {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .health-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .health-card:hover {
            transform: translateY(-2px);
        }

        .health-card.excellent { border-left-color: #059669; }
        .health-card.good { border-left-color: #0ea5e9; }
        .health-card.warning { border-left-color: #f59e0b; }
        .health-card.critical { border-left-color: #dc2626; }

        .health-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-weight: 500;
            color: #374151;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .metric-trend {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .trend-up { color: #059669; }
        .trend-down { color: #dc2626; }
        .trend-stable { color: #6b7280; }

        .progress-ring {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto;
        }

        .progress-ring svg {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            fill: none;
            stroke-width: 8;
        }

        .progress-ring-bg {
            stroke: #e5e7eb;
        }

        .progress-ring-progress {
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }

        .progress-ring-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2rem;
            font-weight: 700;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .kpi-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .kpi-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .kpi-label {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .risk-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .risk-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-left: 4px solid;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .risk-item.high { border-left-color: #dc2626; }
        .risk-item.medium { border-left-color: #f59e0b; }
        .risk-item.low { border-left-color: #059669; }

        .risk-content {
            flex: 1;
        }

        .risk-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .risk-description {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .risk-impact {
            font-size: 0.8rem;
            color: #374151;
        }

        .risk-probability {
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            white-space: nowrap;
        }

        .team-performance {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .team-member {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin: 0 auto 0.5rem;
        }

        .member-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .member-role {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .member-workload {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .workload-high { color: #dc2626; }
        .workload-medium { color: #f59e0b; }
        .workload-low { color: #059669; }

        .alert-banner {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-icon {
            color: #dc2626;
            font-size: 1.2rem;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 600;
            color: #991b1b;
            margin-bottom: 0.25rem;
        }

        .alert-message {
            color: #7f1d1d;
            font-size: 0.9rem;
        }

        @media (max-width: 1024px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
            
            .health-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Project Health Dashboard - Projektový Manažment</h1>
        
        <div class="pm-component">
            <div class="pm-header">
                <h2>🏥 Project Health Dashboard</h2>
                <div class="pm-actions">
                    <div class="refresh-indicator">
                        <span>🔄</span>
                        <span>Posledná aktualizácia: 14:32</span>
                    </div>
                    <button class="btn btn-primary" onclick="generateReport()">📊 Generovať Report</button>
                    <button class="btn btn-primary" onclick="configureAlerts()">🔔 Nastaviť Upozornenia</button>
                </div>
            </div>
            
            <div class="pm-content">
                <!-- Celkový prehľad zdravia -->
                <div class="dashboard-section health-overview">
                    <div class="health-card excellent">
                        <div class="health-metric">
                            <span class="metric-label">Celkové Zdravie Projektu</span>
                            <div class="progress-ring">
                                <svg width="80" height="80">
                                    <circle class="progress-ring-circle progress-ring-bg" cx="40" cy="40" r="32"></circle>
                                    <circle class="progress-ring-circle progress-ring-progress" cx="40" cy="40" r="32" 
                                            stroke="#059669" stroke-dasharray="150 201"></circle>
                                </svg>
                                <div class="progress-ring-text" style="color: #059669;">78%</div>
                            </div>
                        </div>
                        <div class="metric-trend trend-up">
                            ↗ +5% za posledný týždeň
                        </div>
                    </div>
                    
                    <div class="health-card warning">
                        <div class="health-metric">
                            <span class="metric-label">Dodržanie Harmonogramu</span>
                            <div class="progress-ring">
                                <svg width="80" height="80">
                                    <circle class="progress-ring-circle progress-ring-bg" cx="40" cy="40" r="32"></circle>
                                    <circle class="progress-ring-circle progress-ring-progress" cx="40" cy="40" r="32" 
                                            stroke="#f59e0b" stroke-dasharray="120 201"></circle>
                                </svg>
                                <div class="progress-ring-text" style="color: #f59e0b;">65%</div>
                            </div>
                        </div>
                        <div class="metric-trend trend-down">
                            ↘ -8% za posledný týždeň
                        </div>
                    </div>
                    
                    <div class="health-card good">
                        <div class="health-metric">
                            <span class="metric-label">Kvalita Deliverables</span>
                            <div class="progress-ring">
                                <svg width="80" height="80">
                                    <circle class="progress-ring-circle progress-ring-bg" cx="40" cy="40" r="32"></circle>
                                    <circle class="progress-ring-circle progress-ring-progress" cx="40" cy="40" r="32" 
                                            stroke="#0ea5e9" stroke-dasharray="170 201"></circle>
                                </svg>
                                <div class="progress-ring-text" style="color: #0ea5e9;">85%</div>
                            </div>
                        </div>
                        <div class="metric-trend trend-up">
                            ↗ +3% za posledný týždeň
                        </div>
                    </div>
                    
                    <div class="health-card critical">
                        <div class="health-metric">
                            <span class="metric-label">Využitie Rozpočtu</span>
                            <div class="progress-ring">
                                <svg width="80" height="80">
                                    <circle class="progress-ring-circle progress-ring-bg" cx="40" cy="40" r="32"></circle>
                                    <circle class="progress-ring-circle progress-ring-progress" cx="40" cy="40" r="32" 
                                            stroke="#dc2626" stroke-dasharray="185 201"></circle>
                                </svg>
                                <div class="progress-ring-text" style="color: #dc2626;">92%</div>
                            </div>
                        </div>
                        <div class="metric-trend trend-up">
                            ↗ +12% za posledný týždeň
                        </div>
                    </div>
                </div>

                <!-- KPI Metriky -->
                <div class="dashboard-section">
                    <div class="section-title">📈 Kľúčové Metriky</div>
                    <div class="kpi-grid">
                        <div class="kpi-item">
                            <div class="kpi-value" style="color: #059669;">24</div>
                            <div class="kpi-label">Dokončené Úlohy</div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-value" style="color: #f59e0b;">8</div>
                            <div class="kpi-label">Úlohy v Priebehu</div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-value" style="color: #dc2626;">3</div>
                            <div class="kpi-label">Omeškané Úlohy</div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-value" style="color: #6b7280;">12</div>
                            <div class="kpi-label">Zostávajúce Úlohy</div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-value" style="color: #4f46e5;">€45,200</div>
                            <div class="kpi-label">Spotrebovaný Rozpočet</div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-value" style="color: #7c3aed;">€4,800</div>
                            <div class="kpi-label">Zostávajúci Rozpočet</div>
                        </div>
                    </div>
                </div>

                <!-- Riziká a Problémy -->
                <div class="dashboard-section">
                    <div class="section-title">⚠️ Aktívne Riziká</div>
                    
                    <div class="alert-banner">
                        <div class="alert-icon">🚨</div>
                        <div class="alert-content">
                            <div class="alert-title">Kritické Upozornenie</div>
                            <div class="alert-message">Rozpočet prekročený o 8% - vyžaduje okamžitú pozornosť</div>
                        </div>
                    </div>
                    
                    <div class="risk-list">
                        <div class="risk-item high">
                            <div class="risk-content">
                                <div class="risk-title">Omeškanie API Integrácie</div>
                                <div class="risk-description">Externý dodávateľ hlási 2-týždňové omeškanie</div>
                                <div class="risk-impact">Dopad: Celkové omeškanie projektu o 10 dní</div>
                            </div>
                            <div class="risk-probability" style="background: #fee2e2; color: #991b1b;">85%</div>
                        </div>
                        
                        <div class="risk-item medium">
                            <div class="risk-content">
                                <div class="risk-title">Nedostatok QA Zdrojov</div>
                                <div class="risk-description">Tester Anna Horváthová na dovolenke 2 týždne</div>
                                <div class="risk-impact">Dopad: Možné omeškanie testovacej fázy</div>
                            </div>
                            <div class="risk-probability" style="background: #fef3c7; color: #92400e;">60%</div>
                        </div>
                        
                        <div class="risk-item low">
                            <div class="risk-content">
                                <div class="risk-title">Zmena Požiadaviek</div>
                                <div class="risk-description">Klient zvažuje pridanie nových funkcií</div>
                                <div class="risk-impact">Dopad: Možné navýšenie scope a rozpočtu</div>
                            </div>
                            <div class="risk-probability" style="background: #d1fae5; color: #065f46;">30%</div>
                        </div>
                    </div>
                </div>

                <!-- Výkonnosť Tímu -->
                <div class="dashboard-section">
                    <div class="section-title">👥 Výkonnosť Tímu</div>
                    <div class="team-performance">
                        <div class="team-member">
                            <div class="member-avatar">JN</div>
                            <div class="member-name">Ján Novák</div>
                            <div class="member-role">Project Manager</div>
                            <div class="member-workload workload-medium">Zaťaženie: 85%</div>
                        </div>
                        
                        <div class="team-member">
                            <div class="member-avatar">MS</div>
                            <div class="member-name">Mária Svobodová</div>
                            <div class="member-role">UI/UX Designer</div>
                            <div class="member-workload workload-low">Zaťaženie: 65%</div>
                        </div>
                        
                        <div class="team-member">
                            <div class="member-avatar">PK</div>
                            <div class="member-name">Peter Kováč</div>
                            <div class="member-role">Backend Developer</div>
                            <div class="member-workload workload-high">Zaťaženie: 110%</div>
                        </div>
                        
                        <div class="team-member">
                            <div class="member-avatar">AH</div>
                            <div class="member-name">Anna Horváthová</div>
                            <div class="member-role">QA Tester</div>
                            <div class="member-workload workload-low">Zaťaženie: 45%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            updateDashboard();
            startRealTimeUpdates();
        }

        function updateDashboard() {
            // Simulácia real-time aktualizácie dát
            console.log('Dashboard aktualizovaný:', new Date().toLocaleTimeString());
        }

        function startRealTimeUpdates() {
            // Simulácia real-time aktualizácií každých 30 sekúnd
            setInterval(() => {
                updateDashboard();
                updateRefreshIndicator();
            }, 30000);
        }

        function updateRefreshIndicator() {
            const indicator = document.querySelector('.refresh-indicator span:last-child');
            const now = new Date();
            indicator.textContent = `Posledná aktualizácia: ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
        }

        function generateReport() {
            alert('📊 Generovanie Project Health Reportu:\n\n• Celkové zdravie projektu: 78%\n• Identifikované riziká: 3\n• Odporúčania: 5\n• Export do PDF/Excel\n\nReport bude odoslaný na email do 5 minút.');
        }

        function configureAlerts() {
            alert('🔔 Konfigurácia Upozornení:\n\n• Rozpočet > 90%: Email + SMS\n• Omeškanie úloh > 2 dni: Email\n• Nové vysoké riziko: Okamžité upozornenie\n• Týždenný súhrn: Každý piatok\n\nNastavenia uložené.');
        }

        // Simulácia dynamických zmien v dashboarde
        function simulateDataChanges() {
            // Náhodné aktualizácie metrík pre demo účely
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                if (Math.random() < 0.1) { // 10% šanca na zmenu
                    const currentValue = parseInt(value.textContent);
                    if (!isNaN(currentValue)) {
                        const change = Math.floor(Math.random() * 3) - 1; // -1, 0, alebo 1
                        value.textContent = Math.max(0, currentValue + change);
                    }
                }
            });
        }

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
        
        // Simulácia zmien každých 10 sekúnd pre demo
        setInterval(simulateDataChanges, 10000);
    </script>
</body>
</html>
