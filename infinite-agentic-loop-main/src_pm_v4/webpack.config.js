/**
 * PM Master System v4 - Webpack Configuration
 * Production build configuration s optimalizáciami
 */

const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
    mode: isProduction ? 'production' : 'development',
    
    entry: {
        main: './index.html',
        system: './pm_master_system.html',
        config: './pm_system_config.js',
        api: './pm_api_layer.js',
        bridge: './pm_component_bridge.js',
        data: './data/pm_data_manager.js',
        ai: './ai/ai_mock_engine.js'
    },
    
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: isProduction ? '[name].[contenthash].js' : '[name].js',
        chunkFilename: isProduction ? '[name].[contenthash].chunk.js' : '[name].chunk.js',
        clean: true,
        publicPath: '/'
    },
    
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env'],
                        cacheDirectory: true
                    }
                }
            },
            {
                test: /\.css$/,
                use: [
                    isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
                    'css-loader'
                ]
            },
            {
                test: /\.html$/,
                use: [
                    {
                        loader: 'html-loader',
                        options: {
                            minimize: isProduction,
                            sources: {
                                list: [
                                    {
                                        tag: 'script',
                                        attribute: 'src',
                                        type: 'src'
                                    },
                                    {
                                        tag: 'link',
                                        attribute: 'href',
                                        type: 'src'
                                    }
                                ]
                            }
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpe?g|gif|svg|ico)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/images/[name].[hash][ext]'
                }
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/fonts/[name].[hash][ext]'
                }
            }
        ]
    },
    
    plugins: [
        // HTML plugins for each page
        new HtmlWebpackPlugin({
            template: './index.html',
            filename: 'index.html',
            chunks: ['main', 'config', 'api', 'bridge', 'data'],
            minify: isProduction ? {
                removeComments: true,
                collapseWhitespace: true,
                removeRedundantAttributes: true,
                useShortDoctype: true,
                removeEmptyAttributes: true,
                removeStyleLinkTypeAttributes: true,
                keepClosingSlash: true,
                minifyJS: true,
                minifyCSS: true,
                minifyURLs: true
            } : false
        }),
        
        new HtmlWebpackPlugin({
            template: './pm_master_system.html',
            filename: 'pm_master_system.html',
            chunks: ['system', 'config', 'api', 'bridge'],
            minify: isProduction
        }),
        
        // Generate HTML for all 20 components
        ...Array.from({ length: 20 }, (_, i) => {
            const componentNum = i + 1;
            return new HtmlWebpackPlugin({
                template: `./pm_component_${componentNum}.html`,
                filename: `pm_component_${componentNum}.html`,
                chunks: ['config', 'api', 'bridge', 'data', 'ai'],
                minify: isProduction
            });
        }),
        
        // CSS extraction for production
        ...(isProduction ? [
            new MiniCssExtractPlugin({
                filename: '[name].[contenthash].css',
                chunkFilename: '[name].[contenthash].chunk.css'
            })
        ] : []),
        
        // Compression for production
        ...(isProduction ? [
            new CompressionPlugin({
                algorithm: 'gzip',
                test: /\.(js|css|html|svg)$/,
                threshold: 8192,
                minRatio: 0.8
            })
        ] : [])
    ],
    
    optimization: {
        minimize: isProduction,
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    compress: {
                        drop_console: isProduction,
                        drop_debugger: isProduction
                    },
                    format: {
                        comments: false
                    }
                },
                extractComments: false
            }),
            new CssMinimizerPlugin()
        ],
        
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    priority: 5,
                    reuseExistingChunk: true
                },
                components: {
                    test: /pm_component_\d+\.html$/,
                    name: 'components',
                    chunks: 'all',
                    priority: 3
                }
            }
        },
        
        runtimeChunk: {
            name: 'runtime'
        }
    },
    
    resolve: {
        extensions: ['.js', '.html', '.css'],
        alias: {
            '@': path.resolve(__dirname, './'),
            '@components': path.resolve(__dirname, './'),
            '@data': path.resolve(__dirname, './data'),
            '@ai': path.resolve(__dirname, './ai'),
            '@backend': path.resolve(__dirname, './backend')
        }
    },
    
    devServer: {
        static: {
            directory: path.join(__dirname, './'),
        },
        port: 8000,
        hot: true,
        open: true,
        historyApiFallback: true,
        compress: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
            'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
        },
        proxy: {
            '/api': {
                target: 'http://localhost:3001',
                changeOrigin: true,
                secure: false
            },
            '/ws': {
                target: 'ws://localhost:3001',
                ws: true,
                changeOrigin: true
            }
        }
    },
    
    performance: {
        hints: isProduction ? 'warning' : false,
        maxEntrypointSize: 512000,
        maxAssetSize: 512000
    },
    
    stats: {
        colors: true,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false
    },
    
    cache: {
        type: 'filesystem',
        buildDependencies: {
            config: [__filename]
        }
    }
};
