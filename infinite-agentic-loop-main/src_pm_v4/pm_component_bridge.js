/**
 * PM Master System v4 - Component Communication Bridge
 * Inter-component komunikácia a data sharing medzi všetkými 20 komponentmi
 */

// Event Bus for component communication
class PMEventBus {
    constructor() {
        this.events = new Map();
        this.middlewares = [];
    }

    // Subscribe to events
    on(event, callback, options = {}) {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        
        const subscription = {
            callback,
            once: options.once || false,
            priority: options.priority || 0,
            component: options.component || 'unknown',
            id: Date.now() + Math.random()
        };
        
        this.events.get(event).push(subscription);
        
        // Sort by priority (higher first)
        this.events.get(event).sort((a, b) => b.priority - a.priority);
        
        return subscription.id;
    }

    // Subscribe once
    once(event, callback, options = {}) {
        return this.on(event, callback, { ...options, once: true });
    }

    // Unsubscribe from events
    off(event, subscriptionId) {
        if (this.events.has(event)) {
            const subscriptions = this.events.get(event);
            const index = subscriptions.findIndex(sub => sub.id === subscriptionId);
            if (index > -1) {
                subscriptions.splice(index, 1);
            }
        }
    }

    // Emit events
    emit(event, data = {}, options = {}) {
        const eventData = {
            type: event,
            data,
            timestamp: new Date(),
            source: options.source || 'system',
            ...options
        };

        // Apply middlewares
        for (const middleware of this.middlewares) {
            try {
                const result = middleware(eventData);
                if (result === false) {
                    console.log(`Event ${event} blocked by middleware`);
                    return false;
                }
            } catch (error) {
                console.error('Middleware error:', error);
            }
        }

        if (this.events.has(event)) {
            const subscriptions = [...this.events.get(event)];
            
            subscriptions.forEach(subscription => {
                try {
                    subscription.callback(eventData);
                    
                    // Remove if once
                    if (subscription.once) {
                        this.off(event, subscription.id);
                    }
                } catch (error) {
                    console.error(`Event handler error for ${event}:`, error);
                }
            });
        }

        // Log event for debugging
        if (PMSystemConfig.settings.debug) {
            console.log(`📡 Event emitted: ${event}`, eventData);
        }

        return true;
    }

    // Add middleware
    use(middleware) {
        this.middlewares.push(middleware);
    }

    // Get all events for debugging
    getEvents() {
        return Array.from(this.events.keys());
    }

    // Clear all subscriptions
    clear() {
        this.events.clear();
    }
}

// Data Store for shared state
class PMDataStore {
    constructor() {
        this.store = new Map();
        this.watchers = new Map();
        this.history = new Map();
        this.maxHistorySize = 100;
    }

    // Set data
    set(key, value, options = {}) {
        const oldValue = this.store.get(key);
        
        // Store in history
        if (!this.history.has(key)) {
            this.history.set(key, []);
        }
        
        const history = this.history.get(key);
        history.push({
            value: oldValue,
            timestamp: new Date(),
            action: 'set'
        });
        
        // Limit history size
        if (history.length > this.maxHistorySize) {
            history.shift();
        }
        
        // Set new value
        this.store.set(key, value);
        
        // Notify watchers
        if (this.watchers.has(key)) {
            this.watchers.get(key).forEach(watcher => {
                try {
                    watcher.callback(value, oldValue, key);
                } catch (error) {
                    console.error(`Watcher error for ${key}:`, error);
                }
            });
        }
        
        // Emit change event
        PMEventBus.emit('dataStore.change', {
            key,
            value,
            oldValue,
            source: options.source || 'unknown'
        });
        
        return this;
    }

    // Get data
    get(key, defaultValue = null) {
        return this.store.has(key) ? this.store.get(key) : defaultValue;
    }

    // Check if key exists
    has(key) {
        return this.store.has(key);
    }

    // Delete data
    delete(key) {
        const value = this.store.get(key);
        const deleted = this.store.delete(key);
        
        if (deleted) {
            // Add to history
            if (!this.history.has(key)) {
                this.history.set(key, []);
            }
            
            this.history.get(key).push({
                value,
                timestamp: new Date(),
                action: 'delete'
            });
            
            // Emit change event
            PMEventBus.emit('dataStore.change', {
                key,
                value: undefined,
                oldValue: value,
                action: 'delete'
            });
        }
        
        return deleted;
    }

    // Watch for changes
    watch(key, callback, options = {}) {
        if (!this.watchers.has(key)) {
            this.watchers.set(key, []);
        }
        
        const watcher = {
            callback,
            immediate: options.immediate || false,
            component: options.component || 'unknown',
            id: Date.now() + Math.random()
        };
        
        this.watchers.get(key).push(watcher);
        
        // Call immediately if requested
        if (watcher.immediate && this.has(key)) {
            callback(this.get(key), undefined, key);
        }
        
        return watcher.id;
    }

    // Unwatch
    unwatch(key, watcherId) {
        if (this.watchers.has(key)) {
            const watchers = this.watchers.get(key);
            const index = watchers.findIndex(w => w.id === watcherId);
            if (index > -1) {
                watchers.splice(index, 1);
            }
        }
    }

    // Get history
    getHistory(key) {
        return this.history.get(key) || [];
    }

    // Clear all data
    clear() {
        this.store.clear();
        this.watchers.clear();
        this.history.clear();
    }

    // Get all keys
    keys() {
        return Array.from(this.store.keys());
    }

    // Get store size
    size() {
        return this.store.size;
    }
}

// Component Registry
class PMComponentRegistry {
    constructor() {
        this.components = new Map();
        this.instances = new Map();
        this.dependencies = new Map();
    }

    // Register component
    register(componentId, componentInfo) {
        this.components.set(componentId, {
            ...componentInfo,
            registeredAt: new Date(),
            status: 'registered'
        });
        
        // Register dependencies
        if (componentInfo.dependencies) {
            this.dependencies.set(componentId, componentInfo.dependencies);
        }
        
        PMEventBus.emit('component.registered', { componentId, componentInfo });
        
        console.log(`📦 Component registered: ${componentId} - ${componentInfo.name}`);
    }

    // Unregister component
    unregister(componentId) {
        const component = this.components.get(componentId);
        if (component) {
            this.components.delete(componentId);
            this.instances.delete(componentId);
            this.dependencies.delete(componentId);
            
            PMEventBus.emit('component.unregistered', { componentId, component });
            
            console.log(`📦 Component unregistered: ${componentId}`);
        }
    }

    // Get component info
    get(componentId) {
        return this.components.get(componentId);
    }

    // Check if component is registered
    has(componentId) {
        return this.components.has(componentId);
    }

    // Get all components
    getAll() {
        return Array.from(this.components.entries());
    }

    // Set component instance
    setInstance(componentId, instance) {
        this.instances.set(componentId, instance);
        
        if (this.components.has(componentId)) {
            const component = this.components.get(componentId);
            component.status = 'active';
            component.lastActive = new Date();
        }
        
        PMEventBus.emit('component.activated', { componentId, instance });
    }

    // Get component instance
    getInstance(componentId) {
        return this.instances.get(componentId);
    }

    // Get dependencies
    getDependencies(componentId) {
        return this.dependencies.get(componentId) || [];
    }

    // Check if all dependencies are loaded
    areDependenciesLoaded(componentId) {
        const deps = this.getDependencies(componentId);
        return deps.every(depId => this.instances.has(depId));
    }
}

// Component Communication Manager
class PMComponentCommunication {
    constructor() {
        this.messageQueue = [];
        this.processingQueue = false;
    }

    // Send message between components
    sendMessage(fromComponent, toComponent, message, data = {}) {
        const messageObj = {
            id: Date.now() + Math.random(),
            from: fromComponent,
            to: toComponent,
            message,
            data,
            timestamp: new Date(),
            status: 'pending'
        };

        this.messageQueue.push(messageObj);
        this.processQueue();
        
        return messageObj.id;
    }

    // Broadcast message to all components
    broadcast(fromComponent, message, data = {}) {
        const components = PMComponentRegistry.getAll();
        const messageIds = [];
        
        components.forEach(([componentId]) => {
            if (componentId !== fromComponent) {
                const messageId = this.sendMessage(fromComponent, componentId, message, data);
                messageIds.push(messageId);
            }
        });
        
        return messageIds;
    }

    // Process message queue
    async processQueue() {
        if (this.processingQueue) return;
        
        this.processingQueue = true;
        
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            await this.deliverMessage(message);
        }
        
        this.processingQueue = false;
    }

    // Deliver message to component
    async deliverMessage(message) {
        try {
            const targetInstance = PMComponentRegistry.getInstance(message.to);
            
            if (targetInstance && typeof targetInstance.receiveMessage === 'function') {
                await targetInstance.receiveMessage(message);
                message.status = 'delivered';
                
                PMEventBus.emit('message.delivered', message);
            } else {
                message.status = 'failed';
                message.error = 'Target component not found or no receiveMessage method';
                
                PMEventBus.emit('message.failed', message);
            }
        } catch (error) {
            message.status = 'error';
            message.error = error.message;
            
            PMEventBus.emit('message.error', { message, error });
            console.error('Message delivery error:', error);
        }
    }

    // Request data from component
    async requestData(fromComponent, toComponent, dataType, params = {}) {
        return new Promise((resolve, reject) => {
            const requestId = Date.now() + Math.random();
            
            // Listen for response
            const responseHandler = PMEventBus.once(`dataResponse.${requestId}`, (event) => {
                if (event.data.success) {
                    resolve(event.data.result);
                } else {
                    reject(new Error(event.data.error));
                }
            });
            
            // Send request
            this.sendMessage(fromComponent, toComponent, 'dataRequest', {
                requestId,
                dataType,
                params
            });
            
            // Timeout after 10 seconds
            setTimeout(() => {
                PMEventBus.off(`dataResponse.${requestId}`, responseHandler);
                reject(new Error('Data request timeout'));
            }, 10000);
        });
    }

    // Share data between components
    shareData(fromComponent, dataType, data, targetComponents = []) {
        const shareEvent = {
            from: fromComponent,
            dataType,
            data,
            timestamp: new Date()
        };
        
        if (targetComponents.length > 0) {
            // Send to specific components
            targetComponents.forEach(componentId => {
                this.sendMessage(fromComponent, componentId, 'dataShare', shareEvent);
            });
        } else {
            // Broadcast to all
            this.broadcast(fromComponent, 'dataShare', shareEvent);
        }
        
        PMEventBus.emit('data.shared', shareEvent);
    }
}

// Initialize global instances
const PMEventBus = new PMEventBus();
const PMDataStore = new PMDataStore();
const PMComponentRegistry = new PMComponentRegistry();
const PMComponentCommunication = new PMComponentCommunication();

// Global middleware for logging
PMEventBus.use((eventData) => {
    if (PMSystemConfig.settings.debug) {
        console.log(`🔄 Event: ${eventData.type}`, eventData);
    }
    return true;
});

// Auto-register components from config
Object.entries(PMSystemConfig.components).forEach(([id, config]) => {
    PMComponentRegistry.register(parseInt(id), config);
});

// Component Base Class
class PMComponent {
    constructor(componentId, options = {}) {
        this.componentId = componentId;
        this.options = options;
        this.eventSubscriptions = [];
        this.dataWatchers = [];
        
        // Register instance
        PMComponentRegistry.setInstance(componentId, this);
        
        // Setup default event handlers
        this.setupEventHandlers();
        
        console.log(`🧩 Component initialized: ${componentId}`);
    }

    // Setup default event handlers
    setupEventHandlers() {
        // Handle data requests
        this.on('message.dataRequest', (event) => {
            if (event.data.to === this.componentId) {
                this.handleDataRequest(event.data);
            }
        });
        
        // Handle data sharing
        this.on('message.dataShare', (event) => {
            if (event.data.to === this.componentId || event.data.to === 'all') {
                this.handleDataShare(event.data);
            }
        });
    }

    // Subscribe to events
    on(event, callback, options = {}) {
        const subscriptionId = PMEventBus.on(event, callback, {
            ...options,
            component: this.componentId
        });
        this.eventSubscriptions.push({ event, subscriptionId });
        return subscriptionId;
    }

    // Emit events
    emit(event, data, options = {}) {
        return PMEventBus.emit(event, data, {
            ...options,
            source: this.componentId
        });
    }

    // Watch data store
    watch(key, callback, options = {}) {
        const watcherId = PMDataStore.watch(key, callback, {
            ...options,
            component: this.componentId
        });
        this.dataWatchers.push({ key, watcherId });
        return watcherId;
    }

    // Set data in store
    setData(key, value) {
        return PMDataStore.set(key, value, { source: this.componentId });
    }

    // Get data from store
    getData(key, defaultValue) {
        return PMDataStore.get(key, defaultValue);
    }

    // Send message to another component
    sendMessage(toComponent, message, data) {
        return PMComponentCommunication.sendMessage(this.componentId, toComponent, message, data);
    }

    // Broadcast message
    broadcast(message, data) {
        return PMComponentCommunication.broadcast(this.componentId, message, data);
    }

    // Request data from another component
    async requestData(fromComponent, dataType, params) {
        return PMComponentCommunication.requestData(this.componentId, fromComponent, dataType, params);
    }

    // Share data with other components
    shareData(dataType, data, targetComponents) {
        return PMComponentCommunication.shareData(this.componentId, dataType, data, targetComponents);
    }

    // Handle incoming messages (override in subclasses)
    async receiveMessage(message) {
        console.log(`📨 Component ${this.componentId} received message:`, message);
    }

    // Handle data requests (override in subclasses)
    handleDataRequest(request) {
        console.log(`📊 Component ${this.componentId} received data request:`, request);
        
        // Send default response
        PMEventBus.emit(`dataResponse.${request.requestId}`, {
            data: {
                success: false,
                error: 'Data request not implemented'
            }
        });
    }

    // Handle data sharing (override in subclasses)
    handleDataShare(shareEvent) {
        console.log(`📤 Component ${this.componentId} received shared data:`, shareEvent);
    }

    // Cleanup when component is destroyed
    destroy() {
        // Unsubscribe from events
        this.eventSubscriptions.forEach(({ event, subscriptionId }) => {
            PMEventBus.off(event, subscriptionId);
        });
        
        // Unwatch data
        this.dataWatchers.forEach(({ key, watcherId }) => {
            PMDataStore.unwatch(key, watcherId);
        });
        
        // Unregister instance
        PMComponentRegistry.unregister(this.componentId);
        
        console.log(`🧩 Component destroyed: ${this.componentId}`);
    }
}

// Export to global scope
window.PMEventBus = PMEventBus;
window.PMDataStore = PMDataStore;
window.PMComponentRegistry = PMComponentRegistry;
window.PMComponentCommunication = PMComponentCommunication;
window.PMComponent = PMComponent;

console.log('🌉 PM Component Bridge Initialized - v4.0.0');
