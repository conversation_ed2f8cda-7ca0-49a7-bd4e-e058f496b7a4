<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Analytics & Business Intelligence - Projektový Manažment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c4a6e 0%, #0369a1 100%);
            min-height: 100vh;
            color: #333;
        }

        main {
            max-width: 1800px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .pm-component {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .pm-header {
            background: linear-gradient(135deg, #0369a1 0%, #0c4a6e 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pm-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .analytics-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .pm-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .pm-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
            padding: 2rem;
        }

        .analytics-overview {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .analytics-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            border: 1px solid #0369a1;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .analytics-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .analytics-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0369a1, #0c4a6e);
        }

        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #0369a1;
        }

        .card-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #0c4a6e;
        }

        .card-label {
            color: #0369a1;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .card-insight {
            font-size: 0.8rem;
            color: #0c4a6e;
            background: rgba(255,255,255,0.7);
            padding: 0.5rem;
            border-radius: 6px;
            font-style: italic;
        }

        .chart-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            height: 300px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-bottom: 1rem;
        }

        .chart-placeholder {
            color: #6b7280;
            text-align: center;
        }

        .trend-chart {
            display: flex;
            align-items: end;
            justify-content: space-around;
            height: 200px;
            padding: 1rem;
        }

        .trend-bar {
            background: linear-gradient(to top, #0369a1, #0ea5e9);
            border-radius: 4px 4px 0 0;
            min-width: 20px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .trend-bar:hover {
            background: linear-gradient(to top, #0c4a6e, #0369a1);
            transform: scaleY(1.05);
        }

        .bar-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            color: #6b7280;
            white-space: nowrap;
        }

        .bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            font-weight: 600;
            color: #1f2937;
        }

        .pie-chart {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .pie-segment {
            fill: none;
            stroke-width: 30;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .pie-segment:hover {
            stroke-width: 35;
        }

        .chart-legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .kpi-dashboard {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }

        .kpi-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .kpi-item {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .kpi-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #0369a1;
        }

        .kpi-label {
            color: #6b7280;
            margin-bottom: 0.75rem;
            font-weight: 500;
        }

        .kpi-progress {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-excellent { background: #10b981; }
        .progress-good { background: #3b82f6; }
        .progress-average { background: #f59e0b; }
        .progress-poor { background: #ef4444; }

        .kpi-target {
            font-size: 0.8rem;
            color: #9ca3af;
        }

        .predictive-analytics {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 2rem;
        }

        .predictive-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .prediction-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .prediction-card {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #f59e0b;
        }

        .prediction-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .prediction-type {
            font-weight: 600;
            color: #92400e;
        }

        .prediction-confidence {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #d1fae5;
            color: #065f46;
        }

        .prediction-content {
            color: #92400e;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .prediction-impact {
            background: rgba(255,255,255,0.9);
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
        }

        .impact-label {
            font-weight: 600;
            color: #7c2d12;
            margin-bottom: 0.25rem;
        }

        .impact-text {
            color: #92400e;
        }

        .bi-insights {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border: 2px solid #8b5cf6;
            border-radius: 12px;
            padding: 2rem;
        }

        .insights-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .insight-card {
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #c4b5fd;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .insight-card:hover {
            background: rgba(255,255,255,0.95);
            transform: translateY(-2px);
        }

        .insight-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .insight-title {
            font-weight: 600;
            color: #5b21b6;
        }

        .insight-priority {
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .priority-high { background: #fee2e2; color: #991b1b; }
        .priority-medium { background: #fef3c7; color: #92400e; }
        .priority-low { background: #d1fae5; color: #065f46; }

        .insight-description {
            color: #7c3aed;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .insight-action {
            background: rgba(255,255,255,0.9);
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
        }

        .action-label {
            font-weight: 600;
            color: #5b21b6;
            margin-bottom: 0.25rem;
        }

        .action-text {
            color: #7c3aed;
        }

        @media (max-width: 1200px) {
            .pm-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .analytics-overview {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .kpi-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Advanced Analytics & Business Intelligence - Projektový Manažment</h1>

        <div class="pm-component">
            <div class="pm-header">
                <h2>📊 Advanced Analytics & Business Intelligence</h2>
                <div class="analytics-indicator">
                    🧠 AI Analytics: Active
                </div>
                <div class="pm-actions">
                    <button class="btn btn-primary" onclick="generateReport()">📋 Generovať Report</button>
                    <button class="btn btn-primary" onclick="exportAnalytics()">📤 Export Analytics</button>
                </div>
            </div>

            <div class="pm-content">
                <!-- Analytics Overview -->
                <div class="analytics-overview">
                    <div class="analytics-card">
                        <div class="card-icon">📈</div>
                        <div class="card-value">94.7%</div>
                        <div class="card-label">Project Success Rate</div>
                        <div class="card-insight">
                            ****% oproti minulému kvartálu
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="card-icon">⚡</div>
                        <div class="card-value">87%</div>
                        <div class="card-label">Team Efficiency</div>
                        <div class="card-insight">
                            Optimálna úroveň produktivity
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="card-icon">💰</div>
                        <div class="card-value">€2.3M</div>
                        <div class="card-label">ROI Generated</div>
                        <div class="card-insight">
                            312% návratnosť investícií
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-value">96%</div>
                        <div class="card-label">On-Time Delivery</div>
                        <div class="card-insight">
                            Najlepší výsledok za 2 roky
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="card-icon">👥</div>
                        <div class="card-value">8.9/10</div>
                        <div class="card-label">Team Satisfaction</div>
                        <div class="card-insight">
                            Vysoká spokojnosť tímu
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="card-icon">🔍</div>
                        <div class="card-value">0.8%</div>
                        <div class="card-label">Defect Rate</div>
                        <div class="card-insight">
                            Výrazne pod priemerom odvetvia
                        </div>
                    </div>
                </div>

                <!-- Performance Trends -->
                <div class="chart-section">
                    <div class="chart-title">
                        📈 Performance Trendy (Posledných 12 Mesiacov)
                    </div>
                    <div class="chart-container">
                        <div class="trend-chart">
                            <div class="trend-bar" style="height: 60%;">
                                <div class="bar-value">78%</div>
                                <div class="bar-label">Jan</div>
                            </div>
                            <div class="trend-bar" style="height: 70%;">
                                <div class="bar-value">82%</div>
                                <div class="bar-label">Feb</div>
                            </div>
                            <div class="trend-bar" style="height: 75%;">
                                <div class="bar-value">85%</div>
                                <div class="bar-label">Mar</div>
                            </div>
                            <div class="trend-bar" style="height: 80%;">
                                <div class="bar-value">87%</div>
                                <div class="bar-label">Apr</div>
                            </div>
                            <div class="trend-bar" style="height: 85%;">
                                <div class="bar-value">89%</div>
                                <div class="bar-label">Máj</div>
                            </div>
                            <div class="trend-bar" style="height: 90%;">
                                <div class="bar-value">91%</div>
                                <div class="bar-label">Jún</div>
                            </div>
                            <div class="trend-bar" style="height: 88%;">
                                <div class="bar-value">90%</div>
                                <div class="bar-label">Júl</div>
                            </div>
                            <div class="trend-bar" style="height: 92%;">
                                <div class="bar-value">93%</div>
                                <div class="bar-label">Aug</div>
                            </div>
                            <div class="trend-bar" style="height: 95%;">
                                <div class="bar-value">95%</div>
                                <div class="bar-label">Sep</div>
                            </div>
                            <div class="trend-bar" style="height: 93%;">
                                <div class="bar-value">94%</div>
                                <div class="bar-label">Okt</div>
                            </div>
                            <div class="trend-bar" style="height: 96%;">
                                <div class="bar-value">96%</div>
                                <div class="bar-label">Nov</div>
                            </div>
                            <div class="trend-bar" style="height: 98%;">
                                <div class="bar-value">97%</div>
                                <div class="bar-label">Dec</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #0369a1;"></div>
                            <span>Project Success Rate</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #0ea5e9;"></div>
                            <span>Team Efficiency</span>
                        </div>
                    </div>
                </div>

                <!-- Resource Distribution -->
                <div class="chart-section">
                    <div class="chart-title">
                        🥧 Distribúcia Zdrojov
                    </div>
                    <div class="chart-container">
                        <div class="pie-chart">
                            <svg width="200" height="200">
                                <circle class="pie-segment" cx="100" cy="100" r="80"
                                        stroke="#0369a1" stroke-dasharray="125 314" stroke-dashoffset="0"></circle>
                                <circle class="pie-segment" cx="100" cy="100" r="80"
                                        stroke="#0ea5e9" stroke-dasharray="94 314" stroke-dashoffset="-125"></circle>
                                <circle class="pie-segment" cx="100" cy="100" r="80"
                                        stroke="#38bdf8" stroke-dasharray="63 314" stroke-dashoffset="-219"></circle>
                                <circle class="pie-segment" cx="100" cy="100" r="80"
                                        stroke="#7dd3fc" stroke-dasharray="32 314" stroke-dashoffset="-282"></circle>
                            </svg>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #0369a1;"></div>
                            <span>Development (40%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #0ea5e9;"></div>
                            <span>Testing (30%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #38bdf8;"></div>
                            <span>Design (20%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #7dd3fc;"></div>
                            <span>Management (10%)</span>
                        </div>
                    </div>
                </div>

                <!-- KPI Dashboard -->
                <div class="kpi-dashboard">
                    <div class="kpi-title">
                        🎯 Kľúčové Výkonnostné Indikátory
                    </div>
                    <div class="kpi-grid">
                        <div class="kpi-item">
                            <div class="kpi-value">2.3 dní</div>
                            <div class="kpi-label">Priemerný Cycle Time</div>
                            <div class="kpi-progress">
                                <div class="progress-fill progress-excellent" style="width: 92%;"></div>
                            </div>
                            <div class="kpi-target">Cieľ: < 3 dni</div>
                        </div>

                        <div class="kpi-item">
                            <div class="kpi-value">94%</div>
                            <div class="kpi-label">First-Time Quality</div>
                            <div class="kpi-progress">
                                <div class="progress-fill progress-excellent" style="width: 94%;"></div>
                            </div>
                            <div class="kpi-target">Cieľ: > 90%</div>
                        </div>

                        <div class="kpi-item">
                            <div class="kpi-value">87%</div>
                            <div class="kpi-label">Resource Utilization</div>
                            <div class="kpi-progress">
                                <div class="progress-fill progress-good" style="width: 87%;"></div>
                            </div>
                            <div class="kpi-target">Cieľ: 80-90%</div>
                        </div>

                        <div class="kpi-item">
                            <div class="kpi-value">€1.2M</div>
                            <div class="kpi-label">Cost Savings</div>
                            <div class="kpi-progress">
                                <div class="progress-fill progress-excellent" style="width: 96%;"></div>
                            </div>
                            <div class="kpi-target">Cieľ: €1M</div>
                        </div>

                        <div class="kpi-item">
                            <div class="kpi-value">4.8/5</div>
                            <div class="kpi-label">Client Satisfaction</div>
                            <div class="kpi-progress">
                                <div class="progress-fill progress-excellent" style="width: 96%;"></div>
                            </div>
                            <div class="kpi-target">Cieľ: > 4.5</div>
                        </div>

                        <div class="kpi-item">
                            <div class="kpi-value">12%</div>
                            <div class="kpi-label">Innovation Index</div>
                            <div class="kpi-progress">
                                <div class="progress-fill progress-average" style="width: 75%;"></div>
                            </div>
                            <div class="kpi-target">Cieľ: > 15%</div>
                        </div>
                    </div>
                </div>

                <!-- Predictive Analytics -->
                <div class="predictive-analytics">
                    <div class="predictive-title">
                        🔮 Prediktívna Analytika
                    </div>
                    <div class="prediction-grid">
                        <div class="prediction-card">
                            <div class="prediction-header">
                                <div class="prediction-type">Project Timeline Prediction</div>
                                <div class="prediction-confidence">89% presnosť</div>
                            </div>
                            <div class="prediction-content">
                                Na základe aktuálneho tempa a historických dát, projekt E-shop Modernizácia bude dokončený 3 dni pred plánovaným termínom.
                            </div>
                            <div class="prediction-impact">
                                <div class="impact-label">Dopad:</div>
                                <div class="impact-text">Možnosť skoršieho spustenia a úspory €3,200 na nákladoch</div>
                            </div>
                        </div>

                        <div class="prediction-card">
                            <div class="prediction-header">
                                <div class="prediction-type">Resource Demand Forecast</div>
                                <div class="prediction-confidence">92% presnosť</div>
                            </div>
                            <div class="prediction-content">
                                V nasledujúcich 2 mesiacoch sa očakáva zvýšenie dopytu po frontend developeroch o 35% kvôli novým projektom.
                            </div>
                            <div class="prediction-impact">
                                <div class="impact-label">Dopad:</div>
                                <div class="impact-text">Potreba náboru 2 nových frontend developerov do augusta</div>
                            </div>
                        </div>

                        <div class="prediction-card">
                            <div class="prediction-header">
                                <div class="prediction-type">Budget Variance Prediction</div>
                                <div class="prediction-confidence">85% presnosť</div>
                            </div>
                            <div class="prediction-content">
                                Aktuálne projekty budú dokončené s priemernou úsporou 4.2% z plánovaného rozpočtu.
                            </div>
                            <div class="prediction-impact">
                                <div class="impact-label">Dopad:</div>
                                <div class="impact-text">Celková úspora €10,300 môže byť reinvestovaná do inovácií</div>
                            </div>
                        </div>

                        <div class="prediction-card">
                            <div class="prediction-header">
                                <div class="prediction-type">Quality Risk Assessment</div>
                                <div class="prediction-confidence">91% presnosť</div>
                            </div>
                            <div class="prediction-content">
                                Identifikované riziko zvýšenia defect rate o 15% v module platobného systému kvôli komplexnosti.
                            </div>
                            <div class="prediction-impact">
                                <div class="impact-label">Dopad:</div>
                                <div class="impact-text">Odporúčanie: Pridať 2 dni na dodatočné testovanie</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Intelligence Insights -->
                <div class="bi-insights">
                    <div class="insights-title">
                        💡 Business Intelligence Insights
                    </div>
                    <div class="insights-grid">
                        <div class="insight-card" onclick="applyInsight(1)">
                            <div class="insight-header">
                                <div class="insight-title">Optimalizácia Workflow</div>
                                <div class="insight-priority priority-high">Vysoká</div>
                            </div>
                            <div class="insight-description">
                                Automatizácia code review procesu môže znížiť cycle time o 23% a zvýšiť kvalitu kódu.
                            </div>
                            <div class="insight-action">
                                <div class="action-label">Odporúčaná akcia:</div>
                                <div class="action-text">Implementovať AI-powered code review nástroje</div>
                            </div>
                        </div>

                        <div class="insight-card" onclick="applyInsight(2)">
                            <div class="insight-header">
                                <div class="insight-title">Team Performance</div>
                                <div class="insight-priority priority-medium">Stredná</div>
                            </div>
                            <div class="insight-description">
                                Tím A dosahuje o 15% lepšie výsledky pri práci na menších, iteratívnych úlohách.
                            </div>
                            <div class="insight-action">
                                <div class="action-label">Odporúčaná akcia:</div>
                                <div class="action-text">Rozdeliť veľké úlohy na menšie komponenty</div>
                            </div>
                        </div>

                        <div class="insight-card" onclick="applyInsight(3)">
                            <div class="insight-header">
                                <div class="insight-title">Resource Allocation</div>
                                <div class="insight-priority priority-high">Vysoká</div>
                            </div>
                            <div class="insight-description">
                                Prerozdelenie senior developerov medzi projekty môže zvýšiť celkovú produktivitu o 18%.
                            </div>
                            <div class="insight-action">
                                <div class="action-label">Odporúčaná akcia:</div>
                                <div class="action-text">Rebalancovať tímové zloženie do konca mesiaca</div>
                            </div>
                        </div>

                        <div class="insight-card" onclick="applyInsight(4)">
                            <div class="insight-header">
                                <div class="insight-title">Technology Stack</div>
                                <div class="insight-priority priority-low">Nízka</div>
                            </div>
                            <div class="insight-description">
                                Migrácia na novšiu verziu frameworku môže zlepšiť performance o 12% a znížiť maintenance náklady.
                            </div>
                            <div class="insight-action">
                                <div class="action-label">Odporúčaná akcia:</div>
                                <div class="action-text">Naplánovať migráciu na Q3 2024</div>
                            </div>
                        </div>

                        <div class="insight-card" onclick="applyInsight(5)">
                            <div class="insight-header">
                                <div class="insight-title">Client Communication</div>
                                <div class="insight-priority priority-medium">Stredná</div>
                            </div>
                            <div class="insight-description">
                                Projekty s týždennými client demo sessions majú o 25% vyššiu client satisfaction.
                            </div>
                            <div class="insight-action">
                                <div class="action-label">Odporúčaná akcia:</div>
                                <div class="action-text">Štandardizovať týždenné demo sessions</div>
                            </div>
                        </div>

                        <div class="insight-card" onclick="applyInsight(6)">
                            <div class="insight-header">
                                <div class="insight-title">Innovation Opportunity</div>
                                <div class="insight-priority priority-high">Vysoká</div>
                            </div>
                            <div class="insight-description">
                                Investícia do AI/ML capabilities môže otvoriť nové revenue streams v hodnote €500K ročne.
                            </div>
                            <div class="insight-action">
                                <div class="action-label">Odporúčaná akcia:</div>
                                <div class="action-text">Vytvoriť AI/ML center of excellence</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function initPMComponent() {
            animateCharts();
            updateAnalytics();
            startDataRefresh();
        }

        function animateCharts() {
            // Animácia trend bars
            const bars = document.querySelectorAll('.trend-bar');
            bars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.opacity = '1';
                    bar.style.transform = 'scaleY(1)';
                }, index * 100);
            });

            // Animácia pie chart
            const segments = document.querySelectorAll('.pie-segment');
            segments.forEach((segment, index) => {
                setTimeout(() => {
                    segment.style.opacity = '1';
                }, index * 200);
            });
        }

        function updateAnalytics() {
            // Simulácia aktualizácie analytics dát
            console.log('Analytics data aktualizované:', new Date().toLocaleTimeString());
        }

        function startDataRefresh() {
            // Automatické obnovenie dát každých 5 minút
            setInterval(() => {
                updateAnalytics();
                refreshPredictions();
            }, 300000);
        }

        function refreshPredictions() {
            // Simulácia refresh prediktívnych modelov
            console.log('Prediktívne modely aktualizované');
        }

        function generateReport() {
            alert('📋 Generujem Advanced Analytics Report...\n\n• Performance metrics: ✓\n• Predictive analytics: ✓\n• Business intelligence insights: ✓\n• KPI dashboard: ✓\n• Trend analysis: ✓\n\nReport obsahuje:\n✓ Executive summary\n✓ Detailed analytics\n✓ Predictive forecasts\n✓ Actionable insights\n✓ Recommendations\n\nReport bude vygenerovaný za 3 minúty a odoslaný na email.');
        }

        function exportAnalytics() {
            alert('📤 Export Analytics Data...\n\nExportujem:\n• Raw analytics data (CSV)\n• Processed metrics (Excel)\n• Visualization charts (PNG/PDF)\n• Predictive models (JSON)\n• BI insights (PDF)\n\nFormáty:\n✓ CSV pre raw data\n✓ Excel pre metrics\n✓ PDF pre reporty\n✓ JSON pre API integrácie\n\nExport dokončený za 2 minúty!');
        }

        function applyInsight(insightId) {
            const insights = {
                1: 'Optimalizácia Workflow: AI-powered code review nástroje budú implementované do konca mesiaca.',
                2: 'Team Performance: Úlohy budú rozdelené na menšie komponenty pre lepšiu efektívnosť.',
                3: 'Resource Allocation: Senior developeri budú prerozdelení pre optimálnu produktivitu.',
                4: 'Technology Stack: Migrácia frameworku naplánovaná na Q3 2024.',
                5: 'Client Communication: Týždenné demo sessions štandardizované pre všetky projekty.',
                6: 'Innovation Opportunity: AI/ML center of excellence bude vytvorený do septembra.'
            };

            alert(`💡 Business Intelligence Insight Aplikovaný\n\n${insights[insightId]}\n\nAkcia bude implementovaná a monitorovaná.\nOčakávané zlepšenie bude merané v nasledujúcich reportoch.`);
        }

        // Interaktívne trend bars
        document.querySelectorAll('.trend-bar').forEach(bar => {
            bar.addEventListener('click', function() {
                const month = this.querySelector('.bar-label').textContent;
                const value = this.querySelector('.bar-value').textContent;
                alert(`📊 Detail pre ${month}\n\nPerformance: ${value}\n\nKľúčové metriky:\n• Project success rate: ${value}\n• Team efficiency: ${Math.round(parseInt(value) * 0.9)}%\n• Client satisfaction: ${Math.round(parseInt(value) * 0.95)}%\n• Quality score: ${Math.round(parseInt(value) * 1.02)}%\n\nTrendy a insights dostupné v detailnom reporte.`);
            });
        });

        // Interaktívne pie segments
        document.querySelectorAll('.pie-segment').forEach((segment, index) => {
            const categories = ['Development', 'Testing', 'Design', 'Management'];
            const percentages = ['40%', '30%', '20%', '10%'];

            segment.addEventListener('click', function() {
                alert(`🥧 Resource Distribution: ${categories[index]}\n\nPodiel: ${percentages[index]}\n\nDetaily:\n• Alokované hodiny: ${Math.round(Math.random() * 1000 + 500)}h\n• Počet ľudí: ${Math.round(Math.random() * 10 + 5)}\n• Náklady: €${Math.round(Math.random() * 50000 + 25000)}\n• Efektívnosť: ${Math.round(Math.random() * 20 + 80)}%\n\nOptimalizačné možnosti identifikované.`);
            });
        });

        // Inicializácia komponentu
        document.addEventListener('DOMContentLoaded', initPMComponent);
    </script>
</body>
</html>
