/**
 * PM Master System v4 - Configuration & Integration Layer
 * Centralizovaná konfigurácia pre všetkých 20 komponentov
 */

// System Configuration
const PMSystemConfig = {
    version: '4.0.0',
    buildDate: '2024-06-15',
    environment: 'production',
    
    // API Configuration
    api: {
        baseUrl: '/api/v4',
        timeout: 30000,
        retryAttempts: 3,
        endpoints: {
            projects: '/projects',
            tasks: '/tasks',
            teams: '/teams',
            analytics: '/analytics',
            notifications: '/notifications',
            integrations: '/integrations',
            ai: '/ai-assistant',
            files: '/files',
            reports: '/reports',
            settings: '/settings'
        }
    },
    
    // Component Registry
    components: {
        1: {
            id: 1,
            name: 'Inteligentný Dokumentový Hub',
            file: 'pm_component_1.html',
            category: 'Knowledge',
            level: 'Knowledge',
            icon: '📚',
            description: 'AI-powered dokumentový manažment s kategoriz<PERSON>ciou',
            features: ['AI Kategorizácia', 'Verzio<PERSON><PERSON>rol<PERSON>', 'Smart Search', 'Collaboration'],
            dependencies: [],
            apiEndpoints: ['files', 'ai'],
            permissions: ['read', 'write', 'admin'],
            status: 'active'
        },
        2: {
            id: 2,
            name: 'Adaptívny Gantt Plánovač',
            file: 'pm_component_2.html',
            category: 'Project',
            level: 'Project',
            icon: '📊',
            description: 'AI-optimalizované projektové plánovanie s resource scheduling',
            features: ['AI Optimalizácia', 'Resource Scheduling', 'Critical Path', 'Dependencies'],
            dependencies: [7], // Resource Optimizer
            apiEndpoints: ['projects', 'tasks', 'teams'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        3: {
            id: 3,
            name: 'Project Health Dashboard',
            file: 'pm_component_3.html',
            category: 'Operational',
            level: 'Operational',
            icon: '💚',
            description: 'Real-time monitoring zdravia projektov s KPI tracking',
            features: ['Real-time KPI', 'Health Scoring', 'Alert System', 'Trend Analysis'],
            dependencies: [9, 18], // Performance Analyzer, Advanced Analytics
            apiEndpoints: ['projects', 'analytics'],
            permissions: ['read'],
            status: 'active'
        },
        4: {
            id: 4,
            name: 'Smart Meeting Planner',
            file: 'pm_component_4.html',
            category: 'Project',
            level: 'Project',
            icon: '📅',
            description: 'AI-powered plánovanie stretnutí s availability optimization',
            features: ['AI Scheduling', 'Availability Check', 'Auto Invites', 'Meeting Analytics'],
            dependencies: [10], // Stakeholder Hub
            apiEndpoints: ['teams', 'notifications'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        5: {
            id: 5,
            name: 'Template Generátor',
            file: 'pm_component_5.html',
            category: 'Knowledge',
            level: 'Knowledge',
            icon: '📄',
            description: 'AI-powered generovanie projektových šablón',
            features: ['AI Templates', 'Custom Categories', 'Version Control', 'Sharing'],
            dependencies: [1, 16], // Document Hub, AI Assistant
            apiEndpoints: ['files', 'ai'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        6: {
            id: 6,
            name: 'Risk Predictor & Mitigation',
            file: 'pm_component_6.html',
            category: 'Management',
            level: 'Management',
            icon: '⚠️',
            description: 'AI predikcia rizík s automatickými mitigation plánmi',
            features: ['AI Risk Prediction', 'Mitigation Plans', 'Risk Scoring', 'Alert System'],
            dependencies: [16, 18], // AI Assistant, Advanced Analytics
            apiEndpoints: ['projects', 'ai', 'analytics'],
            permissions: ['read', 'write', 'admin'],
            status: 'active'
        },
        7: {
            id: 7,
            name: 'Resource Optimizer',
            file: 'pm_component_7.html',
            category: 'Operational',
            level: 'Operational',
            icon: '👤',
            description: 'AI optimalizácia ľudských zdrojov a capacity planning',
            features: ['AI Optimization', 'Capacity Planning', 'Skill Matching', 'Workload Balance'],
            dependencies: [9], // Performance Analyzer
            apiEndpoints: ['teams', 'projects', 'ai'],
            permissions: ['read', 'write', 'admin'],
            status: 'active'
        },
        8: {
            id: 8,
            name: 'Knowledge Base Builder',
            file: 'pm_component_8.html',
            category: 'Knowledge',
            level: 'Knowledge',
            icon: '📖',
            description: 'Markdown-based wiki a knowledge management systém',
            features: ['Markdown Editor', 'Wiki Structure', 'Search', 'Collaboration'],
            dependencies: [1], // Document Hub
            apiEndpoints: ['files'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        9: {
            id: 9,
            name: 'Performance Analyzer',
            file: 'pm_component_9.html',
            category: 'Management',
            level: 'Management',
            icon: '📈',
            description: 'Analýza výkonnosti tímu s AI insights',
            features: ['Performance Metrics', 'AI Insights', 'Trend Analysis', 'Benchmarking'],
            dependencies: [18], // Advanced Analytics
            apiEndpoints: ['teams', 'analytics', 'ai'],
            permissions: ['read', 'admin'],
            status: 'active'
        },
        10: {
            id: 10,
            name: 'Stakeholder Communication Hub',
            file: 'pm_component_10.html',
            category: 'Management',
            level: 'Management',
            icon: '💬',
            description: 'Centralizovaná komunikácia so stakeholdermi',
            features: ['Communication Center', 'Auto Updates', 'Feedback Collection', 'Analytics'],
            dependencies: [14], // Client Portal
            apiEndpoints: ['notifications', 'analytics'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        11: {
            id: 11,
            name: 'Quality Assurance & Testing',
            file: 'pm_component_11.html',
            category: 'Operational',
            level: 'Operational',
            icon: '🧪',
            description: 'Komplexný QA a testing management systém',
            features: ['Test Management', 'Bug Tracking', 'Quality Metrics', 'Automation'],
            dependencies: [15], // DevOps Pipeline
            apiEndpoints: ['projects', 'analytics'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        12: {
            id: 12,
            name: 'Budget Tracker & Financial Analytics',
            file: 'pm_component_12.html',
            category: 'Management',
            level: 'Management',
            icon: '💰',
            description: 'Finančná analytika a budget forecasting s AI',
            features: ['Budget Tracking', 'AI Forecasting', 'Cost Analysis', 'ROI Calculation'],
            dependencies: [18, 20], // Advanced Analytics, Strategic Portfolio
            apiEndpoints: ['projects', 'analytics', 'ai'],
            permissions: ['read', 'write', 'admin'],
            status: 'active'
        },
        13: {
            id: 13,
            name: 'Agile Sprint Manager & Scrum Board',
            file: 'pm_component_13.html',
            category: 'Operational',
            level: 'Operational',
            icon: '🏃‍♂️',
            description: 'Kompletný Agile/Scrum board management',
            features: ['Kanban Board', 'Sprint Planning', 'Velocity Tracking', 'Burndown Charts'],
            dependencies: [2, 9], // Gantt Planner, Performance Analyzer
            apiEndpoints: ['projects', 'tasks', 'teams'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        14: {
            id: 14,
            name: 'Client Portal & Feedback Manager',
            file: 'pm_component_14.html',
            category: 'Project',
            level: 'Project',
            icon: '👤',
            description: 'Client portal s feedback systémom a komunikáciou',
            features: ['Client Dashboard', 'Feedback System', 'Progress Sharing', 'Communication'],
            dependencies: [10], // Stakeholder Hub
            apiEndpoints: ['projects', 'notifications'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        15: {
            id: 15,
            name: 'Deployment Pipeline & DevOps',
            file: 'pm_component_15.html',
            category: 'Enterprise',
            level: 'Enterprise',
            icon: '🚀',
            description: 'CI/CD pipeline management a DevOps monitoring',
            features: ['CI/CD Pipeline', 'Environment Monitoring', 'Deployment Tracking', 'Automation'],
            dependencies: [19], // Enterprise Integration
            apiEndpoints: ['integrations', 'analytics'],
            permissions: ['read', 'write', 'admin'],
            status: 'active'
        },
        16: {
            id: 16,
            name: 'AI Project Assistant & Automation',
            file: 'pm_component_16.html',
            category: 'Enterprise',
            level: 'Enterprise',
            icon: '🤖',
            description: 'AI asistent s automatizáciou a intelligent insights',
            features: ['AI Chat', 'Automation Hub', 'Smart Insights', 'Predictive Analytics'],
            dependencies: [],
            apiEndpoints: ['ai', 'analytics'],
            permissions: ['read', 'write'],
            status: 'active'
        },
        17: {
            id: 17,
            name: 'Master Dashboard & Control Center',
            file: 'pm_component_17.html',
            category: 'Executive',
            level: 'Executive',
            icon: '🎛️',
            description: 'Centrálny dashboard s overview všetkých komponentov',
            features: ['System Overview', 'Component Management', 'Health Monitoring', 'Quick Actions'],
            dependencies: [18, 19], // Advanced Analytics, Enterprise Integration
            apiEndpoints: ['analytics', 'settings'],
            permissions: ['read', 'admin'],
            status: 'active'
        },
        18: {
            id: 18,
            name: 'Advanced Analytics & Business Intelligence',
            file: 'pm_component_18.html',
            category: 'Executive',
            level: 'Executive',
            icon: '📊',
            description: 'Pokročilé analytics a BI s prediktívnymi modelmi',
            features: ['Advanced Analytics', 'Predictive Models', 'BI Dashboards', 'Data Visualization'],
            dependencies: [],
            apiEndpoints: ['analytics', 'ai'],
            permissions: ['read', 'admin'],
            status: 'active'
        },
        19: {
            id: 19,
            name: 'Enterprise Integration & API Management',
            file: 'pm_component_19.html',
            category: 'Enterprise',
            level: 'Enterprise',
            icon: '🔗',
            description: 'API management a enterprise systémové integrácie',
            features: ['API Management', 'System Integration', 'Webhook Management', 'Security'],
            dependencies: [],
            apiEndpoints: ['integrations'],
            permissions: ['read', 'write', 'admin'],
            status: 'active'
        },
        20: {
            id: 20,
            name: 'Strategic Portfolio Management & Executive Dashboard',
            file: 'pm_component_20.html',
            category: 'Executive',
            level: 'Executive',
            icon: '👑',
            description: 'Strategic portfolio management pre executive level',
            features: ['Portfolio Matrix', 'Strategic Planning', 'Executive Insights', 'Financial Overview'],
            dependencies: [12, 18], // Budget Tracker, Advanced Analytics
            apiEndpoints: ['projects', 'analytics'],
            permissions: ['read', 'admin'],
            status: 'active'
        }
    },
    
    // User Roles & Permissions
    roles: {
        admin: {
            name: 'System Administrator',
            permissions: ['read', 'write', 'admin'],
            components: [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],
            level: 'all'
        },
        executive: {
            name: 'Executive Manager',
            permissions: ['read', 'admin'],
            components: [17,18,19,20,12,9,6],
            level: 'executive'
        },
        manager: {
            name: 'Project Manager',
            permissions: ['read', 'write'],
            components: [1,2,3,4,5,6,7,8,9,10,11,12,13,14],
            level: 'management'
        },
        developer: {
            name: 'Developer',
            permissions: ['read', 'write'],
            components: [1,2,3,4,5,8,11,13,15],
            level: 'operational'
        },
        client: {
            name: 'Client',
            permissions: ['read'],
            components: [14,3],
            level: 'project'
        }
    },
    
    // System Settings
    settings: {
        theme: 'dark',
        language: 'sk',
        notifications: true,
        autoSave: true,
        refreshInterval: 30000,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
        aiEnabled: true,
        analyticsEnabled: true
    },
    
    // Integration Settings
    integrations: {
        jira: { enabled: true, url: '', apiKey: '', status: 'connected' },
        slack: { enabled: true, webhook: '', status: 'connected' },
        github: { enabled: true, token: '', status: 'connected' },
        office365: { enabled: true, clientId: '', status: 'syncing' },
        sap: { enabled: false, url: '', status: 'error' },
        tableau: { enabled: true, server: '', status: 'connected' }
    },
    
    // AI Configuration
    ai: {
        enabled: true,
        provider: 'openai',
        model: 'gpt-4',
        maxTokens: 4000,
        temperature: 0.7,
        features: {
            documentAnalysis: true,
            riskPrediction: true,
            resourceOptimization: true,
            performanceInsights: true,
            budgetForecasting: true,
            templateGeneration: true
        }
    },
    
    // Analytics Configuration
    analytics: {
        enabled: true,
        realTimeUpdates: true,
        dataRetention: 365, // days
        aggregationInterval: 300, // seconds
        metrics: {
            performance: true,
            financial: true,
            quality: true,
            resources: true,
            risks: true,
            client: true
        }
    }
};

// Component Dependencies Manager
class ComponentDependencyManager {
    static getDependencies(componentId) {
        const component = PMSystemConfig.components[componentId];
        return component ? component.dependencies : [];
    }
    
    static getDependents(componentId) {
        const dependents = [];
        Object.values(PMSystemConfig.components).forEach(comp => {
            if (comp.dependencies.includes(componentId)) {
                dependents.push(comp.id);
            }
        });
        return dependents;
    }
    
    static canLoadComponent(componentId, userRole) {
        const component = PMSystemConfig.components[componentId];
        const role = PMSystemConfig.roles[userRole];
        
        if (!component || !role) return false;
        
        // Check if user has access to component
        if (!role.components.includes(componentId)) return false;
        
        // Check if user has required permissions
        const hasPermission = component.permissions.some(perm => 
            role.permissions.includes(perm)
        );
        
        return hasPermission;
    }
    
    static getLoadOrder(componentIds) {
        // Topological sort for dependency resolution
        const visited = new Set();
        const result = [];
        
        function visit(id) {
            if (visited.has(id)) return;
            visited.add(id);
            
            const deps = ComponentDependencyManager.getDependencies(id);
            deps.forEach(depId => {
                if (componentIds.includes(depId)) {
                    visit(depId);
                }
            });
            
            result.push(id);
        }
        
        componentIds.forEach(id => visit(id));
        return result;
    }
}

// System State Manager
class PMSystemState {
    constructor() {
        this.state = {
            currentUser: null,
            currentComponent: 17,
            loadedComponents: new Set(),
            systemHealth: 'healthy',
            notifications: [],
            settings: { ...PMSystemConfig.settings }
        };
    }
    
    setState(key, value) {
        this.state[key] = value;
        this.notifyStateChange(key, value);
    }
    
    getState(key) {
        return this.state[key];
    }
    
    notifyStateChange(key, value) {
        // Emit custom event for state changes
        window.dispatchEvent(new CustomEvent('pmStateChange', {
            detail: { key, value }
        }));
    }
    
    loadComponent(componentId) {
        this.state.loadedComponents.add(componentId);
        this.setState('currentComponent', componentId);
    }
    
    unloadComponent(componentId) {
        this.state.loadedComponents.delete(componentId);
    }
    
    addNotification(notification) {
        this.state.notifications.push({
            ...notification,
            id: Date.now(),
            timestamp: new Date()
        });
        this.notifyStateChange('notifications', this.state.notifications);
    }
    
    removeNotification(notificationId) {
        this.state.notifications = this.state.notifications.filter(
            n => n.id !== notificationId
        );
        this.notifyStateChange('notifications', this.state.notifications);
    }
}

// Export for global use
window.PMSystemConfig = PMSystemConfig;
window.ComponentDependencyManager = ComponentDependencyManager;
window.PMSystemState = new PMSystemState();

console.log('🎯 PM System Configuration Loaded - v4.0.0');
