/**
 * PM Master System v4 - API Integration Layer
 * Centralizovaná API komunikácia pre všetkých 20 komponentov
 */

// API Client Class
class PMAPIClient {
    constructor(config = {}) {
        this.baseUrl = config.baseUrl || PMSystemConfig.api.baseUrl;
        this.timeout = config.timeout || PMSystemConfig.api.timeout;
        this.retryAttempts = config.retryAttempts || PMSystemConfig.api.retryAttempts;
        this.headers = {
            'Content-Type': 'application/json',
            'X-PM-Version': PMSystemConfig.version,
            ...config.headers
        };
    }

    // Generic HTTP request method
    async request(method, endpoint, data = null, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: method.toUpperCase(),
            headers: { ...this.headers, ...options.headers },
            ...options
        };

        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            config.body = JSON.stringify(data);
        }

        let lastError;
        for (let attempt = 0; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, config);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                return {
                    success: true,
                    data: result,
                    status: response.status,
                    headers: response.headers
                };
            } catch (error) {
                lastError = error;
                if (attempt < this.retryAttempts) {
                    await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
                }
            }
        }

        return {
            success: false,
            error: lastError.message,
            status: 0
        };
    }

    // HTTP method shortcuts
    async get(endpoint, options = {}) {
        return this.request('GET', endpoint, null, options);
    }

    async post(endpoint, data, options = {}) {
        return this.request('POST', endpoint, data, options);
    }

    async put(endpoint, data, options = {}) {
        return this.request('PUT', endpoint, data, options);
    }

    async patch(endpoint, data, options = {}) {
        return this.request('PATCH', endpoint, data, options);
    }

    async delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, options);
    }

    // Utility method for delays
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Projects API
class ProjectsAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.projects;
    }

    async getAll(filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const url = queryString ? `${this.endpoint}?${queryString}` : this.endpoint;
        return this.client.get(url);
    }

    async getById(id) {
        return this.client.get(`${this.endpoint}/${id}`);
    }

    async create(projectData) {
        return this.client.post(this.endpoint, projectData);
    }

    async update(id, projectData) {
        return this.client.put(`${this.endpoint}/${id}`, projectData);
    }

    async delete(id) {
        return this.client.delete(`${this.endpoint}/${id}`);
    }

    async getHealth(id) {
        return this.client.get(`${this.endpoint}/${id}/health`);
    }

    async getMetrics(id, timeRange = '30d') {
        return this.client.get(`${this.endpoint}/${id}/metrics?range=${timeRange}`);
    }

    async getTimeline(id) {
        return this.client.get(`${this.endpoint}/${id}/timeline`);
    }

    async getRisks(id) {
        return this.client.get(`${this.endpoint}/${id}/risks`);
    }

    async getBudget(id) {
        return this.client.get(`${this.endpoint}/${id}/budget`);
    }
}

// Tasks API
class TasksAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.tasks;
    }

    async getAll(projectId = null, filters = {}) {
        let url = this.endpoint;
        if (projectId) {
            filters.projectId = projectId;
        }
        const queryString = new URLSearchParams(filters).toString();
        if (queryString) url += `?${queryString}`;
        return this.client.get(url);
    }

    async getById(id) {
        return this.client.get(`${this.endpoint}/${id}`);
    }

    async create(taskData) {
        return this.client.post(this.endpoint, taskData);
    }

    async update(id, taskData) {
        return this.client.put(`${this.endpoint}/${id}`, taskData);
    }

    async delete(id) {
        return this.client.delete(`${this.endpoint}/${id}`);
    }

    async updateStatus(id, status) {
        return this.client.patch(`${this.endpoint}/${id}/status`, { status });
    }

    async assignUser(id, userId) {
        return this.client.patch(`${this.endpoint}/${id}/assign`, { userId });
    }

    async addComment(id, comment) {
        return this.client.post(`${this.endpoint}/${id}/comments`, comment);
    }

    async getComments(id) {
        return this.client.get(`${this.endpoint}/${id}/comments`);
    }
}

// Teams API
class TeamsAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.teams;
    }

    async getAll() {
        return this.client.get(this.endpoint);
    }

    async getById(id) {
        return this.client.get(`${this.endpoint}/${id}`);
    }

    async getMembers(id) {
        return this.client.get(`${this.endpoint}/${id}/members`);
    }

    async addMember(id, memberData) {
        return this.client.post(`${this.endpoint}/${id}/members`, memberData);
    }

    async removeMember(id, memberId) {
        return this.client.delete(`${this.endpoint}/${id}/members/${memberId}`);
    }

    async getPerformance(id, timeRange = '30d') {
        return this.client.get(`${this.endpoint}/${id}/performance?range=${timeRange}`);
    }

    async getWorkload(id) {
        return this.client.get(`${this.endpoint}/${id}/workload`);
    }

    async getAvailability(id, dateRange = {}) {
        const queryString = new URLSearchParams(dateRange).toString();
        const url = queryString ? `${this.endpoint}/${id}/availability?${queryString}` : `${this.endpoint}/${id}/availability`;
        return this.client.get(url);
    }
}

// Analytics API
class AnalyticsAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.analytics;
    }

    async getDashboard(type = 'overview') {
        return this.client.get(`${this.endpoint}/dashboard/${type}`);
    }

    async getMetrics(category, timeRange = '30d') {
        return this.client.get(`${this.endpoint}/metrics/${category}?range=${timeRange}`);
    }

    async getReports(type, filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const url = queryString ? `${this.endpoint}/reports/${type}?${queryString}` : `${this.endpoint}/reports/${type}`;
        return this.client.get(url);
    }

    async generateReport(config) {
        return this.client.post(`${this.endpoint}/reports/generate`, config);
    }

    async getPredictions(model, data) {
        return this.client.post(`${this.endpoint}/predictions/${model}`, data);
    }

    async getInsights(category) {
        return this.client.get(`${this.endpoint}/insights/${category}`);
    }
}

// AI Assistant API
class AIAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.ai;
    }

    async chat(message, context = {}) {
        return this.client.post(`${this.endpoint}/chat`, { message, context });
    }

    async analyze(type, data) {
        return this.client.post(`${this.endpoint}/analyze/${type}`, data);
    }

    async generateTemplate(type, requirements) {
        return this.client.post(`${this.endpoint}/templates/generate`, { type, requirements });
    }

    async predictRisks(projectData) {
        return this.client.post(`${this.endpoint}/risks/predict`, projectData);
    }

    async optimizeResources(constraints) {
        return this.client.post(`${this.endpoint}/resources/optimize`, constraints);
    }

    async getInsights(category, data) {
        return this.client.post(`${this.endpoint}/insights/${category}`, data);
    }
}

// Files API
class FilesAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.files;
    }

    async upload(file, metadata = {}) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('metadata', JSON.stringify(metadata));

        return this.client.request('POST', `${this.endpoint}/upload`, formData, {
            headers: { 'Content-Type': undefined } // Let browser set multipart boundary
        });
    }

    async getById(id) {
        return this.client.get(`${this.endpoint}/${id}`);
    }

    async getAll(filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const url = queryString ? `${this.endpoint}?${queryString}` : this.endpoint;
        return this.client.get(url);
    }

    async delete(id) {
        return this.client.delete(`${this.endpoint}/${id}`);
    }

    async getVersions(id) {
        return this.client.get(`${this.endpoint}/${id}/versions`);
    }

    async categorize(id) {
        return this.client.post(`${this.endpoint}/${id}/categorize`);
    }
}

// Notifications API
class NotificationsAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.notifications;
    }

    async getAll(filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const url = queryString ? `${this.endpoint}?${queryString}` : this.endpoint;
        return this.client.get(url);
    }

    async send(notification) {
        return this.client.post(`${this.endpoint}/send`, notification);
    }

    async markAsRead(id) {
        return this.client.patch(`${this.endpoint}/${id}/read`);
    }

    async getPreferences(userId) {
        return this.client.get(`${this.endpoint}/preferences/${userId}`);
    }

    async updatePreferences(userId, preferences) {
        return this.client.put(`${this.endpoint}/preferences/${userId}`, preferences);
    }
}

// Integrations API
class IntegrationsAPI {
    constructor(client) {
        this.client = client;
        this.endpoint = PMSystemConfig.api.endpoints.integrations;
    }

    async getAll() {
        return this.client.get(this.endpoint);
    }

    async getById(id) {
        return this.client.get(`${this.endpoint}/${id}`);
    }

    async configure(id, config) {
        return this.client.put(`${this.endpoint}/${id}/config`, config);
    }

    async test(id) {
        return this.client.post(`${this.endpoint}/${id}/test`);
    }

    async sync(id) {
        return this.client.post(`${this.endpoint}/${id}/sync`);
    }

    async getStatus(id) {
        return this.client.get(`${this.endpoint}/${id}/status`);
    }

    async getLogs(id, filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const url = queryString ? `${this.endpoint}/${id}/logs?${queryString}` : `${this.endpoint}/${id}/logs`;
        return this.client.get(url);
    }
}

// Main API Manager
class PMAPIManager {
    constructor() {
        this.client = new PMAPIClient();
        
        // Initialize all API modules
        this.projects = new ProjectsAPI(this.client);
        this.tasks = new TasksAPI(this.client);
        this.teams = new TeamsAPI(this.client);
        this.analytics = new AnalyticsAPI(this.client);
        this.ai = new AIAPI(this.client);
        this.files = new FilesAPI(this.client);
        this.notifications = new NotificationsAPI(this.client);
        this.integrations = new IntegrationsAPI(this.client);
    }

    // Global error handler
    handleError(error, context = '') {
        console.error(`PM API Error ${context}:`, error);
        
        // Show user-friendly notification
        if (window.PMSystem && window.PMSystem.showNotification) {
            window.PMSystem.showNotification('error', 'API Error', 
                `Chyba pri komunikácii so serverom: ${error.message || error}`);
        }
        
        return {
            success: false,
            error: error.message || error,
            context
        };
    }

    // Health check
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response;
        } catch (error) {
            return this.handleError(error, 'Health Check');
        }
    }

    // Batch operations
    async batchRequest(requests) {
        const results = await Promise.allSettled(requests);
        return results.map((result, index) => ({
            index,
            success: result.status === 'fulfilled',
            data: result.status === 'fulfilled' ? result.value : null,
            error: result.status === 'rejected' ? result.reason : null
        }));
    }
}

// WebSocket Manager for real-time updates
class PMWebSocketManager {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.listeners = new Map();
    }

    connect() {
        try {
            const wsUrl = `ws://${window.location.host}/ws`;
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                console.log('🔌 WebSocket connected');
                this.reconnectAttempts = 0;
                this.emit('connected');
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.emit(data.type, data.payload);
                } catch (error) {
                    console.error('WebSocket message parse error:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('🔌 WebSocket disconnected');
                this.emit('disconnected');
                this.attemptReconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.emit('error', error);
            };
        } catch (error) {
            console.error('WebSocket connection failed:', error);
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`🔄 Attempting WebSocket reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
        }
    }

    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`WebSocket event handler error for ${event}:`, error);
                }
            });
        }
    }

    send(type, payload) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({ type, payload }));
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

// Initialize global API instances
window.PMAPI = new PMAPIManager();
window.PMWebSocket = new PMWebSocketManager();

// Auto-connect WebSocket
if (PMSystemConfig.settings.notifications) {
    window.PMWebSocket.connect();
}

console.log('🔗 PM API Layer Initialized - v4.0.0');
