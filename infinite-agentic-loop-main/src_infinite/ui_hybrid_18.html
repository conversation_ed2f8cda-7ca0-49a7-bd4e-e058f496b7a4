<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brutalist Navigation Center - Iteration 18</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Concrete texture overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(255,255,255,0.03) 2px,
                    rgba(255,255,255,0.03) 4px
                ),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(255,255,255,0.03) 2px,
                    rgba(255,255,255,0.03) 4px
                );
            pointer-events: none;
            z-index: 1;
        }

        /* Main Navigation Structure */
        .brutalist-nav {
            position: relative;
            background: #2a2a2a;
            box-shadow: 
                0 10px 30px rgba(0,0,0,0.8),
                inset 0 1px 0 rgba(255,255,255,0.1),
                inset 0 -1px 0 rgba(0,0,0,0.5);
            z-index: 100;
        }

        /* Top Bar - Search and Quick Actions */
        .nav-top-bar {
            background: linear-gradient(180deg, #333 0%, #2a2a2a 100%);
            border-bottom: 3px solid #000;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 20px;
            position: relative;
        }

        .nav-top-bar::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 0;
            right: 0;
            height: 3px;
            background: #666;
            box-shadow: 0 3px 10px rgba(0,0,0,0.5);
        }

        /* Logo Block */
        .logo-block {
            width: 60px;
            height: 60px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 900;
            font-size: 24px;
            color: #fff;
            text-transform: uppercase;
            letter-spacing: -2px;
            box-shadow: 
                inset -3px -3px 10px rgba(255,255,255,0.1),
                inset 3px 3px 10px rgba(0,0,0,0.5);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .logo-block:hover {
            transform: translateY(-2px);
            box-shadow: 
                inset -3px -3px 10px rgba(255,255,255,0.2),
                inset 3px 3px 10px rgba(0,0,0,0.7),
                0 5px 20px rgba(0,0,0,0.5);
        }

        /* Search Container */
        .search-container {
            flex: 1;
            max-width: 600px;
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 50px;
            background: #1a1a1a;
            border: 3px solid #444;
            color: #fff;
            font-size: 16px;
            padding: 0 50px 0 20px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 
                inset 0 2px 5px rgba(0,0,0,0.5),
                0 1px 0 rgba(255,255,255,0.1);
        }

        .search-input:focus {
            outline: none;
            border-color: #666;
            background: #222;
            box-shadow: 
                inset 0 2px 5px rgba(0,0,0,0.7),
                0 0 20px rgba(255,255,255,0.1);
        }

        .search-button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #444;
            border: none;
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .search-button:hover {
            background: #555;
            transform: translateY(-50%) scale(1.1);
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            width: 50px;
            height: 50px;
            background: #333;
            border: 2px solid #555;
            color: #aaa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 
                inset 0 -3px 0 rgba(0,0,0,0.5),
                0 2px 5px rgba(0,0,0,0.3);
        }

        .action-btn:hover {
            background: #444;
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 
                inset 0 -3px 0 rgba(0,0,0,0.5),
                0 5px 15px rgba(0,0,0,0.5);
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        /* Breadcrumb Bar */
        .breadcrumb-bar {
            background: #222;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            border-bottom: 2px solid #111;
            box-shadow: inset 0 -2px 5px rgba(0,0,0,0.3);
        }

        .breadcrumb-item {
            color: #888;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
            padding: 5px 10px;
        }

        .breadcrumb-item:hover {
            color: #fff;
        }

        .breadcrumb-separator {
            color: #555;
            font-weight: 900;
            font-size: 18px;
        }

        .breadcrumb-item.active {
            color: #fff;
            background: #333;
            box-shadow: 
                inset 0 2px 5px rgba(0,0,0,0.5),
                0 1px 0 rgba(255,255,255,0.1);
        }

        /* Tab Navigation */
        .tab-container {
            background: #1a1a1a;
            display: flex;
            border-bottom: 5px solid #000;
            position: relative;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: #444 #222;
        }

        .tab-container::-webkit-scrollbar {
            height: 8px;
        }

        .tab-container::-webkit-scrollbar-track {
            background: #222;
        }

        .tab-container::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 0;
        }

        .tab {
            min-width: 150px;
            padding: 20px 30px;
            background: #2a2a2a;
            border: none;
            color: #888;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            border-right: 2px solid #000;
            box-shadow: inset 0 -5px 0 transparent;
        }

        .tab:hover {
            background: #333;
            color: #aaa;
        }

        .tab.active {
            background: #444;
            color: #fff;
            box-shadow: 
                inset 0 -5px 0 #666,
                0 5px 20px rgba(0,0,0,0.5);
            transform: translateY(-2px);
        }

        .tab-close {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #222;
            color: #666;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .tab:hover .tab-close {
            opacity: 1;
        }

        .tab-close:hover {
            background: #ff4444;
            color: #fff;
        }

        /* Navigation Memory */
        .nav-memory {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #2a2a2a;
            border: 3px solid #444;
            width: 60px;
            padding: 10px 0;
            box-shadow: 
                -5px 5px 20px rgba(0,0,0,0.5),
                inset 0 0 20px rgba(0,0,0,0.3);
            z-index: 200;
            transition: width 0.3s ease;
        }

        .nav-memory:hover {
            width: 250px;
        }

        .memory-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .memory-item:hover {
            background: #333;
        }

        .memory-icon {
            width: 30px;
            height: 30px;
            background: #444;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #aaa;
            flex-shrink: 0;
        }

        .memory-text {
            margin-left: 15px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-size: 14px;
            color: #ccc;
        }

        .nav-memory:hover .memory-text {
            opacity: 1;
        }

        /* Content Area */
        .content {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }

        .content-block {
            background: #2a2a2a;
            border: 3px solid #444;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 
                5px 5px 20px rgba(0,0,0,0.5),
                inset 0 0 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .content-block::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #666, #444, #666);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .content-block:hover::before {
            opacity: 1;
        }

        h1 {
            font-size: 48px;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: -2px;
            margin-bottom: 20px;
            text-shadow: 3px 3px 0 #000;
        }

        p {
            font-size: 16px;
            line-height: 1.8;
            color: #aaa;
            margin-bottom: 15px;
        }

        /* State indicator */
        .state-indicator {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #333;
            border: 2px solid #555;
            padding: 15px 25px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #888;
            box-shadow: 0 5px 20px rgba(0,0,0,0.5);
            z-index: 200;
        }

        /* Animations */
        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .brutalist-nav {
            animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-block {
            animation: fadeIn 0.8s ease-out;
        }
    </style>
</head>
<body>
    <!-- Brutalist Navigation Center -->
    <nav class="brutalist-nav">
        <!-- Top Bar -->
        <div class="nav-top-bar">
            <div class="logo-block">BN</div>
            
            <div class="search-container">
                <input type="text" class="search-input" placeholder="SEARCH NAVIGATION..." />
                <button class="search-button">→</button>
            </div>
            
            <div class="quick-actions">
                <button class="action-btn" title="Dashboard">⊞</button>
                <button class="action-btn" title="Settings">⚙</button>
                <button class="action-btn" title="Notifications">◉</button>
                <button class="action-btn" title="Profile">▣</button>
            </div>
        </div>
        
        <!-- Breadcrumb Bar -->
        <div class="breadcrumb-bar">
            <a href="#" class="breadcrumb-item">HOME</a>
            <span class="breadcrumb-separator">▸</span>
            <a href="#" class="breadcrumb-item">PROJECTS</a>
            <span class="breadcrumb-separator">▸</span>
            <a href="#" class="breadcrumb-item">BRUTALISM</a>
            <span class="breadcrumb-separator">▸</span>
            <a href="#" class="breadcrumb-item active">NAVIGATION</a>
        </div>
        
        <!-- Tab Container -->
        <div class="tab-container">
            <button class="tab active">
                OVERVIEW
                <button class="tab-close">×</button>
            </button>
            <button class="tab">
                COMPONENTS
                <button class="tab-close">×</button>
            </button>
            <button class="tab">
                DOCUMENTATION
                <button class="tab-close">×</button>
            </button>
            <button class="tab">
                EXAMPLES
                <button class="tab-close">×</button>
            </button>
            <button class="tab">
                RESOURCES
                <button class="tab-close">×</button>
            </button>
        </div>
    </nav>
    
    <!-- Navigation Memory Panel -->
    <div class="nav-memory">
        <div class="memory-item">
            <div class="memory-icon">1</div>
            <span class="memory-text">Dashboard Overview</span>
        </div>
        <div class="memory-item">
            <div class="memory-icon">2</div>
            <span class="memory-text">Project Settings</span>
        </div>
        <div class="memory-item">
            <div class="memory-icon">3</div>
            <span class="memory-text">Component Library</span>
        </div>
        <div class="memory-item">
            <div class="memory-icon">4</div>
            <span class="memory-text">User Analytics</span>
        </div>
        <div class="memory-item">
            <div class="memory-icon">5</div>
            <span class="memory-text">System Config</span>
        </div>
    </div>
    
    <!-- Content Area -->
    <main class="content">
        <div class="content-block">
            <h1>BRUTALIST NAVIGATION</h1>
            <p>A monolithic navigation system built with the principles of architectural brutalism. Raw concrete aesthetics meet functional navigation patterns.</p>
            <p>This navigation center combines multiple UI patterns into a cohesive, imposing structure that dominates the interface with purposeful weight and presence.</p>
        </div>
        
        <div class="content-block">
            <h1>FEATURES</h1>
            <p>▪ Multi-level navigation hierarchy with tabs, breadcrumbs, and quick actions</p>
            <p>▪ Integrated search functionality with brutalist styling</p>
            <p>▪ Navigation state memory for quick access to recent locations</p>
            <p>▪ Heavy, architectural animations that emphasize structure</p>
            <p>▪ Concrete texture overlays and stark geometric forms</p>
        </div>
    </main>
    
    <!-- State Indicator -->
    <div class="state-indicator">
        Navigation State: Active | Memory: 5 Items | Tabs: 5 Open
    </div>
    
    <script>
        // Tab functionality
        const tabs = document.querySelectorAll('.tab');
        const tabCloseButtons = document.querySelectorAll('.tab-close');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                if (!e.target.classList.contains('tab-close')) {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Update state indicator
                    updateStateIndicator();
                }
            });
        });
        
        // Tab close functionality
        tabCloseButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const tab = btn.parentElement;
                tab.style.transform = 'translateX(100%)';
                tab.style.opacity = '0';
                setTimeout(() => {
                    tab.remove();
                    updateStateIndicator();
                }, 300);
            });
        });
        
        // Search functionality
        const searchInput = document.querySelector('.search-input');
        const searchButton = document.querySelector('.search-button');
        
        searchButton.addEventListener('click', () => {
            if (searchInput.value.trim()) {
                // Add to navigation memory
                addToMemory(searchInput.value);
                searchInput.value = '';
            }
        });
        
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && searchInput.value.trim()) {
                addToMemory(searchInput.value);
                searchInput.value = '';
            }
        });
        
        // Navigation memory
        const memoryItems = document.querySelectorAll('.memory-item');
        let memoryIndex = 6;
        
        function addToMemory(text) {
            const navMemory = document.querySelector('.nav-memory');
            const newItem = document.createElement('div');
            newItem.className = 'memory-item';
            newItem.innerHTML = `
                <div class="memory-icon">${memoryIndex}</div>
                <span class="memory-text">${text}</span>
            `;
            navMemory.appendChild(newItem);
            memoryIndex++;
            
            // Add click handler
            newItem.addEventListener('click', () => {
                console.log('Navigating to:', text);
            });
            
            updateStateIndicator();
        }
        
        // Memory item clicks
        memoryItems.forEach(item => {
            item.addEventListener('click', () => {
                const text = item.querySelector('.memory-text').textContent;
                console.log('Navigating to:', text);
            });
        });
        
        // Quick action buttons
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.getAttribute('title');
                console.log('Quick action:', action);
                
                // Visual feedback
                btn.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 200);
            });
        });
        
        // Breadcrumb navigation
        const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
        breadcrumbItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                breadcrumbItems.forEach(bc => bc.classList.remove('active'));
                item.classList.add('active');
            });
        });
        
        // Logo animation
        const logo = document.querySelector('.logo-block');
        logo.addEventListener('click', () => {
            logo.style.transform = 'rotate(180deg) scale(0.8)';
            setTimeout(() => {
                logo.style.transform = '';
            }, 600);
        });
        
        // Update state indicator
        function updateStateIndicator() {
            const indicator = document.querySelector('.state-indicator');
            const tabCount = document.querySelectorAll('.tab').length;
            const memoryCount = document.querySelectorAll('.memory-item').length;
            indicator.textContent = `Navigation State: Active | Memory: ${memoryCount} Items | Tabs: ${tabCount} Open`;
        }
        
        // Parallax effect on scroll
        let lastScrollY = 0;
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            const nav = document.querySelector('.brutalist-nav');
            
            if (scrollY > lastScrollY && scrollY > 100) {
                nav.style.transform = 'translateY(-50%)';
            } else {
                nav.style.transform = 'translateY(0)';
            }
            
            lastScrollY = scrollY;
        });
        
        // Initialize
        updateStateIndicator();
    </script>
</body>
</html>