<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cosmic Analytics Command Center - Iteration 24</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Starfield Background */
        .starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: radial-gradient(ellipse at center, #0a0a2e 0%, #000 100%);
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 4s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* Nebula Effect */
        .nebula {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.3;
            background: 
                radial-gradient(ellipse at 20% 30%, rgba(138, 43, 226, 0.4) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 70%, rgba(30, 144, 255, 0.4) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 50%, rgba(255, 20, 147, 0.3) 0%, transparent 70%);
            animation: nebulaPulse 20s ease-in-out infinite;
        }

        @keyframes nebulaPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.5; }
        }

        /* Main Container */
        .command-center {
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            font-size: 3em;
            background: linear-gradient(135deg, #00ffff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            margin-bottom: 10px;
            animation: glowPulse 3s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.3); }
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 0.9em;
            color: #0ff;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #0f0;
            box-shadow: 0 0 10px #0f0;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        /* Widget Base */
        .widget {
            background: rgba(10, 10, 46, 0.8);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 0 30px rgba(0, 255, 255, 0.3),
                inset 0 0 20px rgba(138, 43, 226, 0.1);
            transition: all 0.3s ease;
        }

        .widget:hover {
            border-color: rgba(255, 0, 255, 0.5);
            box-shadow: 
                0 0 50px rgba(255, 0, 255, 0.4),
                inset 0 0 30px rgba(138, 43, 226, 0.2);
            transform: translateY(-5px);
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid rgba(0, 255, 255, 0.3);
            padding-bottom: 10px;
        }

        .widget-title {
            font-size: 1.2em;
            color: #0ff;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .widget-icon {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 255, 255, 0.2);
            border-radius: 50%;
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Chart Containers */
        .chart-container {
            height: 300px;
            position: relative;
            margin: 10px 0;
        }

        /* Bar Chart */
        .bar-chart {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: 100%;
            padding: 10px;
        }

        .bar {
            width: 30px;
            background: linear-gradient(to top, #00ffff, #ff00ff);
            border-radius: 5px 5px 0 0;
            position: relative;
            animation: growBar 2s ease-out forwards;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        @keyframes growBar {
            from { height: 0; }
        }

        .bar-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8em;
            color: #0ff;
        }

        /* Line Chart */
        .line-chart {
            position: relative;
            height: 100%;
        }

        .line-path {
            stroke: #0ff;
            stroke-width: 3;
            fill: none;
            filter: drop-shadow(0 0 10px #0ff);
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 3s ease-out forwards;
        }

        @keyframes drawLine {
            to { stroke-dashoffset: 0; }
        }

        .data-point {
            fill: #ff00ff;
            stroke: #fff;
            stroke-width: 2;
            filter: drop-shadow(0 0 15px #ff00ff);
            animation: pulsePoint 2s infinite;
        }

        @keyframes pulsePoint {
            0%, 100% { r: 5; }
            50% { r: 8; }
        }

        /* Orbital Map */
        .orbital-map {
            position: relative;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .orbit {
            position: absolute;
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            animation: rotateOrbit 20s linear infinite;
        }

        @keyframes rotateOrbit {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .planet {
            position: absolute;
            border-radius: 50%;
            box-shadow: 0 0 30px currentColor;
        }

        .sun {
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, #ffff00, #ff8800);
            box-shadow: 0 0 60px #ff8800;
            animation: sunPulse 4s ease-in-out infinite;
        }

        @keyframes sunPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* KPI Metrics */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .kpi-card {
            background: rgba(138, 43, 226, 0.2);
            border: 1px solid rgba(138, 43, 226, 0.5);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .kpi-card:hover {
            background: rgba(138, 43, 226, 0.3);
            transform: scale(1.05);
        }

        .kpi-value {
            font-size: 2em;
            font-weight: bold;
            background: linear-gradient(135deg, #0ff, #f0f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 10px 0;
        }

        .kpi-label {
            font-size: 0.9em;
            color: #aaa;
            text-transform: uppercase;
        }

        .kpi-trend {
            font-size: 0.8em;
            color: #0f0;
            margin-top: 5px;
        }

        /* Heat Map */
        .heat-map {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 2px;
            padding: 10px;
        }

        .heat-cell {
            aspect-ratio: 1;
            border-radius: 3px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .heat-cell:hover {
            transform: scale(1.2);
            z-index: 10;
        }

        /* Prediction Graph */
        .prediction-container {
            position: relative;
            height: 300px;
            overflow: hidden;
        }

        .prediction-line {
            stroke: #ff00ff;
            stroke-width: 2;
            fill: none;
            stroke-dasharray: 5,5;
            filter: drop-shadow(0 0 10px #ff00ff);
        }

        .confidence-band {
            fill: rgba(255, 0, 255, 0.1);
            stroke: none;
        }

        /* Report Generator */
        .report-generator {
            background: rgba(30, 144, 255, 0.1);
            border: 1px solid rgba(30, 144, 255, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .report-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-button {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid #0ff;
            color: #0ff;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .control-button:hover {
            background: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        /* Holographic Display */
        .holo-display {
            position: relative;
            height: 200px;
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .holo-content {
            position: relative;
            transform-style: preserve-3d;
            animation: holoRotate 10s linear infinite;
        }

        @keyframes holoRotate {
            from { transform: rotateY(0deg); }
            to { transform: rotateY(360deg); }
        }

        .holo-layer {
            position: absolute;
            width: 150px;
            height: 150px;
            border: 2px solid rgba(0, 255, 255, 0.5);
            border-radius: 10px;
            transform-style: preserve-3d;
        }

        /* Scanner Lines */
        .scanner-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, transparent, #0ff, transparent);
            animation: scan 3s linear infinite;
        }

        @keyframes scan {
            from { top: 0; }
            to { top: 100%; }
        }

        /* Data Stream */
        .data-stream {
            height: 150px;
            overflow: hidden;
            font-family: monospace;
            font-size: 0.8em;
            color: #0f0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
        }

        .stream-line {
            margin: 2px 0;
            opacity: 0;
            animation: streamIn 0.5s forwards;
        }

        @keyframes streamIn {
            from { 
                opacity: 0;
                transform: translateX(-20px);
            }
            to { 
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .status-bar {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Starfield Background -->
    <div class="starfield" id="starfield"></div>
    <div class="nebula"></div>

    <div class="command-center">
        <!-- Header -->
        <header class="header">
            <h1>COSMIC ANALYTICS COMMAND CENTER</h1>
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>System: OPTIMAL</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>Data Feed: ACTIVE</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>AI Models: ONLINE</span>
                </div>
            </div>
        </header>

        <!-- Main Dashboard -->
        <div class="dashboard-grid">
            <!-- Stellar Performance Metrics -->
            <div class="widget">
                <div class="widget-header">
                    <h2 class="widget-title">Stellar Performance</h2>
                    <div class="widget-icon">★</div>
                </div>
                <div class="chart-container">
                    <div class="bar-chart" id="barChart"></div>
                </div>
            </div>

            <!-- Orbital Trajectories -->
            <div class="widget">
                <div class="widget-header">
                    <h2 class="widget-title">Orbital Analytics</h2>
                    <div class="widget-icon">◉</div>
                </div>
                <div class="chart-container">
                    <svg class="line-chart" viewBox="0 0 400 300">
                        <path class="line-path" d="M20,280 L80,200 L140,220 L200,150 L260,180 L320,100 L380,120" />
                        <circle class="data-point" cx="20" cy="280" r="5" />
                        <circle class="data-point" cx="80" cy="200" r="5" />
                        <circle class="data-point" cx="140" cy="220" r="5" />
                        <circle class="data-point" cx="200" cy="150" r="5" />
                        <circle class="data-point" cx="260" cy="180" r="5" />
                        <circle class="data-point" cx="320" cy="100" r="5" />
                        <circle class="data-point" cx="380" cy="120" r="5" />
                    </svg>
                </div>
            </div>

            <!-- Mission KPIs -->
            <div class="widget">
                <div class="widget-header">
                    <h2 class="widget-title">Mission KPIs</h2>
                    <div class="widget-icon">▲</div>
                </div>
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-label">Velocity</div>
                        <div class="kpi-value">42.7K</div>
                        <div class="kpi-trend">↑ 12.3%</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-label">Distance</div>
                        <div class="kpi-value">384M</div>
                        <div class="kpi-trend">↑ 8.5%</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-label">Efficiency</div>
                        <div class="kpi-value">94.2%</div>
                        <div class="kpi-trend">↑ 3.1%</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-label">Resources</div>
                        <div class="kpi-value">78.5%</div>
                        <div class="kpi-trend">↓ 2.4%</div>
                    </div>
                </div>
            </div>

            <!-- Cosmic Heat Map -->
            <div class="widget">
                <div class="widget-header">
                    <h2 class="widget-title">Sector Heat Map</h2>
                    <div class="widget-icon">◈</div>
                </div>
                <div class="heat-map" id="heatMap"></div>
            </div>

            <!-- Predictive Models -->
            <div class="widget">
                <div class="widget-header">
                    <h2 class="widget-title">Trajectory Predictions</h2>
                    <div class="widget-icon">⟳</div>
                </div>
                <div class="prediction-container">
                    <svg viewBox="0 0 400 300" style="width: 100%; height: 100%;">
                        <path class="confidence-band" d="M0,200 Q100,150 200,180 T400,100 L400,300 L0,300 Z" />
                        <path class="line-path" d="M0,250 Q100,200 200,230 T400,150" />
                        <path class="prediction-line" d="M200,230 Q300,180 400,150" />
                    </svg>
                </div>
            </div>

            <!-- Planetary System Map -->
            <div class="widget">
                <div class="widget-header">
                    <h2 class="widget-title">System Overview</h2>
                    <div class="widget-icon">☉</div>
                </div>
                <div class="orbital-map">
                    <div class="sun planet"></div>
                    <div class="orbit" style="width: 150px; height: 150px;">
                        <div class="planet" style="width: 20px; height: 20px; background: #87CEEB; top: -10px; left: 50%; margin-left: -10px;"></div>
                    </div>
                    <div class="orbit" style="width: 250px; height: 250px;">
                        <div class="planet" style="width: 30px; height: 30px; background: #FFA500; top: -15px; left: 50%; margin-left: -15px;"></div>
                    </div>
                    <div class="orbit" style="width: 350px; height: 350px;">
                        <div class="planet" style="width: 25px; height: 25px; background: #FF69B4; top: -12px; left: 50%; margin-left: -12px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Generator -->
        <div class="report-generator">
            <div class="widget-header">
                <h2 class="widget-title">Mission Report Generator</h2>
                <div class="widget-icon">⎙</div>
            </div>
            <div class="report-controls">
                <button class="control-button" onclick="generateReport('daily')">Daily Report</button>
                <button class="control-button" onclick="generateReport('weekly')">Weekly Analysis</button>
                <button class="control-button" onclick="generateReport('monthly')">Monthly Overview</button>
                <button class="control-button" onclick="generateReport('custom')">Custom Range</button>
            </div>
            <div class="holo-display">
                <div class="holo-content">
                    <div class="holo-layer" style="transform: translateZ(30px);"></div>
                    <div class="holo-layer" style="transform: translateZ(0px);"></div>
                    <div class="holo-layer" style="transform: translateZ(-30px);"></div>
                </div>
                <div class="scanner-line"></div>
            </div>
        </div>

        <!-- Data Stream -->
        <div class="widget" style="margin-top: 20px;">
            <div class="widget-header">
                <h2 class="widget-title">Live Data Stream</h2>
                <div class="widget-icon">⟋</div>
            </div>
            <div class="data-stream" id="dataStream"></div>
        </div>
    </div>

    <script>
        // Generate Starfield
        function createStarfield() {
            const starfield = document.getElementById('starfield');
            const starCount = 200;
            
            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = Math.random() * 3 + 'px';
                star.style.height = star.style.width;
                star.style.animationDelay = Math.random() * 4 + 's';
                starfield.appendChild(star);
            }
        }

        // Generate Bar Chart
        function createBarChart() {
            const chart = document.getElementById('barChart');
            const data = [75, 45, 90, 60, 85, 70, 95];
            const labels = ['Alpha', 'Beta', 'Gamma', 'Delta', 'Epsilon', 'Zeta', 'Eta'];
            
            data.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.height = value + '%';
                bar.style.animationDelay = index * 0.1 + 's';
                
                const label = document.createElement('div');
                label.className = 'bar-label';
                label.textContent = labels[index];
                bar.appendChild(label);
                
                chart.appendChild(bar);
            });
        }

        // Generate Heat Map
        function createHeatMap() {
            const heatMap = document.getElementById('heatMap');
            
            for (let i = 0; i < 100; i++) {
                const cell = document.createElement('div');
                cell.className = 'heat-cell';
                const intensity = Math.random();
                const hue = (1 - intensity) * 240; // Blue to red
                cell.style.background = `hsl(${hue}, 100%, 50%)`;
                cell.style.opacity = 0.3 + intensity * 0.7;
                cell.title = `Sector ${i + 1}: ${Math.round(intensity * 100)}%`;
                heatMap.appendChild(cell);
            }
        }

        // Data Stream
        function addStreamLine() {
            const stream = document.getElementById('dataStream');
            const messages = [
                'SCANNING: Sector 7G... anomaly detected',
                'DATA: Processing 1.2TB of telemetry data',
                'ALERT: Solar wind velocity increasing',
                'UPDATE: Navigation systems recalibrated',
                'SYNC: Quantum entanglement established',
                'ANALYSIS: Pattern recognition complete',
                'STATUS: All systems nominal',
                'TRANSMISSION: Receiving data from probe X-47'
            ];
            
            const line = document.createElement('div');
            line.className = 'stream-line';
            line.textContent = `[${new Date().toTimeString().split(' ')[0]}] ${messages[Math.floor(Math.random() * messages.length)]}`;
            
            stream.insertBefore(line, stream.firstChild);
            
            // Keep only last 10 lines
            while (stream.children.length > 10) {
                stream.removeChild(stream.lastChild);
            }
        }

        // Report Generator
        function generateReport(type) {
            console.log(`Generating ${type} report...`);
            // Add visual feedback
            const buttons = document.querySelectorAll('.control-button');
            buttons.forEach(btn => {
                if (btn.textContent.toLowerCase().includes(type)) {
                    btn.style.background = 'rgba(0, 255, 0, 0.3)';
                    setTimeout(() => {
                        btn.style.background = 'rgba(0, 255, 255, 0.2)';
                    }, 1000);
                }
            });
        }

        // Initialize
        window.addEventListener('load', () => {
            createStarfield();
            createBarChart();
            createHeatMap();
            
            // Start data stream
            setInterval(addStreamLine, 2000);
            
            // Add initial stream lines
            for (let i = 0; i < 5; i++) {
                setTimeout(addStreamLine, i * 100);
            }
        });

        // Interactive hover effects
        document.querySelectorAll('.widget').forEach(widget => {
            widget.addEventListener('mouseenter', function() {
                this.style.borderColor = 'rgba(255, 0, 255, 0.5)';
            });
            
            widget.addEventListener('mouseleave', function() {
                this.style.borderColor = 'rgba(0, 255, 255, 0.3)';
            });
        });
    </script>
</body>
</html>