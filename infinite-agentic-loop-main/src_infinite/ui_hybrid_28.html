<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Krištáľový Sprievodca Formulárom - Sklenený Morfizmus Téma</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.15)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="1" fill="rgba(255,255,255,0.2)"/></svg>');
            animation: float 20s infinite linear;
            pointer-events: none;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-100vh) rotate(360deg); }
        }

        main {
            width: 90%;
            max-width: 600px;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 300;
            color: white;
            margin-bottom: 3rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .crystal-form-wizard {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3rem;
            position: relative;
        }

        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
        }

        .progress-step.active {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.6);
            color: white;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .progress-step.completed {
            background: rgba(76, 175, 80, 0.3);
            border-color: rgba(76, 175, 80, 0.6);
            color: white;
        }

        .progress-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-50%);
            z-index: 1;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, rgba(76, 175, 80, 0.6), rgba(255, 255, 255, 0.4));
            width: 0%;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 1px;
        }

        .form-step {
            display: none;
            animation: slideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-step.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .step-title {
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1.5rem;
            font-weight: 300;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select {
            width: 100%;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            outline: none;
            cursor: pointer;
        }

        .form-select option {
            background: rgba(102, 126, 234, 0.9);
            color: white;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .checkbox-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .checkbox-item input[type="checkbox"] {
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            position: relative;
        }

        .checkbox-item input[type="checkbox"]:checked {
            background: rgba(76, 175, 80, 0.6);
            border-color: rgba(76, 175, 80, 0.8);
        }

        .checkbox-item input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            gap: 1rem;
        }

        .nav-button {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 120px;
        }

        .nav-button.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-button.secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }

        .nav-button.primary {
            background: rgba(76, 175, 80, 0.3);
            color: white;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }

        .nav-button.primary:hover {
            background: rgba(76, 175, 80, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
        }

        .nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .save-indicator {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 20px;
            color: rgba(76, 175, 80, 1);
            font-size: 0.8rem;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .save-indicator.show {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <main>
        <h1>Krištáľový Sprievodca Formulárom - Sklenený Morfizmus Téma</h1>
        
        <div class="crystal-form-wizard">
            <div class="save-indicator" id="saveIndicator">Automaticky uložené ✓</div>
            
            <div class="progress-indicator">
                <div class="progress-line">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-step active" data-step="1">1</div>
                <div class="progress-step" data-step="2">2</div>
                <div class="progress-step" data-step="3">3</div>
                <div class="progress-step" data-step="4">4</div>
            </div>
            
            <!-- Krok 1: Osobné Informácie -->
            <div class="form-step active" id="step1">
                <h2 class="step-title">Osobné Informácie</h2>
                <div class="form-group">
                    <label class="form-label" for="firstName">Krstné Meno</label>
                    <input type="text" class="form-input" id="firstName" placeholder="Zadajte vaše krstné meno" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="lastName">Priezvisko</label>
                    <input type="text" class="form-input" id="lastName" placeholder="Zadajte vaše priezvisko" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="email">Emailová Adresa</label>
                    <input type="email" class="form-input" id="email" placeholder="Zadajte vašu emailovú adresu" required>
                </div>
            </div>
            
            <!-- Krok 2: Preferencie -->
            <div class="form-step" id="step2">
                <h2 class="step-title">Preferencie</h2>
                <div class="form-group">
                    <label class="form-label" for="category">Kategória</label>
                    <select class="form-select" id="category" required>
                        <option value="">Vyberte kategóriu</option>
                        <option value="business">Podnikanie</option>
                        <option value="personal">Osobné</option>
                        <option value="education">Vzdelávanie</option>
                        <option value="creative">Kreatívne</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Záujmy</label>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="interests" value="technology">
                            <span>Technológie</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="interests" value="design">
                            <span>Dizajn</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="interests" value="marketing">
                            <span>Marketing</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="interests" value="finance">
                            <span>Financie</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Krok 3: Dodatočné Detaily -->
            <div class="form-step" id="step3">
                <h2 class="step-title">Dodatočné Detaily</h2>
                <div class="form-group">
                    <label class="form-label" for="company">Spoločnosť</label>
                    <input type="text" class="form-input" id="company" placeholder="Zadajte názov vašej spoločnosti">
                </div>
                <div class="form-group">
                    <label class="form-label" for="message">Správa</label>
                    <textarea class="form-input" id="message" rows="4" placeholder="Povedzte nám viac o sebe..."></textarea>
                </div>
            </div>
            
            <!-- Krok 4: Kontrola -->
            <div class="form-step" id="step4">
                <h2 class="step-title">Kontrola a Odoslanie</h2>
                <div id="reviewContent">
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 1.5rem; border-radius: 12px; margin-bottom: 1rem;">
                        <h3 style="color: white; margin-bottom: 1rem;">Osobné Informácie</h3>
                        <p style="color: rgba(255, 255, 255, 0.9);">Skontrolujte vaše údaje pred odoslaním.</p>
                    </div>
                </div>
            </div>
            
            <div class="form-navigation">
                <button type="button" class="nav-button secondary" id="prevButton" disabled>Predchádzajúci</button>
                <button type="button" class="nav-button primary" id="nextButton">Ďalší</button>
            </div>
        </div>
    </main>

    <script>
        let currentStep = 1;
        const totalSteps = 4;
        
        const prevButton = document.getElementById('prevButton');
        const nextButton = document.getElementById('nextButton');
        const progressFill = document.getElementById('progressFill');
        const saveIndicator = document.getElementById('saveIndicator');

        function updateProgress() {
            const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
            progressFill.style.width = progress + '%';
            
            document.querySelectorAll('.progress-step').forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');
                
                if (stepNumber < currentStep) {
                    step.classList.add('completed');
                } else if (stepNumber === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        function showStep(step) {
            document.querySelectorAll('.form-step').forEach(s => s.classList.remove('active'));
            document.getElementById(`step${step}`).classList.add('active');
            
            prevButton.disabled = step === 1;
            nextButton.textContent = step === totalSteps ? 'Odoslať' : 'Ďalší';
        }

        function showSaveIndicator() {
            saveIndicator.classList.add('show');
            setTimeout(() => {
                saveIndicator.classList.remove('show');
            }, 2000);
        }

        nextButton.addEventListener('click', () => {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
                updateProgress();
                showSaveIndicator();
            } else {
                alert('Formulár úspešne odoslaný!');
            }
        });

        prevButton.addEventListener('click', () => {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
                updateProgress();
            }
        });

        document.querySelectorAll('.form-input, .form-select').forEach(input => {
            input.addEventListener('input', () => {
                setTimeout(() => {
                    showSaveIndicator();
                }, 1000);
            });
        });

        updateProgress();
    </script>
</body>
</html>
