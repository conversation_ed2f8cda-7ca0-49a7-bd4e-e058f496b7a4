<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metamorphic Crystal Reality Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a0f2e 0%, #2d1b3e 25%, #3e2853 50%, #2d1b3e 75%, #1a0f2e 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Crystalline background effects */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(147, 102, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 40%),
                radial-gradient(circle at 80% 80%, rgba(168, 50, 121, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 30% 70%, rgba(76, 175, 80, 0.06) 0%, transparent 40%);
            pointer-events: none;
            z-index: -1;
        }

        /* Metamorphic crystal formations */
        .crystal-formation {
            position: absolute;
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(147, 102, 255, 0.2));
            clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
            animation: metamorphicGrowth 8s infinite;
        }

        .crystal-formation:nth-child(1) { top: 10%; left: 15%; animation-delay: 0s; }
        .crystal-formation:nth-child(2) { top: 25%; right: 20%; animation-delay: 2s; }
        .crystal-formation:nth-child(3) { bottom: 30%; left: 10%; animation-delay: 4s; }
        .crystal-formation:nth-child(4) { bottom: 15%; right: 15%; animation-delay: 6s; }

        @keyframes metamorphicGrowth {
            0%, 100% { transform: scale(0.8) rotate(0deg); opacity: 0.3; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
        }

        main {
            position: relative;
            z-index: 1;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #ffffff;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #9366ff, #ffffff, #a832a0, #4caf50);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: crystallineShimmer 4s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(147, 102, 255, 0.3);
        }

        @keyframes crystallineShimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hybrid-component {
            background: linear-gradient(135deg, 
                rgba(26, 15, 46, 0.9) 0%, 
                rgba(45, 27, 62, 0.9) 25%, 
                rgba(62, 40, 83, 0.9) 50%, 
                rgba(45, 27, 62, 0.9) 75%, 
                rgba(26, 15, 46, 0.9) 100%);
            border: 2px solid rgba(147, 102, 255, 0.3);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 0 30px rgba(147, 102, 255, 0.2),
                inset 0 0 20px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
        }

        /* Crystalline border effects */
        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #9366ff, #ffffff, #a832a0, #4caf50);
            border-radius: 20px;
            z-index: -1;
            opacity: 0.6;
            animation: crystallineBorder 3s linear infinite;
        }

        @keyframes crystallineBorder {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Reality Interface Controls Layout */
        .reality-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* AR Visualization Panel */
        .ar-visualization {
            grid-column: 1 / 2;
            grid-row: 1 / 2;
            background: linear-gradient(135deg, rgba(147, 102, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(147, 102, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .ar-visualization::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
            animation: crystallineRefraction 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes crystallineRefraction {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        .ar-title {
            color: #9366ff;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .crystal-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #9366ff, #ffffff);
            clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }

        /* 3D Manipulation Controls */
        .manipulation-3d {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            background: linear-gradient(135deg, rgba(168, 50, 121, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(168, 50, 121, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
        }

        /* Spatial Mapping Interface */
        .spatial-mapping {
            grid-column: 1 / 3;
            grid-row: 2 / 3;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
        }

        /* Crystal Matrix Display */
        .crystal-matrix {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .matrix-node {
            aspect-ratio: 1;
            background: linear-gradient(45deg, rgba(147, 102, 255, 0.3), rgba(255, 255, 255, 0.1));
            border: 1px solid rgba(147, 102, 255, 0.5);
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .matrix-node:hover {
            transform: scale(1.2);
            background: linear-gradient(45deg, rgba(147, 102, 255, 0.6), rgba(255, 255, 255, 0.3));
            box-shadow: 0 0 15px rgba(147, 102, 255, 0.5);
        }

        .matrix-node.active {
            background: linear-gradient(45deg, #9366ff, #ffffff);
            animation: metamorphicPulse 1s ease-in-out infinite;
        }

        @keyframes metamorphicPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.3); }
        }

        /* Reality Anchors */
        .reality-anchors {
            grid-column: 1 / 2;
            grid-row: 3 / 4;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
        }

        /* Dimensional Controls */
        .dimensional-controls {
            grid-column: 2 / 3;
            grid-row: 3 / 4;
            background: linear-gradient(135deg, rgba(233, 30, 99, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(233, 30, 99, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
        }

        /* Control Elements */
        .crystal-slider {
            width: 100%;
            height: 8px;
            background: linear-gradient(90deg, rgba(147, 102, 255, 0.2), rgba(255, 255, 255, 0.1));
            border-radius: 4px;
            margin: 1rem 0;
            position: relative;
            cursor: pointer;
        }

        .crystal-slider::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 30%;
            width: 16px;
            height: 16px;
            background: linear-gradient(45deg, #9366ff, #ffffff);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(147, 102, 255, 0.5);
            transition: all 0.3s ease;
        }

        .crystal-button {
            background: linear-gradient(135deg, rgba(147, 102, 255, 0.2), rgba(255, 255, 255, 0.1));
            border: 1px solid rgba(147, 102, 255, 0.5);
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin: 0.5rem;
        }

        .crystal-button:hover {
            background: linear-gradient(135deg, rgba(147, 102, 255, 0.4), rgba(255, 255, 255, 0.2));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(147, 102, 255, 0.3);
        }

        .crystal-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .crystal-button:hover::before {
            left: 100%;
        }

        /* Environment Controls */
        .environment-sphere {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(147, 102, 255, 0.1));
            border: 2px solid rgba(147, 102, 255, 0.5);
            margin: 1rem auto;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .environment-sphere:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(147, 102, 255, 0.5);
        }

        .environment-sphere::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 20%;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #ffffff, rgba(147, 102, 255, 0.8));
            border-radius: 50%;
            animation: sphereHighlight 3s ease-in-out infinite;
        }

        @keyframes sphereHighlight {
            0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.8; }
            50% { transform: translate(20px, 20px) scale(1.5); opacity: 0.3; }
        }

        /* Mixed Reality Coordination Hub */
        .coordination-hub {
            margin-top: 2rem;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(147, 102, 255, 0.1));
            border: 2px solid rgba(147, 102, 255, 0.4);
            border-radius: 20px;
            padding: 2rem;
            position: relative;
        }

        .coordination-hub::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(147, 102, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: coordinationPulse 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes coordinationPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
        }

        .sync-indicators {
            display: flex;
            justify-content: space-around;
            margin-top: 1rem;
        }

        .sync-indicator {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, rgba(147, 102, 255, 0.3), rgba(255, 255, 255, 0.1));
            border: 2px solid rgba(147, 102, 255, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-weight: bold;
            animation: syncRotation 2s linear infinite;
        }

        .sync-indicator:nth-child(odd) {
            animation-direction: reverse;
        }

        @keyframes syncRotation {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Pressure visualization */
        .pressure-gauge {
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, 
                rgba(76, 175, 80, 0.3) 0%, 
                rgba(255, 193, 7, 0.3) 30%, 
                rgba(255, 152, 0, 0.3) 60%, 
                rgba(244, 67, 54, 0.3) 100%);
            border-radius: 10px;
            position: relative;
            margin: 1rem 0;
        }

        .pressure-gauge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 40%;
            height: 100%;
            background: linear-gradient(90deg, rgba(76, 175, 80, 0.8), rgba(255, 193, 7, 0.8));
            border-radius: 10px;
            animation: pressureFluctuation 3s ease-in-out infinite;
        }

        @keyframes pressureFluctuation {
            0%, 100% { width: 40%; }
            50% { width: 70%; }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .reality-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(5, auto);
            }

            .ar-visualization { grid-column: 1; grid-row: 1; }
            .manipulation-3d { grid-column: 1; grid-row: 2; }
            .spatial-mapping { grid-column: 1; grid-row: 3; }
            .reality-anchors { grid-column: 1; grid-row: 4; }
            .dimensional-controls { grid-column: 1; grid-row: 5; }

            h1 { font-size: 2rem; }
            .hybrid-component { padding: 1rem; }
        }

        /* Accessibility enhancements */
        .crystal-button:focus,
        .matrix-node:focus,
        .crystal-slider:focus {
            outline: 2px solid #9366ff;
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <div class="crystal-formation"></div>
    <div class="crystal-formation"></div>
    <div class="crystal-formation"></div>
    <div class="crystal-formation"></div>

    <main>
        <h1>Reality Interface - Metamorphic Crystal Theme</h1>
        <div class="hybrid-component">
            <div class="reality-grid">
                <!-- AR Visualization Panel -->
                <div class="ar-visualization">
                    <div class="ar-title">
                        <div class="crystal-icon" aria-hidden="true"></div>
                        AR Crystal Visualization
                    </div>
                    <div class="crystal-matrix" role="grid" aria-label="Spatial Crystal Matrix">
                        <div class="matrix-node active" role="gridcell" tabindex="0" aria-label="Crystal node 1, active"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Crystal node 2"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Crystal node 3"></div>
                        <div class="matrix-node active" role="gridcell" tabindex="0" aria-label="Crystal node 4, active"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Crystal node 5"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Crystal node 6"></div>
                        <div class="matrix-node active" role="gridcell" tabindex="0" aria-label="Crystal node 7, active"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Crystal node 8"></div>
                    </div>
                    <div class="crystal-button" role="button" tabindex="0">Activate AR Overlay</div>
                    <div class="crystal-button" role="button" tabindex="0">Scan Environment</div>
                </div>

                <!-- 3D Manipulation Controls -->
                <div class="manipulation-3d">
                    <div class="ar-title">
                        <div class="crystal-icon" aria-hidden="true"></div>
                        3D Crystal Manipulation
                    </div>
                    <div class="environment-sphere" role="button" tabindex="0" aria-label="3D manipulation sphere"></div>
                    <div class="crystal-slider" role="slider" aria-label="Rotation control" aria-valuemin="0" aria-valuemax="100" aria-valuenow="30"></div>
                    <div class="crystal-button" role="button" tabindex="0">Transform Object</div>
                    <div class="crystal-button" role="button" tabindex="0">Lock Position</div>
                </div>

                <!-- Spatial Mapping Interface -->
                <div class="spatial-mapping">
                    <div class="ar-title">
                        <div class="crystal-icon" aria-hidden="true"></div>
                        Crystal Lattice Spatial Mapping
                    </div>
                    <div class="pressure-gauge" role="progressbar" aria-label="Metamorphic pressure" aria-valuemin="0" aria-valuemax="100" aria-valuenow="40">
                        <span class="sr-only">Metamorphic pressure at 40%</span>
                    </div>
                    <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                        <div class="crystal-button" role="button" tabindex="0">Map Surface</div>
                        <div class="crystal-button" role="button" tabindex="0">Analyze Depth</div>
                        <div class="crystal-button" role="button" tabindex="0">Generate Lattice</div>
                        <div class="crystal-button" role="button" tabindex="0">Export Coordinates</div>
                    </div>
                </div>

                <!-- Reality Anchors -->
                <div class="reality-anchors">
                    <div class="ar-title">
                        <div class="crystal-icon" aria-hidden="true"></div>
                        Crystal Formation Anchors
                    </div>
                    <div class="crystal-matrix" role="grid" aria-label="Reality anchor points" style="grid-template-columns: repeat(4, 1fr); margin: 1rem 0;">
                        <div class="matrix-node active" role="gridcell" tabindex="0" aria-label="Anchor point 1, set"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Anchor point 2"></div>
                        <div class="matrix-node active" role="gridcell" tabindex="0" aria-label="Anchor point 3, set"></div>
                        <div class="matrix-node" role="gridcell" tabindex="0" aria-label="Anchor point 4"></div>
                    </div>
                    <div class="crystal-button" role="button" tabindex="0">Set Anchor</div>
                    <div class="crystal-button" role="button" tabindex="0">Clear All</div>
                </div>

                <!-- Dimensional Controls -->
                <div class="dimensional-controls">
                    <div class="ar-title">
                        <div class="crystal-icon" aria-hidden="true"></div>
                        Crystalline Axis Control
                    </div>
                    <div class="crystal-slider" role="slider" aria-label="X-axis crystal alignment" aria-valuemin="0" aria-valuemax="100" aria-valuenow="30"></div>
                    <div class="crystal-slider" role="slider" aria-label="Y-axis crystal alignment" aria-valuemin="0" aria-valuemax="100" aria-valuenow="60"></div>
                    <div class="crystal-slider" role="slider" aria-label="Z-axis crystal alignment" aria-valuemin="0" aria-valuemax="100" aria-valuenow="45"></div>
                    <div class="crystal-button" role="button" tabindex="0">Apply Transform</div>
                </div>
            </div>

            <!-- Mixed Reality Coordination Hub -->
            <div class="coordination-hub">
                <div class="ar-title" style="text-align: center; margin-bottom: 1.5rem;">
                    <div class="crystal-icon" aria-hidden="true"></div>
                    Crystal Synchronization Hub
                </div>
                <div class="sync-indicators">
                    <div class="sync-indicator" aria-label="AR sync">AR</div>
                    <div class="sync-indicator" aria-label="3D sync">3D</div>
                    <div class="sync-indicator" aria-label="Spatial sync">SP</div>
                    <div class="sync-indicator" aria-label="Anchor sync">AN</div>
                    <div class="sync-indicator" aria-label="Dimensional sync">DM</div>
                </div>
                <div style="text-align: center; margin-top: 1.5rem;">
                    <div class="crystal-button" role="button" tabindex="0">Synchronize All Systems</div>
                    <div class="crystal-button" role="button" tabindex="0">Export Configuration</div>
                    <div class="crystal-button" role="button" tabindex="0">Save Crystal State</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Metamorphic Crystal Reality Interface System
        class MetamorphicCrystalRealityInterface {
            constructor() {
                this.crystalState = {
                    arActive: false,
                    manipulationMode: 'transform',
                    spatialMapping: false,
                    anchorPoints: [],
                    dimensionalAlignment: { x: 30, y: 60, z: 45 },
                    pressure: 40,
                    temperature: 25
                };
                
                this.initializeInterface();
                this.bindEvents();
                this.startMetamorphicAnimations();
            }

            initializeInterface() {
                // Initialize crystal matrix interactions
                this.initializeCrystalMatrix();
                
                // Setup 3D manipulation sphere
                this.initialize3DManipulation();
                
                // Configure spatial mapping
                this.initializeSpatialMapping();
                
                // Setup reality anchors
                this.initializeRealityAnchors();
                
                // Initialize dimensional controls
                this.initializeDimensionalControls();
                
                // Start synchronization hub
                this.initializeSyncHub();
            }

            initializeCrystalMatrix() {
                const matrixNodes = document.querySelectorAll('.matrix-node');
                matrixNodes.forEach((node, index) => {
                    node.addEventListener('click', () => this.toggleCrystalNode(node, index));
                    node.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.toggleCrystalNode(node, index);
                        }
                    });
                });
            }

            toggleCrystalNode(node, index) {
                node.classList.toggle('active');
                this.triggerMetamorphicTransformation(node);
                this.updateCrystalResonance();
                
                // Update AR visualization
                if (this.crystalState.arActive) {
                    this.updateAROverlay();
                }
            }

            triggerMetamorphicTransformation(element) {
                element.style.transform = 'scale(1.5) rotate(180deg)';
                element.style.background = 'linear-gradient(45deg, #9366ff, #ffffff, #a832a0)';
                
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.background = '';
                }, 500);
                
                // Create pressure wave effect
                this.createPressureWave(element);
            }

            createPressureWave(origin) {
                const wave = document.createElement('div');
                wave.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 10px;
                    height: 10px;
                    background: radial-gradient(circle, rgba(147, 102, 255, 0.6), transparent);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                    animation: pressureExpansion 1s ease-out forwards;
                `;
                
                const rect = origin.getBoundingClientRect();
                wave.style.left = rect.left + rect.width / 2 + 'px';
                wave.style.top = rect.top + rect.height / 2 + 'px';
                
                document.body.appendChild(wave);
                
                setTimeout(() => wave.remove(), 1000);
            }

            initialize3DManipulation() {
                const sphere = document.querySelector('.environment-sphere');
                let isDragging = false;
                let startX, startY;

                sphere.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    sphere.style.cursor = 'grabbing';
                });

                document.addEventListener('mousemove', (e) => {
                    if (!isDragging) return;
                    
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    
                    const rotationX = deltaY * 0.5;
                    const rotationY = deltaX * 0.5;
                    
                    sphere.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg) scale(1.1)`;
                    
                    this.updateDimensionalAlignment(rotationX, rotationY);
                });

                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        isDragging = false;
                        sphere.style.cursor = 'pointer';
                        sphere.style.transform = '';
                    }
                });
            }

            updateDimensionalAlignment(rotX, rotY) {
                this.crystalState.dimensionalAlignment.x = Math.max(0, Math.min(100, 50 + rotX));
                this.crystalState.dimensionalAlignment.y = Math.max(0, Math.min(100, 50 + rotY));
                
                this.updateDimensionalSliders();
                this.triggerCrystallineResonance();
            }

            updateDimensionalSliders() {
                const sliders = document.querySelectorAll('.dimensional-controls .crystal-slider');
                sliders.forEach((slider, index) => {
                    const axis = ['x', 'y', 'z'][index];
                    const value = this.crystalState.dimensionalAlignment[axis];
                    slider.style.setProperty('--slider-value', value + '%');
                    
                    if (slider.querySelector('::before')) {
                        slider.style.setProperty('--slider-position', value + '%');
                    }
                });
            }

            initializeSpatialMapping() {
                const pressureGauge = document.querySelector('.pressure-gauge');
                
                // Simulate dynamic pressure changes
                setInterval(() => {
                    this.crystalState.pressure = Math.max(20, Math.min(80, 
                        this.crystalState.pressure + (Math.random() - 0.5) * 10));
                    
                    pressureGauge.style.setProperty('--pressure-level', this.crystalState.pressure + '%');
                    this.updateMetamorphicState();
                }, 2000);
            }

            updateMetamorphicState() {
                const pressure = this.crystalState.pressure;
                let metamorphicPhase;
                
                if (pressure < 30) {
                    metamorphicPhase = 'contact';
                } else if (pressure < 60) {
                    metamorphicPhase = 'regional';
                } else {
                    metamorphicPhase = 'dynamic';
                }
                
                this.applyCrystallineTransformation(metamorphicPhase);
            }

            applyCrystallineTransformation(phase) {
                const components = document.querySelectorAll('.reality-grid > div');
                
                components.forEach(component => {
                    switch (phase) {
                        case 'contact':
                            component.style.filter = 'hue-rotate(0deg) brightness(1)';
                            break;
                        case 'regional':
                            component.style.filter = 'hue-rotate(60deg) brightness(1.1)';
                            break;
                        case 'dynamic':
                            component.style.filter = 'hue-rotate(120deg) brightness(1.2)';
                            break;
                    }
                });
            }

            initializeRealityAnchors() {
                const anchorNodes = document.querySelectorAll('.reality-anchors .matrix-node');
                
                anchorNodes.forEach((node, index) => {
                    node.addEventListener('click', () => {
                        if (node.classList.contains('active')) {
                            this.removeAnchor(index);
                        } else {
                            this.setAnchor(index, node);
                        }
                    });
                });
            }

            setAnchor(index, node) {
                node.classList.add('active');
                this.crystalState.anchorPoints.push({
                    id: index,
                    position: { x: Math.random() * 100, y: Math.random() * 100, z: Math.random() * 100 },
                    stability: Math.random() * 0.5 + 0.5
                });
                
                this.createAnchorFormation(node);
                this.updateSpatialStability();
            }

            removeAnchor(index) {
                const anchorNodes = document.querySelectorAll('.reality-anchors .matrix-node');
                anchorNodes[index].classList.remove('active');
                
                this.crystalState.anchorPoints = this.crystalState.anchorPoints.filter(anchor => anchor.id !== index);
                this.updateSpatialStability();
            }

            createAnchorFormation(node) {
                // Create visual anchor formation effect
                for (let i = 0; i < 6; i++) {
                    const crystal = document.createElement('div');
                    crystal.style.cssText = `
                        position: absolute;
                        width: 8px;
                        height: 8px;
                        background: linear-gradient(45deg, #9366ff, #ffffff);
                        clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
                        animation: anchorCrystalFloat 2s ease-in-out ${i * 0.2}s infinite;
                        pointer-events: none;
                    `;
                    
                    const rect = node.getBoundingClientRect();
                    crystal.style.left = rect.left + Math.random() * 40 - 20 + 'px';
                    crystal.style.top = rect.top + Math.random() * 40 - 20 + 'px';
                    
                    document.body.appendChild(crystal);
                    
                    setTimeout(() => crystal.remove(), 2000);
                }
            }

            updateSpatialStability() {
                const stability = this.crystalState.anchorPoints.length > 0 
                    ? this.crystalState.anchorPoints.reduce((acc, anchor) => acc + anchor.stability, 0) / this.crystalState.anchorPoints.length
                    : 0;
                
                this.updateCrystallineCoherence(stability);
            }

            updateCrystallineCoherence(stability) {
                const syncIndicators = document.querySelectorAll('.sync-indicator');
                
                syncIndicators.forEach(indicator => {
                    const intensity = stability * 0.8 + 0.2;
                    indicator.style.background = `linear-gradient(45deg, 
                        rgba(147, 102, 255, ${intensity}), 
                        rgba(255, 255, 255, ${intensity * 0.5}))`;
                    indicator.style.animationDuration = (3 - stability * 2) + 's';
                });
            }

            initializeDimensionalControls() {
                const sliders = document.querySelectorAll('.dimensional-controls .crystal-slider');
                
                sliders.forEach((slider, index) => {
                    slider.addEventListener('click', (e) => {
                        const rect = slider.getBoundingClientRect();
                        const percentage = ((e.clientX - rect.left) / rect.width) * 100;
                        
                        const axis = ['x', 'y', 'z'][index];
                        this.crystalState.dimensionalAlignment[axis] = percentage;
                        
                        this.updateSliderPosition(slider, percentage);
                        this.triggerDimensionalResonance(axis, percentage);
                    });
                });
            }

            updateSliderPosition(slider, percentage) {
                slider.style.setProperty('--slider-position', percentage + '%');
                
                // Update visual feedback
                const beforeElement = window.getComputedStyle(slider, '::before');
                slider.style.setProperty('--slider-value', percentage + '%');
            }

            triggerDimensionalResonance(axis, value) {
                // Create dimensional wave effect
                const allComponents = document.querySelectorAll('.reality-grid > div');
                
                allComponents.forEach((component, index) => {
                    setTimeout(() => {
                        component.style.transform = `translateX(${Math.sin(value * 0.1) * 5}px) translateY(${Math.cos(value * 0.1) * 3}px)`;
                        
                        setTimeout(() => {
                            component.style.transform = '';
                        }, 300);
                    }, index * 100);
                });
                
                this.updateCrystalResonance();
            }

            initializeSyncHub() {
                const syncButton = document.querySelector('.coordination-hub .crystal-button');
                
                if (syncButton) {
                    syncButton.addEventListener('click', () => {
                        this.synchronizeAllSystems();
                    });
                }
                
                // Start continuous synchronization monitoring
                this.startSyncMonitoring();
            }

            synchronizeAllSystems() {
                // Create global synchronization effect
                const allElements = document.querySelectorAll('.matrix-node, .sync-indicator, .crystal-button');
                
                allElements.forEach((element, index) => {
                    setTimeout(() => {
                        element.style.animation = 'none';
                        element.offsetHeight; // Trigger reflow
                        element.style.animation = 'metamorphicPulse 0.5s ease-in-out';
                        
                        setTimeout(() => {
                            element.style.animation = '';
                        }, 500);
                    }, index * 50);
                });
                
                // Update all system states
                this.updateAllSystemStates();
                this.createGlobalResonanceEffect();
            }

            startSyncMonitoring() {
                setInterval(() => {
                    this.updateSyncIndicators();
                    this.maintainCrystallineStability();
                }, 1000);
            }

            updateSyncIndicators() {
                const indicators = document.querySelectorAll('.sync-indicator');
                const systems = ['AR', '3D', 'SP', 'AN', 'DM'];
                
                indicators.forEach((indicator, index) => {
                    const systemHealth = Math.random() * 0.3 + 0.7; // 70-100% health
                    const color = systemHealth > 0.9 ? '#4caf50' : systemHealth > 0.7 ? '#ffc107' : '#f44336';
                    
                    indicator.style.borderColor = color;
                    indicator.style.boxShadow = `0 0 10px ${color}`;
                });
            }

            updateCrystalResonance() {
                // Calculate overall system resonance
                const activeNodes = document.querySelectorAll('.matrix-node.active').length;
                const anchorCount = this.crystalState.anchorPoints.length;
                const dimensionalStability = Object.values(this.crystalState.dimensionalAlignment)
                    .reduce((acc, val) => acc + Math.abs(val - 50), 0) / 150;
                
                const resonance = (activeNodes * 0.3 + anchorCount * 0.4 + (1 - dimensionalStability) * 0.3);
                
                this.applyCrystallineResonance(resonance);
            }

            applyCrystallineResonance(intensity) {
                const component = document.querySelector('.hybrid-component');
                
                if (intensity > 0.7) {
                    component.style.boxShadow = `
                        0 0 30px rgba(147, 102, 255, ${intensity}),
                        inset 0 0 20px rgba(255, 255, 255, ${intensity * 0.3}),
                        0 0 60px rgba(147, 102, 255, ${intensity * 0.5})
                    `;
                } else {
                    component.style.boxShadow = `
                        0 0 30px rgba(147, 102, 255, 0.2),
                        inset 0 0 20px rgba(255, 255, 255, 0.05)
                    `;
                }
            }

            createGlobalResonanceEffect() {
                // Create expanding resonance wave
                const wave = document.createElement('div');
                wave.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    width: 50px;
                    height: 50px;
                    background: radial-gradient(circle, rgba(147, 102, 255, 0.4), transparent 70%);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 9999;
                    animation: globalResonance 2s ease-out forwards;
                    transform: translate(-50%, -50%);
                `;
                
                document.body.appendChild(wave);
                
                setTimeout(() => wave.remove(), 2000);
            }

            updateAllSystemStates() {
                // Synchronize all subsystem states
                this.crystalState.arActive = true;
                this.crystalState.spatialMapping = true;
                
                // Update UI to reflect synchronized state
                const buttons = document.querySelectorAll('.crystal-button');
                buttons.forEach(button => {
                    button.style.background = 'linear-gradient(135deg, rgba(147, 102, 255, 0.4), rgba(255, 255, 255, 0.2))';
                    
                    setTimeout(() => {
                        button.style.background = '';
                    }, 1000);
                });
            }

            maintainCrystallineStability() {
                // Auto-adjust system parameters to maintain stability
                const targetPressure = 50;
                const pressureDiff = this.crystalState.pressure - targetPressure;
                
                if (Math.abs(pressureDiff) > 20) {
                    this.crystalState.pressure += pressureDiff > 0 ? -2 : 2;
                    this.updateMetamorphicState();
                }
                
                // Maintain dimensional alignment
                Object.keys(this.crystalState.dimensionalAlignment).forEach(axis => {
                    const current = this.crystalState.dimensionalAlignment[axis];
                    const target = 50;
                    const diff = target - current;
                    
                    if (Math.abs(diff) > 30) {
                        this.crystalState.dimensionalAlignment[axis] += diff * 0.1;
                        this.updateDimensionalSliders();
                    }
                });
            }

            bindEvents() {
                // Global event bindings for enhanced interactions
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.resetAllSystems();
                    }
                });
                
                // Window resize handling
                window.addEventListener('resize', () => {
                    this.recalculatePositions();
                });
                
                // Visibility change handling
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.pauseAnimations();
                    } else {
                        this.resumeAnimations();
                    }
                });
            }

            resetAllSystems() {
                // Reset to initial state
                this.crystalState = {
                    arActive: false,
                    manipulationMode: 'transform',
                    spatialMapping: false,
                    anchorPoints: [],
                    dimensionalAlignment: { x: 30, y: 60, z: 45 },
                    pressure: 40,
                    temperature: 25
                };
                
                // Reset UI elements
                const activeNodes = document.querySelectorAll('.matrix-node.active');
                activeNodes.forEach(node => node.classList.remove('active'));
                
                this.updateAllSystemStates();
                this.createGlobalResonanceEffect();
            }

            startMetamorphicAnimations() {
                // Start continuous metamorphic animations
                this.animateBackgroundCrystals();
                this.animatePressureFluctuations();
                this.animateCrystallineGrowth();
            }

            animateBackgroundCrystals() {
                const crystals = document.querySelectorAll('.crystal-formation');
                
                crystals.forEach((crystal, index) => {
                    setInterval(() => {
                        const intensity = Math.random() * 0.5 + 0.5;
                        crystal.style.opacity = intensity;
                        crystal.style.transform = `scale(${0.8 + intensity * 0.4}) rotate(${Math.random() * 360}deg)`;
                    }, 2000 + index * 500);
                });
            }

            animatePressureFluctuations() {
                setInterval(() => {
                    if (this.crystalState.spatialMapping) {
                        const variation = (Math.random() - 0.5) * 10;
                        this.crystalState.pressure = Math.max(10, Math.min(90, this.crystalState.pressure + variation));
                        this.updateMetamorphicState();
                    }
                }, 3000);
            }

            animateCrystallineGrowth() {
                setInterval(() => {
                    const components = document.querySelectorAll('.reality-grid > div');
                    
                    components.forEach((component, index) => {
                        setTimeout(() => {
                            component.style.transform = 'scale(1.02)';
                            
                            setTimeout(() => {
                                component.style.transform = '';
                            }, 200);
                        }, index * 100);
                    });
                }, 8000);
            }

            recalculatePositions() {
                // Recalculate positions for responsive design
                this.updateDimensionalSliders();
                this.updateSpatialStability();
            }

            pauseAnimations() {
                document.querySelectorAll('*').forEach(el => {
                    el.style.animationPlayState = 'paused';
                });
            }

            resumeAnimations() {
                document.querySelectorAll('*').forEach(el => {
                    el.style.animationPlayState = 'running';
                });
            }

            updateAROverlay() {
                // Update AR visualization based on current state
                const activeNodes = document.querySelectorAll('.ar-visualization .matrix-node.active');
                
                activeNodes.forEach(node => {
                    node.style.background = 'linear-gradient(45deg, #9366ff, #ffffff, rgba(76, 175, 80, 0.8))';
                    
                    setTimeout(() => {
                        node.style.background = '';
                    }, 1000);
                });
            }
        }

        // Add CSS animations for enhanced effects
        const additionalStyles = document.createElement('style');
        additionalStyles.textContent = `
            @keyframes anchorCrystalFloat {
                0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
                50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
            }
            
            @keyframes pressureExpansion {
                0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
                100% { transform: translate(-50%, -50%) scale(20); opacity: 0; }
            }
            
            @keyframes globalResonance {
                0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
                100% { transform: translate(-50%, -50%) scale(50); opacity: 0; }
            }
            
            .crystal-slider {
                --slider-position: 30%;
                --slider-value: 30%;
            }
            
            .crystal-slider::before {
                left: var(--slider-position, 30%) !important;
            }
            
            .pressure-gauge {
                --pressure-level: 40%;
            }
            
            .pressure-gauge::before {
                width: var(--pressure-level, 40%) !important;
            }
        `;
        document.head.appendChild(additionalStyles);

        // Initialize the Metamorphic Crystal Reality Interface
        document.addEventListener('DOMContentLoaded', () => {
            window.metamorphicCrystalInterface = new MetamorphicCrystalRealityInterface();
            
            console.log('Metamorphic Crystal Reality Interface initialized successfully');
            console.log('Geological transformation systems online');
            console.log('Crystal lattice coordination active');
        });

        // Enhanced button interactions
        document.addEventListener('DOMContentLoaded', () => {
            const buttons = document.querySelectorAll('.crystal-button');
            
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    
                    switch(action) {
                        case 'Activate AR Overlay':
                            window.metamorphicCrystalInterface.crystalState.arActive = !window.metamorphicCrystalInterface.crystalState.arActive;
                            this.textContent = window.metamorphicCrystalInterface.crystalState.arActive ? 'Deactivate AR Overlay' : 'Activate AR Overlay';
                            window.metamorphicCrystalInterface.updateAROverlay();
                            break;
                            
                        case 'Scan Environment':
                            window.metamorphicCrystalInterface.createGlobalResonanceEffect();
                            break;
                            
                        case 'Transform Object':
                            window.metamorphicCrystalInterface.triggerDimensionalResonance('transform', Math.random() * 100);
                            break;
                            
                        case 'Map Surface':
                            window.metamorphicCrystalInterface.crystalState.spatialMapping = true;
                            break;
                            
                        case 'Set Anchor':
                            const availableNodes = document.querySelectorAll('.reality-anchors .matrix-node:not(.active)');
                            if (availableNodes.length > 0) {
                                availableNodes[0].click();
                            }
                            break;
                            
                        case 'Synchronize All Systems':
                            window.metamorphicCrystalInterface.synchronizeAllSystems();
                            break;
                    }
                    
                    // Visual feedback for button press
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>