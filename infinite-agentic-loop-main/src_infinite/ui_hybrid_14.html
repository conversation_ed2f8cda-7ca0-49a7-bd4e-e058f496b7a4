<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zen Dashboard Widget - Hybrid UI Component</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-x: hidden;
        }

        /* Zen Container */
        .zen-dashboard {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 24px;
            padding: 40px;
            max-width: 1200px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: breathe 6s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        @keyframes breathe {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-5px) scale(1.01); }
        }

        /* Zen Orb Background */
        .zen-orb {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(108, 117, 125, 0.1) 0%, transparent 70%);
            pointer-events: none;
            animation: float 20s ease-in-out infinite;
        }

        .zen-orb:nth-child(1) {
            width: 400px;
            height: 400px;
            top: -100px;
            right: -100px;
            animation-delay: 0s;
        }

        .zen-orb:nth-child(2) {
            width: 300px;
            height: 300px;
            bottom: -100px;
            left: -100px;
            animation-delay: 10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(30px, -30px) rotate(120deg); }
            66% { transform: translate(-20px, 20px) rotate(240deg); }
        }

        /* Header Section */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 10;
        }

        .dashboard-title {
            font-size: 28px;
            color: #2c3e50;
            font-weight: 300;
            letter-spacing: 1px;
            opacity: 0;
            animation: fadeInUp 1s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
            from {
                opacity: 0;
                transform: translateY(20px);
            }
        }

        /* Control Buttons */
        .control-buttons {
            display: flex;
            gap: 15px;
            opacity: 0;
            animation: fadeInUp 1s ease 0.2s forwards;
        }

        .zen-button {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .zen-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            color: #495057;
        }

        .zen-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(108, 117, 125, 0.1);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .zen-button:active::before {
            width: 300px;
            height: 300px;
        }

        /* Chart Container */
        .chart-container {
            background: #fafbfc;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            opacity: 0;
            animation: fadeInUp 1s ease 0.4s forwards;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            color: #495057;
            font-weight: 400;
        }

        .chart-type-selector {
            display: flex;
            gap: 10px;
        }

        .chart-type-btn {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 13px;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .chart-type-btn.active {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        /* Canvas Chart */
        #zenChart {
            width: 100%;
            height: 300px;
            opacity: 0;
            animation: fadeIn 1s ease 0.8s forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* Filter Section */
        .filter-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            opacity: 0;
            animation: fadeInUp 1s ease 0.6s forwards;
        }

        .filter-group {
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .filter-group:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .filter-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 10px;
            display: block;
        }

        .filter-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            color: #495057;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #6c757d;
            box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
        }

        /* Alert Section */
        .alert-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
            opacity: 0;
            animation: fadeInUp 1s ease 0.8s forwards;
            position: relative;
            overflow: hidden;
        }

        .alert-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .alert-message {
            font-size: 14px;
            opacity: 0.9;
        }

        .alert-dismiss {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .alert-dismiss:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Settings Modal */
        .settings-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .settings-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .settings-content {
            background: white;
            border-radius: 16px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .settings-modal.active .settings-content {
            transform: scale(1);
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .settings-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: 400;
        }

        .close-settings {
            background: none;
            border: none;
            font-size: 24px;
            color: #6c757d;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close-settings:hover {
            color: #495057;
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
            display: block;
        }

        .setting-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 26px;
            background: #e0e0e0;
            border-radius: 13px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #667eea;
        }

        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(24px);
        }

        /* Zen Loading Animation */
        .zen-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .zen-loader.active {
            display: block;
        }

        .zen-circle {
            width: 40px;
            height: 40px;
            border: 3px solid #e0e0e0;
            border-top-color: #6c757d;
            border-radius: 50%;
            animation: zenSpin 1s linear infinite;
        }

        @keyframes zenSpin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .zen-dashboard {
                padding: 20px;
            }

            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }

            .control-buttons {
                width: 100%;
                justify-content: flex-start;
                flex-wrap: wrap;
            }

            .filter-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="zen-dashboard">
        <!-- Zen Orbs -->
        <div class="zen-orb"></div>
        <div class="zen-orb"></div>

        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">Mindful Analytics</h1>
            <div class="control-buttons">
                <button class="zen-button" onclick="refreshData()">
                    <span>🔄 Refresh</span>
                </button>
                <button class="zen-button" onclick="exportData()">
                    <span>📊 Export</span>
                </button>
                <button class="zen-button" onclick="toggleSettings()">
                    <span>⚙️ Settings</span>
                </button>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="chart-container">
            <div class="chart-header">
                <h2 class="chart-title">Flow Visualization</h2>
                <div class="chart-type-selector">
                    <button class="chart-type-btn active" onclick="changeChartType('line')">Line</button>
                    <button class="chart-type-btn" onclick="changeChartType('bar')">Bar</button>
                    <button class="chart-type-btn" onclick="changeChartType('area')">Area</button>
                </div>
            </div>
            <canvas id="zenChart"></canvas>
            <div class="zen-loader">
                <div class="zen-circle"></div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-group">
                <label class="filter-label">Time Period</label>
                <select class="filter-select" onchange="applyFilter('period', this.value)">
                    <option value="day">Today</option>
                    <option value="week" selected>This Week</option>
                    <option value="month">This Month</option>
                    <option value="year">This Year</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">Category</label>
                <select class="filter-select" onchange="applyFilter('category', this.value)">
                    <option value="all">All Categories</option>
                    <option value="meditation">Meditation</option>
                    <option value="focus">Focus</option>
                    <option value="energy">Energy</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">View Mode</label>
                <select class="filter-select" onchange="applyFilter('mode', this.value)">
                    <option value="summary">Summary</option>
                    <option value="detailed">Detailed</option>
                    <option value="comparison">Comparison</option>
                </select>
            </div>
        </div>

        <!-- Alert Section -->
        <div class="alert-section" id="alertSection">
            <div class="alert-icon">✨</div>
            <div class="alert-content">
                <div class="alert-title">Mindful Moment</div>
                <div class="alert-message">Your focus levels have increased by 25% this week. Keep up the great work!</div>
            </div>
            <button class="alert-dismiss" onclick="dismissAlert()">Dismiss</button>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="settings-modal" id="settingsModal">
        <div class="settings-content">
            <div class="settings-header">
                <h2 class="settings-title">Dashboard Settings</h2>
                <button class="close-settings" onclick="toggleSettings()">&times;</button>
            </div>
            <div class="setting-item">
                <label class="setting-label">Enable Animations</label>
                <div class="setting-toggle">
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>Smooth transitions and effects</span>
                </div>
            </div>
            <div class="setting-item">
                <label class="setting-label">Auto Refresh</label>
                <div class="setting-toggle">
                    <div class="toggle-switch" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>Update data every 5 minutes</span>
                </div>
            </div>
            <div class="setting-item">
                <label class="setting-label">Mindful Notifications</label>
                <div class="setting-toggle">
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>Gentle reminders and insights</span>
                </div>
            </div>
            <div class="setting-item">
                <label class="setting-label">Dark Mode</label>
                <div class="setting-toggle">
                    <div class="toggle-switch" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                    <span>Reduce eye strain at night</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chart configuration
        let currentChartType = 'line';
        let chartData = {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Focus Level',
                data: [65, 72, 78, 75, 82, 88, 85],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }]
        };

        // Initialize chart
        const canvas = document.getElementById('zenChart');
        const ctx = canvas.getContext('2d');
        let animationFrame;

        function drawChart() {
            const width = canvas.width = canvas.offsetWidth;
            const height = canvas.height = canvas.offsetHeight;
            const padding = 40;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Draw grid
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 0.5;
            for (let i = 0; i <= 5; i++) {
                const y = padding + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();
            }

            // Draw data
            const dataset = chartData.datasets[0];
            const maxValue = Math.max(...dataset.data);
            const points = dataset.data.map((value, index) => ({
                x: padding + (chartWidth / (dataset.data.length - 1)) * index,
                y: height - padding - (value / maxValue) * chartHeight
            }));

            if (currentChartType === 'line') {
                // Draw line
                ctx.beginPath();
                ctx.strokeStyle = dataset.borderColor;
                ctx.lineWidth = 3;
                points.forEach((point, index) => {
                    if (index === 0) {
                        ctx.moveTo(point.x, point.y);
                    } else {
                        const prevPoint = points[index - 1];
                        const cp1x = prevPoint.x + (point.x - prevPoint.x) / 2;
                        const cp1y = prevPoint.y;
                        const cp2x = prevPoint.x + (point.x - prevPoint.x) / 2;
                        const cp2y = point.y;
                        ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, point.x, point.y);
                    }
                });
                ctx.stroke();

                // Draw area
                ctx.beginPath();
                ctx.fillStyle = dataset.backgroundColor;
                ctx.moveTo(points[0].x, height - padding);
                points.forEach(point => ctx.lineTo(point.x, point.y));
                ctx.lineTo(points[points.length - 1].x, height - padding);
                ctx.closePath();
                ctx.fill();

                // Draw points
                points.forEach(point => {
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 5, 0, Math.PI * 2);
                    ctx.fillStyle = '#fff';
                    ctx.fill();
                    ctx.strokeStyle = dataset.borderColor;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                });
            } else if (currentChartType === 'bar') {
                const barWidth = chartWidth / dataset.data.length * 0.6;
                const barSpacing = chartWidth / dataset.data.length * 0.4;
                
                dataset.data.forEach((value, index) => {
                    const barHeight = (value / maxValue) * chartHeight;
                    const x = padding + (barWidth + barSpacing) * index + barSpacing / 2;
                    const y = height - padding - barHeight;
                    
                    ctx.fillStyle = dataset.borderColor;
                    ctx.fillRect(x, y, barWidth, barHeight);
                });
            } else if (currentChartType === 'area') {
                // Draw filled area with gradient
                const gradient = ctx.createLinearGradient(0, padding, 0, height - padding);
                gradient.addColorStop(0, 'rgba(102, 126, 234, 0.4)');
                gradient.addColorStop(1, 'rgba(102, 126, 234, 0.05)');
                
                ctx.beginPath();
                ctx.fillStyle = gradient;
                ctx.moveTo(points[0].x, height - padding);
                points.forEach(point => ctx.lineTo(point.x, point.y));
                ctx.lineTo(points[points.length - 1].x, height - padding);
                ctx.closePath();
                ctx.fill();
            }

            // Draw labels
            ctx.fillStyle = '#6c757d';
            ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.textAlign = 'center';
            chartData.labels.forEach((label, index) => {
                const x = padding + (chartWidth / (chartData.labels.length - 1)) * index;
                ctx.fillText(label, x, height - padding + 20);
            });
        }

        // Chart functions
        function changeChartType(type) {
            currentChartType = type;
            document.querySelectorAll('.chart-type-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            drawChart();
        }

        // Data functions
        function refreshData() {
            const loader = document.querySelector('.zen-loader');
            loader.classList.add('active');
            
            // Simulate data refresh
            setTimeout(() => {
                // Generate new random data
                chartData.datasets[0].data = chartData.datasets[0].data.map(() => 
                    Math.floor(Math.random() * 30) + 60
                );
                drawChart();
                loader.classList.remove('active');
                showNotification('Data refreshed successfully');
            }, 1500);
        }

        function exportData() {
            const data = {
                title: 'Mindful Analytics Export',
                date: new Date().toISOString(),
                chartType: currentChartType,
                data: chartData
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mindful-analytics-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            showNotification('Data exported successfully');
        }

        function applyFilter(filterType, value) {
            console.log(`Applying filter: ${filterType} = ${value}`);
            // Simulate filter application
            refreshData();
        }

        // UI functions
        function toggleSettings() {
            const modal = document.getElementById('settingsModal');
            modal.classList.toggle('active');
        }

        function toggleSetting(element) {
            element.classList.toggle('active');
        }

        function dismissAlert() {
            const alertSection = document.getElementById('alertSection');
            alertSection.style.transition = 'all 0.5s ease';
            alertSection.style.opacity = '0';
            alertSection.style.transform = 'translateX(100%)';
            setTimeout(() => {
                alertSection.style.display = 'none';
            }, 500);
        }

        function showNotification(message) {
            // Create temporary notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #667eea;
                color: white;
                padding: 15px 25px;
                border-radius: 12px;
                box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
                animation: slideIn 0.5s ease, slideOut 0.5s ease 2s forwards;
                z-index: 1001;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 2500);
        }

        // Add notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize
        window.addEventListener('load', () => {
            drawChart();
        });

        window.addEventListener('resize', () => {
            cancelAnimationFrame(animationFrame);
            animationFrame = requestAnimationFrame(drawChart);
        });

        // Auto-refresh simulation
        let autoRefreshInterval;
        function toggleAutoRefresh(enabled) {
            if (enabled) {
                autoRefreshInterval = setInterval(() => {
                    refreshData();
                }, 300000); // 5 minutes
            } else {
                clearInterval(autoRefreshInterval);
            }
        }

        // Mindful reminder
        setTimeout(() => {
            showNotification('Remember to take a mindful breath 🧘');
        }, 10000);
    </script>
</body>
</html>