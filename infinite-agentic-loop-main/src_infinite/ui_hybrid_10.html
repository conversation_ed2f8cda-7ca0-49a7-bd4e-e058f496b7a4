<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hyperdimensional Multi-Reality Explorer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: radial-gradient(ellipse at center, #1a0d26 0%, #0a0a0f 100%);
            color: #e6d5ff;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Hyperdimensional Background Animation */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(45deg, transparent 48%, rgba(147, 51, 234, 0.1) 49%, rgba(147, 51, 234, 0.1) 51%, transparent 52%),
                linear-gradient(-45deg, transparent 48%, rgba(79, 70, 229, 0.1) 49%, rgba(79, 70, 229, 0.1) 51%, transparent 52%),
                linear-gradient(90deg, transparent 48%, rgba(236, 72, 153, 0.05) 49%, rgba(236, 72, 153, 0.05) 51%, transparent 52%);
            background-size: 40px 40px, 40px 40px, 80px 80px;
            animation: hyperdimensionalFlow 20s linear infinite;
            pointer-events: none;
        }

        @keyframes hyperdimensionalFlow {
            0% { transform: translateX(0) translateY(0) rotate(0deg); }
            25% { transform: translateX(-20px) translateY(-20px) rotate(90deg); }
            50% { transform: translateX(0) translateY(-40px) rotate(180deg); }
            75% { transform: translateX(20px) translateY(-20px) rotate(270deg); }
            100% { transform: translateX(0) translateY(0) rotate(360deg); }
        }

        main {
            position: relative;
            z-index: 10;
            padding: 2rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1 {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            background: linear-gradient(45deg, #9333ea, #4f46e5, #ec4899, #f59e0b);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: hyperdimensionalGlow 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(147, 51, 234, 0.5);
        }

        @keyframes hyperdimensionalGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hybrid-component {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 1.5rem;
            height: calc(100vh - 8rem);
        }

        /* Hypercube Navigation Panel */
        .hypercube-navigator {
            grid-column: 1;
            grid-row: 1 / -1;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(79, 70, 229, 0.1));
            border: 2px solid rgba(147, 51, 234, 0.3);
            border-radius: 20px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .hypercube-navigator::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(147, 51, 234, 0.1), transparent);
            animation: hypercubeRotation 8s linear infinite;
            pointer-events: none;
        }

        @keyframes hypercubeRotation {
            0% { transform: rotate(0deg) scale(0.8); }
            100% { transform: rotate(360deg) scale(0.8); }
        }

        .dimension-controls {
            position: relative;
            z-index: 5;
        }

        .dimension-selector {
            margin-bottom: 1rem;
        }

        .dimension-selector h3 {
            color: #c084fc;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .dimension-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .dimension-cube {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));
            border: 1px solid rgba(147, 51, 234, 0.5);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transform-style: preserve-3d;
            transition: all 0.3s ease;
        }

        .dimension-cube:hover {
            transform: rotateX(15deg) rotateY(15deg) scale(1.1);
            box-shadow: 0 0 20px rgba(147, 51, 234, 0.6);
        }

        .dimension-cube.active {
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.4), rgba(79, 70, 229, 0.4));
            box-shadow: inset 0 0 20px rgba(147, 51, 234, 0.8);
        }

        .coordinate-display {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .coordinate-value {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            color: #a855f7;
        }

        /* Reality Explorer Main Panel */
        .reality-explorer {
            grid-column: 2;
            grid-row: 1 / -1;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(236, 72, 153, 0.1));
            border: 2px solid rgba(79, 70, 229, 0.3);
            border-radius: 20px;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .reality-explorer::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: realityPulse 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes realityPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.1; }
        }

        .reality-viewport {
            position: relative;
            z-index: 5;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .viewport-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .reality-selector {
            display: flex;
            gap: 0.5rem;
        }

        .reality-tab {
            padding: 0.5rem 1rem;
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));
            border: 1px solid rgba(147, 51, 234, 0.4);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .reality-tab:hover,
        .reality-tab.active {
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.4), rgba(79, 70, 229, 0.4));
            box-shadow: 0 0 15px rgba(147, 51, 234, 0.5);
            transform: translateY(-2px);
        }

        .dimensional-status {
            color: #fbbf24;
            font-size: 0.9rem;
        }

        .reality-content {
            flex: 1;
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            overflow-y: auto;
            position: relative;
        }

        .reality-stream {
            display: none;
        }

        .reality-stream.active {
            display: block;
        }

        .timeline-entry {
            background: linear-gradient(90deg, rgba(147, 51, 234, 0.1), rgba(79, 70, 229, 0.1));
            border-left: 3px solid #9333ea;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            position: relative;
        }

        .timeline-entry::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 1rem;
            width: 12px;
            height: 12px;
            background: #9333ea;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(147, 51, 234, 0.8);
        }

        .entry-timestamp {
            color: #fbbf24;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .entry-reality {
            color: #c084fc;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .entry-description {
            color: #e6d5ff;
            line-height: 1.4;
        }

        /* Portal Management Panel */
        .portal-manager {
            grid-column: 3;
            grid-row: 1;
            background: linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(245, 158, 11, 0.1));
            border: 2px solid rgba(236, 72, 153, 0.3);
            border-radius: 20px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .portal-manager::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(236, 72, 153, 0.05) 50%, transparent 70%);
            animation: portalShimmer 3s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes portalShimmer {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        .portal-grid {
            position: relative;
            z-index: 5;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .portal-gateway {
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(236, 72, 153, 0.3) 0%, rgba(245, 158, 11, 0.1) 100%);
            border: 2px solid rgba(236, 72, 153, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .portal-gateway:hover {
            transform: scale(1.1) rotate(10deg);
            box-shadow: 0 0 25px rgba(236, 72, 153, 0.8);
        }

        .portal-gateway.active {
            animation: portalActive 2s ease-in-out infinite;
        }

        @keyframes portalActive {
            0%, 100% { 
                box-shadow: 0 0 15px rgba(236, 72, 153, 0.5);
                transform: rotate(0deg);
            }
            50% { 
                box-shadow: 0 0 30px rgba(236, 72, 153, 1);
                transform: rotate(180deg);
            }
        }

        .portal-gateway::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60%;
            height: 60%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: portalInner 1.5s ease-in-out infinite;
        }

        @keyframes portalInner {
            0%, 100% { transform: translate(-50%, -50%) scale(0.8); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        /* Multiversal Data Access */
        .data-access {
            grid-column: 3;
            grid-row: 2;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(147, 51, 234, 0.1));
            border: 2px solid rgba(245, 158, 11, 0.3);
            border-radius: 20px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .data-matrix {
            position: relative;
            z-index: 5;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .data-stream {
            flex: 1;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 10px;
            padding: 1rem;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        .data-entry {
            color: #fbbf24;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: dataFlow 0.5s ease-in-out forwards;
        }

        @keyframes dataFlow {
            from { 
                opacity: 0; 
                transform: translateX(-20px); 
            }
            to { 
                opacity: 1; 
                transform: translateX(0); 
            }
        }

        .search-interface {
            margin-bottom: 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 8px;
            color: #fbbf24;
            font-family: 'Courier New', monospace;
        }

        .search-input:focus {
            outline: none;
            border-color: rgba(245, 158, 11, 0.6);
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
        }

        /* Interactive Controls */
        .control-overlay {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 20;
        }

        .hyperdimensional-button {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.3), rgba(79, 70, 229, 0.3));
            border: 2px solid rgba(147, 51, 234, 0.5);
            border-radius: 25px;
            color: #e6d5ff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
            position: relative;
            overflow: hidden;
        }

        .hyperdimensional-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .hyperdimensional-button:hover::before {
            left: 100%;
        }

        .hyperdimensional-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(147, 51, 234, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                gap: 1rem;
            }
            
            .hypercube-navigator,
            .portal-manager,
            .data-access {
                grid-column: 1;
                grid-row: auto;
            }
            
            .reality-explorer {
                grid-column: 1;
                grid-row: auto;
                order: -1;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        [role="button"]:focus,
        button:focus,
        .dimension-cube:focus,
        .portal-gateway:focus {
            outline: 2px solid #9333ea;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <main>
        <h1>Multi-Reality Explorer - Hyperdimensional Theme</h1>
        <div class="hybrid-component">
            <!-- Hypercube Navigation Panel -->
            <div class="hypercube-navigator">
                <div class="dimension-controls">
                    <div class="dimension-selector">
                        <h3>Dimensional Matrix</h3>
                        <div class="dimension-grid">
                            <div class="dimension-cube active" data-dimension="3D" role="button" tabindex="0" aria-label="3D Reality Dimension">
                                <span>3D</span>
                            </div>
                            <div class="dimension-cube" data-dimension="4D" role="button" tabindex="0" aria-label="4D Temporal Dimension">
                                <span>4D</span>
                            </div>
                            <div class="dimension-cube" data-dimension="5D" role="button" tabindex="0" aria-label="5D Probability Dimension">
                                <span>5D</span>
                            </div>
                            <div class="dimension-cube" data-dimension="∞D" role="button" tabindex="0" aria-label="Infinite Dimension">
                                <span>∞D</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="coordinate-display">
                        <h4 style="color: #c084fc; margin-bottom: 0.5rem;">Hyperspatial Coordinates</h4>
                        <div class="coordinate-value">
                            <span>X-Reality:</span>
                            <span id="coord-x">147.23</span>
                        </div>
                        <div class="coordinate-value">
                            <span>Y-Timeline:</span>
                            <span id="coord-y">-89.47</span>
                        </div>
                        <div class="coordinate-value">
                            <span>Z-Probability:</span>
                            <span id="coord-z">256.81</span>
                        </div>
                        <div class="coordinate-value">
                            <span>W-Dimension:</span>
                            <span id="coord-w">∞.∞∞</span>
                        </div>
                        <div class="coordinate-value">
                            <span>Ψ-Quantum:</span>
                            <span id="coord-psi">0.707i</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reality Explorer Main Panel -->
            <div class="reality-explorer">
                <div class="reality-viewport">
                    <div class="viewport-header">
                        <div class="reality-selector">
                            <div class="reality-tab active" data-reality="prime" role="button" tabindex="0">Prime Reality</div>
                            <div class="reality-tab" data-reality="alternate" role="button" tabindex="0">Alternate Timeline</div>
                            <div class="reality-tab" data-reality="parallel" role="button" tabindex="0">Parallel Universe</div>
                            <div class="reality-tab" data-reality="quantum" role="button" tabindex="0">Quantum Flux</div>
                        </div>
                        <div class="dimensional-status" id="reality-status">
                            Reality Anchor: Stable | Dimensional Drift: 0.03%
                        </div>
                    </div>
                    
                    <div class="reality-content">
                        <div class="reality-stream active" id="prime-reality">
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:32:18.947 UTC</div>
                                <div class="entry-reality">Prime Reality Α-1</div>
                                <div class="entry-description">Hyperdimensional multi-reality explorer initialized. Quantum state coherence at 99.7%. All dimensional anchors secure.</div>
                            </div>
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:31:45.233 UTC</div>
                                <div class="entry-reality">Prime Reality Α-1</div>
                                <div class="entry-description">Tessaract projection stabilized. 4D geometric manifold accessible through hypercube interface.</div>
                            </div>
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:31:12.089 UTC</div>
                                <div class="entry-reality">Prime Reality Α-1</div>
                                <div class="entry-description">Multiversal data streams synchronized. Cross-dimensional information exchange enabled.</div>
                            </div>
                        </div>
                        
                        <div class="reality-stream" id="alternate-reality">
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:32:18.947 UTC-β</div>
                                <div class="entry-reality">Alternate Timeline Β-7</div>
                                <div class="entry-description">Reality divergence detected at quantum level. Timeline split probability: 73.2%</div>
                            </div>
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:30:55.412 UTC-β</div>
                                <div class="entry-reality">Alternate Timeline Β-7</div>
                                <div class="entry-description">Hyperspatial navigation successful. Klein bottle topology integrated into interface design.</div>
                            </div>
                        </div>
                        
                        <div class="reality-stream" id="parallel-reality">
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:32:18.947 UTC-∥</div>
                                <div class="entry-reality">Parallel Universe Γ-∞</div>
                                <div class="entry-description">Non-Euclidean geometry detected. Curved spacetime interface adapted for higher-dimensional navigation.</div>
                            </div>
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:29:33.678 UTC-∥</div>
                                <div class="entry-reality">Parallel Universe Γ-∞</div>
                                <div class="entry-description">Möbius strip portal configuration complete. Infinite loop navigation pathway established.</div>
                            </div>
                        </div>
                        
                        <div class="reality-stream" id="quantum-reality">
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:32:18.947 UTC-Ψ</div>
                                <div class="entry-reality">Quantum Flux Δ-?</div>
                                <div class="entry-description">Superposition state maintained. Multiple reality branches accessible simultaneously through quantum entanglement.</div>
                            </div>
                            <div class="timeline-entry">
                                <div class="entry-timestamp">2025.06.07 14:28:07.156 UTC-Ψ</div>
                                <div class="entry-reality">Quantum Flux Δ-?</div>
                                <div class="entry-description">Hyperdimensional wave function collapsed into observable state. Reality matrix recalibrated.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Portal Management Panel -->
            <div class="portal-manager">
                <h3 style="color: #ec4899; margin-bottom: 1rem; text-align: center;">Dimensional Portals</h3>
                <div class="portal-grid">
                    <div class="portal-gateway active" data-portal="1" role="button" tabindex="0" aria-label="Portal to Dimension 1">
                        <span style="color: #ec4899; font-weight: bold;">Ω1</span>
                    </div>
                    <div class="portal-gateway" data-portal="2" role="button" tabindex="0" aria-label="Portal to Dimension 2">
                        <span style="color: #f59e0b; font-weight: bold;">Ω2</span>
                    </div>
                    <div class="portal-gateway" data-portal="3" role="button" tabindex="0" aria-label="Portal to Dimension 3">
                        <span style="color: #9333ea; font-weight: bold;">Ω3</span>
                    </div>
                    <div class="portal-gateway" data-portal="4" role="button" tabindex="0" aria-label="Portal to Dimension 4">
                        <span style="color: #4f46e5; font-weight: bold;">Ω∞</span>
                    </div>
                </div>
                <div style="background: rgba(0, 0, 0, 0.3); border-radius: 10px; padding: 1rem; margin-top: 1rem;">
                    <div style="color: #ec4899; font-size: 0.9rem; margin-bottom: 0.5rem;">Portal Status</div>
                    <div style="color: #fbbf24; font-size: 0.8rem;" id="portal-status">
                        Gateway Ω1: Active<br>
                        Energy Flow: 847.2 THz<br>
                        Stability: 94.7%
                    </div>
                </div>
            </div>

            <!-- Multiversal Data Access -->
            <div class="data-access">
                <div class="data-matrix">
                    <h3 style="color: #f59e0b; margin-bottom: 1rem; text-align: center;">Multiversal Data</h3>
                    <div class="search-interface">
                        <input type="text" class="search-input" placeholder="Search across dimensions..." aria-label="Search multiversal data">
                    </div>
                    <div class="data-stream" id="data-stream">
                        <div class="data-entry">[∞.∞∞.∞∞∞] Hypercube.nav > Reality.prime.access()</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Dimension.4D > Tesseract.project(geometry)</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Portal.Ω1 > Gateway.stabilize(energy)</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Timeline.β7 > Branch.probability(0.732)</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Quantum.flux > Superposition.maintain()</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Möbius.strip > Topology.infinite_loop()</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Klein.bottle > Geometry.non_euclidean()</div>
                        <div class="data-entry">[∞.∞∞.∞∞∞] Spacetime.curve > Navigation.adaptive()</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Controls -->
        <div class="control-overlay">
            <button class="hyperdimensional-button" id="fold-reality" aria-label="Fold Reality">
                <span>Fold Reality</span>
            </button>
            <button class="hyperdimensional-button" id="quantum-jump" aria-label="Quantum Jump">
                <span>Quantum Jump</span>
            </button>
            <button class="hyperdimensional-button" id="sync-dimensions" aria-label="Sync Dimensions">
                <span>Sync Dimensions</span>
            </button>
        </div>
    </main>

    <script>
        // Hyperdimensional Multi-Reality Explorer Controller
        class HyperdimensionalExplorer {
            constructor() {
                this.currentDimension = '3D';
                this.currentReality = 'prime';
                this.activePortal = '1';
                this.coordinates = {
                    x: 147.23,
                    y: -89.47,
                    z: 256.81,
                    w: '∞.∞∞',
                    psi: '0.707i'
                };
                this.dataEntries = [
                    '[∞.∞∞.∞∞∞] Hypercube.nav > Reality.prime.access()',
                    '[∞.∞∞.∞∞∞] Dimension.4D > Tesseract.project(geometry)',
                    '[∞.∞∞.∞∞∞] Portal.Ω1 > Gateway.stabilize(energy)',
                    '[∞.∞∞.∞∞∞] Timeline.β7 > Branch.probability(0.732)',
                    '[∞.∞∞.∞∞∞] Quantum.flux > Superposition.maintain()',
                    '[∞.∞∞.∞∞∞] Möbius.strip > Topology.infinite_loop()',
                    '[∞.∞∞.∞∞∞] Klein.bottle > Geometry.non_euclidean()',
                    '[∞.∞∞.∞∞∞] Spacetime.curve > Navigation.adaptive()'
                ];
                
                this.init();
            }

            init() {
                this.bindEvents();
                this.startCoordinateUpdates();
                this.startDataFlow();
                this.initializePortalStatus();
            }

            bindEvents() {
                // Dimension cube navigation
                document.querySelectorAll('.dimension-cube').forEach(cube => {
                    cube.addEventListener('click', (e) => this.switchDimension(e.target.dataset.dimension));
                    cube.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.switchDimension(e.target.dataset.dimension);
                        }
                    });
                });

                // Reality tab navigation
                document.querySelectorAll('.reality-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => this.switchReality(e.target.dataset.reality));
                    tab.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.switchReality(e.target.dataset.reality);
                        }
                    });
                });

                // Portal gateway management
                document.querySelectorAll('.portal-gateway').forEach(portal => {
                    portal.addEventListener('click', (e) => this.activatePortal(e.target.dataset.portal));
                    portal.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.activatePortal(e.target.dataset.portal);
                        }
                    });
                });

                // Hyperdimensional control buttons
                document.getElementById('fold-reality').addEventListener('click', () => this.foldReality());
                document.getElementById('quantum-jump').addEventListener('click', () => this.quantumJump());
                document.getElementById('sync-dimensions').addEventListener('click', () => this.syncDimensions());

                // Multiversal search
                document.querySelector('.search-input').addEventListener('input', (e) => this.searchMultiversalData(e.target.value));
            }

            switchDimension(dimension) {
                // Update active dimension cube
                document.querySelectorAll('.dimension-cube').forEach(cube => {
                    cube.classList.remove('active');
                });
                document.querySelector(`[data-dimension="${dimension}"]`).classList.add('active');

                this.currentDimension = dimension;
                this.updateCoordinatesForDimension(dimension);
                this.addDataEntry(`[∞.∞∞.∞∞∞] Dimension.switch > ${dimension}.activated()`);
            }

            switchReality(reality) {
                // Update active reality tab
                document.querySelectorAll('.reality-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-reality="${reality}"]`).classList.add('active');

                // Update reality content
                document.querySelectorAll('.reality-stream').forEach(stream => {
                    stream.classList.remove('active');
                });
                document.getElementById(`${reality}-reality`).classList.add('active');

                this.currentReality = reality;
                this.updateRealityStatus(reality);
                this.addDataEntry(`[∞.∞∞.∞∞∞] Reality.switch > ${reality}.accessed()`);
            }

            activatePortal(portalId) {
                // Update active portal
                document.querySelectorAll('.portal-gateway').forEach(portal => {
                    portal.classList.remove('active');
                });
                document.querySelector(`[data-portal="${portalId}"]`).classList.add('active');

                this.activePortal = portalId;
                this.updatePortalStatus(portalId);
                this.addDataEntry(`[∞.∞∞.∞∞∞] Portal.Ω${portalId} > Gateway.activated()`);
            }

            updateCoordinatesForDimension(dimension) {
                const variations = {
                    '3D': { x: 147.23, y: -89.47, z: 256.81, w: '∞.∞∞', psi: '0.707i' },
                    '4D': { x: 892.15, y: 434.68, z: -127.39, w: '4.444', psi: '0.866i' },
                    '5D': { x: -356.77, y: 789.23, z: 512.44, w: '5.555', psi: '1.000i' },
                    '∞D': { x: '∞.∞∞', y: '∞.∞∞', z: '∞.∞∞', w: '∞.∞∞', psi: '∞.∞∞i' }
                };

                this.coordinates = variations[dimension];
                this.updateCoordinateDisplay();
            }

            updateCoordinateDisplay() {
                document.getElementById('coord-x').textContent = this.coordinates.x;
                document.getElementById('coord-y').textContent = this.coordinates.y;
                document.getElementById('coord-z').textContent = this.coordinates.z;
                document.getElementById('coord-w').textContent = this.coordinates.w;
                document.getElementById('coord-psi').textContent = this.coordinates.psi;
            }

            updateRealityStatus(reality) {
                const statuses = {
                    'prime': 'Reality Anchor: Stable | Dimensional Drift: 0.03%',
                    'alternate': 'Timeline Divergence: 73.2% | Probability Flux: High',
                    'parallel': 'Universe Sync: 94.7% | Quantum Entanglement: Active',
                    'quantum': 'Superposition: Maintained | Wave Function: Collapsed'
                };

                document.getElementById('reality-status').textContent = statuses[reality];
            }

            updatePortalStatus(portalId) {
                const statuses = {
                    '1': 'Gateway Ω1: Active<br>Energy Flow: 847.2 THz<br>Stability: 94.7%',
                    '2': 'Gateway Ω2: Standby<br>Energy Flow: 623.8 THz<br>Stability: 87.3%',
                    '3': 'Gateway Ω3: Charging<br>Energy Flow: 1205.4 THz<br>Stability: 96.8%',
                    '4': 'Gateway Ω∞: Infinite<br>Energy Flow: ∞ THz<br>Stability: ∞%'
                };

                document.getElementById('portal-status').innerHTML = statuses[portalId];
            }

            startCoordinateUpdates() {
                setInterval(() => {
                    if (this.currentDimension !== '∞D') {
                        this.coordinates.x += (Math.random() - 0.5) * 0.1;
                        this.coordinates.y += (Math.random() - 0.5) * 0.1;
                        this.coordinates.z += (Math.random() - 0.5) * 0.1;
                        this.updateCoordinateDisplay();
                    }
                }, 1000);
            }

            startDataFlow() {
                const dataStream = document.getElementById('data-stream');
                let entryIndex = this.dataEntries.length;

                setInterval(() => {
                    const entry = document.createElement('div');
                    entry.className = 'data-entry';
                    entry.textContent = this.dataEntries[entryIndex % this.dataEntries.length];
                    
                    dataStream.appendChild(entry);
                    dataStream.scrollTop = dataStream.scrollHeight;
                    
                    // Keep only last 20 entries
                    while (dataStream.children.length > 20) {
                        dataStream.removeChild(dataStream.firstChild);
                    }
                    
                    entryIndex++;
                }, 2000);
            }

            addDataEntry(entry) {
                const dataStream = document.getElementById('data-stream');
                const entryElement = document.createElement('div');
                entryElement.className = 'data-entry';
                entryElement.textContent = entry;
                
                dataStream.appendChild(entryElement);
                dataStream.scrollTop = dataStream.scrollHeight;
                
                // Keep only last 20 entries
                while (dataStream.children.length > 20) {
                    dataStream.removeChild(dataStream.firstChild);
                }
            }

            searchMultiversalData(query) {
                if (query.length > 2) {
                    this.addDataEntry(`[∞.∞∞.∞∞∞] Search.multiversal > Query("${query}").executing()`);
                }
            }

            foldReality() {
                this.addDataEntry(`[∞.∞∞.∞∞∞] Reality.fold > Spacetime.curvature.maximum()`);
                document.body.style.filter = 'hue-rotate(180deg)';
                setTimeout(() => {
                    document.body.style.filter = '';
                }, 2000);
            }

            quantumJump() {
                this.addDataEntry(`[∞.∞∞.∞∞∞] Quantum.jump > Timeline.branch.random()`);
                
                // Randomly switch to a different reality
                const realities = ['prime', 'alternate', 'parallel', 'quantum'];
                const randomReality = realities[Math.floor(Math.random() * realities.length)];
                this.switchReality(randomReality);
            }

            syncDimensions() {
                this.addDataEntry(`[∞.∞∞.∞∞∞] Dimensions.sync > All.realities.harmonized()`);
                
                // Briefly activate all portals
                document.querySelectorAll('.portal-gateway').forEach(portal => {
                    portal.classList.add('active');
                });
                
                setTimeout(() => {
                    document.querySelectorAll('.portal-gateway').forEach(portal => {
                        portal.classList.remove('active');
                    });
                    document.querySelector(`[data-portal="${this.activePortal}"]`).classList.add('active');
                }, 3000);
            }

            initializePortalStatus() {
                this.updatePortalStatus(this.activePortal);
            }
        }

        // Initialize the Hyperdimensional Multi-Reality Explorer
        document.addEventListener('DOMContentLoaded', () => {
            new HyperdimensionalExplorer();
        });

        // Enhanced keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                        e.preventDefault();
                        const portal = document.querySelector(`[data-portal="${e.key}"]`);
                        if (portal) portal.click();
                        break;
                    case 'f':
                        e.preventDefault();
                        document.getElementById('fold-reality').click();
                        break;
                    case 'j':
                        e.preventDefault();
                        document.getElementById('quantum-jump').click();
                        break;
                    case 's':
                        e.preventDefault();
                        document.getElementById('sync-dimensions').click();
                        break;
                }
            }
        });
    </script>
</body>
</html>