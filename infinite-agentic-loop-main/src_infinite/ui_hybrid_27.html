<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neurálna Súborová Matica - Cyberpunk Budúcnosť Téma</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: #0a0a0a;
            color: #00ff41;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, #ff006e22 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #8338ec22 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, #3a86ff22 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        main {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 900;
            color: #00ff41;
            margin-bottom: 2rem;
            text-shadow: 0 0 20px #00ff41;
            animation: glitch 3s infinite;
        }

        @keyframes glitch {
            0%, 100% { transform: translateX(0); }
            20% { transform: translateX(-2px); }
            40% { transform: translateX(2px); }
            60% { transform: translateX(-1px); }
            80% { transform: translateX(1px); }
        }

        .neural-file-matrix {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff41;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 
                0 0 30px rgba(0, 255, 65, 0.3),
                inset 0 0 30px rgba(0, 255, 65, 0.1);
            position: relative;
            overflow: hidden;
        }

        .neural-file-matrix::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.1), transparent);
            animation: scan 4s infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .upload-zone {
            border: 2px dashed #ff006e;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 0, 110, 0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .upload-zone:hover, .upload-zone.dragover {
            border-color: #00ff41;
            background: rgba(0, 255, 65, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        }

        .upload-text {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #ff006e;
        }

        .upload-zone:hover .upload-text {
            color: #00ff41;
        }

        .file-input {
            display: none;
        }

        .upload-button {
            background: linear-gradient(45deg, #ff006e, #8338ec);
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            color: white;
            font-family: inherit;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 0, 110, 0.3);
        }

        .progress-container {
            margin-bottom: 2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .progress-container.active {
            opacity: 1;
            transform: translateY(0);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff41, #3a86ff);
            width: 0%;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
        }

        .progress-text {
            font-size: 0.9rem;
            color: #00ff41;
        }

        .file-browser {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .file-item {
            background: rgba(0, 255, 65, 0.05);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .file-item:hover {
            border-color: #00ff41;
            background: rgba(0, 255, 65, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 65, 0.2);
        }

        .file-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #ff006e;
        }

        .file-name {
            font-weight: 700;
            margin-bottom: 0.3rem;
            color: #00ff41;
        }

        .file-size {
            font-size: 0.8rem;
            color: #8338ec;
        }

        .file-preview {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #3a86ff;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .file-preview.active {
            opacity: 1;
            transform: translateY(0);
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(58, 134, 255, 0.3);
        }

        .preview-title {
            font-size: 1.3rem;
            color: #3a86ff;
            font-weight: 700;
        }

        .validation-status {
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .validation-status.valid {
            background: rgba(0, 255, 65, 0.2);
            color: #00ff41;
            border: 1px solid #00ff41;
        }

        .validation-status.invalid {
            background: rgba(255, 0, 110, 0.2);
            color: #ff006e;
            border: 1px solid #ff006e;
        }

        .preview-content {
            color: #ffffff;
            line-height: 1.6;
            font-family: 'Courier New', monospace;
        }

        .neural-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 65, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 65, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <main>
        <h1>Neurálna Súborová Matica - Cyberpunk Budúcnosť Téma</h1>

        <div class="neural-file-matrix">
            <div class="neural-grid"></div>

            <div class="upload-zone" id="uploadZone">
                <div class="upload-text">PRESUŇTE SÚBORY DO NEURÁLNEHO ROZHRANIA</div>
                <input type="file" class="file-input" id="fileInput" multiple>
                <button class="upload-button" onclick="document.getElementById('fileInput').click()">
                    SPUSTIŤ NAHRÁVANIE
                </button>
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">NEURÁLNE SPRACOVANIE: 0%</div>
            </div>
            
            <div class="file-browser" id="fileBrowser">
                <div class="file-item" data-file="neural_map.dat">
                    <div class="file-icon">🧠</div>
                    <div class="file-name">neural_map.dat</div>
                    <div class="file-size">2.4 MB</div>
                </div>
                <div class="file-item" data-file="quantum_key.enc">
                    <div class="file-icon">🔐</div>
                    <div class="file-name">quantum_key.enc</div>
                    <div class="file-size">512 KB</div>
                </div>
                <div class="file-item" data-file="matrix_core.exe">
                    <div class="file-icon">⚡</div>
                    <div class="file-name">matrix_core.exe</div>
                    <div class="file-size">15.7 MB</div>
                </div>
                <div class="file-item" data-file="cyber_protocol.json">
                    <div class="file-icon">📡</div>
                    <div class="file-name">cyber_protocol.json</div>
                    <div class="file-size">1.1 MB</div>
                </div>
            </div>
            
            <div class="file-preview" id="filePreview">
                <div class="preview-header">
                    <div class="preview-title" id="previewTitle">NEURAL_MAP.DAT</div>
                    <div class="validation-status valid" id="validationStatus">VALIDATED</div>
                </div>
                <div class="preview-content" id="previewContent">
                    NEURAL NETWORK MAPPING DATA<br>
                    ============================<br><br>
                    SYNAPTIC CONNECTIONS: 847,392<br>
                    NEURAL PATHWAYS: OPTIMIZED<br>
                    QUANTUM ENTANGLEMENT: STABLE<br>
                    ENCRYPTION LEVEL: MAXIMUM<br><br>
                    [CLASSIFIED DATA STREAM]<br>
                    01001000 01100101 01101100 01101100 01101111<br>
                    MATRIX INTERFACE READY...
                </div>
            </div>
        </div>
    </main>

    <script>
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const fileBrowser = document.getElementById('fileBrowser');
        const filePreview = document.getElementById('filePreview');
        const previewTitle = document.getElementById('previewTitle');
        const previewContent = document.getElementById('previewContent');
        const validationStatus = document.getElementById('validationStatus');

        // Drag and drop functionality
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                simulateUpload();
            }
        }

        function simulateUpload() {
            progressContainer.classList.add('active');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;
                
                progressFill.style.width = progress + '%';
                progressText.textContent = `NEURAL PROCESSING: ${Math.floor(progress)}%`;
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        progressContainer.classList.remove('active');
                        addNewFile();
                    }, 1000);
                }
            }, 200);
        }

        function addNewFile() {
            const newFile = document.createElement('div');
            newFile.className = 'file-item';
            newFile.dataset.file = 'uploaded_data.neo';
            newFile.innerHTML = `
                <div class="file-icon">🆕</div>
                <div class="file-name">uploaded_data.neo</div>
                <div class="file-size">${(Math.random() * 10 + 1).toFixed(1)} MB</div>
            `;
            
            fileBrowser.appendChild(newFile);
            
            // Add click event to new file
            newFile.addEventListener('click', () => showFilePreview('uploaded_data.neo'));
            
            // Glitch effect for new file
            newFile.style.animation = 'glitch 0.5s ease-out';
        }

        // File browser interactions
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', () => {
                const fileName = item.dataset.file;
                showFilePreview(fileName);
            });
        });

        function showFilePreview(fileName) {
            previewTitle.textContent = fileName.toUpperCase();
            
            const fileData = {
                'neural_map.dat': {
                    content: `NEURAL NETWORK MAPPING DATA\n============================\n\nSYNAPTIC CONNECTIONS: 847,392\nNEURAL PATHWAYS: OPTIMIZED\nQUANTUM ENTANGLEMENT: STABLE\nENCRYPTION LEVEL: MAXIMUM\n\n[CLASSIFIED DATA STREAM]\n01001000 01100101 01101100 01101100 01101111\nMATRIX INTERFACE READY...`,
                    valid: true
                },
                'quantum_key.enc': {
                    content: `QUANTUM ENCRYPTION KEY\n=====================\n\nKEY LENGTH: 4096-BIT\nQUANTUM STATE: SUPERPOSITION\nENTANGLEMENT: VERIFIED\n\n[ENCRYPTED PAYLOAD]\n∞∆∇∞∆∇∞∆∇∞∆∇∞∆∇∞∆∇\nACCESS LEVEL: OMEGA CLEARANCE`,
                    valid: true
                },
                'matrix_core.exe': {
                    content: `MATRIX CORE EXECUTABLE\n=====================\n\nVERSION: 3.14.159\nCOMPILED: 2087.03.14\nARCHITECTURE: QUANTUM-X64\n\n[BINARY SIGNATURE]\n11010011 10101010 11110000\nEXECUTION STATUS: READY`,
                    valid: false
                },
                'uploaded_data.neo': {
                    content: `UPLOADED NEURAL DATA\n===================\n\nFILE TYPE: NEOMORPHIC\nSTATUS: PROCESSING\nINTEGRITY: VERIFYING\n\n[DATA STREAM]\nNEW NEURAL PATHWAYS DETECTED\nINTEGRATING WITH MATRIX...`,
                    valid: true
                }
            };

            const data = fileData[fileName] || fileData['neural_map.dat'];
            
            previewContent.innerHTML = data.content.replace(/\n/g, '<br>');
            
            validationStatus.textContent = data.valid ? 'VALIDATED' : 'CORRUPTED';
            validationStatus.className = `validation-status ${data.valid ? 'valid' : 'invalid'}`;
            
            filePreview.classList.add('active');
        }

        // Initialize with first file preview
        setTimeout(() => {
            showFilePreview('neural_map.dat');
        }, 1000);

        // Cyberpunk glitch effects
        setInterval(() => {
            if (Math.random() < 0.1) {
                document.body.style.filter = 'hue-rotate(10deg)';
                setTimeout(() => {
                    document.body.style.filter = '';
                }, 100);
            }
        }, 2000);
    </script>
</body>
</html>
