<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mindful Search Hub - Zen Philosophy Theme</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
        }

        main {
            width: 90%;
            max-width: 800px;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            font-size: 2rem;
            font-weight: 300;
            color: #34495e;
            margin-bottom: 3rem;
            letter-spacing: 2px;
        }

        .zen-search-hub {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .search-container {
            position: relative;
            margin-bottom: 2rem;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 2px solid transparent;
            border-radius: 50px;
            font-size: 1.1rem;
            background: #f8f9fa;
            transition: all 0.4s ease;
            outline: none;
            font-family: inherit;
        }

        .search-input:focus {
            border-color: #74b9ff;
            background: white;
            box-shadow: 0 0 0 4px rgba(116, 185, 255, 0.1);
        }

        .search-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.5s ease;
        }

        .search-filters.active {
            opacity: 1;
            transform: translateY(0);
        }

        .filter-tag {
            padding: 0.5rem 1rem;
            background: #ddd6fe;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-tag:hover, .filter-tag.active {
            background: #8b5cf6;
            color: white;
            transform: translateY(-2px);
        }

        .recent-searches {
            margin-bottom: 1.5rem;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.5s ease 0.1s;
        }

        .recent-searches.active {
            opacity: 1;
            transform: translateY(0);
        }

        .recent-title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-weight: 300;
        }

        .recent-item {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            margin: 0.2rem;
            background: #e9ecef;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recent-item:hover {
            background: #74b9ff;
            color: white;
        }

        .results-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
            max-height: 0;
            overflow: hidden;
        }

        .results-preview.active {
            opacity: 1;
            transform: translateY(0);
            max-height: 400px;
        }

        .result-item {
            padding: 1rem;
            border-left: 3px solid #74b9ff;
            margin-bottom: 1rem;
            background: white;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .result-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .result-title {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 0.3rem;
        }

        .result-snippet {
            font-size: 0.9rem;
            color: #6c757d;
            line-height: 1.4;
        }

        .breathing-animation {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .zen-indicator {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 12px;
            height: 12px;
            background: #10ac84;
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <main>
        <h1>Mindful Search Hub - Zen Philosophy Theme</h1>
        
        <div class="zen-search-hub breathing-animation">
            <div class="zen-indicator"></div>
            
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search with mindful intention..." id="searchInput">
            </div>
            
            <div class="search-filters" id="searchFilters">
                <button class="filter-tag" data-filter="all">All</button>
                <button class="filter-tag" data-filter="documents">Documents</button>
                <button class="filter-tag" data-filter="images">Images</button>
                <button class="filter-tag" data-filter="videos">Videos</button>
                <button class="filter-tag" data-filter="people">People</button>
            </div>
            
            <div class="recent-searches" id="recentSearches">
                <div class="recent-title">Recent mindful searches</div>
                <span class="recent-item">meditation techniques</span>
                <span class="recent-item">breathing exercises</span>
                <span class="recent-item">mindfulness apps</span>
                <span class="recent-item">zen gardens</span>
                <span class="recent-item">peaceful music</span>
            </div>
            
            <div class="results-preview" id="resultsPreview">
                <div class="result-item">
                    <div class="result-title">The Art of Mindful Searching</div>
                    <div class="result-snippet">Discover how to approach information seeking with intention and presence, transforming your digital interactions into moments of mindfulness...</div>
                </div>
                <div class="result-item">
                    <div class="result-title">Zen and the Digital Age</div>
                    <div class="result-snippet">Exploring how ancient wisdom can guide our modern relationship with technology and information consumption...</div>
                </div>
                <div class="result-item">
                    <div class="result-title">Breathing Space in Search</div>
                    <div class="result-snippet">Creating moments of pause and reflection in our quest for knowledge, allowing wisdom to emerge naturally...</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        const searchInput = document.getElementById('searchInput');
        const searchFilters = document.getElementById('searchFilters');
        const recentSearches = document.getElementById('recentSearches');
        const resultsPreview = document.getElementById('resultsPreview');
        const filterTags = document.querySelectorAll('.filter-tag');
        const recentItems = document.querySelectorAll('.recent-item');

        // Mindful typing delay for contemplative search
        let searchTimeout;
        
        searchInput.addEventListener('focus', () => {
            setTimeout(() => {
                searchFilters.classList.add('active');
                recentSearches.classList.add('active');
            }, 200);
        });

        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            
            if (e.target.value.length > 2) {
                searchTimeout = setTimeout(() => {
                    resultsPreview.classList.add('active');
                }, 800); // Mindful delay for contemplation
            } else {
                resultsPreview.classList.remove('active');
            }
        });

        // Filter tag interactions with zen-like transitions
        filterTags.forEach(tag => {
            tag.addEventListener('click', () => {
                filterTags.forEach(t => t.classList.remove('active'));
                tag.classList.add('active');
                
                // Gentle visual feedback
                tag.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    tag.style.transform = '';
                }, 150);
            });
        });

        // Recent search item interactions
        recentItems.forEach(item => {
            item.addEventListener('click', () => {
                searchInput.value = item.textContent;
                searchInput.focus();
                
                setTimeout(() => {
                    resultsPreview.classList.add('active');
                }, 300);
            });
        });

        // Breathing animation control based on interaction
        const hubElement = document.querySelector('.zen-search-hub');
        let isInteracting = false;

        searchInput.addEventListener('focus', () => {
            isInteracting = true;
            hubElement.style.animationDuration = '6s'; // Slower breathing when focused
        });

        searchInput.addEventListener('blur', () => {
            isInteracting = false;
            hubElement.style.animationDuration = '4s'; // Normal breathing
        });

        // Zen-like keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                searchInput.blur();
                searchFilters.classList.remove('active');
                recentSearches.classList.remove('active');
                resultsPreview.classList.remove('active');
            }
        });
    </script>
</body>
</html>
