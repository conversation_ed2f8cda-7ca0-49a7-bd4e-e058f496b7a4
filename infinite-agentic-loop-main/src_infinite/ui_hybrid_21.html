<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ocean Depths Calendar Dashboard - Iteration 21</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Aqua+Grotesque&family=Orbitron:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Orbitron', sans-serif;
            background: #000814;
            color: #e0fbfc;
            overflow: hidden;
            position: relative;
            height: 100vh;
        }
        
        /* Ocean Background */
        .ocean-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, 
                #001845 0%, 
                #001233 20%, 
                #000814 50%, 
                #000514 80%, 
                #000000 100%);
            z-index: -3;
        }
        
        /* Caustic Light Effects */
        .caustics {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><filter id="turbulence"><feTurbulence baseFrequency="0.02" numOctaves="3" result="turbulence"/><feColorMatrix in="turbulence" type="saturate" values="0"/></filter></defs><rect width="100%" height="100%" filter="url(%23turbulence)" opacity="0.1"/></svg>');
            mix-blend-mode: screen;
            opacity: 0.3;
            z-index: -2;
            animation: causticMove 20s infinite linear;
        }
        
        @keyframes causticMove {
            0% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-20px, -20px) scale(1.1); }
            100% { transform: translate(0, 0) scale(1); }
        }
        
        /* Bubbles */
        .bubble {
            position: absolute;
            background: radial-gradient(circle at 30% 30%, rgba(224, 251, 252, 0.4), rgba(224, 251, 252, 0.1));
            border-radius: 50%;
            animation: float 10s infinite ease-in-out;
            z-index: -1;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(100vh) translateX(0); }
            50% { transform: translateY(-20vh) translateX(20px); }
        }
        
        /* Bioluminescent Particles */
        .bioluminescent {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00f5ff;
            border-radius: 50%;
            box-shadow: 0 0 10px #00f5ff, 0 0 20px #00f5ff;
            animation: bioluminesce 5s infinite ease-in-out;
            z-index: 1;
        }
        
        @keyframes bioluminesce {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.5); }
        }
        
        /* Main Calendar Container */
        .calendar-dashboard {
            position: relative;
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background: rgba(0, 18, 51, 0.8);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 50px rgba(0, 245, 255, 0.2);
            z-index: 10;
        }
        
        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 8, 20, 0.6);
            border-radius: 15px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }
        
        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(90deg, #00f5ff, #0077be, #00f5ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shimmer 3s infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.3); }
        }
        
        /* View Selector */
        .view-selector {
            display: flex;
            gap: 10px;
        }
        
        .view-btn {
            padding: 10px 20px;
            background: rgba(0, 119, 190, 0.3);
            border: 1px solid #0077be;
            border-radius: 25px;
            color: #e0fbfc;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Orbitron', sans-serif;
        }
        
        .view-btn:hover {
            background: rgba(0, 119, 190, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 245, 255, 0.3);
        }
        
        .view-btn.active {
            background: #0077be;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
        }
        
        /* Main Content Area */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        /* Calendar View */
        .calendar-view {
            background: rgba(0, 12, 36, 0.6);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        /* Month Navigation */
        .month-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .month-nav h2 {
            font-size: 1.8em;
            color: #00f5ff;
        }
        
        .nav-btn {
            background: none;
            border: 1px solid #0077be;
            color: #00f5ff;
            padding: 10px 15px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .nav-btn:hover {
            background: rgba(0, 119, 190, 0.3);
            transform: scale(1.1);
        }
        
        /* Calendar Grid */
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
        }
        
        .day-header {
            text-align: center;
            padding: 10px;
            color: #0077be;
            font-weight: bold;
            border-bottom: 2px solid rgba(0, 119, 190, 0.3);
        }
        
        .calendar-day {
            aspect-ratio: 1;
            background: rgba(0, 18, 51, 0.5);
            border: 1px solid rgba(0, 245, 255, 0.1);
            border-radius: 10px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .calendar-day:hover {
            background: rgba(0, 119, 190, 0.2);
            border-color: #00f5ff;
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
        }
        
        .calendar-day.today {
            background: rgba(0, 119, 190, 0.4);
            border-color: #00f5ff;
            box-shadow: inset 0 0 20px rgba(0, 245, 255, 0.3);
        }
        
        .calendar-day.other-month {
            opacity: 0.3;
        }
        
        .day-number {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .day-events {
            font-size: 0.8em;
            margin-top: 5px;
        }
        
        .event-dot {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 3px;
            box-shadow: 0 0 5px currentColor;
        }
        
        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        /* Weather Widget */
        .weather-widget {
            background: rgba(0, 12, 36, 0.6);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            text-align: center;
        }
        
        .weather-icon {
            font-size: 3em;
            margin: 10px 0;
            animation: weatherFloat 3s infinite ease-in-out;
        }
        
        @keyframes weatherFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .weather-temp {
            font-size: 2em;
            color: #00f5ff;
            margin: 10px 0;
        }
        
        /* Event List */
        .event-list {
            background: rgba(0, 12, 36, 0.6);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            max-height: 400px;
            overflow-y: auto;
        }
        
        .event-list::-webkit-scrollbar {
            width: 8px;
        }
        
        .event-list::-webkit-scrollbar-track {
            background: rgba(0, 18, 51, 0.5);
            border-radius: 4px;
        }
        
        .event-list::-webkit-scrollbar-thumb {
            background: #0077be;
            border-radius: 4px;
        }
        
        .event-item {
            background: rgba(0, 18, 51, 0.5);
            border-left: 3px solid #00f5ff;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .event-item:hover {
            background: rgba(0, 119, 190, 0.2);
            transform: translateX(5px);
        }
        
        .event-time {
            color: #0077be;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .event-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        /* Bottom Tools */
        .bottom-tools {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        /* Quick Add */
        .quick-add {
            background: rgba(0, 12, 36, 0.6);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }
        
        .quick-add input {
            width: 100%;
            padding: 10px;
            background: rgba(0, 18, 51, 0.8);
            border: 1px solid #0077be;
            border-radius: 10px;
            color: #e0fbfc;
            font-family: 'Orbitron', sans-serif;
            margin-bottom: 10px;
        }
        
        .quick-add input:focus {
            outline: none;
            border-color: #00f5ff;
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
        }
        
        .add-btn {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #0077be, #00f5ff);
            border: none;
            border-radius: 10px;
            color: #000814;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Orbitron', sans-serif;
        }
        
        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 245, 255, 0.4);
        }
        
        /* Reminders */
        .reminders {
            background: rgba(0, 12, 36, 0.6);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }
        
        .reminder-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(0, 18, 51, 0.5);
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .reminder-item:hover {
            background: rgba(0, 119, 190, 0.2);
        }
        
        .reminder-checkbox {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            cursor: pointer;
        }
        
        /* Scheduling Assistant */
        .scheduling-assistant {
            background: rgba(0, 12, 36, 0.6);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }
        
        .time-slot {
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(0, 18, 51, 0.5);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }
        
        .time-slot:hover {
            background: rgba(0, 119, 190, 0.3);
            border: 1px solid #00f5ff;
        }
        
        .time-slot.available {
            border-left: 3px solid #00ff88;
        }
        
        /* Marine Life Decorations */
        .fish {
            position: absolute;
            font-size: 2em;
            color: rgba(0, 245, 255, 0.3);
            animation: swim 20s infinite linear;
            z-index: 0;
        }
        
        @keyframes swim {
            0% { transform: translateX(-100px) translateY(0) scaleX(1); }
            49% { transform: translateX(calc(100vw + 100px)) translateY(20px) scaleX(1); }
            50% { transform: translateX(calc(100vw + 100px)) translateY(20px) scaleX(-1); }
            99% { transform: translateX(-100px) translateY(0) scaleX(-1); }
            100% { transform: translateX(-100px) translateY(0) scaleX(1); }
        }
        
        /* Wave Effect */
        .wave {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120"><path fill="%230077be" fill-opacity="0.1" d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path></svg>');
            background-size: 1200px 100px;
            animation: wave 10s infinite linear;
            z-index: 1;
        }
        
        @keyframes wave {
            0% { background-position-x: 0; }
            100% { background-position-x: 1200px; }
        }
        
        /* Depth Indicator */
        .depth-indicator {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 10px;
            height: 300px;
            background: linear-gradient(to bottom, #00f5ff, #0077be, #001845);
            border-radius: 5px;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            z-index: 100;
        }
        
        .depth-marker {
            position: absolute;
            width: 30px;
            height: 2px;
            background: #00f5ff;
            left: -10px;
            animation: depthPulse 2s infinite;
        }
        
        @keyframes depthPulse {
            0%, 100% { opacity: 0.5; width: 30px; }
            50% { opacity: 1; width: 40px; }
        }
        
        /* Modal for Event Details */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 8, 20, 0.9);
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 18, 51, 0.95);
            border: 1px solid #00f5ff;
            border-radius: 20px;
            padding: 30px;
            width: 500px;
            box-shadow: 0 0 50px rgba(0, 245, 255, 0.5);
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2em;
            color: #00f5ff;
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .close-modal:hover {
            transform: rotate(90deg);
        }
    </style>
</head>
<body>
    <!-- Ocean Background -->
    <div class="ocean-bg"></div>
    <div class="caustics"></div>
    
    <!-- Bubbles -->
    <div class="bubble" style="width: 20px; height: 20px; left: 10%; animation-delay: 0s;"></div>
    <div class="bubble" style="width: 15px; height: 15px; left: 30%; animation-delay: 2s;"></div>
    <div class="bubble" style="width: 25px; height: 25px; left: 50%; animation-delay: 4s;"></div>
    <div class="bubble" style="width: 10px; height: 10px; left: 70%; animation-delay: 6s;"></div>
    <div class="bubble" style="width: 30px; height: 30px; left: 90%; animation-delay: 8s;"></div>
    
    <!-- Bioluminescent Particles -->
    <div class="bioluminescent" style="top: 20%; left: 15%; animation-delay: 0s;"></div>
    <div class="bioluminescent" style="top: 40%; left: 80%; animation-delay: 1s;"></div>
    <div class="bioluminescent" style="top: 60%; left: 25%; animation-delay: 2s;"></div>
    <div class="bioluminescent" style="top: 80%; left: 60%; animation-delay: 3s;"></div>
    
    <!-- Marine Life -->
    <div class="fish" style="top: 30%; animation-delay: 0s;">🐟</div>
    <div class="fish" style="top: 60%; animation-delay: 5s;">🐠</div>
    <div class="fish" style="top: 80%; animation-delay: 10s;">🦈</div>
    
    <!-- Wave Effect -->
    <div class="wave"></div>
    
    <!-- Depth Indicator -->
    <div class="depth-indicator">
        <div class="depth-marker" style="top: 20%;"></div>
        <div class="depth-marker" style="top: 50%; animation-delay: 0.5s;"></div>
        <div class="depth-marker" style="top: 80%; animation-delay: 1s;"></div>
    </div>
    
    <!-- Main Calendar Dashboard -->
    <div class="calendar-dashboard">
        <!-- Header -->
        <div class="header">
            <h1>Ocean Depths Calendar</h1>
            <div class="view-selector">
                <button class="view-btn active" data-view="month">Month</button>
                <button class="view-btn" data-view="week">Week</button>
                <button class="view-btn" data-view="day">Day</button>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Calendar View -->
            <div class="calendar-view">
                <div class="month-nav">
                    <button class="nav-btn" id="prevMonth">‹</button>
                    <h2 id="currentMonth">January 2024</h2>
                    <button class="nav-btn" id="nextMonth">›</button>
                </div>
                <div class="calendar-grid" id="calendarGrid">
                    <!-- Calendar will be populated by JavaScript -->
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Weather Widget -->
                <div class="weather-widget">
                    <h3>Weather Forecast</h3>
                    <div class="weather-icon">🌊</div>
                    <div class="weather-temp">72°F</div>
                    <p>Ocean Breeze</p>
                    <p style="font-size: 0.9em; color: #0077be;">Perfect for diving</p>
                </div>
                
                <!-- Event List -->
                <div class="event-list">
                    <h3 style="margin-bottom: 15px;">Today's Events</h3>
                    <div class="event-item">
                        <div>
                            <div class="event-time">9:00 AM</div>
                            <div class="event-title">Deep Sea Exploration</div>
                            <div style="font-size: 0.9em; color: #0077be;">Research vessel departure</div>
                        </div>
                    </div>
                    <div class="event-item">
                        <div>
                            <div class="event-time">2:00 PM</div>
                            <div class="event-title">Marine Biology Conference</div>
                            <div style="font-size: 0.9em; color: #0077be;">Virtual meeting room</div>
                        </div>
                    </div>
                    <div class="event-item">
                        <div>
                            <div class="event-time">6:00 PM</div>
                            <div class="event-title">Sunset Dive</div>
                            <div style="font-size: 0.9em; color: #0077be;">Coral reef observation</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Tools -->
        <div class="bottom-tools">
            <!-- Quick Add -->
            <div class="quick-add">
                <h3 style="margin-bottom: 15px;">Quick Add Event</h3>
                <input type="text" placeholder="Event title..." id="eventTitle">
                <input type="datetime-local" id="eventDateTime">
                <button class="add-btn">Add to Calendar</button>
            </div>
            
            <!-- Reminders -->
            <div class="reminders">
                <h3 style="margin-bottom: 15px;">Reminders</h3>
                <div class="reminder-item">
                    <input type="checkbox" class="reminder-checkbox">
                    <span>Check diving equipment</span>
                </div>
                <div class="reminder-item">
                    <input type="checkbox" class="reminder-checkbox">
                    <span>Review ocean current data</span>
                </div>
                <div class="reminder-item">
                    <input type="checkbox" class="reminder-checkbox">
                    <span>Prepare research notes</span>
                </div>
            </div>
            
            <!-- Scheduling Assistant -->
            <div class="scheduling-assistant">
                <h3 style="margin-bottom: 15px;">Available Time Slots</h3>
                <div class="time-slot available">10:00 AM - 11:00 AM</div>
                <div class="time-slot available">3:00 PM - 4:00 PM</div>
                <div class="time-slot available">7:00 PM - 8:00 PM</div>
            </div>
        </div>
    </div>
    
    <!-- Modal -->
    <div class="modal" id="eventModal">
        <div class="modal-content">
            <span class="close-modal" id="closeModal">&times;</span>
            <h2 style="color: #00f5ff; margin-bottom: 20px;">Event Details</h2>
            <div id="modalContent">
                <!-- Event details will be populated here -->
            </div>
        </div>
    </div>
    
    <script>
        // Calendar Logic
        const months = ['January', 'February', 'March', 'April', 'May', 'June', 
                       'July', 'August', 'September', 'October', 'November', 'December'];
        let currentDate = new Date();
        let currentMonth = currentDate.getMonth();
        let currentYear = currentDate.getFullYear();
        
        // Sample events data
        const events = {
            '2024-1-15': [
                { time: '10:00 AM', title: 'Submarine Test', color: '#00ff88' },
                { time: '2:00 PM', title: 'Ocean Mapping', color: '#00f5ff' }
            ],
            '2024-1-20': [
                { time: '9:00 AM', title: 'Deep Dive Training', color: '#ff6b6b' }
            ],
            '2024-1-25': [
                { time: '11:00 AM', title: 'Marine Research', color: '#4ecdc4' },
                { time: '3:00 PM', title: 'Equipment Check', color: '#ffe66d' }
            ]
        };
        
        function generateCalendar() {
            const firstDay = new Date(currentYear, currentMonth, 1).getDay();
            const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
            const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();
            
            const calendarGrid = document.getElementById('calendarGrid');
            calendarGrid.innerHTML = '';
            
            // Add day headers
            const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            dayHeaders.forEach(day => {
                const header = document.createElement('div');
                header.className = 'day-header';
                header.textContent = day;
                calendarGrid.appendChild(header);
            });
            
            // Add previous month days
            for (let i = firstDay - 1; i >= 0; i--) {
                const dayDiv = createDayElement(daysInPrevMonth - i, true);
                calendarGrid.appendChild(dayDiv);
            }
            
            // Add current month days
            const today = new Date();
            for (let day = 1; day <= daysInMonth; day++) {
                const isToday = day === today.getDate() && 
                               currentMonth === today.getMonth() && 
                               currentYear === today.getFullYear();
                const dayDiv = createDayElement(day, false, isToday);
                calendarGrid.appendChild(dayDiv);
            }
            
            // Add next month days
            const remainingDays = 42 - (firstDay + daysInMonth);
            for (let day = 1; day <= remainingDays; day++) {
                const dayDiv = createDayElement(day, true);
                calendarGrid.appendChild(dayDiv);
            }
            
            // Update month display
            document.getElementById('currentMonth').textContent = 
                `${months[currentMonth]} ${currentYear}`;
        }
        
        function createDayElement(day, isOtherMonth, isToday) {
            const dayDiv = document.createElement('div');
            dayDiv.className = 'calendar-day';
            if (isOtherMonth) dayDiv.classList.add('other-month');
            if (isToday) dayDiv.classList.add('today');
            
            const dayNumber = document.createElement('div');
            dayNumber.className = 'day-number';
            dayNumber.textContent = day;
            dayDiv.appendChild(dayNumber);
            
            // Add events if any
            const dateKey = `${currentYear}-${currentMonth + 1}-${day}`;
            if (events[dateKey] && !isOtherMonth) {
                const dayEvents = document.createElement('div');
                dayEvents.className = 'day-events';
                events[dateKey].forEach(event => {
                    const dot = document.createElement('span');
                    dot.className = 'event-dot';
                    dot.style.backgroundColor = event.color;
                    dayEvents.appendChild(dot);
                });
                dayDiv.appendChild(dayEvents);
            }
            
            // Add click event
            dayDiv.addEventListener('click', () => {
                if (!isOtherMonth) {
                    showDayDetails(day);
                }
            });
            
            // Add hover sound effect (visual feedback)
            dayDiv.addEventListener('mouseenter', () => {
                createRipple(dayDiv);
            });
            
            return dayDiv;
        }
        
        function createRipple(element) {
            const ripple = document.createElement('div');
            ripple.style.position = 'absolute';
            ripple.style.width = '20px';
            ripple.style.height = '20px';
            ripple.style.background = 'radial-gradient(circle, rgba(0, 245, 255, 0.6) 0%, transparent 70%)';
            ripple.style.borderRadius = '50%';
            ripple.style.top = '50%';
            ripple.style.left = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.animation = 'rippleExpand 0.6s ease-out';
            element.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        }
        
        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes rippleExpand {
                0% { width: 20px; height: 20px; opacity: 1; }
                100% { width: 100px; height: 100px; opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        function showDayDetails(day) {
            const modal = document.getElementById('eventModal');
            const modalContent = document.getElementById('modalContent');
            const dateKey = `${currentYear}-${currentMonth + 1}-${day}`;
            
            modalContent.innerHTML = `
                <h3 style="color: #0077be; margin-bottom: 15px;">
                    ${months[currentMonth]} ${day}, ${currentYear}
                </h3>
            `;
            
            if (events[dateKey]) {
                events[dateKey].forEach(event => {
                    modalContent.innerHTML += `
                        <div style="background: rgba(0, 18, 51, 0.5); padding: 15px; 
                                    border-left: 3px solid ${event.color}; 
                                    margin-bottom: 10px; border-radius: 0 10px 10px 0;">
                            <div style="color: #00f5ff; font-size: 0.9em;">${event.time}</div>
                            <div style="font-weight: bold; margin-top: 5px;">${event.title}</div>
                        </div>
                    `;
                });
            } else {
                modalContent.innerHTML += '<p style="color: #0077be;">No events scheduled for this day.</p>';
            }
            
            modal.style.display = 'block';
        }
        
        // Navigation
        document.getElementById('prevMonth').addEventListener('click', () => {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            generateCalendar();
        });
        
        document.getElementById('nextMonth').addEventListener('click', () => {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            generateCalendar();
        });
        
        // View selector
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                // In a real application, this would change the calendar view
                createRipple(this);
            });
        });
        
        // Quick add event
        document.querySelector('.add-btn').addEventListener('click', () => {
            const title = document.getElementById('eventTitle').value;
            const dateTime = document.getElementById('eventDateTime').value;
            
            if (title && dateTime) {
                // In a real application, this would add the event to the calendar
                alert(`Event "${title}" scheduled for ${new Date(dateTime).toLocaleString()}`);
                document.getElementById('eventTitle').value = '';
                document.getElementById('eventDateTime').value = '';
            }
        });
        
        // Modal close
        document.getElementById('closeModal').addEventListener('click', () => {
            document.getElementById('eventModal').style.display = 'none';
        });
        
        // Reminder checkboxes
        document.querySelectorAll('.reminder-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    this.parentElement.style.textDecoration = 'line-through';
                    this.parentElement.style.opacity = '0.5';
                } else {
                    this.parentElement.style.textDecoration = 'none';
                    this.parentElement.style.opacity = '1';
                }
            });
        });
        
        // Time slot selection
        document.querySelectorAll('.time-slot').forEach(slot => {
            slot.addEventListener('click', function() {
                this.style.background = 'rgba(0, 119, 190, 0.5)';
                this.style.border = '1px solid #00f5ff';
                setTimeout(() => {
                    this.style.background = '';
                    this.style.border = '';
                }, 2000);
            });
        });
        
        // Create more bubbles dynamically
        function createBubble() {
            const bubble = document.createElement('div');
            bubble.className = 'bubble';
            bubble.style.width = Math.random() * 20 + 10 + 'px';
            bubble.style.height = bubble.style.width;
            bubble.style.left = Math.random() * 100 + '%';
            bubble.style.animationDuration = Math.random() * 10 + 10 + 's';
            bubble.style.animationDelay = Math.random() * 5 + 's';
            document.body.appendChild(bubble);
            
            setTimeout(() => bubble.remove(), 20000);
        }
        
        // Create bubbles periodically
        setInterval(createBubble, 3000);
        
        // Initialize calendar
        generateCalendar();
        
        // Animate bioluminescent particles
        document.querySelectorAll('.bioluminescent').forEach((particle, index) => {
            setInterval(() => {
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
            }, 10000 + index * 2000);
        });
        
        // Sound effect simulation with visual feedback
        function playBubbleSound() {
            const indicator = document.createElement('div');
            indicator.style.position = 'fixed';
            indicator.style.bottom = '20px';
            indicator.style.right = '20px';
            indicator.style.padding = '10px 20px';
            indicator.style.background = 'rgba(0, 245, 255, 0.2)';
            indicator.style.border = '1px solid #00f5ff';
            indicator.style.borderRadius = '20px';
            indicator.style.color = '#00f5ff';
            indicator.textContent = '🔊 Bubble sound';
            indicator.style.zIndex = '1000';
            document.body.appendChild(indicator);
            
            setTimeout(() => indicator.remove(), 1000);
        }
        
        // Add sound feedback to interactions
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('calendar-day') || 
                e.target.classList.contains('view-btn') ||
                e.target.classList.contains('add-btn')) {
                playBubbleSound();
            }
        });
    </script>
</body>
</html>