<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fractal Geometry Infinite Navigation</title>
    <style>
        :root {
            --golden-ratio: 1.618;
            --phi: 1.618;
            --fractal-primary: #1a1a2e;
            --fractal-secondary: #16213e;
            --fractal-accent: #0f3460;
            --fractal-gold: #e94560;
            --fractal-spiral: #f39c12;
            --sierpinski-blue: #2980b9;
            --mandelbrot-purple: #8e44ad;
            --julia-green: #27ae60;
            --fibonacci-orange: #d35400;
            --infinite-white: #ecf0f1;
            --shadow-depth: rgba(0, 0, 0, 0.618);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(45deg, var(--fractal-primary), var(--fractal-secondary));
            color: var(--infinite-white);
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Fractal Background Animation */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(ellipse at 20% 30%, var(--mandelbrot-purple) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 70%, var(--sierpinski-blue) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 50%, var(--julia-green) 0%, transparent 70%);
            animation: fractalDrift 20s infinite linear;
            opacity: 0.3;
            z-index: -1;
        }

        @keyframes fractalDrift {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(0.9); }
            100% { transform: rotate(360deg) scale(1); }
        }

        main {
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        h1 {
            text-align: center;
            padding: calc(1rem * var(--phi));
            font-size: calc(1.5rem * var(--golden-ratio));
            background: linear-gradient(45deg, var(--fractal-gold), var(--fibonacci-orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px var(--fractal-gold);
            letter-spacing: 0.1em;
            position: relative;
        }

        .hybrid-component {
            flex: 1;
            display: grid;
            grid-template-columns: minmax(250px, calc(250px * var(--phi))) 1fr;
            grid-template-rows: auto 1fr auto;
            grid-template-areas: 
                "nav-header zoom-controls"
                "nav-tree infinite-viewport"
                "breadcrumbs status-bar";
            height: 100%;
            gap: 1px;
            background: var(--shadow-depth);
        }

        /* Fractal Navigation Header */
        .nav-header {
            grid-area: nav-header;
            background: var(--fractal-accent);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 2px solid var(--fractal-gold);
        }

        .recursive-search {
            flex: 1;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            background: var(--fractal-secondary);
            border: 1px solid var(--sierpinski-blue);
            border-radius: calc(4px * var(--phi));
            color: var(--infinite-white);
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--fractal-gold);
            box-shadow: 0 0 15px var(--fractal-gold);
            transform: scale(1.02);
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--fractal-secondary);
            border: 1px solid var(--sierpinski-blue);
            border-radius: calc(4px * var(--phi));
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-suggestion {
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .search-suggestion:hover {
            background: var(--fractal-accent);
            border-left-color: var(--fractal-gold);
            transform: translateX(calc(5px * var(--phi)));
        }

        /* Zoom Controls */
        .zoom-controls {
            grid-area: zoom-controls;
            background: var(--fractal-accent);
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            border-bottom: 2px solid var(--fractal-gold);
        }

        .zoom-button {
            background: var(--fractal-secondary);
            border: 1px solid var(--sierpinski-blue);
            color: var(--infinite-white);
            padding: 0.5rem 1rem;
            border-radius: calc(4px * var(--phi));
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            min-width: calc(40px * var(--phi));
        }

        .zoom-button:hover {
            background: var(--fractal-gold);
            border-color: var(--fractal-gold);
            transform: scale(var(--phi));
            box-shadow: 0 0 15px var(--fractal-gold);
        }

        .zoom-level {
            color: var(--fractal-gold);
            font-weight: bold;
            min-width: 100px;
            text-align: center;
        }

        /* Hierarchical Navigation Tree */
        .nav-tree {
            grid-area: nav-tree;
            background: var(--fractal-secondary);
            overflow-y: auto;
            overflow-x: hidden;
            padding: 1rem;
            position: relative;
        }

        .tree-level {
            margin-left: calc(1rem * var(--phi));
            position: relative;
        }

        .tree-level::before {
            content: '';
            position: absolute;
            left: calc(-0.5rem * var(--phi));
            top: 0;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to bottom, var(--fractal-gold), transparent);
        }

        .tree-item {
            padding: calc(0.5rem * var(--phi));
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: calc(4px * var(--phi));
            position: relative;
            margin: 2px 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tree-item::before {
            content: '';
            width: calc(8px * var(--phi));
            height: calc(8px * var(--phi));
            background: var(--fractal-gold);
            border-radius: 50%;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .tree-item:hover {
            background: var(--fractal-accent);
            transform: translateX(calc(5px * var(--phi)));
            box-shadow: 0 2px 10px var(--shadow-depth);
        }

        .tree-item:hover::before {
            background: var(--fibonacci-orange);
            transform: scale(var(--phi));
            box-shadow: 0 0 10px var(--fibonacci-orange);
        }

        .tree-item.active {
            background: var(--fractal-accent);
            border-left: 3px solid var(--fractal-gold);
        }

        .tree-item.active::before {
            background: var(--fractal-gold);
            animation: fractalPulse 2s infinite;
        }

        @keyframes fractalPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(var(--phi)); opacity: 0.618; }
        }

        /* Infinite Viewport */
        .infinite-viewport {
            grid-area: infinite-viewport;
            background: var(--fractal-primary);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--fractal-accent);
        }

        .content-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transition: transform 0.5s cubic-bezier(0.618, 0, 0.382, 1);
            transform-origin: center center;
        }

        .infinite-scroll-container {
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        .content-section {
            padding: calc(2rem * var(--phi));
            margin: 1rem;
            background: var(--fractal-secondary);
            border-radius: calc(8px * var(--phi));
            border: 1px solid var(--sierpinski-blue);
            transition: all 0.3s ease;
            position: relative;
        }

        .content-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--fractal-gold), var(--fibonacci-orange), var(--julia-green), var(--sierpinski-blue));
            background-size: 200% 100%;
            animation: gradientShift 3s infinite linear;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            100% { background-position: 200% 50%; }
        }

        .content-section:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px var(--shadow-depth);
            border-color: var(--fractal-gold);
        }

        .section-title {
            color: var(--fractal-gold);
            font-size: calc(1.2rem * var(--phi));
            margin-bottom: 1rem;
            position: relative;
        }

        .section-content {
            line-height: var(--phi);
            opacity: 0.9;
        }

        /* Fractal Breadcrumbs */
        .breadcrumbs {
            grid-area: breadcrumbs;
            background: var(--fractal-accent);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            overflow-x: auto;
            border-top: 2px solid var(--fractal-gold);
        }

        .breadcrumb {
            color: var(--infinite-white);
            text-decoration: none;
            padding: 0.25rem 0.5rem;
            border-radius: calc(4px * var(--phi));
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
        }

        .breadcrumb::after {
            content: '→';
            margin-left: 0.5rem;
            color: var(--fractal-gold);
        }

        .breadcrumb:last-child::after {
            display: none;
        }

        .breadcrumb:hover {
            background: var(--fractal-secondary);
            color: var(--fractal-gold);
            transform: scale(1.05);
        }

        .breadcrumb.current {
            background: var(--fractal-gold);
            color: var(--fractal-primary);
            font-weight: bold;
        }

        /* Status Bar */
        .status-bar {
            grid-area: status-bar;
            background: var(--fractal-accent);
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            border-top: 1px solid var(--sierpinski-blue);
        }

        .status-info {
            display: flex;
            gap: 2rem;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--julia-green);
            animation: fractalPulse 2s infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 1fr auto auto;
                grid-template-areas: 
                    "nav-header"
                    "zoom-controls"
                    "infinite-viewport"
                    "breadcrumbs"
                    "status-bar";
            }
            
            .nav-tree {
                display: none;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Custom Scrollbars */
        .nav-tree::-webkit-scrollbar,
        .infinite-scroll-container::-webkit-scrollbar {
            width: 8px;
        }

        .nav-tree::-webkit-scrollbar-track,
        .infinite-scroll-container::-webkit-scrollbar-track {
            background: var(--fractal-primary);
        }

        .nav-tree::-webkit-scrollbar-thumb,
        .infinite-scroll-container::-webkit-scrollbar-thumb {
            background: var(--fractal-gold);
            border-radius: 4px;
        }

        .nav-tree::-webkit-scrollbar-thumb:hover,
        .infinite-scroll-container::-webkit-scrollbar-thumb:hover {
            background: var(--fibonacci-orange);
        }
    </style>
</head>
<body>
    <main>
        <h1>Infinite Navigation - Fractal Geometry Theme</h1>
        <div class="hybrid-component">
            <!-- Navigation Header with Recursive Search -->
            <div class="nav-header">
                <div class="recursive-search">
                    <input type="text" class="search-input" placeholder="Search across infinite layers..." 
                           aria-label="Recursive search across all content layers">
                    <div class="search-suggestions" id="searchSuggestions"></div>
                </div>
            </div>

            <!-- Zoom Controls -->
            <div class="zoom-controls">
                <button class="zoom-button" id="zoomOut" aria-label="Zoom out">−</button>
                <div class="zoom-level" id="zoomLevel">1.000x</div>
                <button class="zoom-button" id="zoomIn" aria-label="Zoom in">+</button>
                <button class="zoom-button" id="resetZoom" aria-label="Reset zoom">⌂</button>
            </div>

            <!-- Hierarchical Navigation Tree -->
            <nav class="nav-tree" id="navTree" role="navigation" aria-label="Fractal navigation tree">
                <div class="tree-level">
                    <div class="tree-item active" data-path="root" data-level="0">
                        <span>∞ Root Universe</span>
                    </div>
                    <div class="tree-level">
                        <div class="tree-item" data-path="root.mandelbrot" data-level="1">
                            <span>⟐ Mandelbrot Set</span>
                        </div>
                        <div class="tree-level">
                            <div class="tree-item" data-path="root.mandelbrot.bulb" data-level="2">
                                <span>◈ Main Bulb</span>
                            </div>
                            <div class="tree-item" data-path="root.mandelbrot.tendrils" data-level="2">
                                <span>⟨ Fractal Tendrils</span>
                            </div>
                        </div>
                        <div class="tree-item" data-path="root.julia" data-level="1">
                            <span>◎ Julia Sets</span>
                        </div>
                        <div class="tree-level">
                            <div class="tree-item" data-path="root.julia.connected" data-level="2">
                                <span>⟡ Connected Sets</span>
                            </div>
                            <div class="tree-item" data-path="root.julia.disconnected" data-level="2">
                                <span>⟢ Cantor Dust</span>
                            </div>
                        </div>
                        <div class="tree-item" data-path="root.sierpinski" data-level="1">
                            <span>△ Sierpinski Triangle</span>
                        </div>
                        <div class="tree-level">
                            <div class="tree-item" data-path="root.sierpinski.gasket" data-level="2">
                                <span>▲ Gasket Pattern</span>
                            </div>
                            <div class="tree-item" data-path="root.sierpinski.carpet" data-level="2">
                                <span>▢ Carpet Variation</span>
                            </div>
                        </div>
                        <div class="tree-item" data-path="root.dragon" data-level="1">
                            <span>⟐ Dragon Curve</span>
                        </div>
                        <div class="tree-item" data-path="root.fibonacci" data-level="1">
                            <span>φ Fibonacci Spiral</span>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Infinite Viewport -->
            <div class="infinite-viewport">
                <div class="content-layer" id="contentLayer">
                    <div class="infinite-scroll-container" id="scrollContainer">
                        <div class="content-section" data-depth="0">
                            <h2 class="section-title">∞ Welcome to the Fractal Universe</h2>
                            <div class="section-content">
                                <p>Enter the infinite realm where mathematics meets art. Each click, scroll, and zoom reveals new layers of complexity, following the golden ratio φ = 1.618... that governs natural beauty.</p>
                                <p>Navigate through self-similar patterns that repeat endlessly at every scale. Discover the infinite depth hidden within finite boundaries.</p>
                            </div>
                        </div>

                        <div class="content-section" data-depth="1">
                            <h2 class="section-title">⟐ The Mandelbrot Set</h2>
                            <div class="section-content">
                                <p>The most famous fractal, defined by the simple equation z² + c. Its boundary contains infinite complexity, with spiral arms and self-similar miniature copies appearing at every zoom level.</p>
                                <p>Zoom depth: 1 → ∞. Each iteration reveals new mathematical poetry written in the language of complex numbers.</p>
                            </div>
                        </div>

                        <div class="content-section" data-depth="2">
                            <h2 class="section-title">◎ Julia Sets Collection</h2>
                            <div class="section-content">
                                <p>For each point c in the complex plane, there exists a unique Julia set. Connected sets form intricate, filled shapes while disconnected sets create Cantor dust - infinite sparse structures.</p>
                                <p>Interactive parameter: c = φ + φi creates particularly elegant connected Julia sets.</p>
                            </div>
                        </div>

                        <div class="content-section" data-depth="3">
                            <h2 class="section-title">△ Sierpinski's Geometry</h2>
                            <div class="section-content">
                                <p>The Sierpinski triangle demonstrates perfect self-similarity. Start with a triangle, remove the center triangle, repeat forever. The result has zero area but infinite perimeter.</p>
                                <p>Dimension: log(3)/log(2) ≈ 1.585 - between a line and a plane, existing in fractional dimensions.</p>
                            </div>
                        </div>

                        <div class="content-section" data-depth="4">
                            <h2 class="section-title">⟐ Dragon Curve Evolution</h2>
                            <div class="section-content">
                                <p>Fold a strip of paper infinitely many times, then unfold it at right angles. The result is the dragon curve - a space-filling curve that never intersects itself.</p>
                                <p>Generation n+1 = Generation n + (90° turn) + (Generation n reversed). Infinite complexity from simple rules.</p>
                            </div>
                        </div>

                        <div class="content-section" data-depth="5">
                            <h2 class="section-title">φ Golden Ratio Spirals</h2>
                            <div class="section-content">
                                <p>The Fibonacci sequence converges to φ, appearing in nautilus shells, galaxy arms, and flower petals. The golden spiral grows by a factor of φ every quarter turn.</p>
                                <p>φ = (1 + √5)/2 ≈ 1.618033988749... The most irrational number, resisting rational approximation.</p>
                            </div>
                        </div>

                        <!-- Loading indicator for infinite scroll -->
                        <div class="content-section" id="loadingSection" style="text-align: center; opacity: 0.5;">
                            <h2 class="section-title">∞ Generating infinite content...</h2>
                            <div class="section-content">
                                <p>Recursively creating new fractal dimensions...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fractal Breadcrumbs -->
            <nav class="breadcrumbs" role="navigation" aria-label="Current location in fractal hierarchy">
                <a href="#" class="breadcrumb" data-level="0">∞ Universe</a>
                <a href="#" class="breadcrumb current" data-level="1">Root</a>
            </nav>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-info">
                    <div class="status-item">
                        <div class="status-indicator"></div>
                        <span>Fractal Engine Active</span>
                    </div>
                    <div class="status-item">
                        <span>Depth: <span id="currentDepth">∞</span></span>
                    </div>
                    <div class="status-item">
                        <span>Scale: <span id="currentScale">1:1</span></span>
                    </div>
                </div>
                <div class="status-info">
                    <div class="status-item">
                        <span>φ = 1.618...</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Fractal Geometry Infinite Navigation System
        class FractalInfiniteNavigation {
            constructor() {
                this.currentZoom = 1.0;
                this.currentPath = 'root';
                this.currentDepth = 0;
                this.contentCounter = 6;
                this.goldenRatio = 1.618033988749;
                this.maxZoom = 10.0;
                this.minZoom = 0.1;
                
                this.initializeEventListeners();
                this.initializeInfiniteScroll();
                this.initializeSearch();
                this.updateStatusBar();
                
                // Generate initial fractal patterns
                this.generateFractalContent();
            }

            initializeEventListeners() {
                // Zoom controls
                document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
                document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
                document.getElementById('resetZoom').addEventListener('click', () => this.resetZoom());

                // Navigation tree
                document.getElementById('navTree').addEventListener('click', (e) => {
                    if (e.target.closest('.tree-item')) {
                        this.navigateToItem(e.target.closest('.tree-item'));
                    }
                });

                // Breadcrumb navigation
                document.querySelector('.breadcrumbs').addEventListener('click', (e) => {
                    e.preventDefault();
                    if (e.target.classList.contains('breadcrumb')) {
                        this.navigateToBreadcrumb(e.target);
                    }
                });

                // Keyboard navigation
                document.addEventListener('keydown', (e) => this.handleKeyboard(e));

                // Zoom with mouse wheel
                document.getElementById('contentLayer').addEventListener('wheel', (e) => {
                    e.preventDefault();
                    if (e.deltaY < 0) {
                        this.zoomIn();
                    } else {
                        this.zoomOut();
                    }
                });
            }

            initializeInfiniteScroll() {
                const container = document.getElementById('scrollContainer');
                const loadingSection = document.getElementById('loadingSection');

                container.addEventListener('scroll', () => {
                    const scrollTop = container.scrollTop;
                    const scrollHeight = container.scrollHeight;
                    const clientHeight = container.clientHeight;

                    // Load more content when near bottom
                    if (scrollTop + clientHeight >= scrollHeight - 100) {
                        this.loadMoreContent();
                    }

                    // Update current depth based on scroll position
                    this.updateDepthFromScroll();
                });
            }

            initializeSearch() {
                const searchInput = document.querySelector('.search-input');
                const suggestions = document.getElementById('searchSuggestions');
                
                const searchTerms = [
                    'Mandelbrot main bulb',
                    'Julia connected sets',
                    'Sierpinski gasket',
                    'Dragon curve evolution',
                    'Fibonacci spiral',
                    'Golden ratio patterns',
                    'Fractal dimensions',
                    'Infinite recursion',
                    'Complex plane mapping',
                    'Self-similar structures'
                ];

                searchInput.addEventListener('input', (e) => {
                    const query = e.target.value.toLowerCase();
                    if (query.length > 2) {
                        const matches = searchTerms.filter(term => 
                            term.toLowerCase().includes(query)
                        );
                        this.showSearchSuggestions(matches, suggestions);
                    } else {
                        suggestions.style.display = 'none';
                    }
                });

                searchInput.addEventListener('blur', () => {
                    setTimeout(() => suggestions.style.display = 'none', 200);
                });
            }

            showSearchSuggestions(matches, container) {
                container.innerHTML = '';
                if (matches.length > 0) {
                    matches.forEach(match => {
                        const suggestion = document.createElement('div');
                        suggestion.className = 'search-suggestion';
                        suggestion.textContent = match;
                        suggestion.addEventListener('click', () => {
                            this.searchRecursively(match);
                            container.style.display = 'none';
                        });
                        container.appendChild(suggestion);
                    });
                    container.style.display = 'block';
                } else {
                    container.style.display = 'none';
                }
            }

            searchRecursively(term) {
                // Simulate recursive search through fractal layers
                const searchDepth = Math.floor(Math.random() * 5) + 1;
                const searchPath = `search.${term.replace(/\s+/g, '_').toLowerCase()}`;
                
                this.animateSearchTraversal(searchDepth, () => {
                    this.highlightSearchResult(term);
                });
            }

            animateSearchTraversal(depth, callback) {
                let currentDepth = 0;
                const interval = setInterval(() => {
                    currentDepth++;
                    document.getElementById('currentDepth').textContent = `Searching level ${currentDepth}...`;
                    
                    if (currentDepth >= depth) {
                        clearInterval(interval);
                        callback();
                    }
                }, 200);
            }

            highlightSearchResult(term) {
                // Find and highlight matching content
                const sections = document.querySelectorAll('.content-section');
                sections.forEach(section => {
                    const content = section.textContent.toLowerCase();
                    if (content.includes(term.toLowerCase())) {
                        section.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        section.style.borderColor = 'var(--fractal-gold)';
                        section.style.boxShadow = '0 0 20px var(--fractal-gold)';
                        
                        setTimeout(() => {
                            section.style.borderColor = '';
                            section.style.boxShadow = '';
                        }, 3000);
                        return;
                    }
                });
            }

            zoomIn() {
                if (this.currentZoom < this.maxZoom) {
                    this.currentZoom *= this.goldenRatio;
                    this.updateZoom();
                }
            }

            zoomOut() {
                if (this.currentZoom > this.minZoom) {
                    this.currentZoom /= this.goldenRatio;
                    this.updateZoom();
                }
            }

            resetZoom() {
                this.currentZoom = 1.0;
                this.updateZoom();
            }

            updateZoom() {
                const layer = document.getElementById('contentLayer');
                layer.style.transform = `scale(${this.currentZoom})`;
                
                document.getElementById('zoomLevel').textContent = 
                    `${this.currentZoom.toFixed(3)}x`;
                
                // Update scale status
                const scaleRatio = Math.round(1 / this.currentZoom);
                document.getElementById('currentScale').textContent = 
                    `1:${scaleRatio}`;
                
                // Reveal different content at different zoom levels
                this.revealZoomContent();
            }

            revealZoomContent() {
                const sections = document.querySelectorAll('.content-section');
                sections.forEach((section, index) => {
                    const depth = parseInt(section.dataset.depth);
                    const zoomThreshold = Math.pow(this.goldenRatio, depth);
                    
                    if (this.currentZoom >= zoomThreshold * 0.5) {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    } else {
                        section.style.opacity = '0.3';
                        section.style.transform = 'translateY(20px)';
                    }
                });
            }

            navigateToItem(item) {
                // Update active state
                document.querySelectorAll('.tree-item').forEach(el => 
                    el.classList.remove('active'));
                item.classList.add('active');

                // Update current path and depth
                this.currentPath = item.dataset.path;
                this.currentDepth = parseInt(item.dataset.level);

                // Update breadcrumbs
                this.updateBreadcrumbs();
                
                // Animate navigation
                this.animateNavigation(item);
                
                // Update status
                this.updateStatusBar();
            }

            navigateToBreadcrumb(breadcrumb) {
                const level = parseInt(breadcrumb.dataset.level);
                this.navigateToLevel(level);
            }

            navigateToLevel(level) {
                this.currentDepth = level;
                this.updateBreadcrumbs();
                this.updateStatusBar();
                
                // Auto-scroll to appropriate content
                const targetSection = document.querySelector(`[data-depth="${level}"]`);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }

            updateBreadcrumbs() {
                const breadcrumbs = document.querySelector('.breadcrumbs');
                const pathParts = this.currentPath.split('.');
                
                breadcrumbs.innerHTML = '';
                
                pathParts.forEach((part, index) => {
                    const breadcrumb = document.createElement('a');
                    breadcrumb.href = '#';
                    breadcrumb.className = 'breadcrumb';
                    breadcrumb.dataset.level = index;
                    
                    if (index === pathParts.length - 1) {
                        breadcrumb.classList.add('current');
                    }
                    
                    // Format breadcrumb text
                    const symbols = ['∞', '⟐', '◎', '△', '⟐', 'φ'];
                    const names = ['Universe', 'Mandelbrot', 'Julia', 'Sierpinski', 'Dragon', 'Fibonacci'];
                    breadcrumb.textContent = `${symbols[index % symbols.length]} ${names[index % names.length]}`;
                    
                    breadcrumbs.appendChild(breadcrumb);
                });
            }

            animateNavigation(item) {
                const container = document.getElementById('scrollContainer');
                const targetIndex = parseInt(item.dataset.level);
                
                // Smooth scroll to corresponding content
                const targetSection = document.querySelector(`[data-depth="${targetIndex}"]`);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                
                // Visual feedback
                item.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    item.style.transform = '';
                }, 300);
            }

            loadMoreContent() {
                const container = document.getElementById('scrollContainer');
                const loadingSection = document.getElementById('loadingSection');
                
                // Generate new fractal content sections
                for (let i = 0; i < 3; i++) {
                    const section = this.createFractalSection(this.contentCounter++);
                    container.insertBefore(section, loadingSection);
                }
                
                // Animate new content appearance
                const newSections = container.querySelectorAll('.content-section');
                newSections.forEach((section, index) => {
                    if (index >= newSections.length - 3) {
                        section.style.opacity = '0';
                        section.style.transform = 'translateY(50px)';
                        
                        setTimeout(() => {
                            section.style.transition = 'all 0.5s ease';
                            section.style.opacity = '1';
                            section.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            }

            createFractalSection(index) {
                const section = document.createElement('div');
                section.className = 'content-section';
                section.dataset.depth = index;
                
                const titles = [
                    '∞ Infinite Zoom Layers',
                    '⟐ Recursive Structures',
                    '◈ Boundary Phenomena',
                    '⟨ Chaotic Dynamics',
                    '△ Self-Organization',
                    'φ Natural Patterns'
                ];
                
                const contents = [
                    'Each zoom level reveals new mathematical structures, following the principle of infinite self-similarity across all scales.',
                    'Recursive algorithms generate patterns that repeat at every level, creating infinite complexity from simple rules.',
                    'At the boundary between order and chaos, fractal structures emerge with properties that transcend integer dimensions.',
                    'Dynamic systems exhibit sensitive dependence on initial conditions, leading to beautiful chaotic attractors.',
                    'Complex systems spontaneously organize into fractal patterns, demonstrating nature\'s preference for golden ratio proportions.',
                    'From spiral galaxies to nautilus shells, the golden ratio φ appears throughout nature as the most aesthetically pleasing proportion.'
                ];
                
                section.innerHTML = `
                    <h2 class="section-title">${titles[index % titles.length]}</h2>
                    <div class="section-content">
                        <p>${contents[index % contents.length]}</p>
                        <p>Depth level: ${index} | Scale factor: φ^${index} ≈ ${Math.pow(this.goldenRatio, index).toFixed(3)}</p>
                    </div>
                `;
                
                return section;
            }

            updateDepthFromScroll() {
                const container = document.getElementById('scrollContainer');
                const sections = container.querySelectorAll('.content-section:not(#loadingSection)');
                const scrollTop = container.scrollTop;
                const containerHeight = container.clientHeight;
                
                sections.forEach((section, index) => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    
                    if (scrollTop >= sectionTop - containerHeight / 2 && 
                        scrollTop < sectionTop + sectionHeight - containerHeight / 2) {
                        this.currentDepth = index;
                    }
                });
                
                this.updateStatusBar();
            }

            generateFractalContent() {
                // Add mathematical precision to content generation
                const container = document.getElementById('scrollContainer');
                const sections = container.querySelectorAll('.content-section');
                
                sections.forEach((section, index) => {
                    const depth = parseInt(section.dataset.depth);
                    const scaleData = document.createElement('div');
                    scaleData.style.cssText = `
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        font-size: 0.8em;
                        opacity: 0.7;
                        font-family: monospace;
                    `;
                    scaleData.textContent = `φ^${depth} = ${Math.pow(this.goldenRatio, depth).toFixed(6)}`;
                    section.style.position = 'relative';
                    section.appendChild(scaleData);
                });
            }

            updateStatusBar() {
                document.getElementById('currentDepth').textContent = 
                    this.currentDepth === 0 ? '∞' : this.currentDepth;
            }

            handleKeyboard(e) {
                switch(e.key) {
                    case '+':
                    case '=':
                        e.preventDefault();
                        this.zoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        this.zoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        this.resetZoom();
                        break;
                    case 'ArrowUp':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            this.navigateToLevel(Math.max(0, this.currentDepth - 1));
                        }
                        break;
                    case 'ArrowDown':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            this.navigateToLevel(this.currentDepth + 1);
                        }
                        break;
                }
            }
        }

        // Initialize the Fractal Infinite Navigation system
        document.addEventListener('DOMContentLoaded', () => {
            new FractalInfiniteNavigation();
        });

        // Add golden ratio animation to page elements
        document.addEventListener('DOMContentLoaded', () => {
            const elements = document.querySelectorAll('.tree-item, .content-section, .zoom-button');
            elements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transition = 'all 0.618s cubic-bezier(0.618, 0, 0.382, 1)';
                });
            });
        });
    </script>
</body>
</html>