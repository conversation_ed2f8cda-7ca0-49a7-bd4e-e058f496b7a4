<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Victorian Aethernet Communication Hub</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap');
        
        :root {
            --brass: #B87333;
            --bronze: #CD7F32;
            --copper: #B87E6C;
            --mahogany: #4B1F14;
            --leather: #3C241E;
            --parchment: #F4E8D0;
            --ink: #1A1A1A;
            --emerald: #50C878;
            --ruby: #E0115F;
            --steam: rgba(255, 255, 255, 0.6);
            --shadow: rgba(0, 0, 0, 0.8);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Crimson Text', serif;
            background: var(--mahogany);
            color: var(--parchment);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Victorian wallpaper pattern background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 50%, transparent 20%, var(--mahogany) 20.5%, var(--mahogany) 23%, transparent 23.5%),
                radial-gradient(circle at 40% 50%, transparent 20%, var(--mahogany) 20.5%, var(--mahogany) 23%, transparent 23.5%),
                radial-gradient(circle at 30% 35%, transparent 15%, var(--leather) 15.5%, var(--leather) 17%, transparent 17.5%),
                radial-gradient(circle at 30% 65%, transparent 15%, var(--leather) 15.5%, var(--leather) 17%, transparent 17.5%);
            background-size: 100px 100px;
            opacity: 0.3;
            pointer-events: none;
            z-index: 1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 2;
        }

        /* Header with brass pipes and steam effects */
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            padding: 40px 20px;
            background: linear-gradient(to bottom, rgba(26, 26, 26, 0.9), rgba(76, 31, 20, 0.9));
            border: 3px solid var(--brass);
            border-radius: 10px;
            box-shadow: 
                inset 0 0 50px rgba(184, 115, 51, 0.3),
                0 10px 30px var(--shadow);
        }

        .header::before,
        .header::after {
            content: '';
            position: absolute;
            width: 60px;
            height: 100%;
            background: linear-gradient(to bottom, var(--brass), var(--bronze));
            top: 0;
            border-radius: 30px;
            box-shadow: inset -5px 0 10px rgba(0, 0, 0, 0.5);
        }

        .header::before { left: -30px; }
        .header::after { right: -30px; }

        .header h1 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--brass);
            text-shadow: 
                2px 2px 4px var(--shadow),
                0 0 20px rgba(184, 115, 51, 0.5);
            letter-spacing: 0.1em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-style: italic;
            color: var(--copper);
            font-size: 1.1rem;
        }

        /* Steam animation */
        .steam-pipe {
            position: absolute;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, var(--brass), var(--bronze));
            border-radius: 50%;
            box-shadow: 
                inset -3px -3px 5px rgba(0, 0, 0, 0.5),
                2px 2px 5px rgba(0, 0, 0, 0.3);
        }

        .steam-pipe::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 60px;
            background: var(--steam);
            filter: blur(10px);
            animation: steam-rise 3s infinite;
            opacity: 0;
            left: 5px;
            bottom: 20px;
        }

        @keyframes steam-rise {
            0% {
                opacity: 0;
                transform: translateY(0) scale(0.8);
            }
            50% {
                opacity: 0.6;
            }
            100% {
                opacity: 0;
                transform: translateY(-40px) scale(1.5);
            }
        }

        .steam-pipe.top-left { top: 20px; left: 20px; }
        .steam-pipe.top-right { top: 20px; right: 20px; }

        /* Main communication hub layout */
        .communication-hub {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            gap: 20px;
            height: calc(100vh - 200px);
            min-height: 600px;
        }

        /* Brass-framed panels */
        .panel {
            background: rgba(26, 26, 26, 0.9);
            border: 3px solid var(--brass);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 
                inset 0 0 30px rgba(0, 0, 0, 0.5),
                0 5px 20px rgba(0, 0, 0, 0.7);
        }

        .panel::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--brass), var(--bronze), var(--copper), var(--bronze), var(--brass));
            z-index: -1;
            opacity: 0.7;
            animation: brass-shimmer 10s linear infinite;
        }

        @keyframes brass-shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .panel-header {
            padding: 15px 20px;
            background: linear-gradient(to bottom, var(--leather), rgba(60, 36, 30, 0.8));
            border-bottom: 2px solid var(--brass);
            position: relative;
        }

        .panel-title {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            color: var(--brass);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Gear decorations */
        .gear {
            width: 25px;
            height: 25px;
            position: relative;
            display: inline-block;
        }

        .gear::before,
        .gear::after {
            content: '';
            position: absolute;
            border-radius: 50%;
        }

        .gear::before {
            width: 100%;
            height: 100%;
            border: 3px solid var(--brass);
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
        }

        .gear::after {
            width: 10px;
            height: 10px;
            background: var(--brass);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .gear.spinning {
            animation: gear-rotate 4s linear infinite;
        }

        @keyframes gear-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* User presence panel */
        .presence-panel {
            overflow-y: auto;
        }

        .user-list {
            padding: 15px;
        }

        .user-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            margin-bottom: 10px;
            background: rgba(244, 232, 208, 0.05);
            border: 1px solid var(--bronze);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .user-item:hover {
            background: rgba(244, 232, 208, 0.1);
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 3px solid var(--bronze);
            background: var(--leather);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            color: var(--brass);
            font-size: 1.2rem;
            position: relative;
        }

        .presence-indicator {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid var(--ink);
            animation: pulse 2s infinite;
        }

        .presence-indicator.online {
            background: var(--emerald);
            box-shadow: 0 0 10px var(--emerald);
        }

        .presence-indicator.busy {
            background: var(--ruby);
            box-shadow: 0 0 10px var(--ruby);
        }

        .presence-indicator.away {
            background: var(--brass);
            box-shadow: 0 0 10px var(--brass);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: var(--parchment);
            margin-bottom: 2px;
        }

        .user-status {
            font-size: 0.85rem;
            color: var(--copper);
            font-style: italic;
        }

        /* Chat interface */
        .chat-panel {
            display: flex;
            flex-direction: column;
        }

        .message-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: 
                radial-gradient(ellipse at top, rgba(244, 232, 208, 0.02), transparent),
                radial-gradient(ellipse at bottom, rgba(184, 115, 51, 0.02), transparent);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 10px;
            position: relative;
            max-width: 70%;
            animation: message-appear 0.3s ease-out;
        }

        @keyframes message-appear {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.sent {
            background: rgba(184, 115, 51, 0.2);
            border: 1px solid var(--brass);
            margin-left: auto;
        }

        .message.received {
            background: rgba(60, 36, 30, 0.3);
            border: 1px solid var(--bronze);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.85rem;
        }

        .message-sender {
            color: var(--brass);
            font-weight: 600;
        }

        .message-time {
            color: var(--copper);
            font-style: italic;
        }

        .message-content {
            color: var(--parchment);
            line-height: 1.5;
        }

        /* Message input with brass typewriter styling */
        .message-input-container {
            padding: 20px;
            background: var(--leather);
            border-top: 2px solid var(--brass);
            position: relative;
        }

        .input-wrapper {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 15px;
            background: rgba(244, 232, 208, 0.9);
            border: 2px solid var(--bronze);
            border-radius: 5px;
            color: var(--ink);
            font-family: 'Crimson Text', serif;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--brass);
            box-shadow: 0 0 15px rgba(184, 115, 51, 0.4);
        }

        .message-input::placeholder {
            color: rgba(26, 26, 26, 0.5);
            font-style: italic;
        }

        .send-button {
            padding: 12px 20px;
            background: linear-gradient(to bottom, var(--brass), var(--bronze));
            border: none;
            border-radius: 5px;
            color: var(--ink);
            font-family: 'Cinzel', serif;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 
                0 3px 10px rgba(0, 0, 0, 0.5),
                inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 5px 15px rgba(0, 0, 0, 0.7),
                inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .send-button:active {
            transform: translateY(0);
            box-shadow: 
                0 2px 5px rgba(0, 0, 0, 0.5),
                inset 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Control center panel */
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
        }

        /* Video call controls with Victorian telegraph aesthetic */
        .video-controls {
            background: rgba(60, 36, 30, 0.5);
            border: 2px solid var(--bronze);
            border-radius: 10px;
            padding: 20px;
        }

        .video-preview {
            width: 100%;
            height: 200px;
            background: var(--ink);
            border: 3px solid var(--brass);
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .video-preview::before {
            content: 'AETHER VISION PORTAL';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--brass);
            font-family: 'Cinzel', serif;
            font-size: 0.9rem;
            letter-spacing: 0.2em;
            opacity: 0.5;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .control-btn {
            padding: 10px;
            background: var(--leather);
            border: 2px solid var(--bronze);
            border-radius: 5px;
            color: var(--brass);
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            font-size: 0.9rem;
        }

        .control-btn:hover {
            background: rgba(184, 115, 51, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
        }

        .control-btn.active {
            background: var(--brass);
            color: var(--ink);
        }

        /* Notification center */
        .notification-center {
            background: rgba(60, 36, 30, 0.5);
            border: 2px solid var(--bronze);
            border-radius: 10px;
            padding: 20px;
            max-height: 250px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(244, 232, 208, 0.05);
            border-left: 3px solid var(--brass);
            border-radius: 3px;
            font-size: 0.9rem;
            position: relative;
            padding-left: 30px;
        }

        .notification-item::before {
            content: '⚙';
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--brass);
            font-size: 1.1rem;
        }

        .notification-time {
            font-size: 0.8rem;
            color: var(--copper);
            font-style: italic;
            margin-top: 3px;
        }

        /* Collaborative whiteboard */
        .whiteboard-section {
            flex: 1;
            background: rgba(60, 36, 30, 0.5);
            border: 2px solid var(--bronze);
            border-radius: 10px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .whiteboard-tools {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--bronze);
        }

        .tool-btn {
            width: 40px;
            height: 40px;
            background: var(--leather);
            border: 2px solid var(--bronze);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--brass);
            transition: all 0.3s;
        }

        .tool-btn:hover {
            background: rgba(184, 115, 51, 0.2);
            transform: scale(1.1);
        }

        .tool-btn.active {
            background: var(--brass);
            color: var(--ink);
        }

        .whiteboard-canvas {
            flex: 1;
            background: rgba(244, 232, 208, 0.9);
            border: 3px solid var(--brass);
            border-radius: 5px;
            position: relative;
            overflow: hidden;
        }

        .canvas-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: rgba(26, 26, 26, 0.3);
            font-style: italic;
        }

        /* Loading animation */
        .loading-gears {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
            z-index: 1000;
        }

        .loading-gears.active {
            display: block;
        }

        .gear-container {
            position: relative;
            width: 100px;
            height: 100px;
        }

        .gear-large,
        .gear-small {
            position: absolute;
            border: 5px solid var(--brass);
            border-radius: 50%;
            background: var(--leather);
            box-shadow: 
                0 0 20px rgba(184, 115, 51, 0.5),
                inset 0 0 10px rgba(0, 0, 0, 0.5);
        }

        .gear-large {
            width: 80px;
            height: 80px;
            animation: gear-rotate 3s linear infinite;
        }

        .gear-small {
            width: 50px;
            height: 50px;
            top: 35px;
            left: 50px;
            animation: gear-rotate-reverse 2s linear infinite;
        }

        @keyframes gear-rotate-reverse {
            from { transform: rotate(0deg); }
            to { transform: rotate(-360deg); }
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .communication-hub {
                grid-template-columns: 250px 1fr 300px;
            }
        }

        @media (max-width: 900px) {
            .communication-hub {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }

            .presence-panel,
            .control-panel {
                max-height: 300px;
            }
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 12px;
        }

        ::-webkit-scrollbar-track {
            background: var(--leather);
            border: 1px solid var(--bronze);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, var(--brass), var(--bronze));
            border-radius: 6px;
            border: 1px solid var(--bronze);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, var(--bronze), var(--copper));
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="steam-pipe top-left"></div>
            <div class="steam-pipe top-right"></div>
            <h1>Victorian Aethernet Communication Hub</h1>
            <p class="subtitle">Pneumatic Message Relay & Telegraphic Conference Chamber</p>
        </header>

        <main class="communication-hub">
            <!-- User Presence Panel -->
            <aside class="panel presence-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <span class="gear spinning"></span>
                        Active Operators
                    </h2>
                </div>
                <div class="user-list">
                    <div class="user-item">
                        <div class="user-avatar">
                            VB
                            <span class="presence-indicator online"></span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Victoria Blackwood</div>
                            <div class="user-status">Operating from London Station</div>
                        </div>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar">
                            AS
                            <span class="presence-indicator online"></span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Arthur Sterling</div>
                            <div class="user-status">Transmitting via Edinburgh Relay</div>
                        </div>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar">
                            EC
                            <span class="presence-indicator busy"></span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Eleanor Cogsworth</div>
                            <div class="user-status">Engaged in Conference</div>
                        </div>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar">
                            RG
                            <span class="presence-indicator away"></span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Reginald Gearhart</div>
                            <div class="user-status">Away - Maintenance Protocol</div>
                        </div>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar">
                            MW
                            <span class="presence-indicator online"></span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Margaret Whitmore</div>
                            <div class="user-status">Ready at Manchester Hub</div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Chat Interface -->
            <section class="panel chat-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <span class="gear"></span>
                        Pneumatic Message Exchange
                    </h2>
                </div>
                <div class="message-area" id="messageArea">
                    <div class="message received">
                        <div class="message-header">
                            <span class="message-sender">Victoria Blackwood</span>
                            <span class="message-time">10:42 AM</span>
                        </div>
                        <div class="message-content">
                            Good morning, colleagues. I trust the aethernet connection is functioning properly today?
                        </div>
                    </div>
                    <div class="message sent">
                        <div class="message-header">
                            <span class="message-sender">You</span>
                            <span class="message-time">10:43 AM</span>
                        </div>
                        <div class="message-content">
                            Indeed, Miss Blackwood. The pneumatic tubes are operating at optimal pressure.
                        </div>
                    </div>
                    <div class="message received">
                        <div class="message-header">
                            <span class="message-sender">Arthur Sterling</span>
                            <span class="message-time">10:45 AM</span>
                        </div>
                        <div class="message-content">
                            Splendid! I've prepared the schematics for our new automaton design. Shall we proceed with the conference?
                        </div>
                    </div>
                    <div class="message received">
                        <div class="message-header">
                            <span class="message-sender">Eleanor Cogsworth</span>
                            <span class="message-time">10:46 AM</span>
                        </div>
                        <div class="message-content">
                            One moment, Mr. Sterling. I'm adjusting the brass resonators for clearer audio transmission.
                        </div>
                    </div>
                </div>
                <div class="message-input-container">
                    <div class="input-wrapper">
                        <input type="text" class="message-input" id="messageInput" placeholder="Compose your telegraphic message...">
                        <button class="send-button" id="sendButton">Send</button>
                    </div>
                </div>
            </section>

            <!-- Control Center -->
            <aside class="panel control-panel">
                <!-- Video Call Controls -->
                <div class="video-controls">
                    <h3 class="panel-title" style="margin-bottom: 15px; font-size: 1rem;">
                        <span class="gear"></span>
                        Aether Vision Controls
                    </h3>
                    <div class="video-preview"></div>
                    <div class="control-buttons">
                        <button class="control-btn" id="cameraBtn">
                            <span>📷</span>
                            <span>Vision</span>
                        </button>
                        <button class="control-btn" id="micBtn">
                            <span>🎙️</span>
                            <span>Voice</span>
                        </button>
                        <button class="control-btn" id="shareBtn">
                            <span>📊</span>
                            <span>Share</span>
                        </button>
                        <button class="control-btn" id="recordBtn">
                            <span>⏺️</span>
                            <span>Record</span>
                        </button>
                        <button class="control-btn active" id="callBtn">
                            <span>📞</span>
                            <span>Connect</span>
                        </button>
                        <button class="control-btn" id="settingsBtn">
                            <span>⚙️</span>
                            <span>Settings</span>
                        </button>
                    </div>
                </div>

                <!-- Notification Center -->
                <div class="notification-center">
                    <h3 class="panel-title" style="margin-bottom: 15px; font-size: 1rem;">
                        <span class="gear"></span>
                        Telegraph Notifications
                    </h3>
                    <div class="notification-item">
                        <div>New blueprint received from Workshop #7</div>
                        <div class="notification-time">5 minutes ago</div>
                    </div>
                    <div class="notification-item">
                        <div>Steam pressure optimal in all chambers</div>
                        <div class="notification-time">12 minutes ago</div>
                    </div>
                    <div class="notification-item">
                        <div>Conference scheduled: Quarterly Invention Review</div>
                        <div class="notification-time">1 hour ago</div>
                    </div>
                    <div class="notification-item">
                        <div>Maintenance completed on Relay Station Beta</div>
                        <div class="notification-time">2 hours ago</div>
                    </div>
                </div>

                <!-- Collaborative Whiteboard -->
                <div class="whiteboard-section">
                    <h3 class="panel-title" style="margin-bottom: 15px; font-size: 1rem;">
                        <span class="gear"></span>
                        Mechanical Drawing Board
                    </h3>
                    <div class="whiteboard-tools">
                        <button class="tool-btn active" data-tool="pen">✏️</button>
                        <button class="tool-btn" data-tool="eraser">🧹</button>
                        <button class="tool-btn" data-tool="ruler">📏</button>
                        <button class="tool-btn" data-tool="compass">⭕</button>
                        <button class="tool-btn" data-tool="clear">🗑️</button>
                    </div>
                    <div class="whiteboard-canvas">
                        <div class="canvas-placeholder">
                            <p>Collaborative Drawing Surface</p>
                            <p style="font-size: 0.9rem; margin-top: 5px;">Click tools to begin sketching</p>
                        </div>
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <!-- Loading Animation -->
    <div class="loading-gears" id="loadingGears">
        <div class="gear-container">
            <div class="gear-large"></div>
            <div class="gear-small"></div>
        </div>
    </div>

    <script>
        // Message handling
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const messageArea = document.getElementById('messageArea');

        function addMessage(content, sender = 'You', sent = true) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sent ? 'sent' : 'received'}`;
            
            const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">${sender}</span>
                    <span class="message-time">${time}</span>
                </div>
                <div class="message-content">${content}</div>
            `;
            
            messageArea.appendChild(messageDiv);
            messageArea.scrollTop = messageArea.scrollHeight;
            
            // Add steam effect to steam pipes
            document.querySelectorAll('.steam-pipe').forEach(pipe => {
                pipe.classList.add('active');
                setTimeout(() => pipe.classList.remove('active'), 500);
            });
        }

        function sendMessage() {
            const content = messageInput.value.trim();
            if (content) {
                addMessage(content);
                messageInput.value = '';
                
                // Simulate response
                setTimeout(() => {
                    const responses = [
                        "Acknowledged. Processing your transmission through the pneumatic relay.",
                        "Message received via the aethernet. The brass tubes are humming with activity.",
                        "Your correspondence has been logged in the mechanical registry.",
                        "Splendid! The telegraph wires are singing with your words.",
                        "Indeed, a most excellent observation. The cogs of progress turn ever forward."
                    ];
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    const senders = ['Victoria Blackwood', 'Arthur Sterling', 'Eleanor Cogsworth'];
                    const randomSender = senders[Math.floor(Math.random() * senders.length)];
                    addMessage(randomResponse, randomSender, false);
                }, 1500);
            }
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        // Control buttons
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id !== 'callBtn') {
                    this.classList.toggle('active');
                }
                
                // Show loading gears briefly
                const loadingGears = document.getElementById('loadingGears');
                loadingGears.classList.add('active');
                setTimeout(() => loadingGears.classList.remove('active'), 800);
            });
        });

        // Whiteboard tools
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                if (this.dataset.tool === 'clear') {
                    const canvas = document.querySelector('.whiteboard-canvas');
                    canvas.innerHTML = `
                        <div class="canvas-placeholder">
                            <p>Canvas Cleared</p>
                            <p style="font-size: 0.9rem; margin-top: 5px;">Ready for new designs</p>
                        </div>
                    `;
                }
            });
        });

        // User presence updates
        function updateUserPresence() {
            const indicators = document.querySelectorAll('.presence-indicator');
            indicators.forEach(indicator => {
                if (Math.random() > 0.7) {
                    const states = ['online', 'busy', 'away'];
                    const currentState = Array.from(indicator.classList).find(c => states.includes(c));
                    indicator.classList.remove(currentState);
                    const newState = states[Math.floor(Math.random() * states.length)];
                    indicator.classList.add(newState);
                }
            });
        }

        // Periodic updates
        setInterval(updateUserPresence, 10000);

        // Add new notifications periodically
        function addNotification() {
            const notifications = [
                "New message received via pneumatic tube",
                "Boiler pressure reading: Normal",
                "Clockwork mechanism requires winding",
                "Telegraph line established with Paris office",
                "Steam valve inspection completed",
                "New patent registered: Automated Tea Dispenser"
            ];
            
            const notificationCenter = document.querySelector('.notification-center');
            const existingNotifications = notificationCenter.querySelectorAll('.notification-item');
            
            if (existingNotifications.length > 5) {
                existingNotifications[existingNotifications.length - 1].remove();
            }
            
            const notification = document.createElement('div');
            notification.className = 'notification-item';
            notification.innerHTML = `
                <div>${notifications[Math.floor(Math.random() * notifications.length)]}</div>
                <div class="notification-time">Just now</div>
            `;
            
            notificationCenter.insertBefore(notification, existingNotifications[0]);
        }

        setInterval(addNotification, 30000);

        // Initialize with some activity
        setTimeout(() => {
            addMessage("The morning assembly is about to commence. All stations report ready.", "Margaret Whitmore", false);
        }, 3000);
    </script>
</body>
</html>