<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playful Input Intelligence - UI Hybrid 19</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: white;
            font-size: 3em;
            margin-bottom: 30px;
            text-shadow: 3px 3px 0 rgba(0,0,0,0.2);
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .input-playground {
            background: white;
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: visible;
        }

        .input-group {
            margin-bottom: 40px;
            position: relative;
        }

        .input-wrapper {
            position: relative;
            margin-bottom: 10px;
        }

        input {
            width: 100%;
            padding: 20px 25px;
            font-size: 18px;
            border: 3px solid #e0e0e0;
            border-radius: 20px;
            outline: none;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            background: #f8f8f8;
            font-family: inherit;
        }

        input:focus {
            border-color: #667eea;
            transform: scale(1.05);
            background: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .label {
            display: inline-block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
            font-size: 20px;
            position: relative;
        }

        .label-emoji {
            display: inline-block;
            font-size: 30px;
            margin-right: 10px;
            animation: wiggle 2s ease-in-out infinite;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-10deg); }
            75% { transform: rotate(10deg); }
        }

        .helper-bubble {
            position: absolute;
            top: -60px;
            right: 20px;
            background: #ffeb3b;
            color: #333;
            padding: 15px 20px;
            border-radius: 20px;
            font-size: 14px;
            opacity: 0;
            transform: scale(0) translateY(20px);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            pointer-events: none;
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .helper-bubble::after {
            content: '';
            position: absolute;
            bottom: -10px;
            right: 30px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #ffeb3b;
        }

        .helper-bubble.show {
            opacity: 1;
            transform: scale(1) translateY(0);
        }

        .autocomplete-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 10px;
            overflow: hidden;
            opacity: 0;
            transform: scaleY(0);
            transform-origin: top;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 20;
        }

        .autocomplete-list.show {
            opacity: 1;
            transform: scaleY(1);
        }

        .autocomplete-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .autocomplete-item:hover {
            background: #667eea;
            color: white;
            transform: translateX(10px);
        }

        .autocomplete-item:last-child {
            border-bottom: none;
        }

        .validation-message {
            position: absolute;
            bottom: -35px;
            left: 20px;
            font-size: 14px;
            font-weight: bold;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .validation-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .validation-message.success {
            color: #4caf50;
        }

        .validation-message.error {
            color: #f44336;
        }

        .validation-message.warning {
            color: #ff9800;
        }

        .character-counter {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;
            color: #999;
            transition: all 0.3s;
            pointer-events: none;
        }

        .character-counter.warning {
            color: #ff9800;
            animation: shake 0.5s;
        }

        .character-counter.error {
            color: #f44336;
            animation: shake 0.5s;
        }

        @keyframes shake {
            0%, 100% { transform: translateY(-50%) translateX(0); }
            25% { transform: translateY(-50%) translateX(-5px); }
            75% { transform: translateY(-50%) translateX(5px); }
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: #4caf50;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .floating-emoji {
            position: absolute;
            font-size: 30px;
            pointer-events: none;
            animation: float-up 2s ease-out forwards;
            z-index: 100;
        }

        @keyframes float-up {
            0% {
                opacity: 1;
                transform: translateY(0) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) rotate(360deg);
            }
        }

        .format-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .format-button {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            font-family: inherit;
            font-weight: bold;
        }

        .format-button:hover {
            transform: translateY(-3px) scale(1.1);
            background: #764ba2;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .format-button:active {
            transform: translateY(0) scale(0.95);
        }

        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            background: #ff6b6b;
            pointer-events: none;
            z-index: 1000;
        }

        @keyframes confetti-fall {
            0% {
                opacity: 1;
                transform: translateY(0) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(100vh) rotate(720deg);
            }
        }

        .mascot {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 100px;
            height: 100px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 50px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .mascot:hover {
            transform: scale(1.2) rotate(10deg);
        }

        .mascot-speech {
            position: absolute;
            bottom: 120px;
            right: 0;
            background: white;
            padding: 15px 20px;
            border-radius: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            width: 200px;
            text-align: center;
            font-size: 14px;
        }

        .mascot-speech.show {
            opacity: 1;
            transform: scale(1);
        }

        .mascot-speech::after {
            content: '';
            position: absolute;
            bottom: -10px;
            right: 40px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Playful Input Playground 🎨</h1>
        
        <div class="input-playground">
            <!-- Email Input with Validation -->
            <div class="input-group">
                <label class="label">
                    <span class="label-emoji">📧</span>
                    Magic Email Box
                </label>
                <div class="input-wrapper">
                    <input type="email" id="emailInput" placeholder="Enter your magical email...">
                    <div class="helper-bubble" id="emailHelper">
                        Try typing your email! I'll help you format it correctly! 🪄
                    </div>
                    <div class="validation-message" id="emailValidation"></div>
                </div>
            </div>

            <!-- Name Input with Autocomplete -->
            <div class="input-group">
                <label class="label">
                    <span class="label-emoji">👤</span>
                    Super Name Finder
                </label>
                <div class="input-wrapper">
                    <input type="text" id="nameInput" placeholder="Start typing a name...">
                    <div class="autocomplete-list" id="nameAutocomplete"></div>
                    <div class="validation-message" id="nameValidation"></div>
                </div>
            </div>

            <!-- Phone Number with Formatting -->
            <div class="input-group">
                <label class="label">
                    <span class="label-emoji">📱</span>
                    Phone Number Formatter
                </label>
                <div class="input-wrapper">
                    <input type="tel" id="phoneInput" placeholder="Type your phone number...">
                    <div class="helper-bubble" id="phoneHelper">
                        I'll format your phone number as you type! 📞
                    </div>
                    <div class="validation-message" id="phoneValidation"></div>
                </div>
                <div class="format-buttons">
                    <button class="format-button" onclick="formatPhone('us')">🇺🇸 US Format</button>
                    <button class="format-button" onclick="formatPhone('uk')">🇬🇧 UK Format</button>
                    <button class="format-button" onclick="formatPhone('intl')">🌍 International</button>
                </div>
            </div>

            <!-- Message Input with Character Counter -->
            <div class="input-group">
                <label class="label">
                    <span class="label-emoji">💬</span>
                    Happy Message Box
                </label>
                <div class="input-wrapper">
                    <input type="text" id="messageInput" placeholder="Write something happy..." maxlength="50">
                    <span class="character-counter" id="charCounter">0/50</span>
                    <div class="progress-bar" id="progressBar"></div>
                    <div class="validation-message" id="messageValidation"></div>
                </div>
            </div>

            <!-- Color Input with Suggestions -->
            <div class="input-group">
                <label class="label">
                    <span class="label-emoji">🎨</span>
                    Color Magic
                </label>
                <div class="input-wrapper">
                    <input type="text" id="colorInput" placeholder="Type a color name or hex code...">
                    <div class="autocomplete-list" id="colorAutocomplete"></div>
                    <div class="validation-message" id="colorValidation"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mascot Helper -->
    <div class="mascot" id="mascot">
        🤖
        <div class="mascot-speech" id="mascotSpeech"></div>
    </div>

    <script>
        // Playful animations and interactions
        function createFloatingEmoji(x, y, emoji) {
            const floater = document.createElement('div');
            floater.className = 'floating-emoji';
            floater.textContent = emoji;
            floater.style.left = x + 'px';
            floater.style.top = y + 'px';
            document.body.appendChild(floater);
            
            setTimeout(() => floater.remove(), 2000);
        }

        function createConfetti() {
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * window.innerWidth + 'px';
                    confetti.style.top = '-10px';
                    confetti.style.background = `hsl(${Math.random() * 360}, 100%, 50%)`;
                    confetti.style.animation = `confetti-fall ${2 + Math.random() * 2}s ease-out forwards`;
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => confetti.remove(), 4000);
                }, i * 50);
            }
        }

        // Email validation with playful feedback
        const emailInput = document.getElementById('emailInput');
        const emailHelper = document.getElementById('emailHelper');
        const emailValidation = document.getElementById('emailValidation');

        emailInput.addEventListener('focus', () => {
            emailHelper.classList.add('show');
            showMascotMessage('Let me help you with your email! 📧');
        });

        emailInput.addEventListener('blur', () => {
            emailHelper.classList.remove('show');
        });

        emailInput.addEventListener('input', (e) => {
            const value = e.target.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            if (value === '') {
                emailValidation.textContent = '';
                emailValidation.classList.remove('show');
            } else if (emailRegex.test(value)) {
                emailValidation.textContent = '✨ Perfect email! You\'re a star!';
                emailValidation.className = 'validation-message success show';
                createFloatingEmoji(e.target.offsetLeft + e.target.offsetWidth - 50, e.target.offsetTop, '⭐');
            } else if (value.includes('@')) {
                emailValidation.textContent = '🎯 Almost there! Just need the domain!';
                emailValidation.className = 'validation-message warning show';
            } else {
                emailValidation.textContent = '💫 Don\'t forget the @ symbol!';
                emailValidation.className = 'validation-message error show';
            }
        });

        // Name autocomplete with suggestions
        const nameInput = document.getElementById('nameInput');
        const nameAutocomplete = document.getElementById('nameAutocomplete');
        const nameValidation = document.getElementById('nameValidation');

        const names = [
            'Alice Wonderland',
            'Bob Builder',
            'Charlie Chocolate',
            'Diana Prince',
            'Edward Scissorhands',
            'Fiona Fairy',
            'George Curious',
            'Helen Hero',
            'Isaac Newton',
            'Julia Joy'
        ];

        nameInput.addEventListener('input', (e) => {
            const value = e.target.value.toLowerCase();
            
            if (value.length > 0) {
                const matches = names.filter(name => name.toLowerCase().includes(value));
                
                if (matches.length > 0) {
                    nameAutocomplete.innerHTML = matches.slice(0, 5).map(name => 
                        `<div class="autocomplete-item" onclick="selectName('${name}')">${name}</div>`
                    ).join('');
                    nameAutocomplete.classList.add('show');
                } else {
                    nameAutocomplete.classList.remove('show');
                }
                
                if (value.length >= 2) {
                    nameValidation.textContent = '🌟 Great name!';
                    nameValidation.className = 'validation-message success show';
                }
            } else {
                nameAutocomplete.classList.remove('show');
                nameValidation.classList.remove('show');
            }
        });

        function selectName(name) {
            nameInput.value = name;
            nameAutocomplete.classList.remove('show');
            createFloatingEmoji(nameInput.offsetLeft + nameInput.offsetWidth / 2, nameInput.offsetTop, '🎉');
            showMascotMessage('Excellent choice! 👏');
        }

        // Phone number formatting
        const phoneInput = document.getElementById('phoneInput');
        const phoneHelper = document.getElementById('phoneHelper');
        const phoneValidation = document.getElementById('phoneValidation');

        phoneInput.addEventListener('focus', () => {
            phoneHelper.classList.add('show');
        });

        phoneInput.addEventListener('blur', () => {
            phoneHelper.classList.remove('show');
        });

        phoneInput.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            
            if (value.length >= 10) {
                phoneValidation.textContent = '📞 Perfect phone number!';
                phoneValidation.className = 'validation-message success show';
            } else if (value.length > 0) {
                phoneValidation.textContent = `📱 ${10 - value.length} more digits to go!`;
                phoneValidation.className = 'validation-message warning show';
            } else {
                phoneValidation.classList.remove('show');
            }
        });

        function formatPhone(format) {
            let value = phoneInput.value.replace(/\D/g, '');
            
            switch(format) {
                case 'us':
                    if (value.length >= 10) {
                        phoneInput.value = `(${value.slice(0,3)}) ${value.slice(3,6)}-${value.slice(6,10)}`;
                    }
                    break;
                case 'uk':
                    if (value.length >= 10) {
                        phoneInput.value = `+44 ${value.slice(0,4)} ${value.slice(4,7)} ${value.slice(7,10)}`;
                    }
                    break;
                case 'intl':
                    if (value.length >= 10) {
                        phoneInput.value = `+${value.slice(0,2)} ${value.slice(2,5)} ${value.slice(5,8)} ${value.slice(8)}`;
                    }
                    break;
            }
            
            createFloatingEmoji(phoneInput.offsetLeft + phoneInput.offsetWidth / 2, phoneInput.offsetTop, '✨');
        }

        // Message input with character counter
        const messageInput = document.getElementById('messageInput');
        const charCounter = document.getElementById('charCounter');
        const progressBar = document.getElementById('progressBar');
        const messageValidation = document.getElementById('messageValidation');

        messageInput.addEventListener('input', (e) => {
            const length = e.target.value.length;
            const maxLength = 50;
            const percentage = (length / maxLength) * 100;
            
            charCounter.textContent = `${length}/${maxLength}`;
            progressBar.style.width = percentage + '%';
            
            if (length >= maxLength * 0.9) {
                charCounter.classList.add('error');
                progressBar.style.background = '#f44336';
                messageValidation.textContent = '🚨 Almost at the limit!';
                messageValidation.className = 'validation-message error show';
            } else if (length >= maxLength * 0.7) {
                charCounter.classList.add('warning');
                charCounter.classList.remove('error');
                progressBar.style.background = '#ff9800';
                messageValidation.textContent = '⚡ You\'re on fire!';
                messageValidation.className = 'validation-message warning show';
            } else {
                charCounter.classList.remove('warning', 'error');
                progressBar.style.background = '#4caf50';
                if (length > 0) {
                    messageValidation.textContent = '💖 Keep going!';
                    messageValidation.className = 'validation-message success show';
                } else {
                    messageValidation.classList.remove('show');
                }
            }
            
            // Check for happy words
            const happyWords = ['happy', 'joy', 'love', 'awesome', 'great', 'wonderful', 'amazing'];
            const foundHappy = happyWords.some(word => e.target.value.toLowerCase().includes(word));
            if (foundHappy) {
                createFloatingEmoji(e.target.offsetLeft + Math.random() * e.target.offsetWidth, e.target.offsetTop, '😊');
            }
        });

        // Color input with suggestions
        const colorInput = document.getElementById('colorInput');
        const colorAutocomplete = document.getElementById('colorAutocomplete');
        const colorValidation = document.getElementById('colorValidation');

        const colors = [
            { name: 'Red', hex: '#FF0000' },
            { name: 'Blue', hex: '#0000FF' },
            { name: 'Green', hex: '#00FF00' },
            { name: 'Purple', hex: '#800080' },
            { name: 'Orange', hex: '#FFA500' },
            { name: 'Pink', hex: '#FFC0CB' },
            { name: 'Yellow', hex: '#FFFF00' },
            { name: 'Turquoise', hex: '#40E0D0' },
            { name: 'Magenta', hex: '#FF00FF' },
            { name: 'Coral', hex: '#FF7F50' }
        ];

        colorInput.addEventListener('input', (e) => {
            const value = e.target.value.toLowerCase();
            
            if (value.length > 0) {
                const matches = colors.filter(color => 
                    color.name.toLowerCase().includes(value) || 
                    color.hex.toLowerCase().includes(value)
                );
                
                if (matches.length > 0) {
                    colorAutocomplete.innerHTML = matches.slice(0, 5).map(color => 
                        `<div class="autocomplete-item" onclick="selectColor('${color.hex}')" style="background: linear-gradient(to right, ${color.hex} 20%, white 20%);">
                            <span style="margin-left: 60px;">${color.name} (${color.hex})</span>
                        </div>`
                    ).join('');
                    colorAutocomplete.classList.add('show');
                } else {
                    colorAutocomplete.classList.remove('show');
                }
                
                // Validate hex color
                if (/^#[0-9A-F]{6}$/i.test(value)) {
                    colorInput.style.borderColor = value;
                    colorInput.style.boxShadow = `0 0 20px ${value}50`;
                    colorValidation.textContent = '🎨 Beautiful color!';
                    colorValidation.className = 'validation-message success show';
                    createConfetti();
                }
            } else {
                colorAutocomplete.classList.remove('show');
                colorValidation.classList.remove('show');
                colorInput.style.borderColor = '';
                colorInput.style.boxShadow = '';
            }
        });

        function selectColor(hex) {
            colorInput.value = hex;
            colorAutocomplete.classList.remove('show');
            colorInput.style.borderColor = hex;
            colorInput.style.boxShadow = `0 0 20px ${hex}50`;
            createFloatingEmoji(colorInput.offsetLeft + colorInput.offsetWidth / 2, colorInput.offsetTop, '🌈');
        }

        // Mascot interactions
        const mascot = document.getElementById('mascot');
        const mascotSpeech = document.getElementById('mascotSpeech');

        function showMascotMessage(message) {
            mascotSpeech.textContent = message;
            mascotSpeech.classList.add('show');
            setTimeout(() => {
                mascotSpeech.classList.remove('show');
            }, 3000);
        }

        mascot.addEventListener('click', () => {
            const messages = [
                'You\'re doing great! 🌟',
                'Keep typing, superstar! ⭐',
                'I believe in you! 💪',
                'You\'re amazing! 🎉',
                'High five! 🖐️',
                'Woohoo! 🎊'
            ];
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            showMascotMessage(randomMessage);
            mascot.style.transform = 'scale(1.2) rotate(360deg)';
            setTimeout(() => {
                mascot.style.transform = 'scale(1) rotate(0deg)';
            }, 500);
        });

        // Global click handlers for autocomplete
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.input-group')) {
                document.querySelectorAll('.autocomplete-list').forEach(list => {
                    list.classList.remove('show');
                });
            }
        });

        // Welcome animation
        setTimeout(() => {
            showMascotMessage('Welcome to the Input Playground! 🎮');
            createFloatingEmoji(window.innerWidth / 2, 100, '🎉');
        }, 1000);
    </script>
</body>
</html>