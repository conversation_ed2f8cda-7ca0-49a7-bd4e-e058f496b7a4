<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plasma Energy Dynamic Power Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Courier New', monospace;
            background: #0a0612;
            color: #00d4ff;
            overflow-x: hidden;
            background-image: 
                radial-gradient(circle at 15% 25%, rgba(138, 43, 226, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 85% 75%, rgba(0, 212, 255, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.08) 0%, transparent 60%),
                radial-gradient(circle at 30% 80%, rgba(0, 255, 127, 0.1) 0%, transparent 40%);
        }

        main {
            padding: 2rem;
            min-height: 100vh;
            position: relative;
        }

        h1 {
            text-align: center;
            font-size: 2.8rem;
            color: #ff00ff;
            text-shadow: 
                0 0 20px #ff00ff, 
                0 0 40px #ff00ff,
                0 0 60px #8a2be2;
            margin-bottom: 2rem;
            animation: plasma-pulse 2.5s ease-in-out infinite;
        }

        @keyframes plasma-pulse {
            0%, 100% { 
                text-shadow: 0 0 20px #ff00ff, 0 0 40px #ff00ff, 0 0 60px #8a2be2;
                color: #ff00ff;
            }
            50% { 
                text-shadow: 0 0 30px #00d4ff, 0 0 50px #00d4ff, 0 0 80px #0099cc;
                color: #00d4ff;
            }
        }

        .hybrid-component {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(10, 6, 18, 0.9);
            border-radius: 25px;
            padding: 2.5rem;
            border: 2px solid #ff00ff;
            box-shadow: 
                0 0 40px rgba(255, 0, 255, 0.4),
                inset 0 0 40px rgba(138, 43, 226, 0.2);
            position: relative;
            overflow: hidden;
        }

        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #ff00ff, #00d4ff, #00ff7f, #8a2be2, #ff00ff);
            border-radius: 25px;
            z-index: -1;
            animation: plasma-field-rotation 6s linear infinite;
            background-size: 400% 400%;
        }

        @keyframes plasma-field-rotation {
            0% { background-position: 0% 0%; }
            100% { background-position: 400% 400%; }
        }

        .power-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .power-panel {
            background: rgba(10, 6, 25, 0.95);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid #8a2be2;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .power-panel:hover {
            box-shadow: 0 0 30px rgba(255, 0, 255, 0.5);
            transform: translateY(-8px);
            border-color: #ff00ff;
        }

        .power-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #00d4ff, #ff00ff, transparent);
            animation: electromagnetic-scan 3s linear infinite;
        }

        @keyframes electromagnetic-scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .panel-header {
            color: #00ff7f;
            font-size: 1.4rem;
            margin-bottom: 1.5rem;
            text-shadow: 0 0 15px #00ff7f;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .plasma-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: radial-gradient(circle, #ff00ff, #8a2be2);
            box-shadow: 0 0 20px #ff00ff;
            animation: plasma-heartbeat 1.8s ease-in-out infinite;
        }

        @keyframes plasma-heartbeat {
            0%, 100% { 
                transform: scale(1); 
                box-shadow: 0 0 20px #ff00ff;
            }
            50% { 
                transform: scale(1.4); 
                box-shadow: 0 0 30px #ff00ff, 0 0 40px #8a2be2;
            }
        }

        /* Energy Flow Visualization */
        .energy-flow {
            grid-column: span 3;
            height: 350px;
            position: relative;
        }

        .plasma-field {
            width: 100%;
            height: 100%;
            position: relative;
            background: radial-gradient(ellipse at center, rgba(255, 0, 255, 0.1), transparent);
            border-radius: 15px;
            overflow: hidden;
        }

        .energy-stream {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00d4ff;
            border-radius: 50%;
            box-shadow: 0 0 15px #00d4ff;
            animation: stream-flow 4s linear infinite;
        }

        @keyframes stream-flow {
            0% { 
                transform: translateX(0) translateY(0) scale(1);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { 
                transform: translateX(600px) translateY(-100px) scale(0.3);
                opacity: 0;
            }
        }

        .power-node {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #ff00ff, #8a2be2);
            border-radius: 50%;
            box-shadow: 0 0 25px #ff00ff;
            animation: node-electromagnetic 3s ease-in-out infinite;
        }

        @keyframes node-electromagnetic {
            0%, 100% { 
                box-shadow: 0 0 25px #ff00ff;
                transform: scale(1);
            }
            50% { 
                box-shadow: 0 0 40px #ff00ff, 0 0 60px #8a2be2;
                transform: scale(1.2);
            }
        }

        .plasma-arc {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #ff00ff, #00d4ff);
            transform-origin: 0 50%;
            animation: arc-discharge 2s ease-in-out infinite;
            box-shadow: 0 0 10px #ff00ff;
        }

        @keyframes arc-discharge {
            0%, 100% { 
                opacity: 0.3;
                box-shadow: 0 0 10px #ff00ff;
            }
            50% { 
                opacity: 1;
                box-shadow: 0 0 20px #ff00ff, 0 0 30px #00d4ff;
            }
        }

        /* Power Distribution Control */
        .distribution-matrix {
            height: 280px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .distribution-cell {
            background: rgba(138, 43, 226, 0.2);
            border: 1px solid #8a2be2;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .distribution-cell:hover {
            background: rgba(255, 0, 255, 0.3);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.6);
            transform: scale(1.05);
        }

        .distribution-cell.active {
            background: rgba(0, 212, 255, 0.4);
            border-color: #00d4ff;
            box-shadow: 0 0 15px #00d4ff;
        }

        .distribution-cell::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(0, 255, 127, 0.4), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .distribution-cell:hover::before {
            opacity: 1;
        }

        /* Consumption Analytics */
        .analytics-display {
            height: 250px;
            position: relative;
        }

        .consumption-chart {
            width: 100%;
            height: 200px;
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid #00d4ff;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        .chart-bar {
            position: absolute;
            bottom: 0;
            width: 8px;
            background: linear-gradient(to top, #8a2be2, #ff00ff);
            border-radius: 4px 4px 0 0;
            box-shadow: 0 0 10px #ff00ff;
            animation: chart-fluctuation 3s ease-in-out infinite;
        }

        @keyframes chart-fluctuation {
            0%, 100% { 
                height: 30%;
                box-shadow: 0 0 10px #ff00ff;
            }
            33% { 
                height: 70%;
                box-shadow: 0 0 15px #ff00ff, 0 0 25px #8a2be2;
            }
            66% { 
                height: 50%;
                box-shadow: 0 0 12px #00d4ff;
            }
        }

        .analytics-metrics {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 15px #00d4ff;
            animation: metric-plasma-glow 2.5s ease-in-out infinite alternate;
        }

        @keyframes metric-plasma-glow {
            from { 
                text-shadow: 0 0 15px #00d4ff;
                color: #00d4ff;
            }
            to { 
                text-shadow: 0 0 25px #ff00ff, 0 0 35px #ff00ff;
                color: #ff00ff;
            }
        }

        .metric-label {
            color: #00ff7f;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Renewable Energy Integration */
        .renewable-sources {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin: 1rem 0;
        }

        .energy-source {
            padding: 1rem;
            background: rgba(0, 255, 127, 0.1);
            border: 1px solid #00ff7f;
            border-radius: 10px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .energy-source:hover {
            box-shadow: 0 0 20px rgba(0, 255, 127, 0.5);
            transform: translateY(-3px);
        }

        .source-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
            animation: source-emission 4s ease-in-out infinite;
        }

        @keyframes source-emission {
            0%, 100% { 
                text-shadow: 0 0 15px #00ff7f;
                transform: scale(1);
            }
            50% { 
                text-shadow: 0 0 25px #00ff7f, 0 0 35px #00d4ff;
                transform: scale(1.1);
            }
        }

        .source-output {
            font-size: 1.2rem;
            color: #00d4ff;
            font-weight: bold;
        }

        /* Load Balancing Interface */
        .load-balancer {
            height: 200px;
            position: relative;
        }

        .balance-display {
            width: 100%;
            height: 150px;
            background: rgba(255, 0, 255, 0.05);
            border: 1px solid #ff00ff;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        .balance-beam {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 80%;
            height: 4px;
            background: linear-gradient(90deg, #8a2be2, #ff00ff, #00d4ff);
            transform: translate(-50%, -50%);
            animation: beam-oscillation 5s ease-in-out infinite;
        }

        @keyframes beam-oscillation {
            0%, 100% { transform: translate(-50%, -50%) rotate(-2deg); }
            50% { transform: translate(-50%, -50%) rotate(2deg); }
        }

        .load-weight {
            position: absolute;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, #ff00ff, #8a2be2);
            border-radius: 50%;
            box-shadow: 0 0 20px #ff00ff;
            animation: weight-float 3s ease-in-out infinite;
        }

        @keyframes weight-float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        /* Energy Storage Management */
        .storage-banks {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 1rem 0;
        }

        .storage-unit {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            border-radius: 10px;
            padding: 1rem;
            position: relative;
            overflow: hidden;
        }

        .storage-level {
            width: 100%;
            height: 20px;
            background: rgba(138, 43, 226, 0.3);
            border-radius: 10px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .storage-fill {
            height: 100%;
            background: linear-gradient(90deg, #8a2be2, #ff00ff, #00d4ff);
            border-radius: 10px;
            transition: width 0.5s ease;
            animation: storage-pulse 4s ease-in-out infinite;
        }

        @keyframes storage-pulse {
            0%, 100% { 
                box-shadow: 0 0 10px #ff00ff;
            }
            50% { 
                box-shadow: 0 0 20px #ff00ff, 0 0 30px #00d4ff;
            }
        }

        .storage-capacity {
            font-size: 1.1rem;
            color: #00ff7f;
            text-align: center;
        }

        /* Smart Grid Coordination */
        .grid-status {
            grid-column: span 3;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 2rem;
            background: rgba(255, 0, 255, 0.05);
            border-radius: 15px;
            border: 2px solid #ff00ff;
        }

        .grid-node {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle, #ff00ff, #8a2be2);
            box-shadow: 0 0 30px #ff00ff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 10px white;
            animation: grid-synchronization 6s linear infinite;
            position: relative;
        }

        @keyframes grid-synchronization {
            0% { 
                box-shadow: 0 0 30px #ff00ff;
                transform: scale(1);
            }
            33% { 
                box-shadow: 0 0 40px #00d4ff;
                transform: scale(1.05);
            }
            66% { 
                box-shadow: 0 0 35px #00ff7f;
                transform: scale(1.02);
            }
            100% { 
                box-shadow: 0 0 30px #ff00ff;
                transform: scale(1);
            }
        }

        .grid-connection {
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #ff00ff, #00d4ff);
            position: relative;
            animation: connection-flow 2s linear infinite;
        }

        @keyframes connection-flow {
            0% { box-shadow: 0 0 10px #ff00ff; }
            50% { box-shadow: 0 0 20px #00d4ff; }
            100% { box-shadow: 0 0 10px #ff00ff; }
        }

        /* Control Interface */
        .control-interface {
            grid-column: span 3;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            align-items: center;
            padding: 2rem;
            background: rgba(138, 43, 226, 0.1);
            border-radius: 15px;
            border: 1px solid #8a2be2;
        }

        .plasma-button {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff00ff, #8a2be2);
            border: none;
            border-radius: 30px;
            color: white;
            font-weight: bold;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        .plasma-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 30px rgba(255, 0, 255, 0.6);
        }

        .plasma-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: all 0.6s;
        }

        .plasma-button:hover::before {
            left: 100%;
        }

        .plasma-slider {
            flex: 1;
            min-width: 200px;
            height: 10px;
            background: rgba(255, 0, 255, 0.3);
            border-radius: 5px;
            outline: none;
            cursor: pointer;
            position: relative;
        }

        .plasma-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: radial-gradient(circle, #ff00ff, #8a2be2);
            box-shadow: 0 0 20px #ff00ff;
            cursor: pointer;
        }

        .power-indicator {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.1rem;
            color: #00d4ff;
        }

        .indicator-light {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #00d4ff;
            box-shadow: 0 0 15px #00d4ff;
            animation: indicator-blink 2s ease-in-out infinite;
        }

        @keyframes indicator-blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .power-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .energy-flow,
            .grid-status,
            .control-interface {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .power-grid {
                grid-template-columns: 1fr;
            }
            
            .energy-flow,
            .grid-status,
            .control-interface {
                grid-column: span 1;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .renewable-sources {
                grid-template-columns: 1fr;
            }
            
            .distribution-matrix {
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: repeat(5, 1fr);
            }
        }

        /* Accessibility */
        .visually-hidden {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 0, 255, 0.4), transparent);
            animation: loading-plasma-sweep 2s linear infinite;
        }

        @keyframes loading-plasma-sweep {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body>
    <main>
        <h1>Dynamic Power Management - Plasma Energy Theme</h1>
        <div class="hybrid-component">
            <div class="power-grid">
                <!-- Energy Flow Visualization -->
                <div class="power-panel energy-flow">
                    <div class="panel-header">
                        <div class="plasma-indicator"></div>
                        Plasma Energy Flow Network
                    </div>
                    <div class="plasma-field" id="plasmaField">
                        <!-- Energy streams and nodes generated by JavaScript -->
                    </div>
                </div>

                <!-- Power Distribution Control -->
                <div class="power-panel">
                    <div class="panel-header">
                        <div class="plasma-indicator"></div>
                        Distribution Matrix
                    </div>
                    <div class="distribution-matrix" id="distributionMatrix">
                        <!-- Distribution cells generated by JavaScript -->
                    </div>
                </div>

                <!-- Consumption Analytics -->
                <div class="power-panel">
                    <div class="panel-header">
                        <div class="plasma-indicator"></div>
                        Consumption Analytics
                    </div>
                    <div class="analytics-display">
                        <div class="consumption-chart" id="consumptionChart">
                            <!-- Chart bars generated by JavaScript -->
                        </div>
                        <div class="analytics-metrics">
                            <div>
                                <div class="metric-value" id="totalConsumption">847.2</div>
                                <div class="metric-label">MW Total</div>
                            </div>
                            <div>
                                <div class="metric-value" id="efficiency">96.8</div>
                                <div class="metric-label">% Efficiency</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Renewable Energy Integration -->
                <div class="power-panel">
                    <div class="panel-header">
                        <div class="plasma-indicator"></div>
                        Renewable Sources
                    </div>
                    <div class="renewable-sources">
                        <div class="energy-source" data-source="solar">
                            <div class="source-icon">☀️</div>
                            <div class="source-output" id="solarOutput">324.5 MW</div>
                        </div>
                        <div class="energy-source" data-source="wind">
                            <div class="source-icon">💨</div>
                            <div class="source-output" id="windOutput">189.7 MW</div>
                        </div>
                        <div class="energy-source" data-source="fusion">
                            <div class="source-icon">⚛️</div>
                            <div class="source-output" id="fusionOutput">533.0 MW</div>
                        </div>
                    </div>
                </div>

                <!-- Load Balancing Interface -->
                <div class="power-panel">
                    <div class="panel-header">
                        <div class="plasma-indicator"></div>
                        Load Balancing
                    </div>
                    <div class="load-balancer">
                        <div class="balance-display" id="balanceDisplay">
                            <!-- Balance visualization generated by JavaScript -->
                        </div>
                        <div class="power-indicator">
                            <div class="indicator-light"></div>
                            <span>System Balance: <span id="balanceStatus">Optimal</span></span>
                        </div>
                    </div>
                </div>

                <!-- Energy Storage Management -->
                <div class="power-panel">
                    <div class="panel-header">
                        <div class="plasma-indicator"></div>
                        Storage Management
                    </div>
                    <div class="storage-banks">
                        <div class="storage-unit">
                            <div class="storage-capacity">Plasma Bank A</div>
                            <div class="storage-level">
                                <div class="storage-fill" style="width: 78%" id="storageA"></div>
                            </div>
                            <div class="storage-capacity">78% (1.2 TWh)</div>
                        </div>
                        <div class="storage-unit">
                            <div class="storage-capacity">Plasma Bank B</div>
                            <div class="storage-level">
                                <div class="storage-fill" style="width: 45%" id="storageB"></div>
                            </div>
                            <div class="storage-capacity">45% (0.7 TWh)</div>
                        </div>
                    </div>
                </div>

                <!-- Smart Grid Coordination -->
                <div class="power-panel grid-status">
                    <div class="grid-node">Grid A</div>
                    <div class="grid-connection"></div>
                    <div class="grid-node">Central</div>
                    <div class="grid-connection"></div>
                    <div class="grid-node">Grid B</div>
                    <div class="grid-connection"></div>
                    <div class="grid-node">Grid C</div>
                </div>

                <!-- Control Interface -->
                <div class="control-interface">
                    <button class="plasma-button" onclick="togglePowerFlow()">
                        Toggle Power Flow
                    </button>
                    <button class="plasma-button" onclick="optimizeDistribution()">
                        Optimize Distribution
                    </button>
                    <button class="plasma-button" onclick="emergencyProtocol()">
                        Emergency Protocol
                    </button>
                    <div class="power-indicator">
                        <div class="indicator-light"></div>
                        <span>Plasma Field Intensity:</span>
                    </div>
                    <label for="plasmaIntensity" class="visually-hidden">Plasma Field Intensity Control</label>
                    <input type="range" class="plasma-slider" id="plasmaIntensity" 
                           min="0" max="100" value="85" 
                           onchange="adjustPlasmaField(this.value)">
                    <button class="plasma-button" onclick="resetSystem()">
                        Reset System
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        class PlasmaEnergyPowerManagement {
            constructor() {
                this.isActive = true;
                this.powerFlow = 847.2;
                this.efficiency = 96.8;
                this.plasmaIntensity = 85;
                this.solarOutput = 324.5;
                this.windOutput = 189.7;
                this.fusionOutput = 533.0;
                this.storageA = 78;
                this.storageB = 45;
                this.balanceStatus = 'Optimal';
                
                this.init();
                this.startPlasmaCycle();
            }

            init() {
                this.generatePlasmaField();
                this.generateDistributionMatrix();
                this.generateConsumptionChart();
                this.generateLoadBalance();
                this.updateMetrics();
            }

            generatePlasmaField() {
                const field = document.getElementById('plasmaField');
                field.innerHTML = '';
                
                // Generate power nodes
                const nodeCount = 8;
                const nodes = [];
                
                for (let i = 0; i < nodeCount; i++) {
                    const node = document.createElement('div');
                    node.className = 'power-node';
                    
                    const x = Math.random() * 85 + 5;
                    const y = Math.random() * 75 + 10;
                    
                    node.style.left = x + '%';
                    node.style.top = y + '%';
                    node.style.animationDelay = Math.random() * 3 + 's';
                    
                    field.appendChild(node);
                    nodes.push({ element: node, x, y });
                }
                
                // Generate plasma arcs between nodes
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        if (Math.random() < 0.4) {
                            this.createPlasmaArc(field, nodes[i], nodes[j]);
                        }
                    }
                }
                
                // Generate energy streams
                for (let i = 0; i < 15; i++) {
                    setTimeout(() => {
                        this.createEnergyStream(field);
                    }, i * 200);
                }
            }

            createPlasmaArc(parent, node1, node2) {
                const arc = document.createElement('div');
                arc.className = 'plasma-arc';
                
                const dx = node2.x - node1.x;
                const dy = node2.y - node1.y;
                const length = Math.sqrt(dx * dx + dy * dy);
                const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                
                arc.style.left = node1.x + '%';
                arc.style.top = node1.y + '%';
                arc.style.width = length + '%';
                arc.style.transform = `rotate(${angle}deg)`;
                arc.style.animationDelay = Math.random() * 2 + 's';
                
                parent.appendChild(arc);
            }

            createEnergyStream(parent) {
                const stream = document.createElement('div');
                stream.className = 'energy-stream';
                
                stream.style.left = '5%';
                stream.style.top = Math.random() * 80 + 10 + '%';
                stream.style.animationDelay = Math.random() * 4 + 's';
                
                const colors = ['#ff00ff', '#00d4ff', '#00ff7f', '#8a2be2'];
                const color = colors[Math.floor(Math.random() * colors.length)];
                stream.style.background = color;
                stream.style.boxShadow = `0 0 15px ${color}`;
                
                parent.appendChild(stream);
                
                setTimeout(() => {
                    if (stream.parentNode) {
                        stream.parentNode.removeChild(stream);
                    }
                }, 4000);
            }

            generateDistributionMatrix() {
                const matrix = document.getElementById('distributionMatrix');
                matrix.innerHTML = '';
                
                for (let i = 0; i < 16; i++) {
                    const cell = document.createElement('div');
                    cell.className = 'distribution-cell';
                    
                    const load = Math.floor(Math.random() * 100);
                    cell.textContent = load + '%';
                    cell.dataset.load = load;
                    
                    if (load > 80) {
                        cell.classList.add('active');
                    }
                    
                    cell.addEventListener('click', () => {
                        this.toggleDistributionCell(cell);
                    });
                    
                    matrix.appendChild(cell);
                }
            }

            toggleDistributionCell(cell) {
                cell.classList.toggle('active');
                this.playPlasmaFeedback();
                this.calculateDistributionBalance();
            }

            generateConsumptionChart() {
                const chart = document.getElementById('consumptionChart');
                chart.innerHTML = '';
                
                const barCount = 20;
                for (let i = 0; i < barCount; i++) {
                    const bar = document.createElement('div');
                    bar.className = 'chart-bar';
                    bar.style.left = (i * 4.5) + '%';
                    bar.style.animationDelay = (i * 0.2) + 's';
                    bar.style.animationDuration = (Math.random() * 2 + 2) + 's';
                    chart.appendChild(bar);
                }
            }

            generateLoadBalance() {
                const display = document.getElementById('balanceDisplay');
                
                // Create balance beam
                const beam = document.createElement('div');
                beam.className = 'balance-beam';
                display.appendChild(beam);
                
                // Create load weights
                const weights = [
                    { left: '20%', top: '30%' },
                    { left: '50%', top: '20%' },
                    { left: '75%', top: '35%' }
                ];
                
                weights.forEach((pos, index) => {
                    const weight = document.createElement('div');
                    weight.className = 'load-weight';
                    weight.style.left = pos.left;
                    weight.style.top = pos.top;
                    weight.style.animationDelay = (index * 0.5) + 's';
                    display.appendChild(weight);
                });
            }

            updateMetrics() {
                setInterval(() => {
                    if (this.isActive) {
                        // Update consumption metrics
                        this.powerFlow += (Math.random() - 0.5) * 20;
                        this.efficiency += (Math.random() - 0.5) * 0.5;
                        
                        // Update renewable outputs
                        this.solarOutput += (Math.random() - 0.5) * 10;
                        this.windOutput += (Math.random() - 0.5) * 15;
                        this.fusionOutput += (Math.random() - 0.5) * 25;
                        
                        // Update storage levels
                        this.storageA += (Math.random() - 0.5) * 2;
                        this.storageB += (Math.random() - 0.5) * 3;
                        
                        // Keep within bounds
                        this.powerFlow = Math.max(500, Math.min(1200, this.powerFlow));
                        this.efficiency = Math.max(85, Math.min(100, this.efficiency));
                        this.solarOutput = Math.max(200, Math.min(400, this.solarOutput));
                        this.windOutput = Math.max(100, Math.min(300, this.windOutput));
                        this.fusionOutput = Math.max(400, Math.min(700, this.fusionOutput));
                        this.storageA = Math.max(0, Math.min(100, this.storageA));
                        this.storageB = Math.max(0, Math.min(100, this.storageB));
                        
                        // Update display
                        document.getElementById('totalConsumption').textContent = this.powerFlow.toFixed(1);
                        document.getElementById('efficiency').textContent = this.efficiency.toFixed(1);
                        document.getElementById('solarOutput').textContent = this.solarOutput.toFixed(1) + ' MW';
                        document.getElementById('windOutput').textContent = this.windOutput.toFixed(1) + ' MW';
                        document.getElementById('fusionOutput').textContent = this.fusionOutput.toFixed(1) + ' MW';
                        
                        // Update storage displays
                        document.getElementById('storageA').style.width = this.storageA + '%';
                        document.getElementById('storageB').style.width = this.storageB + '%';
                        
                        this.updateBalanceStatus();
                    }
                }, 3000);
            }

            updateBalanceStatus() {
                const totalGeneration = this.solarOutput + this.windOutput + this.fusionOutput;
                const balance = totalGeneration - this.powerFlow;
                
                let status;
                if (Math.abs(balance) < 50) {
                    status = 'Optimal';
                } else if (balance > 0) {
                    status = 'Surplus';
                } else {
                    status = 'Deficit';
                }
                
                this.balanceStatus = status;
                document.getElementById('balanceStatus').textContent = status;
            }

            startPlasmaCycle() {
                // Regenerate plasma field periodically
                setInterval(() => {
                    if (this.isActive) {
                        this.generatePlasmaField();
                    }
                }, 20000);
                
                // Update distribution matrix
                setInterval(() => {
                    if (this.isActive) {
                        this.updateDistributionMatrix();
                    }
                }, 8000);
                
                // Generate new energy streams
                setInterval(() => {
                    if (this.isActive) {
                        const field = document.getElementById('plasmaField');
                        this.createEnergyStream(field);
                    }
                }, 1000);
            }

            updateDistributionMatrix() {
                const cells = document.querySelectorAll('.distribution-cell');
                cells.forEach(cell => {
                    const load = Math.floor(Math.random() * 100);
                    cell.textContent = load + '%';
                    cell.dataset.load = load;
                    
                    if (load > 80) {
                        cell.classList.add('active');
                    } else {
                        cell.classList.remove('active');
                    }
                    
                    // Add electromagnetic discharge effect
                    cell.style.boxShadow = '0 0 25px rgba(255, 0, 255, 0.8)';
                    setTimeout(() => {
                        cell.style.boxShadow = '';
                    }, 400);
                });
            }

            calculateDistributionBalance() {
                const activeCells = document.querySelectorAll('.distribution-cell.active');
                const totalLoad = Array.from(activeCells).reduce((sum, cell) => {
                    return sum + parseInt(cell.dataset.load);
                }, 0);
                
                // Adjust plasma intensity based on load
                this.plasmaIntensity = Math.min(100, totalLoad / activeCells.length);
                document.getElementById('plasmaIntensity').value = this.plasmaIntensity;
                this.adjustPlasmaField(this.plasmaIntensity);
            }

            playPlasmaFeedback() {
                // Visual feedback for user interactions
                const component = document.querySelector('.hybrid-component');
                const originalBoxShadow = component.style.boxShadow;
                
                component.style.boxShadow = '0 0 60px rgba(0, 212, 255, 0.8), inset 0 0 60px rgba(0, 212, 255, 0.3)';
                setTimeout(() => {
                    component.style.boxShadow = originalBoxShadow;
                }, 200);
            }

            togglePowerFlow() {
                this.isActive = !this.isActive;
                const component = document.querySelector('.hybrid-component');
                
                if (!this.isActive) {
                    component.classList.add('loading');
                } else {
                    component.classList.remove('loading');
                }
                
                this.playPlasmaFeedback();
            }

            optimizeDistribution() {
                // Optimize distribution pattern
                const cells = document.querySelectorAll('.distribution-cell');
                cells.forEach((cell, index) => {
                    setTimeout(() => {
                        const optimalLoad = 60 + Math.random() * 20;
                        cell.textContent = optimalLoad.toFixed(0) + '%';
                        cell.dataset.load = optimalLoad;
                        cell.classList.add('active');
                        
                        cell.style.background = 'rgba(0, 255, 127, 0.5)';
                        setTimeout(() => {
                            cell.style.background = '';
                        }, 1000);
                    }, index * 50);
                });
                
                this.playPlasmaFeedback();
            }

            emergencyProtocol() {
                // Emergency protocol - shutdown and safe mode
                const nodes = document.querySelectorAll('.power-node');
                const arcs = document.querySelectorAll('.plasma-arc');
                const streams = document.querySelectorAll('.energy-stream');
                
                // Stop all plasma activity
                nodes.forEach(node => {
                    node.style.boxShadow = '0 0 40px #ff0000';
                    node.style.background = 'radial-gradient(circle, #ff0000, #aa0000)';
                });
                
                arcs.forEach(arc => {
                    arc.style.background = 'linear-gradient(90deg, #ff0000, #aa0000)';
                    arc.style.boxShadow = '0 0 15px #ff0000';
                });
                
                streams.forEach(stream => {
                    stream.style.animationPlayState = 'paused';
                });
                
                // Reset after 3 seconds
                setTimeout(() => {
                    this.generatePlasmaField();
                }, 3000);
                
                this.playPlasmaFeedback();
            }

            resetSystem() {
                this.powerFlow = 847.2;
                this.efficiency = 96.8;
                this.plasmaIntensity = 85;
                this.solarOutput = 324.5;
                this.windOutput = 189.7;
                this.fusionOutput = 533.0;
                this.storageA = 78;
                this.storageB = 45;
                this.balanceStatus = 'Optimal';
                
                document.getElementById('plasmaIntensity').value = 85;
                this.init();
                this.playPlasmaFeedback();
            }

            adjustPlasmaField(value) {
                this.plasmaIntensity = parseInt(value);
                
                // Adjust visual intensity based on slider value
                const intensity = value / 100;
                const component = document.querySelector('.hybrid-component');
                const nodes = document.querySelectorAll('.power-node');
                const arcs = document.querySelectorAll('.plasma-arc');
                
                component.style.boxShadow = `
                    0 0 ${40 + intensity * 30}px rgba(255, 0, 255, ${0.4 + intensity * 0.3}),
                    inset 0 0 ${40 + intensity * 30}px rgba(138, 43, 226, ${0.2 + intensity * 0.2})
                `;
                
                nodes.forEach(node => {
                    node.style.boxShadow = `0 0 ${25 + intensity * 25}px #ff00ff`;
                });
                
                arcs.forEach(arc => {
                    arc.style.boxShadow = `0 0 ${10 + intensity * 20}px #ff00ff`;
                });
            }
        }

        // Global functions for button interactions
        let powerManagement;

        function togglePowerFlow() {
            powerManagement.togglePowerFlow();
        }

        function optimizeDistribution() {
            powerManagement.optimizeDistribution();
        }

        function emergencyProtocol() {
            powerManagement.emergencyProtocol();
        }

        function resetSystem() {
            powerManagement.resetSystem();
        }

        function adjustPlasmaField(value) {
            powerManagement.adjustPlasmaField(value);
        }

        // Initialize the plasma energy power management system
        document.addEventListener('DOMContentLoaded', () => {
            powerManagement = new PlasmaEnergyPowerManagement();
        });

        // Add keyboard accessibility
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'Enter':
                case ' ':
                    if (e.target.classList.contains('plasma-button')) {
                        e.target.click();
                    }
                    break;
                case 'r':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        resetSystem();
                    }
                    break;
                case 't':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        togglePowerFlow();
                    }
                    break;
                case 'o':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        optimizeDistribution();
                    }
                    break;
                case 'e':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        emergencyProtocol();
                    }
                    break;
            }
        });

        // Responsive behavior
        window.addEventListener('resize', () => {
            if (powerManagement) {
                setTimeout(() => {
                    powerManagement.generatePlasmaField();
                }, 300);
            }
        });

        // Add interaction effects for renewable energy sources
        document.addEventListener('click', (e) => {
            if (e.target.closest('.energy-source')) {
                const source = e.target.closest('.energy-source');
                const sourceType = source.dataset.source;
                
                source.style.transform = 'scale(1.1)';
                source.style.boxShadow = '0 0 30px rgba(0, 255, 127, 0.8)';
                
                setTimeout(() => {
                    source.style.transform = '';
                    source.style.boxShadow = '';
                }, 300);
                
                powerManagement.playPlasmaFeedback();
            }
        });
    </script>
</body>
</html>