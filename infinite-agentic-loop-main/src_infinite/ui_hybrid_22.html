<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Hybrid 22 - Neon Circuit Project Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #fff;
            overflow: hidden;
            position: relative;
        }

        /* Circuit Board Background */
        .circuit-bg {
            position: fixed;
            inset: 0;
            background: #0a0a0a;
            overflow: hidden;
        }

        .circuit-grid {
            position: absolute;
            inset: 0;
            background-image: 
                repeating-linear-gradient(0deg, #0f0f0f 0, transparent 1px, transparent 40px, #0f0f0f 41px),
                repeating-linear-gradient(90deg, #0f0f0f 0, transparent 1px, transparent 40px, #0f0f0f 41px);
            opacity: 0.5;
        }

        .circuit-trace {
            position: absolute;
            background: linear-gradient(90deg, transparent, #00ffff, transparent);
            height: 2px;
            animation: trace-flow 3s linear infinite;
        }

        @keyframes trace-flow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Main Container */
        .project-manager {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: grid;
            grid-template-columns: 60px 1fr;
            grid-template-rows: 60px 1fr;
            gap: 2px;
            background: #000;
            padding: 10px;
        }

        /* Header Bar */
        .header-bar {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
            border: 2px solid #00ffff;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .header-bar::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
            animation: scan 3s linear infinite;
        }

        @keyframes scan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .project-title {
            font-size: 24px;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        .header-stats {
            display: flex;
            gap: 30px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-value {
            font-size: 20px;
            color: #ff00ff;
            text-shadow: 0 0 10px rgba(255, 0, 255, 0.8);
        }

        .stat-label {
            font-size: 12px;
            color: #888;
        }

        /* Sidebar */
        .sidebar {
            background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
            border: 2px solid #00ffff;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
            gap: 20px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
        }

        .mode-btn {
            width: 40px;
            height: 40px;
            background: #000;
            border: 2px solid #333;
            border-radius: 8px;
            color: #666;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            position: relative;
        }

        .mode-btn:hover {
            border-color: #00ffff;
            color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }

        .mode-btn.active {
            background: #00ffff;
            color: #000;
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
        }

        .mode-btn.active::after {
            content: '';
            position: absolute;
            inset: -10px;
            border: 2px solid #00ffff;
            border-radius: 12px;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0; }
        }

        /* Main Content Area */
        .main-content {
            background: linear-gradient(135deg, #0a0a0a, #000);
            border: 2px solid #00ffff;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.1);
        }

        /* View Containers */
        .view-container {
            position: absolute;
            inset: 0;
            padding: 20px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s;
        }

        .view-container.active {
            opacity: 1;
            visibility: visible;
        }

        /* Kanban View */
        .kanban-board {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            height: 100%;
        }

        .kanban-column {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .kanban-column::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 10px;
            height: 10px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
        }

        .column-header {
            font-size: 16px;
            font-weight: bold;
            color: #00ffff;
            text-align: center;
            margin-bottom: 15px;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        .task-card {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: move;
            transition: all 0.3s;
            position: relative;
        }

        .task-card:hover {
            border-color: #00ffff;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .task-priority {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: blink 2s infinite;
        }

        .priority-high { background: #ff0080; box-shadow: 0 0 10px #ff0080; }
        .priority-medium { background: #ffff00; box-shadow: 0 0 10px #ffff00; }
        .priority-low { background: #00ff80; box-shadow: 0 0 10px #00ff80; }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .task-title {
            font-size: 14px;
            color: #fff;
            margin-bottom: 5px;
        }

        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }

        .task-assignee {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #000;
        }

        .task-due {
            font-size: 11px;
            color: #888;
        }

        /* Timeline View */
        .timeline-view {
            height: 100%;
            overflow: auto;
        }

        .timeline-header {
            display: grid;
            grid-template-columns: 200px repeat(30, 40px);
            background: rgba(0, 255, 255, 0.1);
            border-bottom: 2px solid #00ffff;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .timeline-label {
            padding: 10px;
            font-weight: bold;
            color: #00ffff;
            border-right: 1px solid #333;
        }

        .timeline-day {
            padding: 10px 5px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-right: 1px solid #222;
        }

        .timeline-row {
            display: grid;
            grid-template-columns: 200px repeat(30, 40px);
            border-bottom: 1px solid #222;
            position: relative;
        }

        .timeline-task-name {
            padding: 15px 10px;
            background: rgba(0, 0, 0, 0.5);
            border-right: 1px solid #333;
            color: #fff;
            font-size: 13px;
        }

        .timeline-bar {
            position: absolute;
            height: 30px;
            background: linear-gradient(90deg, #00ffff, #ff00ff);
            border-radius: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            padding: 0 10px;
            font-size: 11px;
            color: #000;
            font-weight: bold;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s;
        }

        .timeline-bar:hover {
            transform: translateY(-50%) scale(1.05);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
        }

        /* Resource View */
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            height: 100%;
            overflow: auto;
        }

        .resource-card {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            position: relative;
            height: fit-content;
        }

        .resource-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #000;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        }

        .resource-name {
            text-align: center;
            font-size: 16px;
            color: #fff;
            margin-bottom: 10px;
        }

        .resource-role {
            text-align: center;
            font-size: 12px;
            color: #888;
            margin-bottom: 20px;
        }

        .workload-meter {
            height: 8px;
            background: #222;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .workload-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff80, #ffff00, #ff0080);
            transition: width 0.5s;
            box-shadow: 0 0 10px currentColor;
        }

        .workload-text {
            text-align: center;
            font-size: 11px;
            color: #666;
        }

        /* Chat View */
        .chat-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            height: 100%;
        }

        .chat-sidebar {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            overflow-y: auto;
        }

        .channel-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .channel-item:hover {
            background: rgba(0, 255, 255, 0.1);
        }

        .channel-item.active {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid #00ffff;
        }

        .channel-indicator {
            width: 8px;
            height: 8px;
            background: #00ff80;
            border-radius: 50%;
            animation: pulse-indicator 2s infinite;
        }

        @keyframes pulse-indicator {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(0.8); }
        }

        .chat-main {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 15px;
            border-bottom: 1px solid #333;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #000;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
        }

        .message-author {
            font-size: 12px;
            color: #00ffff;
            margin-bottom: 5px;
        }

        .message-text {
            font-size: 14px;
            color: #ccc;
            line-height: 1.4;
        }

        .message-time {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }

        .chat-input {
            padding: 15px;
            border-top: 1px solid #333;
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            color: #fff;
            font-family: inherit;
        }

        .chat-input input:focus {
            outline: none;
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .send-btn {
            background: #00ffff;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .send-btn:hover {
            background: #ff00ff;
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
        }

        /* Files View */
        .files-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            height: 100%;
        }

        .files-tree {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            overflow-y: auto;
        }

        .folder-item {
            margin-bottom: 5px;
        }

        .folder-header {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .folder-header:hover {
            background: rgba(0, 255, 255, 0.1);
        }

        .folder-icon {
            color: #00ffff;
            transition: transform 0.3s;
        }

        .folder-item.open .folder-icon {
            transform: rotate(90deg);
        }

        .folder-contents {
            padding-left: 20px;
            display: none;
        }

        .folder-item.open .folder-contents {
            display: block;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .file-item:hover {
            background: rgba(0, 255, 255, 0.1);
        }

        .file-icon {
            color: #ff00ff;
        }

        .files-main {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 10px;
            padding: 20px;
            overflow-y: auto;
        }

        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 20px;
        }

        .file-card {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .file-card:hover {
            border-color: #00ffff;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }

        .file-card-icon {
            font-size: 48px;
            color: #00ffff;
            margin-bottom: 10px;
        }

        .file-card-name {
            font-size: 12px;
            color: #ccc;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-card-size {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }

        /* Circuit Connections */
        .circuit-connection {
            position: absolute;
            pointer-events: none;
            opacity: 0.3;
        }

        .circuit-path {
            stroke: #00ffff;
            stroke-width: 2;
            fill: none;
            filter: drop-shadow(0 0 5px rgba(0, 255, 255, 0.8));
            stroke-dasharray: 5, 5;
            animation: dash 20s linear infinite;
        }

        @keyframes dash {
            to { stroke-dashoffset: -1000; }
        }

        /* Floating Particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            pointer-events: none;
            animation: float 10s infinite;
        }

        @keyframes float {
            0% { transform: translateY(100vh) scale(0); }
            10% { transform: translateY(90vh) scale(1); }
            90% { transform: translateY(10vh) scale(1); }
            100% { transform: translateY(0) scale(0); }
        }

        /* Icons */
        .icon {
            font-family: Arial, sans-serif;
            font-style: normal;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #111;
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb {
            background: #00ffff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #ff00ff;
            box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="circuit-bg">
        <div class="circuit-grid"></div>
        <div class="circuit-trace" style="top: 10%; width: 300px;"></div>
        <div class="circuit-trace" style="top: 30%; left: 20%; width: 400px;"></div>
        <div class="circuit-trace" style="top: 50%; right: 10%; width: 250px;"></div>
        <div class="circuit-trace" style="top: 70%; left: 30%; width: 350px;"></div>
        <div class="circuit-trace" style="top: 90%; width: 500px;"></div>
    </div>

    <div class="project-manager">
        <div class="header-bar">
            <div class="project-title">NEXUS PROJECT ALPHA</div>
            <div class="header-stats">
                <div class="stat-item">
                    <div class="stat-value">42</div>
                    <div class="stat-label">TASKS</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">8</div>
                    <div class="stat-label">MEMBERS</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">85%</div>
                    <div class="stat-label">PROGRESS</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">5d</div>
                    <div class="stat-label">REMAINING</div>
                </div>
            </div>
        </div>

        <div class="sidebar">
            <button class="mode-btn active" data-view="kanban" title="Kanban Board">
                <span class="icon">📋</span>
            </button>
            <button class="mode-btn" data-view="timeline" title="Timeline">
                <span class="icon">📊</span>
            </button>
            <button class="mode-btn" data-view="resources" title="Resources">
                <span class="icon">👥</span>
            </button>
            <button class="mode-btn" data-view="chat" title="Team Chat">
                <span class="icon">💬</span>
            </button>
            <button class="mode-btn" data-view="files" title="Files">
                <span class="icon">📁</span>
            </button>
        </div>

        <div class="main-content">
            <!-- Kanban View -->
            <div class="view-container active" data-view="kanban">
                <div class="kanban-board">
                    <div class="kanban-column">
                        <div class="column-header">BACKLOG</div>
                        <div class="task-card">
                            <div class="task-priority priority-low"></div>
                            <div class="task-title">Setup CI/CD Pipeline</div>
                            <div class="task-meta">
                                <div class="task-assignee">JD</div>
                                <div class="task-due">Mar 15</div>
                            </div>
                        </div>
                        <div class="task-card">
                            <div class="task-priority priority-medium"></div>
                            <div class="task-title">Database Schema Design</div>
                            <div class="task-meta">
                                <div class="task-assignee">AK</div>
                                <div class="task-due">Mar 18</div>
                            </div>
                        </div>
                    </div>

                    <div class="kanban-column">
                        <div class="column-header">IN PROGRESS</div>
                        <div class="task-card">
                            <div class="task-priority priority-high"></div>
                            <div class="task-title">API Authentication</div>
                            <div class="task-meta">
                                <div class="task-assignee">SM</div>
                                <div class="task-due">Mar 12</div>
                            </div>
                        </div>
                        <div class="task-card">
                            <div class="task-priority priority-medium"></div>
                            <div class="task-title">Frontend Dashboard</div>
                            <div class="task-meta">
                                <div class="task-assignee">LM</div>
                                <div class="task-due">Mar 14</div>
                            </div>
                        </div>
                    </div>

                    <div class="kanban-column">
                        <div class="column-header">TESTING</div>
                        <div class="task-card">
                            <div class="task-priority priority-high"></div>
                            <div class="task-title">User Registration Flow</div>
                            <div class="task-meta">
                                <div class="task-assignee">RK</div>
                                <div class="task-due">Mar 11</div>
                            </div>
                        </div>
                    </div>

                    <div class="kanban-column">
                        <div class="column-header">COMPLETED</div>
                        <div class="task-card">
                            <div class="task-priority priority-low"></div>
                            <div class="task-title">Project Setup</div>
                            <div class="task-meta">
                                <div class="task-assignee">TW</div>
                                <div class="task-due">Mar 05</div>
                            </div>
                        </div>
                        <div class="task-card">
                            <div class="task-priority priority-medium"></div>
                            <div class="task-title">Requirements Analysis</div>
                            <div class="task-meta">
                                <div class="task-assignee">KP</div>
                                <div class="task-due">Mar 08</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Circuit connections -->
                <svg class="circuit-connection" style="width: 100%; height: 100%;">
                    <path class="circuit-path" d="M 200 150 Q 400 100 600 150" />
                    <path class="circuit-path" d="M 600 250 Q 800 200 1000 250" />
                </svg>
            </div>

            <!-- Timeline View -->
            <div class="view-container" data-view="timeline">
                <div class="timeline-view">
                    <div class="timeline-header">
                        <div class="timeline-label">TASKS</div>
                        <div class="timeline-day">1</div>
                        <div class="timeline-day">2</div>
                        <div class="timeline-day">3</div>
                        <div class="timeline-day">4</div>
                        <div class="timeline-day">5</div>
                        <div class="timeline-day">6</div>
                        <div class="timeline-day">7</div>
                        <div class="timeline-day">8</div>
                        <div class="timeline-day">9</div>
                        <div class="timeline-day">10</div>
                        <div class="timeline-day">11</div>
                        <div class="timeline-day">12</div>
                        <div class="timeline-day">13</div>
                        <div class="timeline-day">14</div>
                        <div class="timeline-day">15</div>
                        <div class="timeline-day">16</div>
                        <div class="timeline-day">17</div>
                        <div class="timeline-day">18</div>
                        <div class="timeline-day">19</div>
                        <div class="timeline-day">20</div>
                        <div class="timeline-day">21</div>
                        <div class="timeline-day">22</div>
                        <div class="timeline-day">23</div>
                        <div class="timeline-day">24</div>
                        <div class="timeline-day">25</div>
                        <div class="timeline-day">26</div>
                        <div class="timeline-day">27</div>
                        <div class="timeline-day">28</div>
                        <div class="timeline-day">29</div>
                        <div class="timeline-day">30</div>
                    </div>
                    <div class="timeline-row">
                        <div class="timeline-task-name">Project Setup</div>
                        <div class="timeline-bar" style="left: 240px; width: 200px;">100%</div>
                    </div>
                    <div class="timeline-row">
                        <div class="timeline-task-name">Requirements Analysis</div>
                        <div class="timeline-bar" style="left: 280px; width: 240px;">100%</div>
                    </div>
                    <div class="timeline-row">
                        <div class="timeline-task-name">API Authentication</div>
                        <div class="timeline-bar" style="left: 360px; width: 320px;">60%</div>
                    </div>
                    <div class="timeline-row">
                        <div class="timeline-task-name">Frontend Dashboard</div>
                        <div class="timeline-bar" style="left: 400px; width: 360px;">45%</div>
                    </div>
                    <div class="timeline-row">
                        <div class="timeline-task-name">Database Schema</div>
                        <div class="timeline-bar" style="left: 480px; width: 400px;">30%</div>
                    </div>
                    <div class="timeline-row">
                        <div class="timeline-task-name">CI/CD Pipeline</div>
                        <div class="timeline-bar" style="left: 560px; width: 280px;">15%</div>
                    </div>
                </div>
            </div>

            <!-- Resources View -->
            <div class="view-container" data-view="resources">
                <div class="resource-grid">
                    <div class="resource-card">
                        <div class="resource-avatar">JD</div>
                        <div class="resource-name">John Doe</div>
                        <div class="resource-role">Full Stack Developer</div>
                        <div class="workload-meter">
                            <div class="workload-fill" style="width: 75%;"></div>
                        </div>
                        <div class="workload-text">75% Allocated</div>
                    </div>
                    <div class="resource-card">
                        <div class="resource-avatar">AK</div>
                        <div class="resource-name">Alice Kim</div>
                        <div class="resource-role">Backend Developer</div>
                        <div class="workload-meter">
                            <div class="workload-fill" style="width: 60%;"></div>
                        </div>
                        <div class="workload-text">60% Allocated</div>
                    </div>
                    <div class="resource-card">
                        <div class="resource-avatar">SM</div>
                        <div class="resource-name">Sam Miller</div>
                        <div class="resource-role">Security Engineer</div>
                        <div class="workload-meter">
                            <div class="workload-fill" style="width: 90%;"></div>
                        </div>
                        <div class="workload-text">90% Allocated</div>
                    </div>
                    <div class="resource-card">
                        <div class="resource-avatar">LM</div>
                        <div class="resource-name">Lisa Martin</div>
                        <div class="resource-role">Frontend Developer</div>
                        <div class="workload-meter">
                            <div class="workload-fill" style="width: 80%;"></div>
                        </div>
                        <div class="workload-text">80% Allocated</div>
                    </div>
                    <div class="resource-card">
                        <div class="resource-avatar">RK</div>
                        <div class="resource-name">Ryan Kumar</div>
                        <div class="resource-role">QA Engineer</div>
                        <div class="workload-meter">
                            <div class="workload-fill" style="width: 55%;"></div>
                        </div>
                        <div class="workload-text">55% Allocated</div>
                    </div>
                    <div class="resource-card">
                        <div class="resource-avatar">TW</div>
                        <div class="resource-name">Tom Wilson</div>
                        <div class="resource-role">DevOps Engineer</div>
                        <div class="workload-meter">
                            <div class="workload-fill" style="width: 70%;"></div>
                        </div>
                        <div class="workload-text">70% Allocated</div>
                    </div>
                </div>
            </div>

            <!-- Chat View -->
            <div class="view-container" data-view="chat">
                <div class="chat-container">
                    <div class="chat-sidebar">
                        <div class="channel-item active">
                            <div class="channel-indicator"></div>
                            <span># general</span>
                        </div>
                        <div class="channel-item">
                            <div class="channel-indicator"></div>
                            <span># development</span>
                        </div>
                        <div class="channel-item">
                            <div class="channel-indicator"></div>
                            <span># design</span>
                        </div>
                        <div class="channel-item">
                            <div class="channel-indicator"></div>
                            <span># testing</span>
                        </div>
                        <div class="channel-item">
                            <div class="channel-indicator"></div>
                            <span># random</span>
                        </div>
                    </div>
                    <div class="chat-main">
                        <div class="chat-header"># general</div>
                        <div class="chat-messages">
                            <div class="message">
                                <div class="message-avatar">JD</div>
                                <div class="message-content">
                                    <div class="message-author">John Doe</div>
                                    <div class="message-text">Morning team! Quick reminder about today's standup at 10 AM.</div>
                                    <div class="message-time">9:15 AM</div>
                                </div>
                            </div>
                            <div class="message">
                                <div class="message-avatar">AK</div>
                                <div class="message-content">
                                    <div class="message-author">Alice Kim</div>
                                    <div class="message-text">Thanks for the reminder! I'll share updates on the database migration.</div>
                                    <div class="message-time">9:18 AM</div>
                                </div>
                            </div>
                            <div class="message">
                                <div class="message-avatar">SM</div>
                                <div class="message-content">
                                    <div class="message-author">Sam Miller</div>
                                    <div class="message-text">API authentication module is almost ready. Running final security tests.</div>
                                    <div class="message-time">9:22 AM</div>
                                </div>
                            </div>
                            <div class="message">
                                <div class="message-avatar">LM</div>
                                <div class="message-content">
                                    <div class="message-author">Lisa Martin</div>
                                    <div class="message-text">Great! I'll need the API docs to integrate with the frontend dashboard.</div>
                                    <div class="message-time">9:25 AM</div>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input">
                            <input type="text" placeholder="Type a message...">
                            <button class="send-btn">SEND</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files View -->
            <div class="view-container" data-view="files">
                <div class="files-container">
                    <div class="files-tree">
                        <div class="folder-item open">
                            <div class="folder-header">
                                <span class="folder-icon icon">▶</span>
                                <span>Project Root</span>
                            </div>
                            <div class="folder-contents">
                                <div class="folder-item">
                                    <div class="folder-header">
                                        <span class="folder-icon icon">▶</span>
                                        <span>src</span>
                                    </div>
                                    <div class="folder-contents">
                                        <div class="file-item">
                                            <span class="file-icon icon">📄</span>
                                            <span>index.js</span>
                                        </div>
                                        <div class="file-item">
                                            <span class="file-icon icon">📄</span>
                                            <span>app.js</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="folder-item">
                                    <div class="folder-header">
                                        <span class="folder-icon icon">▶</span>
                                        <span>docs</span>
                                    </div>
                                </div>
                                <div class="file-item">
                                    <span class="file-icon icon">📄</span>
                                    <span>README.md</span>
                                </div>
                                <div class="file-item">
                                    <span class="file-icon icon">📄</span>
                                    <span>package.json</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="files-main">
                        <div class="files-grid">
                            <div class="file-card">
                                <div class="file-card-icon icon">📄</div>
                                <div class="file-card-name">API_Documentation.pdf</div>
                                <div class="file-card-size">2.4 MB</div>
                            </div>
                            <div class="file-card">
                                <div class="file-card-icon icon">🖼️</div>
                                <div class="file-card-name">UI_Mockups.fig</div>
                                <div class="file-card-size">15.2 MB</div>
                            </div>
                            <div class="file-card">
                                <div class="file-card-icon icon">📊</div>
                                <div class="file-card-name">Project_Timeline.xlsx</div>
                                <div class="file-card-size">145 KB</div>
                            </div>
                            <div class="file-card">
                                <div class="file-card-icon icon">🎥</div>
                                <div class="file-card-name">Demo_Video.mp4</div>
                                <div class="file-card-size">52.8 MB</div>
                            </div>
                            <div class="file-card">
                                <div class="file-card-icon icon">📝</div>
                                <div class="file-card-name">Meeting_Notes.docx</div>
                                <div class="file-card-size">89 KB</div>
                            </div>
                            <div class="file-card">
                                <div class="file-card-icon icon">🔐</div>
                                <div class="file-card-name">Security_Audit.pdf</div>
                                <div class="file-card-size">3.1 MB</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Particles -->
    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
    <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
    <div class="particle" style="left: 70%; animation-delay: 6s;"></div>
    <div class="particle" style="left: 90%; animation-delay: 8s;"></div>

    <script>
        // View switching
        const modeButtons = document.querySelectorAll('.mode-btn');
        const viewContainers = document.querySelectorAll('.view-container');

        modeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetView = btn.dataset.view;
                
                // Update active states
                modeButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Switch views
                viewContainers.forEach(container => {
                    if (container.dataset.view === targetView) {
                        container.classList.add('active');
                    } else {
                        container.classList.remove('active');
                    }
                });
            });
        });

        // Kanban drag and drop
        let draggedElement = null;

        document.querySelectorAll('.task-card').forEach(card => {
            card.draggable = true;
            
            card.addEventListener('dragstart', (e) => {
                draggedElement = e.target;
                e.target.style.opacity = '0.5';
            });
            
            card.addEventListener('dragend', (e) => {
                e.target.style.opacity = '';
            });
        });

        document.querySelectorAll('.kanban-column').forEach(column => {
            column.addEventListener('dragover', (e) => {
                e.preventDefault();
                const afterElement = getDragAfterElement(column, e.clientY);
                if (afterElement == null) {
                    column.appendChild(draggedElement);
                } else {
                    column.insertBefore(draggedElement, afterElement);
                }
            });
        });

        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.task-card:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        // Chat functionality
        const chatInput = document.querySelector('.chat-input input');
        const sendBtn = document.querySelector('.send-btn');
        const chatMessages = document.querySelector('.chat-messages');

        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        function sendMessage() {
            const text = chatInput.value.trim();
            if (!text) return;
            
            const message = document.createElement('div');
            message.className = 'message';
            message.innerHTML = `
                <div class="message-avatar">US</div>
                <div class="message-content">
                    <div class="message-author">You</div>
                    <div class="message-text">${text}</div>
                    <div class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                </div>
            `;
            
            chatMessages.appendChild(message);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            chatInput.value = '';
        }

        // File tree toggle
        document.querySelectorAll('.folder-header').forEach(header => {
            header.addEventListener('click', () => {
                const folderItem = header.parentElement;
                folderItem.classList.toggle('open');
            });
        });

        // Timeline hover effects
        document.querySelectorAll('.timeline-bar').forEach(bar => {
            bar.addEventListener('click', () => {
                const progress = prompt('Enter progress percentage (0-100):');
                if (progress && !isNaN(progress) && progress >= 0 && progress <= 100) {
                    bar.textContent = progress + '%';
                    bar.style.opacity = progress == 100 ? '0.7' : '1';
                }
            });
        });

        // Resource card hover effects
        document.querySelectorAll('.resource-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                const fill = card.querySelector('.workload-fill');
                fill.style.filter = 'brightness(1.5)';
            });
            
            card.addEventListener('mouseleave', () => {
                const fill = card.querySelector('.workload-fill');
                fill.style.filter = 'brightness(1)';
            });
        });

        // Channel switching
        document.querySelectorAll('.channel-item').forEach(channel => {
            channel.addEventListener('click', () => {
                document.querySelectorAll('.channel-item').forEach(c => c.classList.remove('active'));
                channel.classList.add('active');
                document.querySelector('.chat-header').textContent = channel.textContent.trim();
                
                // Simulate loading different channel messages
                chatMessages.innerHTML = `
                    <div class="message">
                        <div class="message-avatar">SY</div>
                        <div class="message-content">
                            <div class="message-author">System</div>
                            <div class="message-text">Welcome to ${channel.textContent.trim()}</div>
                            <div class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                        </div>
                    </div>
                `;
            });
        });

        // Add more circuit traces dynamically
        setInterval(() => {
            const trace = document.createElement('div');
            trace.className = 'circuit-trace';
            trace.style.top = Math.random() * 100 + '%';
            trace.style.left = Math.random() * 100 + '%';
            trace.style.width = Math.random() * 300 + 200 + 'px';
            trace.style.animationDuration = Math.random() * 3 + 2 + 's';
            document.querySelector('.circuit-bg').appendChild(trace);
            
            setTimeout(() => trace.remove(), 5000);
        }, 3000);

        // Add blinking effect to random task priorities
        setInterval(() => {
            const priorities = document.querySelectorAll('.task-priority');
            const randomPriority = priorities[Math.floor(Math.random() * priorities.length)];
            if (randomPriority) {
                randomPriority.style.animationDuration = '0.5s';
                setTimeout(() => {
                    randomPriority.style.animationDuration = '2s';
                }, 1000);
            }
        }, 5000);
    </script>
</body>
</html>