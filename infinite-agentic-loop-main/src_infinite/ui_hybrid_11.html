<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organic Nature Search Hub - UI Hybrid 11</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(to bottom, #e8f5e9 0%, #c8e6c9 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        /* Animated background leaves */
        .leaf {
            position: fixed;
            width: 40px;
            height: 40px;
            opacity: 0.1;
            animation: float 20s infinite ease-in-out;
        }

        .leaf:before {
            content: '🌿';
            font-size: 30px;
            position: absolute;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(45deg); }
            50% { transform: translateY(10px) rotate(-45deg); }
            75% { transform: translateY(-10px) rotate(180deg); }
        }

        /* Main container */
        .search-ecosystem {
            width: 90%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(76, 175, 80, 0.2);
            position: relative;
            overflow: hidden;
        }

        .search-ecosystem::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(139, 195, 74, 0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.3; }
        }

        /* Title with growing animation */
        .eco-title {
            text-align: center;
            color: #2e7d32;
            font-size: 2.5em;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .eco-title span {
            display: inline-block;
            animation: grow 0.5s ease-out forwards;
            animation-delay: calc(var(--i) * 0.1s);
            opacity: 0;
            transform: scale(0);
        }

        @keyframes grow {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Search container with organic shape */
        .search-container {
            position: relative;
            z-index: 1;
            margin-bottom: 30px;
        }

        .search-wrapper {
            position: relative;
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            border-radius: 60px;
            padding: 5px;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .search-wrapper:focus-within {
            transform: scale(1.02);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1), 0 5px 20px rgba(139, 195, 74, 0.3);
        }

        .search-input {
            width: 100%;
            padding: 20px 60px 20px 30px;
            border: none;
            background: transparent;
            font-size: 1.2em;
            color: #1b5e20;
            outline: none;
        }

        .search-input::placeholder {
            color: #81c784;
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #4caf50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-icon:hover {
            background: #66bb6a;
            transform: translateY(-50%) scale(1.1);
        }

        .search-icon:before {
            content: '🔍';
            font-size: 20px;
        }

        /* Filter tags with leaf shapes */
        .filter-tags {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
            z-index: 1;
            position: relative;
        }

        .filter-tag {
            background: linear-gradient(45deg, #a5d6a7 0%, #81c784 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            position: relative;
            overflow: hidden;
        }

        .filter-tag:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.3);
            transition: left 0.3s ease;
        }

        .filter-tag:hover:before {
            left: 100%;
        }

        .filter-tag.active {
            background: linear-gradient(45deg, #388e3c 0%, #2e7d32 100%);
            transform: scale(1.05);
        }

        /* Autocomplete suggestions with vine animation */
        .autocomplete {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px;
            margin-top: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-height: 0;
            transition: max-height 0.5s ease, opacity 0.3s ease;
            opacity: 0;
            z-index: 10;
        }

        .autocomplete.show {
            max-height: 300px;
            opacity: 1;
        }

        .suggestion {
            padding: 15px 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .suggestion:hover {
            background: #e8f5e9;
            border-left-color: #4caf50;
            padding-left: 35px;
        }

        .suggestion:before {
            content: '🌱';
            position: absolute;
            left: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .suggestion:hover:before {
            opacity: 1;
        }

        /* Recent searches with growth animation */
        .recent-searches {
            margin-bottom: 30px;
            z-index: 1;
            position: relative;
        }

        .section-title {
            color: #388e3c;
            font-size: 1.2em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title:before {
            content: '🌿';
            font-size: 1.2em;
        }

        .recent-items {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .recent-item {
            background: linear-gradient(135deg, #c5e1a5 0%, #aed581 100%);
            padding: 12px 20px;
            border-radius: 20px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: bloomIn 0.5s ease-out forwards;
            animation-delay: calc(var(--i) * 0.1s);
            opacity: 0;
            transform: scale(0) rotate(-10deg);
        }

        @keyframes bloomIn {
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        .recent-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(139, 195, 74, 0.3);
        }

        /* Results preview with organic cards */
        .results-preview {
            z-index: 1;
            position: relative;
        }

        .result-card {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #81c784;
            position: relative;
            overflow: hidden;
            animation: growFromSeed 0.6s ease-out forwards;
            animation-delay: calc(var(--i) * 0.15s);
            opacity: 0;
            transform: translateY(20px) scale(0.9);
        }

        @keyframes growFromSeed {
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .result-card:hover {
            transform: translateX(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-left-color: #4caf50;
        }

        .result-card:before {
            content: '';
            position: absolute;
            top: -100%;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(139, 195, 74, 0.1) 0%, transparent 50%);
            transition: top 0.5s ease;
        }

        .result-card:hover:before {
            top: 0;
        }

        .result-title {
            color: #2e7d32;
            font-size: 1.1em;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .result-description {
            color: #666;
            line-height: 1.6;
        }

        .result-meta {
            display: flex;
            gap: 15px;
            margin-top: 10px;
            font-size: 0.85em;
            color: #81c784;
        }

        /* Loading animation */
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.show {
            display: block;
        }

        .plant-loader {
            display: inline-block;
            width: 60px;
            height: 60px;
            position: relative;
        }

        .plant-loader:before {
            content: '🌱';
            font-size: 40px;
            position: absolute;
            animation: growPlant 1.5s ease-in-out infinite;
        }

        @keyframes growPlant {
            0%, 100% { transform: scale(0.8) rotate(-5deg); }
            50% { transform: scale(1.2) rotate(5deg); }
        }

        /* Responsive */
        @media (max-width: 600px) {
            .search-ecosystem {
                padding: 25px;
            }
            
            .eco-title {
                font-size: 1.8em;
            }
            
            .filter-tags {
                gap: 8px;
            }
            
            .filter-tag {
                padding: 8px 15px;
                font-size: 0.8em;
            }
        }
    </style>
</head>
<body>
    <!-- Floating leaves background -->
    <div class="leaf" style="top: 10%; left: 5%; animation-delay: 0s;"></div>
    <div class="leaf" style="top: 20%; right: 10%; animation-delay: 2s;"></div>
    <div class="leaf" style="bottom: 30%; left: 15%; animation-delay: 4s;"></div>
    <div class="leaf" style="bottom: 10%; right: 20%; animation-delay: 6s;"></div>
    <div class="leaf" style="top: 50%; left: 80%; animation-delay: 8s;"></div>

    <div class="search-ecosystem">
        <h1 class="eco-title">
            <span style="--i:0">O</span><span style="--i:1">r</span><span style="--i:2">g</span><span style="--i:3">a</span><span style="--i:4">n</span><span style="--i:5">i</span><span style="--i:6">c</span>
            <span style="--i:7"> </span>
            <span style="--i:8">S</span><span style="--i:9">e</span><span style="--i:10">a</span><span style="--i:11">r</span><span style="--i:12">c</span><span style="--i:13">h</span>
        </h1>

        <div class="search-container">
            <div class="search-wrapper">
                <input type="text" class="search-input" placeholder="Let your search grow naturally..." id="searchInput">
                <div class="search-icon" onclick="performSearch()"></div>
                
                <div class="autocomplete" id="autocomplete">
                    <div class="suggestion">Sustainable gardening practices</div>
                    <div class="suggestion">Organic composting methods</div>
                    <div class="suggestion">Native plant species guide</div>
                    <div class="suggestion">Permaculture design principles</div>
                    <div class="suggestion">Natural pest control solutions</div>
                </div>
            </div>
        </div>

        <div class="filter-tags">
            <button class="filter-tag active" onclick="toggleFilter(this)">All Seeds</button>
            <button class="filter-tag" onclick="toggleFilter(this)">Garden Tips</button>
            <button class="filter-tag" onclick="toggleFilter(this)">Plant Care</button>
            <button class="filter-tag" onclick="toggleFilter(this)">Eco Living</button>
            <button class="filter-tag" onclick="toggleFilter(this)">Seasonal</button>
        </div>

        <div class="recent-searches">
            <h3 class="section-title">Recently Planted Searches</h3>
            <div class="recent-items">
                <div class="recent-item" style="--i:0" onclick="quickSearch(this)">Indoor herbs</div>
                <div class="recent-item" style="--i:1" onclick="quickSearch(this)">Butterfly gardens</div>
                <div class="recent-item" style="--i:2" onclick="quickSearch(this)">Rain harvesting</div>
                <div class="recent-item" style="--i:3" onclick="quickSearch(this)">Companion planting</div>
                <div class="recent-item" style="--i:4" onclick="quickSearch(this)">Soil health</div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="plant-loader"></div>
            <p style="color: #4caf50; margin-top: 20px;">Growing your results...</p>
        </div>

        <div class="results-preview" id="results">
            <h3 class="section-title">Sprouting Results</h3>
            <div class="result-card" style="--i:0">
                <h4 class="result-title">Creating a Thriving Indoor Herb Garden</h4>
                <p class="result-description">Discover the secrets to growing fresh herbs year-round in your kitchen. From basil to thyme, learn the perfect conditions for each herb species.</p>
                <div class="result-meta">
                    <span>🌿 Plant Care</span>
                    <span>📅 5 days ago</span>
                    <span>⭐ 4.8 rating</span>
                </div>
            </div>
            <div class="result-card" style="--i:1">
                <h4 class="result-title">Companion Planting: Nature's Partnership</h4>
                <p class="result-description">Learn which plants grow better together. This ancient technique can improve yield, reduce pests, and create a balanced ecosystem in your garden.</p>
                <div class="result-meta">
                    <span>🌱 Garden Tips</span>
                    <span>📅 1 week ago</span>
                    <span>⭐ 4.9 rating</span>
                </div>
            </div>
            <div class="result-card" style="--i:2">
                <h4 class="result-title">Natural Composting Methods for Rich Soil</h4>
                <p class="result-description">Transform kitchen scraps and yard waste into black gold for your garden. Step-by-step guide to creating nutrient-rich compost naturally.</p>
                <div class="result-meta">
                    <span>♻️ Eco Living</span>
                    <span>📅 2 weeks ago</span>
                    <span>⭐ 5.0 rating</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize
        const searchInput = document.getElementById('searchInput');
        const autocomplete = document.getElementById('autocomplete');
        const results = document.getElementById('results');
        const loading = document.getElementById('loading');

        // Sample data for dynamic results
        const searchData = {
            'sustainable': [
                {
                    title: 'Sustainable Water Management in Gardens',
                    description: 'Innovative techniques for conserving water while maintaining a lush garden ecosystem.',
                    category: 'Eco Living',
                    date: '3 days ago',
                    rating: '4.7'
                },
                {
                    title: 'Building Sustainable Garden Structures',
                    description: 'Use reclaimed materials to create beautiful and functional garden features.',
                    category: 'Garden Tips',
                    date: '1 week ago',
                    rating: '4.6'
                }
            ],
            'organic': [
                {
                    title: 'Organic Fertilizers: Feed Your Soil Naturally',
                    description: 'Compare different organic fertilizers and learn when and how to apply them.',
                    category: 'Plant Care',
                    date: '2 days ago',
                    rating: '4.9'
                },
                {
                    title: 'Starting an Organic Vegetable Garden',
                    description: 'Everything you need to know about growing vegetables without synthetic chemicals.',
                    category: 'Garden Tips',
                    date: '5 days ago',
                    rating: '5.0'
                }
            ],
            'native': [
                {
                    title: 'Native Plants for Pollinators',
                    description: 'Attract bees, butterflies, and birds with these beautiful native species.',
                    category: 'Plant Care',
                    date: '1 day ago',
                    rating: '4.8'
                },
                {
                    title: 'Landscaping with Native Grasses',
                    description: 'Low-maintenance alternatives to traditional lawns using local grass species.',
                    category: 'Eco Living',
                    date: '4 days ago',
                    rating: '4.7'
                }
            ]
        };

        // Autocomplete functionality
        searchInput.addEventListener('input', function() {
            if (this.value.length > 0) {
                autocomplete.classList.add('show');
            } else {
                autocomplete.classList.remove('show');
            }
        });

        searchInput.addEventListener('blur', function() {
            setTimeout(() => {
                autocomplete.classList.remove('show');
            }, 200);
        });

        // Search functionality
        function performSearch() {
            const query = searchInput.value.toLowerCase();
            if (!query) return;

            loading.classList.add('show');
            results.style.display = 'none';

            setTimeout(() => {
                loading.classList.remove('show');
                displayResults(query);
            }, 1500);
        }

        function displayResults(query) {
            results.style.display = 'block';
            const resultsContainer = results.querySelector('h3').nextElementSibling;
            
            // Clear existing results except the title
            while (resultsContainer) {
                resultsContainer.remove();
            }

            // Find matching results
            let matchingResults = [];
            for (let key in searchData) {
                if (query.includes(key)) {
                    matchingResults = matchingResults.concat(searchData[key]);
                }
            }

            // If no specific matches, show default results
            if (matchingResults.length === 0) {
                matchingResults = [
                    {
                        title: `Results for "${query}"`,
                        description: 'Exploring new territories in sustainable gardening and organic living.',
                        category: 'Search Results',
                        date: 'Just now',
                        rating: '4.5'
                    }
                ];
            }

            // Add results with animation
            matchingResults.forEach((result, index) => {
                const card = document.createElement('div');
                card.className = 'result-card';
                card.style.setProperty('--i', index);
                card.innerHTML = `
                    <h4 class="result-title">${result.title}</h4>
                    <p class="result-description">${result.description}</p>
                    <div class="result-meta">
                        <span>🌿 ${result.category}</span>
                        <span>📅 ${result.date}</span>
                        <span>⭐ ${result.rating} rating</span>
                    </div>
                `;
                results.appendChild(card);
            });
        }

        // Quick search from recent items
        function quickSearch(element) {
            searchInput.value = element.textContent;
            performSearch();
        }

        // Filter toggle
        function toggleFilter(element) {
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });
            element.classList.add('active');
        }

        // Autocomplete item click
        document.querySelectorAll('.suggestion').forEach(item => {
            item.addEventListener('click', function() {
                searchInput.value = this.textContent;
                autocomplete.classList.remove('show');
                performSearch();
            });
        });

        // Enter key to search
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    </script>
</body>
</html>