<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Synapse Cognitive Dashboard</title>
    <style>
        :root {
            --synapse-fire: #ff6b9d;
            --neural-blue: #4ecdc4;
            --cognition-green: #45b7d1;
            --neuron-purple: #8b5cf6;
            --axon-gold: #ffd93d;
            --dendrite-silver: #e5e7eb;
            --synapse-gap: #1f2937;
            --neural-network: #0f172a;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--neural-network);
            color: var(--dendrite-silver);
            overflow-x: hidden;
            position: relative;
        }

        /* Neural Network Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(255, 107, 157, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 60%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* Animated Neural Pathways */
        .neural-pathway {
            position: fixed;
            width: 2px;
            background: linear-gradient(45deg, var(--synapse-fire), var(--neural-blue));
            opacity: 0.3;
            z-index: 1;
            animation: synapticFire 3s ease-in-out infinite;
        }

        .pathway-1 {
            top: 10%;
            left: 0;
            height: 40%;
            transform: rotate(25deg);
            animation-delay: 0s;
        }

        .pathway-2 {
            top: 30%;
            right: 0;
            height: 60%;
            transform: rotate(-30deg);
            animation-delay: 1s;
        }

        .pathway-3 {
            bottom: 20%;
            left: 20%;
            height: 35%;
            transform: rotate(60deg);
            animation-delay: 2s;
        }

        @keyframes synapticFire {
            0%, 100% { 
                opacity: 0.3;
                box-shadow: 0 0 5px var(--synapse-fire);
            }
            50% { 
                opacity: 0.8;
                box-shadow: 0 0 20px var(--synapse-fire), 0 0 40px var(--neural-blue);
            }
        }

        main {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, var(--synapse-fire), var(--neural-blue), var(--cognition-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, var(--synapse-fire), var(--neural-blue));
            animation: pulseGlow 2s ease-in-out infinite;
        }

        @keyframes pulseGlow {
            0%, 100% { opacity: 0.5; transform: translateX(-50%) scaleX(1); }
            50% { opacity: 1; transform: translateX(-50%) scaleX(1.2); }
        }

        .hybrid-component {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 2rem;
            position: relative;
        }

        /* Neural Dashboard Panel Base */
        .neural-panel {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(255, 107, 157, 0.3);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .neural-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--synapse-fire), var(--neural-blue), var(--cognition-green));
            animation: neuralPulse 2s ease-in-out infinite;
        }

        @keyframes neuralPulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .neural-panel:hover {
            border-color: var(--synapse-fire);
            box-shadow: 0 0 30px rgba(255, 107, 157, 0.3);
            transform: translateY(-5px);
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--synapse-fire);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .neural-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: radial-gradient(circle, var(--synapse-fire), var(--neural-blue));
            animation: neuronFire 1.5s ease-in-out infinite;
        }

        @keyframes neuronFire {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        /* Thought Analytics Panel */
        .thought-analytics {
            grid-column: 1 / 2;
            grid-row: 1 / 2;
        }

        .thought-pattern {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.8rem;
            background: rgba(255, 107, 157, 0.1);
            border-radius: 10px;
            border-left: 3px solid var(--synapse-fire);
        }

        .pattern-intensity {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 1rem;
            animation: synapticPulse 1s ease-in-out infinite;
        }

        .pattern-intensity.high { background: var(--synapse-fire); }
        .pattern-intensity.medium { background: var(--cognition-green); }
        .pattern-intensity.low { background: var(--neural-blue); }

        @keyframes synapticPulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.3); opacity: 1; }
        }

        .pattern-details {
            flex: 1;
        }

        .pattern-name {
            font-weight: 600;
            color: var(--dendrite-silver);
            margin-bottom: 0.3rem;
        }

        .pattern-frequency {
            font-size: 0.9rem;
            color: var(--neural-blue);
        }

        /* Memory Visualization Panel */
        .memory-visualization {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
        }

        .memory-network {
            position: relative;
            height: 200px;
            background: rgba(139, 92, 246, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .memory-node {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle, var(--neuron-purple), var(--cognition-green));
            animation: memoryPulse 2s ease-in-out infinite;
        }

        .memory-node:nth-child(1) { top: 20%; left: 15%; animation-delay: 0s; }
        .memory-node:nth-child(2) { top: 40%; left: 60%; animation-delay: 0.5s; }
        .memory-node:nth-child(3) { top: 70%; left: 30%; animation-delay: 1s; }
        .memory-node:nth-child(4) { top: 30%; left: 80%; animation-delay: 1.5s; }

        @keyframes memoryPulse {
            0%, 100% { 
                transform: scale(1);
                box-shadow: 0 0 10px var(--neuron-purple);
            }
            50% { 
                transform: scale(1.5);
                box-shadow: 0 0 20px var(--neuron-purple), 0 0 30px var(--cognition-green);
            }
        }

        .memory-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, var(--neuron-purple), var(--cognition-green));
            opacity: 0.6;
            animation: connectionFlow 3s ease-in-out infinite;
        }

        .connection-1 {
            top: 30%;
            left: 20%;
            width: 35%;
            transform: rotate(15deg);
        }

        .connection-2 {
            top: 50%;
            left: 40%;
            width: 25%;
            transform: rotate(-20deg);
            animation-delay: 1s;
        }

        @keyframes connectionFlow {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; box-shadow: 0 0 10px var(--neuron-purple); }
        }

        /* Learning Progress Panel */
        .learning-progress {
            grid-column: 1 / 2;
            grid-row: 2 / 3;
        }

        .synapse-strength {
            margin-bottom: 1.5rem;
        }

        .synapse-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            color: var(--dendrite-silver);
            font-size: 0.9rem;
        }

        .synapse-bar {
            height: 8px;
            background: rgba(255, 107, 157, 0.2);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .synapse-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--synapse-fire), var(--axon-gold));
            border-radius: 4px;
            position: relative;
            animation: synapseGrowth 2s ease-in-out infinite;
        }

        @keyframes synapseGrowth {
            0%, 100% { box-shadow: 0 0 5px var(--synapse-fire); }
            50% { box-shadow: 0 0 15px var(--synapse-fire), 0 0 25px var(--axon-gold); }
        }

        .progress-neural { width: 85%; }
        .progress-cognitive { width: 72%; }
        .progress-memory { width: 91%; }

        /* Cognitive Load Panel */
        .cognitive-load {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
        }

        .load-monitor {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .load-metric {
            text-align: center;
            padding: 1rem;
            background: rgba(78, 205, 196, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(78, 205, 196, 0.3);
        }

        .load-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--neural-blue);
            margin-bottom: 0.5rem;
        }

        .load-label {
            font-size: 0.9rem;
            color: var(--dendrite-silver);
        }

        .brainwave-monitor {
            height: 80px;
            background: rgba(15, 23, 42, 0.6);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        .brainwave {
            position: absolute;
            width: 100%;
            height: 2px;
            background: var(--neural-blue);
            top: 50%;
            animation: brainwaveFlow 1.5s ease-in-out infinite;
        }

        @keyframes brainwaveFlow {
            0% { transform: translateX(-100%) scaleY(1); }
            25% { transform: translateX(-50%) scaleY(2); }
            50% { transform: translateX(0%) scaleY(1); }
            75% { transform: translateX(50%) scaleY(3); }
            100% { transform: translateX(100%) scaleY(1); }
        }

        /* Neural Network Insights Panel */
        .network-insights {
            grid-column: 1 / 3;
            grid-row: 3 / 4;
        }

        .insight-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .insight-card {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .insight-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--neuron-purple), var(--cognition-green), var(--synapse-fire));
            border-radius: 15px;
            z-index: -1;
            animation: neuralGlow 3s ease-in-out infinite;
        }

        @keyframes neuralGlow {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        .insight-title {
            font-weight: 600;
            color: var(--neuron-purple);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .insight-neurons {
            display: flex;
            gap: 5px;
            margin-bottom: 1rem;
        }

        .mini-neuron {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--cognition-green);
            animation: miniNeuronFire 1s ease-in-out infinite;
        }

        .mini-neuron:nth-child(2) { animation-delay: 0.2s; }
        .mini-neuron:nth-child(3) { animation-delay: 0.4s; }
        .mini-neuron:nth-child(4) { animation-delay: 0.6s; }

        @keyframes miniNeuronFire {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.5); }
        }

        .insight-data {
            font-size: 0.9rem;
            color: var(--dendrite-silver);
            line-height: 1.6;
        }

        /* Synaptic Communication Lines */
        .synaptic-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, var(--synapse-fire), transparent);
            height: 1px;
            z-index: 1;
            animation: synapticCommunication 4s ease-in-out infinite;
        }

        .line-1 {
            top: 25%;
            left: 0;
            width: 100%;
            animation-delay: 0s;
        }

        .line-2 {
            top: 60%;
            left: 0;
            width: 100%;
            animation-delay: 2s;
        }

        @keyframes synapticCommunication {
            0%, 100% { opacity: 0; transform: scaleX(0); }
            50% { opacity: 0.8; transform: scaleX(1); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(5, auto);
            }

            .thought-analytics { grid-column: 1; grid-row: 1; }
            .memory-visualization { grid-column: 1; grid-row: 2; }
            .learning-progress { grid-column: 1; grid-row: 3; }
            .cognitive-load { grid-column: 1; grid-row: 4; }
            .network-insights { grid-column: 1; grid-row: 5; }

            .insight-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility Enhancements */
        .neural-panel:focus-within {
            outline: 2px solid var(--synapse-fire);
            outline-offset: 2px;
        }

        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Neural Pathways -->
    <div class="neural-pathway pathway-1"></div>
    <div class="neural-pathway pathway-2"></div>
    <div class="neural-pathway pathway-3"></div>

    <!-- Synaptic Communication Lines -->
    <div class="synaptic-line line-1"></div>
    <div class="synaptic-line line-2"></div>

    <main>
        <h1>Cognitive Dashboard - Neural Synapse Theme</h1>
        <div class="hybrid-component">
            
            <!-- Thought Analytics Panel -->
            <div class="neural-panel thought-analytics">
                <div class="panel-title">
                    <div class="neural-icon"></div>
                    Thought Analytics
                </div>
                
                <div class="thought-pattern">
                    <div class="pattern-intensity high"></div>
                    <div class="pattern-details">
                        <div class="pattern-name">Creative Processing</div>
                        <div class="pattern-frequency">142 Hz - High Gamma</div>
                    </div>
                </div>
                
                <div class="thought-pattern">
                    <div class="pattern-intensity medium"></div>
                    <div class="pattern-details">
                        <div class="pattern-name">Analytical Thinking</div>
                        <div class="pattern-frequency">8-12 Hz - Alpha</div>
                    </div>
                </div>
                
                <div class="thought-pattern">
                    <div class="pattern-intensity low"></div>
                    <div class="pattern-details">
                        <div class="pattern-name">Memory Consolidation</div>
                        <div class="pattern-frequency">4-8 Hz - Theta</div>
                    </div>
                </div>
                
                <div class="thought-pattern">
                    <div class="pattern-intensity high"></div>
                    <div class="pattern-details">
                        <div class="pattern-name">Focus Attention</div>
                        <div class="pattern-frequency">13-30 Hz - Beta</div>
                    </div>
                </div>
            </div>

            <!-- Memory Visualization Panel -->
            <div class="neural-panel memory-visualization">
                <div class="panel-title">
                    <div class="neural-icon"></div>
                    Memory Network
                </div>
                
                <div class="memory-network">
                    <div class="memory-connection connection-1"></div>
                    <div class="memory-connection connection-2"></div>
                    <div class="memory-node"></div>
                    <div class="memory-node"></div>
                    <div class="memory-node"></div>
                    <div class="memory-node"></div>
                </div>
                
                <div style="margin-top: 1rem; font-size: 0.9rem; color: var(--dendrite-silver);">
                    <div style="margin-bottom: 0.5rem;">
                        <strong>Active Pathways:</strong> 847 connections
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <strong>Memory Strength:</strong> 
                        <span style="color: var(--neuron-purple);">94.2%</span>
                    </div>
                    <div>
                        <strong>Retrieval Speed:</strong> 
                        <span style="color: var(--cognition-green);">1.3ms</span>
                    </div>
                </div>
            </div>

            <!-- Learning Progress Panel -->
            <div class="neural-panel learning-progress">
                <div class="panel-title">
                    <div class="neural-icon"></div>
                    Synaptic Strengthening
                </div>
                
                <div class="synapse-strength">
                    <div class="synapse-label">
                        <span>Neural Plasticity</span>
                        <span>85%</span>
                    </div>
                    <div class="synapse-bar">
                        <div class="synapse-progress progress-neural"></div>
                    </div>
                </div>
                
                <div class="synapse-strength">
                    <div class="synapse-label">
                        <span>Cognitive Flexibility</span>
                        <span>72%</span>
                    </div>
                    <div class="synapse-bar">
                        <div class="synapse-progress progress-cognitive"></div>
                    </div>
                </div>
                
                <div class="synapse-strength">
                    <div class="synapse-label">
                        <span>Memory Formation</span>
                        <span>91%</span>
                    </div>
                    <div class="synapse-bar">
                        <div class="synapse-progress progress-memory"></div>
                    </div>
                </div>
                
                <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid rgba(255, 107, 157, 0.3);">
                    <div style="font-size: 0.9rem; color: var(--dendrite-silver);">
                        <strong>Learning Rate:</strong> 
                        <span style="color: var(--axon-gold);">+12.4% this week</span>
                    </div>
                </div>
            </div>

            <!-- Cognitive Load Panel -->
            <div class="neural-panel cognitive-load">
                <div class="panel-title">
                    <div class="neural-icon"></div>
                    Brain Activity Monitor
                </div>
                
                <div class="load-monitor">
                    <div class="load-metric">
                        <div class="load-value">74%</div>
                        <div class="load-label">Cognitive Load</div>
                    </div>
                    <div class="load-metric">
                        <div class="load-value">92%</div>
                        <div class="load-label">Processing Power</div>
                    </div>
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <div style="font-size: 0.9rem; color: var(--dendrite-silver); margin-bottom: 0.5rem;">
                        Live Brainwave Activity
                    </div>
                    <div class="brainwave-monitor">
                        <div class="brainwave"></div>
                    </div>
                </div>
                
                <div style="font-size: 0.85rem; color: var(--neural-blue);">
                    Current State: <strong>Deep Focus</strong> • 
                    Optimal for complex problem solving
                </div>
            </div>

            <!-- Neural Network Insights Panel -->
            <div class="neural-panel network-insights">
                <div class="panel-title">
                    <div class="neural-icon"></div>
                    Neural Network Insights
                </div>
                
                <div class="insight-grid">
                    <div class="insight-card">
                        <div class="insight-title">
                            Connection Density
                            <div class="insight-neurons">
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                            </div>
                        </div>
                        <div class="insight-data">
                            Your neural network shows 23% higher connectivity than baseline. 
                            This indicates enhanced cognitive integration and faster information processing.
                        </div>
                    </div>
                    
                    <div class="insight-card">
                        <div class="insight-title">
                            Synaptic Efficiency
                            <div class="insight-neurons">
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                            </div>
                        </div>
                        <div class="insight-data">
                            Signal transmission speed improved by 18% over the past month. 
                            Optimized pathways are reducing cognitive latency significantly.
                        </div>
                    </div>
                    
                    <div class="insight-card">
                        <div class="insight-title">
                            Neuroplasticity Index
                            <div class="insight-neurons">
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                            </div>
                        </div>
                        <div class="insight-data">
                            High adaptability detected in frontal cortex regions. 
                            Brain is actively forming new neural pathways for learning.
                        </div>
                    </div>
                    
                    <div class="insight-card">
                        <div class="insight-title">
                            Cognitive Reserve
                            <div class="insight-neurons">
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                                <div class="mini-neuron"></div>
                            </div>
                        </div>
                        <div class="insight-data">
                            Strong backup pathways identified. Your brain maintains excellent 
                            resilience and can reroute processing during high cognitive demands.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Neural Synapse Dashboard Controller
        class NeuralSynapseController {
            constructor() {
                this.initializeNeuralActivity();
                this.setupSynapticCommunication();
                this.startCognitiveMonitoring();
                this.enableNeuralInteractions();
            }

            initializeNeuralActivity() {
                // Simulate real-time neural firing patterns
                const thoughtPatterns = document.querySelectorAll('.thought-pattern');
                const memoryNodes = document.querySelectorAll('.memory-node');
                
                setInterval(() => {
                    this.updateThoughtIntensity(thoughtPatterns);
                    this.simulateMemoryActivation(memoryNodes);
                }, 2000);
            }

            updateThoughtIntensity(patterns) {
                patterns.forEach((pattern, index) => {
                    const intensity = pattern.querySelector('.pattern-intensity');
                    const intensityLevels = ['low', 'medium', 'high'];
                    const currentLevel = Math.floor(Math.random() * 3);
                    
                    // Reset classes
                    intensity.className = 'pattern-intensity';
                    intensity.classList.add(intensityLevels[currentLevel]);
                    
                    // Update frequency based on intensity
                    const frequencyEl = pattern.querySelector('.pattern-frequency');
                    const frequencies = ['4-8 Hz - Theta', '8-12 Hz - Alpha', '13-30 Hz - Beta', '30-100 Hz - Gamma'];
                    frequencyEl.textContent = frequencies[currentLevel] || frequencies[Math.floor(Math.random() * frequencies.length)];
                });
            }

            simulateMemoryActivation(nodes) {
                nodes.forEach((node, index) => {
                    // Random activation delay to simulate natural neural firing
                    setTimeout(() => {
                        node.style.animation = 'none';
                        node.offsetHeight; // Trigger reflow
                        node.style.animation = `memoryPulse 2s ease-in-out infinite`;
                        node.style.animationDelay = `${Math.random() * 2}s`;
                    }, index * 200);
                });
            }

            setupSynapticCommunication() {
                // Create dynamic synaptic connections between panels
                const panels = document.querySelectorAll('.neural-panel');
                
                panels.forEach(panel => {
                    panel.addEventListener('mouseenter', () => {
                        this.activateSynapticPathway(panel);
                        this.broadcastNeuralSignal(panel);
                    });
                    
                    panel.addEventListener('mouseleave', () => {
                        this.deactivateSynapticPathway(panel);
                    });
                });
            }

            activateSynapticPathway(activePanel) {
                // Enhance border glow and create ripple effect
                activePanel.style.boxShadow = '0 0 40px rgba(255, 107, 157, 0.6), 0 0 80px rgba(78, 205, 196, 0.3)';
                activePanel.style.borderColor = 'var(--synapse-fire)';
                
                // Create neural particle effect
                this.createNeuralParticles(activePanel);
            }

            deactivateSynapticPathway(panel) {
                // Reset to default state
                panel.style.boxShadow = '';
                panel.style.borderColor = '';
            }

            createNeuralParticles(panel) {
                const rect = panel.getBoundingClientRect();
                const particleCount = 5;
                
                for (let i = 0; i < particleCount; i++) {
                    const particle = document.createElement('div');
                    particle.style.cssText = `
                        position: fixed;
                        width: 4px;
                        height: 4px;
                        background: var(--synapse-fire);
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 1000;
                        box-shadow: 0 0 10px var(--synapse-fire);
                    `;
                    
                    particle.style.left = rect.left + Math.random() * rect.width + 'px';
                    particle.style.top = rect.top + Math.random() * rect.height + 'px';
                    
                    document.body.appendChild(particle);
                    
                    // Animate particle
                    particle.animate([
                        { transform: 'scale(1)', opacity: 1 },
                        { transform: 'scale(2)', opacity: 0 }
                    ], {
                        duration: 800,
                        easing: 'ease-out'
                    }).onfinish = () => particle.remove();
                }
            }

            broadcastNeuralSignal(sourcePanel) {
                // Send activation signal to other panels
                const allPanels = document.querySelectorAll('.neural-panel');
                
                allPanels.forEach(panel => {
                    if (panel !== sourcePanel) {
                        // Create brief activation response
                        const icon = panel.querySelector('.neural-icon');
                        if (icon) {
                            icon.style.animation = 'none';
                            icon.offsetHeight; // Trigger reflow
                            icon.style.animation = 'neuronFire 0.5s ease-in-out';
                        }
                    }
                });
            }

            startCognitiveMonitoring() {
                // Update cognitive metrics dynamically
                this.updateCognitiveMetrics();
                setInterval(() => this.updateCognitiveMetrics(), 5000);
                
                // Animate brainwave patterns
                this.animateBrainwaves();
            }

            updateCognitiveMetrics() {
                const loadValue = document.querySelector('.load-metric .load-value');
                const processingValue = document.querySelectorAll('.load-metric .load-value')[1];
                
                if (loadValue && processingValue) {
                    // Simulate realistic cognitive load fluctuations
                    const currentLoad = parseInt(loadValue.textContent);
                    const newLoad = Math.max(60, Math.min(95, currentLoad + (Math.random() - 0.5) * 10));
                    
                    const currentProcessing = parseInt(processingValue.textContent);
                    const newProcessing = Math.max(85, Math.min(100, currentProcessing + (Math.random() - 0.5) * 5));
                    
                    loadValue.textContent = Math.round(newLoad) + '%';
                    processingValue.textContent = Math.round(newProcessing) + '%';
                }
                
                // Update synaptic strength bars
                this.updateSynapticStrength();
            }

            updateSynapticStrength() {
                const progressBars = document.querySelectorAll('.synapse-progress');
                
                progressBars.forEach(bar => {
                    const currentWidth = parseInt(bar.style.width || bar.className.match(/progress-(\w+)/)[1] === 'neural' ? '85' : 
                                                  bar.className.includes('cognitive') ? '72' : '91');
                    const variation = (Math.random() - 0.5) * 6; // ±3% variation
                    const newWidth = Math.max(60, Math.min(95, currentWidth + variation));
                    
                    bar.style.width = newWidth + '%';
                    
                    // Update corresponding label
                    const label = bar.closest('.synapse-strength').querySelector('.synapse-label span:last-child');
                    if (label) {
                        label.textContent = Math.round(newWidth) + '%';
                    }
                });
            }

            animateBrainwaves() {
                const brainwave = document.querySelector('.brainwave');
                if (!brainwave) return;
                
                // Create multiple brainwave layers for more realistic effect
                for (let i = 1; i <= 3; i++) {
                    const wave = brainwave.cloneNode();
                    wave.style.background = `var(--neural-blue)`;
                    wave.style.opacity = 0.3 + (i * 0.2);
                    wave.style.animationDelay = `${i * 0.3}s`;
                    wave.style.height = `${2 + i}px`;
                    brainwave.parentNode.appendChild(wave);
                }
            }

            enableNeuralInteractions() {
                // Add click interactions for deeper engagement
                const insightCards = document.querySelectorAll('.insight-card');
                
                insightCards.forEach(card => {
                    card.addEventListener('click', () => {
                        this.expandInsightDetails(card);
                    });
                });
                
                // Add keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Space' && e.target.classList.contains('insight-card')) {
                        e.preventDefault();
                        this.expandInsightDetails(e.target);
                    }
                });
            }

            expandInsightDetails(card) {
                // Toggle expanded state with neural animation
                const isExpanded = card.classList.contains('expanded');
                
                if (!isExpanded) {
                    card.classList.add('expanded');
                    card.style.transform = 'scale(1.02)';
                    card.style.zIndex = '10';
                    
                    // Add detailed insight data
                    const existingDetails = card.querySelector('.expanded-details');
                    if (!existingDetails) {
                        const details = document.createElement('div');
                        details.className = 'expanded-details';
                        details.style.cssText = `
                            margin-top: 1rem;
                            padding-top: 1rem;
                            border-top: 1px solid rgba(139, 92, 246, 0.3);
                            font-size: 0.85rem;
                            color: var(--cognition-green);
                            animation: fadeInNeuralDetails 0.3s ease-out;
                        `;
                        details.innerHTML = this.getExpandedInsightContent(card);
                        card.appendChild(details);
                    }
                } else {
                    card.classList.remove('expanded');
                    card.style.transform = '';
                    card.style.zIndex = '';
                    
                    const details = card.querySelector('.expanded-details');
                    if (details) details.remove();
                }
            }

            getExpandedInsightContent(card) {
                const title = card.querySelector('.insight-title').textContent.trim();
                
                const expandedContent = {
                    'Connection Density': `
                        <strong>Detailed Analysis:</strong><br>
                        • Frontal-parietal network: +28% connectivity<br>
                        • Default mode network: +19% efficiency<br>
                        • Cross-hemispheric transfer: 15ms faster
                    `,
                    'Synaptic Efficiency': `
                        <strong>Performance Metrics:</strong><br>
                        • Action potential speed: 120 m/s avg<br>
                        • Neurotransmitter recycling: 94% efficiency<br>
                        • Myelin integrity: Excellent condition
                    `,
                    'Neuroplasticity Index': `
                        <strong>Adaptation Indicators:</strong><br>
                        • New dendrite formation: +12% this month<br>
                        • Synaptic pruning: Optimal balance<br>
                        • BDNF levels: 15% above baseline
                    `,
                    'Cognitive Reserve': `
                        <strong>Resilience Factors:</strong><br>
                        • Backup pathway coverage: 89%<br>
                        • Stress resistance: High<br>
                        • Recovery time: 2.3 seconds avg
                    `
                };
                
                return expandedContent[title] || 'Additional neural data processing...';
            }
        }

        // Initialize the Neural Synapse Dashboard
        document.addEventListener('DOMContentLoaded', () => {
            new NeuralSynapseController();
            
            // Add CSS animation keyframes dynamically
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInNeuralDetails {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            `;
            document.head.appendChild(style);
        });

        // Advanced neural synchronization for multi-panel interactions
        class NeuralSynchronizer {
            constructor() {
                this.setupGlobalNeuralState();
                this.initializeCrossModalConnections();
            }

            setupGlobalNeuralState() {
                // Create shared neural state across all dashboard components
                window.neuralState = {
                    cognitiveLoad: 74,
                    processingPower: 92,
                    memoryStrength: 94.2,
                    attentionLevel: 'high',
                    learningMode: 'active'
                };
                
                // Broadcast state changes
                this.broadcastStateUpdates();
            }

            initializeCrossModalConnections() {
                // Connect thought analytics to memory visualization
                const thoughtPatterns = document.querySelectorAll('.thought-pattern');
                const memoryNodes = document.querySelectorAll('.memory-node');
                
                thoughtPatterns.forEach((pattern, index) => {
                    pattern.addEventListener('click', () => {
                        this.activateCorrespondingMemoryNode(index);
                    });
                });
            }

            activateCorrespondingMemoryNode(patternIndex) {
                const memoryNodes = document.querySelectorAll('.memory-node');
                const targetNode = memoryNodes[patternIndex % memoryNodes.length];
                
                if (targetNode) {
                    // Highlight the connected memory node
                    targetNode.style.animation = 'none';
                    targetNode.offsetHeight;
                    targetNode.style.animation = 'memoryPulse 1s ease-in-out 3';
                    targetNode.style.transform = 'scale(1.5)';
                    
                    setTimeout(() => {
                        targetNode.style.transform = '';
                    }, 1000);
                }
            }

            broadcastStateUpdates() {
                setInterval(() => {
                    // Update global neural state
                    window.neuralState.cognitiveLoad += (Math.random() - 0.5) * 10;
                    window.neuralState.cognitiveLoad = Math.max(60, Math.min(95, window.neuralState.cognitiveLoad));
                    
                    // Trigger visual updates across all components
                    this.syncVisualComponents();
                }, 3000);
            }

            syncVisualComponents() {
                // Sync all components to current neural state
                const loadValue = document.querySelector('.load-metric .load-value');
                if (loadValue) {
                    loadValue.textContent = Math.round(window.neuralState.cognitiveLoad) + '%';
                }
                
                // Adjust animation speeds based on cognitive load
                const highLoad = window.neuralState.cognitiveLoad > 80;
                const neuralIcons = document.querySelectorAll('.neural-icon');
                
                neuralIcons.forEach(icon => {
                    icon.style.animationDuration = highLoad ? '1s' : '1.5s';
                });
            }
        }

        // Initialize neural synchronization
        setTimeout(() => new NeuralSynchronizer(), 1000);
    </script>
</body>
</html>