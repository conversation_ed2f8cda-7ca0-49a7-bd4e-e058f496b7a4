<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bioelectric Living Data Ecosystem</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #0a0e1a;
            color: #00ffaa;
            overflow-x: hidden;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(0, 255, 170, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
        }

        main {
            padding: 2rem;
            min-height: 100vh;
            position: relative;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            color: #00ffaa;
            text-shadow: 0 0 20px #00ffaa, 0 0 40px #00ffaa;
            margin-bottom: 2rem;
            animation: bioelectric-pulse 3s ease-in-out infinite;
        }

        @keyframes bioelectric-pulse {
            0%, 100% { text-shadow: 0 0 20px #00ffaa, 0 0 40px #00ffaa; }
            50% { text-shadow: 0 0 30px #00ffaa, 0 0 60px #00ffaa, 0 0 80px #00ffaa; }
        }

        .hybrid-component {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(10, 20, 35, 0.8);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid #00ffaa;
            box-shadow: 
                0 0 30px rgba(0, 255, 170, 0.3),
                inset 0 0 30px rgba(0, 255, 170, 0.1);
            position: relative;
            overflow: hidden;
        }

        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ffaa, #8a2be2, #ffd700, #00ffaa);
            border-radius: 20px;
            z-index: -1;
            animation: bio-circuit-flow 4s linear infinite;
        }

        @keyframes bio-circuit-flow {
            0% { background-position: 0% 0%; }
            100% { background-position: 200% 200%; }
        }

        .ecosystem-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .data-panel {
            background: rgba(0, 20, 40, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid #00ffaa;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .data-panel:hover {
            box-shadow: 0 0 25px rgba(0, 255, 170, 0.4);
            transform: translateY(-5px);
        }

        .data-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ffaa, transparent);
            animation: electrical-scan 2s linear infinite;
        }

        @keyframes electrical-scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .panel-title {
            color: #ffd700;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #ffd700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .bio-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ffaa;
            box-shadow: 0 0 10px #00ffaa;
            animation: bio-heartbeat 1.5s ease-in-out infinite;
        }

        @keyframes bio-heartbeat {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.3); opacity: 0.7; }
        }

        /* Living Data Visualization */
        .data-viz {
            grid-column: span 2;
            height: 300px;
            position: relative;
        }

        .neural-network {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .node {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #00ffaa;
            border-radius: 50%;
            box-shadow: 0 0 15px #00ffaa;
            animation: node-pulse 2s ease-in-out infinite;
        }

        @keyframes node-pulse {
            0%, 100% { box-shadow: 0 0 15px #00ffaa; }
            50% { box-shadow: 0 0 25px #00ffaa, 0 0 35px #00ffaa; }
        }

        .connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, #00ffaa, #8a2be2);
            transform-origin: 0 50%;
            animation: data-flow 3s linear infinite;
        }

        @keyframes data-flow {
            0% { box-shadow: 0 0 5px #00ffaa; }
            50% { box-shadow: 0 0 15px #8a2be2; }
            100% { box-shadow: 0 0 5px #00ffaa; }
        }

        /* Bio-responsive Charts */
        .chart-container {
            height: 150px;
            position: relative;
            margin: 1rem 0;
        }

        .bio-chart {
            width: 100%;
            height: 100%;
            border: 1px solid #00ffaa;
            border-radius: 10px;
            background: rgba(0, 255, 170, 0.05);
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 0;
            width: 2px;
            background: #00ffaa;
            animation: bio-wave 4s ease-in-out infinite;
        }

        @keyframes bio-wave {
            0%, 100% { height: 20%; }
            25% { height: 80%; }
            50% { height: 40%; }
            75% { height: 70%; }
        }

        /* Living Metrics */
        .metric-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(0, 255, 170, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 170, 0.3);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 0 0 15px #ffd700;
            animation: metric-glow 2s ease-in-out infinite alternate;
        }

        @keyframes metric-glow {
            from { text-shadow: 0 0 15px #ffd700; }
            to { text-shadow: 0 0 25px #ffd700, 0 0 35px #ffd700; }
        }

        .metric-label {
            color: #00ffaa;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Electrical Pulse Analytics */
        .pulse-monitor {
            height: 100px;
            background: rgba(0, 20, 40, 0.8);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            margin: 1rem 0;
        }

        .pulse-line {
            position: absolute;
            width: 100%;
            height: 2px;
            top: 50%;
            background: #00ffaa;
            box-shadow: 0 0 10px #00ffaa;
            animation: electrical-pulse 1s linear infinite;
        }

        @keyframes electrical-pulse {
            0% { transform: translateX(-100%) scaleY(1); }
            10% { transform: translateX(-80%) scaleY(3); }
            20% { transform: translateX(-60%) scaleY(1); }
            30% { transform: translateX(-40%) scaleY(2); }
            40% { transform: translateX(-20%) scaleY(1); }
            50% { transform: translateX(0%) scaleY(4); }
            60% { transform: translateX(20%) scaleY(1); }
            70% { transform: translateX(40%) scaleY(2); }
            80% { transform: translateX(60%) scaleY(1); }
            90% { transform: translateX(80%) scaleY(3); }
            100% { transform: translateX(100%) scaleY(1); }
        }

        /* Bio-voltage Monitoring */
        .voltage-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .voltage-cell {
            aspect-ratio: 1;
            background: rgba(0, 255, 170, 0.1);
            border: 1px solid #00ffaa;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .voltage-cell:hover {
            background: rgba(0, 255, 170, 0.2);
            box-shadow: 0 0 15px rgba(0, 255, 170, 0.5);
        }

        .voltage-cell::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.3), transparent);
            transform: translateX(-100%);
            animation: voltage-sweep 3s linear infinite;
        }

        @keyframes voltage-sweep {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Ecosystem Health Indicator */
        .ecosystem-status {
            grid-column: span 2;
            display: flex;
            align-items: center;
            gap: 2rem;
            padding: 1.5rem;
            background: rgba(0, 255, 170, 0.05);
            border-radius: 15px;
            border: 2px solid #00ffaa;
        }

        .status-orb {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle, #00ffaa, #008866);
            box-shadow: 
                0 0 30px #00ffaa,
                inset 0 0 20px rgba(255, 255, 255, 0.2);
            animation: orb-rotation 10s linear infinite;
            position: relative;
        }

        .status-orb::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 20%;
            width: 60%;
            height: 60%;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.6), transparent);
        }

        @keyframes orb-rotation {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .status-info {
            flex: 1;
        }

        .status-title {
            font-size: 1.5rem;
            color: #ffd700;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 10px #ffd700;
        }

        .status-description {
            color: #00ffaa;
            line-height: 1.6;
        }

        /* Interactive Controls */
        .control-panel {
            grid-column: span 2;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
            padding: 1rem;
            background: rgba(138, 43, 226, 0.1);
            border-radius: 10px;
            border: 1px solid #8a2be2;
        }

        .bio-button {
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #00ffaa, #008866);
            border: none;
            border-radius: 25px;
            color: #0a0e1a;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 15px rgba(0, 255, 170, 0.3);
            position: relative;
            overflow: hidden;
        }

        .bio-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 25px rgba(0, 255, 170, 0.5);
        }

        .bio-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: all 0.5s;
        }

        .bio-button:hover::before {
            left: 100%;
        }

        .bio-slider {
            flex: 1;
            height: 8px;
            background: rgba(0, 255, 170, 0.2);
            border-radius: 4px;
            outline: none;
            cursor: pointer;
            position: relative;
        }

        .bio-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #00ffaa;
            box-shadow: 0 0 15px #00ffaa;
            cursor: pointer;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .ecosystem-grid {
                grid-template-columns: 1fr;
            }
            
            .data-viz,
            .ecosystem-status,
            .control-panel {
                grid-column: span 1;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .voltage-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* Accessibility */
        .visually-hidden {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Loading State */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 170, 0.3), transparent);
            animation: loading-sweep 2s linear infinite;
        }

        @keyframes loading-sweep {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body>
    <main>
        <h1>Living Data Ecosystem - Bioelectric Theme</h1>
        <div class="hybrid-component">
            <div class="ecosystem-grid">
                <!-- Living Data Visualization -->
                <div class="data-panel data-viz">
                    <div class="panel-title">
                        <div class="bio-indicator"></div>
                        Neural Data Network
                    </div>
                    <div class="neural-network" id="neuralNetwork">
                        <!-- Neural nodes and connections generated by JavaScript -->
                    </div>
                </div>

                <!-- Bio-responsive Charts -->
                <div class="data-panel">
                    <div class="panel-title">
                        <div class="bio-indicator"></div>
                        Bio-responsive Analytics
                    </div>
                    <div class="chart-container">
                        <div class="bio-chart" id="bioChart">
                            <!-- Chart lines generated by JavaScript -->
                        </div>
                    </div>
                    <div class="metric-display">
                        <div>
                            <div class="metric-value" id="responseRate">94.7%</div>
                            <div class="metric-label">Response Rate</div>
                        </div>
                        <div>
                            <div class="metric-value" id="adaptability">8.3</div>
                            <div class="metric-label">Adaptability</div>
                        </div>
                    </div>
                </div>

                <!-- Living Metrics -->
                <div class="data-panel">
                    <div class="panel-title">
                        <div class="bio-indicator"></div>
                        Living Metrics Stream
                    </div>
                    <div class="metric-display">
                        <div>
                            <div class="metric-value" id="dataFlow">2.4k</div>
                            <div class="metric-label">Data Flow/sec</div>
                        </div>
                        <div>
                            <div class="metric-value" id="energyLevel">87%</div>
                            <div class="metric-label">Energy Level</div>
                        </div>
                    </div>
                    <div class="pulse-monitor">
                        <div class="pulse-line"></div>
                    </div>
                </div>

                <!-- Bio-voltage Monitoring -->
                <div class="data-panel">
                    <div class="panel-title">
                        <div class="bio-indicator"></div>
                        Bio-voltage Grid
                    </div>
                    <div class="voltage-grid" id="voltageGrid">
                        <!-- Voltage cells generated by JavaScript -->
                    </div>
                </div>

                <!-- Ecosystem Health Status -->
                <div class="data-panel ecosystem-status">
                    <div class="status-orb"></div>
                    <div class="status-info">
                        <div class="status-title">Ecosystem Status: Optimal</div>
                        <div class="status-description">
                            All bioelectric systems operating within normal parameters. 
                            Data flows are stable, electrical currents are balanced, 
                            and the living ecosystem is thriving with 98.7% efficiency.
                        </div>
                    </div>
                </div>

                <!-- Interactive Controls -->
                <div class="control-panel">
                    <button class="bio-button" onclick="toggleDataFlow()">
                        Toggle Data Flow
                    </button>
                    <button class="bio-button" onclick="resetEcosystem()">
                        Reset Ecosystem
                    </button>
                    <button class="bio-button" onclick="enhanceBioelectrics()">
                        Enhance Bio-electrics
                    </button>
                    <label for="voltageSlider" class="visually-hidden">Voltage Level Control</label>
                    <input type="range" class="bio-slider" id="voltageSlider" 
                           min="0" max="100" value="75" 
                           onchange="adjustVoltage(this.value)">
                </div>
            </div>
        </div>
    </main>

    <script>
        class BioelectricEcosystem {
            constructor() {
                this.isActive = true;
                this.dataFlowRate = 2400;
                this.voltageLevel = 75;
                this.energyLevel = 87;
                this.responseRate = 94.7;
                this.adaptability = 8.3;
                
                this.init();
                this.startLifeCycle();
            }

            init() {
                this.generateNeuralNetwork();
                this.generateVoltageGrid();
                this.generateBioChart();
                this.updateMetrics();
            }

            generateNeuralNetwork() {
                const network = document.getElementById('neuralNetwork');
                network.innerHTML = '';
                
                // Generate neural nodes
                const nodeCount = 12;
                const nodes = [];
                
                for (let i = 0; i < nodeCount; i++) {
                    const node = document.createElement('div');
                    node.className = 'node';
                    
                    const x = Math.random() * 90 + 5; // 5-95%
                    const y = Math.random() * 80 + 10; // 10-90%
                    
                    node.style.left = x + '%';
                    node.style.top = y + '%';
                    node.style.animationDelay = Math.random() * 2 + 's';
                    
                    network.appendChild(node);
                    nodes.push({ element: node, x, y });
                }
                
                // Generate connections
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        if (Math.random() < 0.3) { // 30% chance of connection
                            this.createConnection(network, nodes[i], nodes[j]);
                        }
                    }
                }
            }

            createConnection(parent, node1, node2) {
                const connection = document.createElement('div');
                connection.className = 'connection';
                
                const dx = node2.x - node1.x;
                const dy = node2.y - node1.y;
                const length = Math.sqrt(dx * dx + dy * dy);
                const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                
                connection.style.left = node1.x + '%';
                connection.style.top = node1.y + '%';
                connection.style.width = length + '%';
                connection.style.transform = `rotate(${angle}deg)`;
                connection.style.animationDelay = Math.random() * 3 + 's';
                
                parent.appendChild(connection);
            }

            generateVoltageGrid() {
                const grid = document.getElementById('voltageGrid');
                grid.innerHTML = '';
                
                for (let i = 0; i < 16; i++) {
                    const cell = document.createElement('div');
                    cell.className = 'voltage-cell';
                    
                    const voltage = (Math.random() * 50 + 25).toFixed(1);
                    cell.textContent = voltage + 'V';
                    cell.style.animationDelay = Math.random() * 3 + 's';
                    
                    // Add hover effect for bio-feedback
                    cell.addEventListener('mouseenter', () => {
                        cell.style.background = 'rgba(255, 215, 0, 0.3)';
                        this.playBioFeedback();
                    });
                    
                    cell.addEventListener('mouseleave', () => {
                        cell.style.background = 'rgba(0, 255, 170, 0.1)';
                    });
                    
                    grid.appendChild(cell);
                }
            }

            generateBioChart() {
                const chart = document.getElementById('bioChart');
                chart.innerHTML = '';
                
                const lineCount = 50;
                for (let i = 0; i < lineCount; i++) {
                    const line = document.createElement('div');
                    line.className = 'chart-line';
                    line.style.left = (i * 2) + '%';
                    line.style.animationDelay = (i * 0.1) + 's';
                    line.style.animationDuration = (Math.random() * 2 + 3) + 's';
                    chart.appendChild(line);
                }
            }

            updateMetrics() {
                // Simulate living data changes
                const responseRateEl = document.getElementById('responseRate');
                const adaptabilityEl = document.getElementById('adaptability');
                const dataFlowEl = document.getElementById('dataFlow');
                const energyLevelEl = document.getElementById('energyLevel');
                
                setInterval(() => {
                    if (this.isActive) {
                        // Small random fluctuations
                        this.responseRate += (Math.random() - 0.5) * 0.5;
                        this.adaptability += (Math.random() - 0.5) * 0.2;
                        this.dataFlowRate += Math.random() * 200 - 100;
                        this.energyLevel += (Math.random() - 0.5) * 2;
                        
                        // Keep within bounds
                        this.responseRate = Math.max(80, Math.min(100, this.responseRate));
                        this.adaptability = Math.max(1, Math.min(10, this.adaptability));
                        this.dataFlowRate = Math.max(1000, Math.min(5000, this.dataFlowRate));
                        this.energyLevel = Math.max(60, Math.min(100, this.energyLevel));
                        
                        // Update display
                        responseRateEl.textContent = this.responseRate.toFixed(1) + '%';
                        adaptabilityEl.textContent = this.adaptability.toFixed(1);
                        dataFlowEl.textContent = (this.dataFlowRate / 1000).toFixed(1) + 'k';
                        energyLevelEl.textContent = Math.round(this.energyLevel) + '%';
                    }
                }, 2000);
            }

            startLifeCycle() {
                // Regenerate neural network periodically
                setInterval(() => {
                    if (this.isActive) {
                        this.generateNeuralNetwork();
                    }
                }, 15000);
                
                // Update voltage grid
                setInterval(() => {
                    if (this.isActive) {
                        this.updateVoltageGrid();
                    }
                }, 5000);
            }

            updateVoltageGrid() {
                const cells = document.querySelectorAll('.voltage-cell');
                cells.forEach(cell => {
                    const voltage = (Math.random() * 50 + 25).toFixed(1);
                    cell.textContent = voltage + 'V';
                    
                    // Add electrical discharge effect
                    cell.style.boxShadow = '0 0 20px rgba(0, 255, 170, 0.8)';
                    setTimeout(() => {
                        cell.style.boxShadow = '';
                    }, 300);
                });
            }

            playBioFeedback() {
                // Visual feedback for user interactions
                document.body.style.background = '#0a0e1a';
                setTimeout(() => {
                    document.body.style.background = '';
                }, 100);
            }

            toggleDataFlow() {
                this.isActive = !this.isActive;
                const component = document.querySelector('.hybrid-component');
                
                if (!this.isActive) {
                    component.classList.add('loading');
                } else {
                    component.classList.remove('loading');
                }
            }

            resetEcosystem() {
                this.responseRate = 94.7;
                this.adaptability = 8.3;
                this.dataFlowRate = 2400;
                this.energyLevel = 87;
                this.voltageLevel = 75;
                
                document.getElementById('voltageSlider').value = 75;
                this.init();
                this.playBioFeedback();
            }

            enhanceBioelectrics() {
                // Temporary enhancement effect
                const nodes = document.querySelectorAll('.node');
                const connections = document.querySelectorAll('.connection');
                
                nodes.forEach(node => {
                    node.style.boxShadow = '0 0 30px #00ffaa, 0 0 50px #00ffaa';
                });
                
                connections.forEach(connection => {
                    connection.style.boxShadow = '0 0 20px #8a2be2';
                });
                
                setTimeout(() => {
                    nodes.forEach(node => {
                        node.style.boxShadow = '';
                    });
                    connections.forEach(connection => {
                        connection.style.boxShadow = '';
                    });
                }, 3000);
                
                this.playBioFeedback();
            }

            adjustVoltage(value) {
                this.voltageLevel = parseInt(value);
                
                // Adjust visual intensity based on voltage
                const intensity = value / 100;
                const component = document.querySelector('.hybrid-component');
                component.style.boxShadow = `
                    0 0 ${30 + intensity * 20}px rgba(0, 255, 170, ${0.3 + intensity * 0.2}),
                    inset 0 0 ${30 + intensity * 20}px rgba(0, 255, 170, ${0.1 + intensity * 0.1})
                `;
            }
        }

        // Global functions for button interactions
        let ecosystem;

        function toggleDataFlow() {
            ecosystem.toggleDataFlow();
        }

        function resetEcosystem() {
            ecosystem.resetEcosystem();
        }

        function enhanceBioelectrics() {
            ecosystem.enhanceBioelectrics();
        }

        function adjustVoltage(value) {
            ecosystem.adjustVoltage(value);
        }

        // Initialize the bioelectric ecosystem when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            ecosystem = new BioelectricEcosystem();
        });

        // Add keyboard accessibility
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'Enter':
                case ' ':
                    if (e.target.classList.contains('bio-button')) {
                        e.target.click();
                    }
                    break;
                case 'r':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        resetEcosystem();
                    }
                    break;
                case 't':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        toggleDataFlow();
                    }
                    break;
            }
        });

        // Responsive behavior
        window.addEventListener('resize', () => {
            if (ecosystem) {
                setTimeout(() => {
                    ecosystem.generateNeuralNetwork();
                }, 300);
            }
        });
    </script>
</body>
</html>