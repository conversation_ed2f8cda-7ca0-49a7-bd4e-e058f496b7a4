<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Art Nouveau Social Feed - Iteration 23</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700&family=Poiret+One&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poiret One', cursive;
            background: linear-gradient(135deg, #f4e8d0 0%, #e8d4b0 50%, #d4c4a0 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* Art Nouveau Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 80%, transparent 30%, rgba(139, 119, 101, 0.1) 30.5%),
                radial-gradient(circle at 80% 20%, transparent 30%, rgba(139, 119, 101, 0.1) 30.5%),
                radial-gradient(circle at 40% 40%, transparent 30%, rgba(139, 119, 101, 0.1) 30.5%);
            pointer-events: none;
        }
        
        /* Main Container */
        .social-garden {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
        }
        
        /* Header with Art Nouveau Typography */
        .header-ornament {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }
        
        .header-ornament h1 {
            font-family: 'Cinzel Decorative', cursive;
            font-size: 3em;
            color: #5a4a3a;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        /* Decorative Flourish */
        .flourish {
            width: 200px;
            height: 60px;
            margin: 0 auto;
            position: relative;
        }
        
        .flourish::before,
        .flourish::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            border: 2px solid #8b7765;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .flourish::before {
            left: 0;
            border-right: none;
            border-top: none;
            transform: translateY(-50%) rotate(45deg);
        }
        
        .flourish::after {
            right: 0;
            border-left: none;
            border-top: none;
            transform: translateY(-50%) rotate(-45deg);
        }
        
        /* Create Post Section */
        .create-post {
            background: rgba(255, 253, 248, 0.9);
            border: 2px solid #b8a590;
            border-radius: 30px 10px 30px 10px;
            padding: 30px;
            margin-bottom: 40px;
            position: relative;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }
        
        .create-post::before,
        .create-post::after {
            content: '❦';
            position: absolute;
            font-size: 2em;
            color: #b8a590;
        }
        
        .create-post::before {
            top: 10px;
            left: 20px;
        }
        
        .create-post::after {
            bottom: 10px;
            right: 20px;
            transform: rotate(180deg);
        }
        
        .post-textarea {
            width: 100%;
            min-height: 100px;
            background: transparent;
            border: 1px solid #d4c4a0;
            border-radius: 15px;
            padding: 15px;
            font-family: 'Poiret One', cursive;
            font-size: 1.1em;
            resize: none;
            transition: all 0.3s ease;
        }
        
        .post-textarea:focus {
            outline: none;
            border-color: #8b7765;
            box-shadow: 0 0 10px rgba(139, 119, 101, 0.2);
        }
        
        .post-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
        
        .post-button {
            background: linear-gradient(135deg, #8b7765 0%, #a08970 100%);
            color: #fff;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-family: 'Cinzel Decorative', cursive;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        
        .post-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.3);
        }
        
        /* Social Post Cards */
        .post-card {
            background: rgba(255, 253, 248, 0.95);
            border: 2px solid #b8a590;
            border-radius: 20px 5px 20px 5px;
            margin-bottom: 30px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .post-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        /* Ornate Corner Decorations */
        .corner-ornament {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #d4c4a0;
        }
        
        .corner-ornament.top-left {
            top: -2px;
            left: -2px;
            border-right: none;
            border-bottom: none;
            border-radius: 20px 0 0 0;
        }
        
        .corner-ornament.bottom-right {
            bottom: -2px;
            right: -2px;
            border-left: none;
            border-top: none;
            border-radius: 0 0 20px 0;
        }
        
        /* User Profile Section */
        .post-header {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e8d4b0;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #b8a590;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #d4c4a0 0%, #b8a590 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            color: #fff;
            font-weight: bold;
        }
        
        .user-info {
            margin-left: 15px;
            flex: 1;
        }
        
        .user-name {
            font-family: 'Cinzel Decorative', cursive;
            font-size: 1.2em;
            color: #5a4a3a;
            margin-bottom: 5px;
        }
        
        .post-time {
            color: #8b7765;
            font-size: 0.9em;
        }
        
        /* Post Content */
        .post-content {
            padding: 20px;
            font-size: 1.1em;
            line-height: 1.6;
            color: #5a4a3a;
        }
        
        /* Reaction Bar */
        .reaction-bar {
            display: flex;
            justify-content: space-around;
            padding: 15px;
            border-top: 1px solid #e8d4b0;
            background: rgba(232, 212, 176, 0.3);
        }
        
        .reaction-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px 15px;
            border-radius: 20px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #8b7765;
            font-family: 'Poiret One', cursive;
        }
        
        .reaction-btn:hover {
            background: rgba(139, 119, 101, 0.1);
            transform: scale(1.05);
        }
        
        .reaction-btn.active {
            color: #5a4a3a;
            background: rgba(139, 119, 101, 0.2);
        }
        
        /* Comments Section */
        .comments-section {
            padding: 20px;
            background: rgba(232, 212, 176, 0.2);
            border-top: 1px solid #e8d4b0;
        }
        
        .comment {
            display: flex;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(255, 253, 248, 0.7);
            border-radius: 15px;
            position: relative;
        }
        
        .comment-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #d4c4a0;
            background: linear-gradient(135deg, #e8d4b0 0%, #d4c4a0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 1.2em;
            flex-shrink: 0;
        }
        
        .comment-content {
            margin-left: 12px;
            flex: 1;
        }
        
        .comment-author {
            font-family: 'Cinzel Decorative', cursive;
            color: #5a4a3a;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .comment-text {
            color: #6a5a4a;
            line-height: 1.4;
        }
        
        /* Comment Input */
        .comment-input-wrapper {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .comment-input {
            flex: 1;
            background: rgba(255, 253, 248, 0.8);
            border: 1px solid #d4c4a0;
            border-radius: 20px;
            padding: 10px 15px;
            font-family: 'Poiret One', cursive;
            transition: all 0.3s ease;
        }
        
        .comment-input:focus {
            outline: none;
            border-color: #8b7765;
            background: rgba(255, 253, 248, 1);
        }
        
        /* Share Menu */
        .share-menu {
            position: absolute;
            bottom: 60px;
            right: 20px;
            background: rgba(255, 253, 248, 0.98);
            border: 2px solid #b8a590;
            border-radius: 15px;
            padding: 10px;
            display: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .share-menu.active {
            display: block;
            animation: unfold 0.3s ease forwards;
        }
        
        @keyframes unfold {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        .share-option {
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 10px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .share-option:hover {
            background: rgba(139, 119, 101, 0.1);
        }
        
        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #8b7765 0%, #a08970 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        
        .fab:hover {
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
        }
        
        /* Animations */
        .post-card {
            animation: fadeInUp 0.5s ease forwards;
            opacity: 0;
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .post-card:nth-child(1) { animation-delay: 0.1s; }
        .post-card:nth-child(2) { animation-delay: 0.2s; }
        .post-card:nth-child(3) { animation-delay: 0.3s; }
        
        /* Vine-like Decorative Elements */
        .vine-decoration {
            position: absolute;
            width: 100px;
            height: 200px;
            opacity: 0.3;
            pointer-events: none;
        }
        
        .vine-left {
            left: -50px;
            top: 100px;
            transform: rotate(-15deg);
        }
        
        .vine-right {
            right: -50px;
            top: 300px;
            transform: rotate(15deg) scaleX(-1);
        }
        
        .vine-decoration svg {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="social-garden">
        <!-- Header Section -->
        <div class="header-ornament">
            <h1>Le Jardin Social</h1>
            <div class="flourish"></div>
        </div>
        
        <!-- Create Post Section -->
        <div class="create-post">
            <textarea class="post-textarea" placeholder="Share your thoughts in the garden..."></textarea>
            <div class="post-actions">
                <div class="media-options">
                    <button class="reaction-btn">
                        <span>📷</span>
                        <span>Photo</span>
                    </button>
                    <button class="reaction-btn">
                        <span>🎨</span>
                        <span>Art</span>
                    </button>
                </div>
                <button class="post-button" onclick="createPost()">Bloom</button>
            </div>
        </div>
        
        <!-- Social Feed -->
        <div id="socialFeed">
            <!-- Post 1 -->
            <div class="post-card">
                <div class="corner-ornament top-left"></div>
                <div class="corner-ornament bottom-right"></div>
                
                <div class="post-header">
                    <div class="user-avatar">AM</div>
                    <div class="user-info">
                        <div class="user-name">Alphonse Mucha</div>
                        <div class="post-time">2 hours ago</div>
                    </div>
                </div>
                
                <div class="post-content">
                    In every natural form, there exists a harmony that speaks to the soul. Today's sunrise reminded me why I paint - to capture these fleeting moments of beauty that nature gifts us daily. 🌅
                </div>
                
                <div class="reaction-bar">
                    <button class="reaction-btn active" onclick="toggleReaction(this)">
                        <span>🌺</span>
                        <span>42</span>
                    </button>
                    <button class="reaction-btn" onclick="toggleComments(this)">
                        <span>💬</span>
                        <span>8</span>
                    </button>
                    <button class="reaction-btn" onclick="toggleShare(this)">
                        <span>🦋</span>
                        <span>Share</span>
                    </button>
                </div>
                
                <div class="comments-section" style="display: none;">
                    <div class="comment">
                        <div class="comment-avatar">GT</div>
                        <div class="comment-content">
                            <div class="comment-author">Gustav Klimt</div>
                            <div class="comment-text">Beautiful sentiment! Nature is indeed our greatest teacher.</div>
                        </div>
                    </div>
                    
                    <div class="comment-input-wrapper">
                        <input type="text" class="comment-input" placeholder="Add your thoughts...">
                        <button class="reaction-btn">
                            <span>🌿</span>
                        </button>
                    </div>
                </div>
                
                <div class="share-menu">
                    <div class="share-option">
                        <span>📧</span>
                        <span>Email</span>
                    </div>
                    <div class="share-option">
                        <span>🔗</span>
                        <span>Copy Link</span>
                    </div>
                    <div class="share-option">
                        <span>📌</span>
                        <span>Save</span>
                    </div>
                </div>
            </div>
            
            <!-- Post 2 -->
            <div class="post-card">
                <div class="corner-ornament top-left"></div>
                <div class="corner-ornament bottom-right"></div>
                
                <div class="post-header">
                    <div class="user-avatar">EG</div>
                    <div class="user-info">
                        <div class="user-name">Émile Gallé</div>
                        <div class="post-time">5 hours ago</div>
                    </div>
                </div>
                
                <div class="post-content">
                    Working on a new glass vase inspired by iris flowers. The way light dances through colored glass mirrors how sunlight filters through petals. Art is nature seen through a temperament. 🌷
                </div>
                
                <div class="reaction-bar">
                    <button class="reaction-btn" onclick="toggleReaction(this)">
                        <span>🌺</span>
                        <span>28</span>
                    </button>
                    <button class="reaction-btn" onclick="toggleComments(this)">
                        <span>💬</span>
                        <span>5</span>
                    </button>
                    <button class="reaction-btn" onclick="toggleShare(this)">
                        <span>🦋</span>
                        <span>Share</span>
                    </button>
                </div>
                
                <div class="comments-section" style="display: none;">
                    <div class="comment-input-wrapper">
                        <input type="text" class="comment-input" placeholder="Add your thoughts...">
                        <button class="reaction-btn">
                            <span>🌿</span>
                        </button>
                    </div>
                </div>
                
                <div class="share-menu">
                    <div class="share-option">
                        <span>📧</span>
                        <span>Email</span>
                    </div>
                    <div class="share-option">
                        <span>🔗</span>
                        <span>Copy Link</span>
                    </div>
                    <div class="share-option">
                        <span>📌</span>
                        <span>Save</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Action Button -->
        <div class="fab" onclick="scrollToTop()">
            <span>✿</span>
        </div>
        
        <!-- Decorative Vines -->
        <div class="vine-decoration vine-left">
            <svg viewBox="0 0 100 200">
                <path d="M50,0 Q30,50 40,100 T50,200" stroke="#b8a590" stroke-width="2" fill="none"/>
                <circle cx="35" cy="60" r="8" fill="#d4c4a0" opacity="0.6"/>
                <circle cx="45" cy="120" r="6" fill="#d4c4a0" opacity="0.6"/>
                <circle cx="40" cy="160" r="7" fill="#d4c4a0" opacity="0.6"/>
            </svg>
        </div>
        
        <div class="vine-decoration vine-right">
            <svg viewBox="0 0 100 200">
                <path d="M50,0 Q30,50 40,100 T50,200" stroke="#b8a590" stroke-width="2" fill="none"/>
                <circle cx="35" cy="60" r="8" fill="#d4c4a0" opacity="0.6"/>
                <circle cx="45" cy="120" r="6" fill="#d4c4a0" opacity="0.6"/>
                <circle cx="40" cy="160" r="7" fill="#d4c4a0" opacity="0.6"/>
            </svg>
        </div>
    </div>
    
    <script>
        // Toggle reaction state
        function toggleReaction(btn) {
            btn.classList.toggle('active');
            const count = btn.querySelector('span:last-child');
            const currentCount = parseInt(count.textContent);
            
            if (btn.classList.contains('active')) {
                count.textContent = currentCount + 1;
                // Animate the emoji
                btn.querySelector('span:first-child').style.animation = 'pulse 0.5s ease';
            } else {
                count.textContent = currentCount - 1;
            }
            
            setTimeout(() => {
                btn.querySelector('span:first-child').style.animation = '';
            }, 500);
        }
        
        // Toggle comments section
        function toggleComments(btn) {
            const postCard = btn.closest('.post-card');
            const commentsSection = postCard.querySelector('.comments-section');
            
            if (commentsSection.style.display === 'none') {
                commentsSection.style.display = 'block';
                commentsSection.style.animation = 'fadeInUp 0.3s ease';
            } else {
                commentsSection.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    commentsSection.style.display = 'none';
                }, 300);
            }
        }
        
        // Toggle share menu
        function toggleShare(btn) {
            const postCard = btn.closest('.post-card');
            const shareMenu = postCard.querySelector('.share-menu');
            
            // Close all other share menus
            document.querySelectorAll('.share-menu').forEach(menu => {
                if (menu !== shareMenu) {
                    menu.classList.remove('active');
                }
            });
            
            shareMenu.classList.toggle('active');
        }
        
        // Create new post
        function createPost() {
            const textarea = document.querySelector('.post-textarea');
            const content = textarea.value.trim();
            
            if (content) {
                const feed = document.getElementById('socialFeed');
                const newPost = createPostElement(content);
                feed.insertBefore(newPost, feed.firstChild);
                
                // Clear textarea
                textarea.value = '';
                
                // Animate new post
                setTimeout(() => {
                    newPost.style.animation = 'fadeInUp 0.5s ease forwards';
                }, 100);
            }
        }
        
        // Create post element
        function createPostElement(content) {
            const post = document.createElement('div');
            post.className = 'post-card';
            post.style.opacity = '0';
            
            const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            post.innerHTML = `
                <div class="corner-ornament top-left"></div>
                <div class="corner-ornament bottom-right"></div>
                
                <div class="post-header">
                    <div class="user-avatar">ME</div>
                    <div class="user-info">
                        <div class="user-name">My Thoughts</div>
                        <div class="post-time">Just now</div>
                    </div>
                </div>
                
                <div class="post-content">${content}</div>
                
                <div class="reaction-bar">
                    <button class="reaction-btn" onclick="toggleReaction(this)">
                        <span>🌺</span>
                        <span>0</span>
                    </button>
                    <button class="reaction-btn" onclick="toggleComments(this)">
                        <span>💬</span>
                        <span>0</span>
                    </button>
                    <button class="reaction-btn" onclick="toggleShare(this)">
                        <span>🦋</span>
                        <span>Share</span>
                    </button>
                </div>
                
                <div class="comments-section" style="display: none;">
                    <div class="comment-input-wrapper">
                        <input type="text" class="comment-input" placeholder="Add your thoughts...">
                        <button class="reaction-btn">
                            <span>🌿</span>
                        </button>
                    </div>
                </div>
                
                <div class="share-menu">
                    <div class="share-option">
                        <span>📧</span>
                        <span>Email</span>
                    </div>
                    <div class="share-option">
                        <span>🔗</span>
                        <span>Copy Link</span>
                    </div>
                    <div class="share-option">
                        <span>📌</span>
                        <span>Save</span>
                    </div>
                </div>
            `;
            
            return post;
        }
        
        // Scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Add comment functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.reaction-btn') && e.target.closest('.comment-input-wrapper')) {
                const wrapper = e.target.closest('.comment-input-wrapper');
                const input = wrapper.querySelector('.comment-input');
                const comment = input.value.trim();
                
                if (comment) {
                    const commentsSection = wrapper.closest('.comments-section');
                    const newComment = document.createElement('div');
                    newComment.className = 'comment';
                    newComment.innerHTML = `
                        <div class="comment-avatar">ME</div>
                        <div class="comment-content">
                            <div class="comment-author">You</div>
                            <div class="comment-text">${comment}</div>
                        </div>
                    `;
                    
                    commentsSection.insertBefore(newComment, wrapper);
                    input.value = '';
                    
                    // Update comment count
                    const postCard = commentsSection.closest('.post-card');
                    const commentBtn = postCard.querySelector('.reaction-bar button:nth-child(2) span:last-child');
                    commentBtn.textContent = parseInt(commentBtn.textContent) + 1;
                }
            }
        });
        
        // Close share menus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.reaction-btn') && !e.target.closest('.share-menu')) {
                document.querySelectorAll('.share-menu').forEach(menu => {
                    menu.classList.remove('active');
                });
            }
        });
        
        // Add pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
            
            @keyframes fadeOut {
                to {
                    opacity: 0;
                    transform: translateY(-10px);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>