<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temporal Flux Time-Aware Workspace</title>
    <style>
        /* Temporal Flux Theme - Time Distortion & Chronological Flow */
        :root {
            --past-sepia: #8B4513;
            --past-fade: rgba(139, 69, 19, 0.2);
            --present-vibrant: #00FFFF;
            --present-glow: rgba(0, 255, 255, 0.4);
            --future-ethereal: #E6E6FA;
            --future-shimmer: rgba(230, 230, 250, 0.3);
            --time-flow: linear-gradient(90deg, var(--past-sepia), var(--present-vibrant), var(--future-ethereal));
            --temporal-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            --chronometer-bg: #1a1a2e;
            --timeline-track: #16213e;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: var(--chronometer-bg);
            color: var(--present-vibrant);
            overflow-x: hidden;
            position: relative;
        }

        /* Temporal Flux Background Animation */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, var(--past-fade) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, var(--future-shimmer) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, var(--present-glow) 0%, transparent 30%);
            animation: temporalFlux 15s infinite alternate;
            z-index: -1;
        }

        @keyframes temporalFlux {
            0% { transform: translateX(-50px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateX(0) rotate(180deg); opacity: 0.6; }
            100% { transform: translateX(50px) rotate(360deg); opacity: 0.3; }
        }

        main {
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            background: var(--time-flow);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 30px;
            animation: chronoGlow 3s ease-in-out infinite alternate;
            text-shadow: var(--temporal-shadow);
        }

        @keyframes chronoGlow {
            0% { filter: brightness(1) blur(0px); }
            100% { filter: brightness(1.3) blur(1px); }
        }

        .hybrid-component {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
        }

        /* Temporal Navigation Hub */
        .temporal-nav {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, var(--chronometer-bg), var(--timeline-track));
            border: 2px solid var(--present-vibrant);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--temporal-shadow);
        }

        .temporal-nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, var(--present-glow), transparent);
            animation: timeWave 4s infinite;
        }

        @keyframes timeWave {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .time-zones {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
        }

        .zone-clock {
            text-align: center;
            padding: 10px;
            border-radius: 10px;
            background: rgba(0, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            min-width: 120px;
            position: relative;
        }

        .zone-clock::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--time-flow);
            animation: timePulse 2s ease-in-out infinite;
        }

        @keyframes timePulse {
            0%, 100% { transform: scaleX(0.3); opacity: 0.5; }
            50% { transform: scaleX(1); opacity: 1; }
        }

        .zone-name {
            font-size: 0.9rem;
            color: var(--future-ethereal);
            margin-bottom: 5px;
        }

        .zone-time {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--present-vibrant);
        }

        /* Task Management with Temporal Awareness */
        .temporal-tasks {
            background: linear-gradient(45deg, var(--past-fade), var(--present-glow));
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .task-timeline {
            position: relative;
            padding-left: 30px;
        }

        .task-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--time-flow);
            animation: timelineFlow 3s linear infinite;
        }

        @keyframes timelineFlow {
            0% { background-position: 0% 0%; }
            100% { background-position: 0% 100%; }
        }

        .task-item {
            position: relative;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(30, 30, 60, 0.7);
            border-radius: 10px;
            border-left: 4px solid var(--present-vibrant);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .task-item::before {
            content: '';
            position: absolute;
            left: -45px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--present-vibrant);
            box-shadow: 0 0 15px var(--present-vibrant);
            animation: taskPulse 2s ease-in-out infinite;
        }

        @keyframes taskPulse {
            0%, 100% { transform: translateY(-50%) scale(0.8); }
            50% { transform: translateY(-50%) scale(1.2); }
        }

        .task-item:hover {
            transform: translateX(10px);
            background: rgba(0, 255, 255, 0.1);
            box-shadow: var(--temporal-shadow);
        }

        .task-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--present-vibrant);
        }

        .task-deadline {
            font-size: 0.9rem;
            color: var(--future-ethereal);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .urgency-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            animation: urgencyBlink 1s ease-in-out infinite;
        }

        .high { background: #ff4444; }
        .medium { background: #ffaa44; }
        .low { background: #44ff44; }

        @keyframes urgencyBlink {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        /* Historical Timeline Integration */
        .historical-timeline {
            background: linear-gradient(135deg, var(--past-fade), var(--timeline-track));
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .timeline-scroll {
            height: 300px;
            overflow-y: auto;
            position: relative;
        }

        .timeline-scroll::-webkit-scrollbar {
            width: 8px;
        }

        .timeline-scroll::-webkit-scrollbar-track {
            background: var(--timeline-track);
            border-radius: 10px;
        }

        .timeline-scroll::-webkit-scrollbar-thumb {
            background: var(--time-flow);
            border-radius: 10px;
        }

        .history-event {
            position: relative;
            padding: 15px;
            margin-bottom: 15px;
            background: rgba(139, 69, 19, 0.2);
            border-radius: 10px;
            border-left: 4px solid var(--past-sepia);
            transform: translateX(-20px);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .history-event:hover {
            transform: translateX(0);
            opacity: 1;
            background: rgba(139, 69, 19, 0.4);
        }

        .event-date {
            font-size: 0.8rem;
            color: var(--past-sepia);
            margin-bottom: 5px;
        }

        .event-title {
            font-weight: bold;
            color: var(--future-ethereal);
            margin-bottom: 5px;
        }

        .event-description {
            font-size: 0.9rem;
            color: rgba(230, 230, 250, 0.8);
        }

        /* Future Prediction Interface */
        .prediction-panel {
            grid-column: 1 / -1;
            background: linear-gradient(45deg, var(--future-shimmer), var(--present-glow));
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .prediction-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .prediction-card {
            background: rgba(230, 230, 250, 0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid var(--future-ethereal);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .prediction-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--time-flow);
            animation: predictionScan 3s linear infinite;
        }

        @keyframes predictionScan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .prediction-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(230, 230, 250, 0.3);
        }

        .prediction-metric {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--future-ethereal);
            margin-bottom: 5px;
        }

        .prediction-label {
            font-size: 0.9rem;
            color: var(--present-vibrant);
            margin-bottom: 10px;
        }

        .confidence-bar {
            width: 100%;
            height: 6px;
            background: rgba(0, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: var(--time-flow);
            border-radius: 3px;
            animation: confidenceGrow 2s ease-out;
        }

        @keyframes confidenceGrow {
            0% { width: 0%; }
            100% { width: var(--confidence-width, 75%); }
        }

        /* Temporal Collaboration Hub */
        .collaboration-hub {
            grid-column: 1 / -1;
            background: linear-gradient(90deg, var(--chronometer-bg), var(--timeline-track), var(--chronometer-bg));
            border-radius: 15px;
            padding: 20px;
            position: relative;
            margin-top: 20px;
        }

        .collab-streams {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .stream-channel {
            min-width: 200px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            position: relative;
        }

        .stream-channel::after {
            content: '';
            position: absolute;
            top: 5px;
            right: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--present-vibrant);
            animation: streamActive 1s ease-in-out infinite;
        }

        @keyframes streamActive {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .stream-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--present-vibrant);
        }

        .stream-participants {
            font-size: 0.8rem;
            color: var(--future-ethereal);
            margin-bottom: 10px;
        }

        .temporal-message {
            background: rgba(30, 30, 60, 0.5);
            padding: 8px;
            border-radius: 5px;
            font-size: 0.9rem;
            border-left: 3px solid var(--present-vibrant);
            margin-bottom: 8px;
            animation: messageAppear 0.5s ease-out;
        }

        @keyframes messageAppear {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* Control Panel */
        .temporal-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(26, 26, 46, 0.9);
            border: 2px solid var(--present-vibrant);
            border-radius: 50px;
            padding: 15px 25px;
            display: flex;
            gap: 15px;
            backdrop-filter: blur(10px);
            box-shadow: var(--temporal-shadow);
        }

        .control-btn {
            background: transparent;
            border: 1px solid var(--present-vibrant);
            color: var(--present-vibrant);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 0.9rem;
        }

        .control-btn:hover {
            background: var(--present-vibrant);
            color: var(--chronometer-bg);
            box-shadow: 0 0 15px var(--present-vibrant);
            transform: scale(1.05);
        }

        .control-btn.active {
            background: var(--present-vibrant);
            color: var(--chronometer-bg);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .time-zones {
                flex-wrap: wrap;
                gap: 10px;
            }

            .prediction-grid {
                grid-template-columns: 1fr;
            }

            .collab-streams {
                flex-direction: column;
            }

            .temporal-controls {
                position: static;
                justify-content: center;
                margin-top: 20px;
                border-radius: 15px;
            }
        }

        /* Accessibility Enhancements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .task-item:focus,
        .prediction-card:focus,
        .control-btn:focus {
            outline: 2px solid var(--present-vibrant);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <main>
        <h1>Time-Aware Workspace - Temporal Flux Theme</h1>
        <div class="hybrid-component">
            <!-- Temporal Navigation Hub -->
            <div class="temporal-nav">
                <h2>Global Time Synchronization</h2>
                <div class="time-zones">
                    <div class="zone-clock" data-timezone="UTC">
                        <div class="zone-name">UTC</div>
                        <div class="zone-time" id="utc-time">--:--</div>
                    </div>
                    <div class="zone-clock" data-timezone="EST">
                        <div class="zone-name">New York</div>
                        <div class="zone-time" id="est-time">--:--</div>
                    </div>
                    <div class="zone-clock" data-timezone="PST">
                        <div class="zone-name">San Francisco</div>
                        <div class="zone-time" id="pst-time">--:--</div>
                    </div>
                    <div class="zone-clock" data-timezone="JST">
                        <div class="zone-name">Tokyo</div>
                        <div class="zone-time" id="jst-time">--:--</div>
                    </div>
                    <div class="zone-clock" data-timezone="CET">
                        <div class="zone-name">Berlin</div>
                        <div class="zone-time" id="cet-time">--:--</div>
                    </div>
                </div>
            </div>

            <!-- Temporal Task Management -->
            <div class="temporal-tasks">
                <h3>Time-Aware Task Pipeline</h3>
                <div class="task-timeline">
                    <div class="task-item" tabindex="0" data-priority="high">
                        <div class="task-title">Temporal Data Analysis</div>
                        <div class="task-deadline">
                            <span class="urgency-indicator high"></span>
                            Due in 2 hours 45 minutes
                        </div>
                    </div>
                    <div class="task-item" tabindex="0" data-priority="medium">
                        <div class="task-title">Cross-Timeline Synchronization</div>
                        <div class="task-deadline">
                            <span class="urgency-indicator medium"></span>
                            Due tomorrow at 09:00 EST
                        </div>
                    </div>
                    <div class="task-item" tabindex="0" data-priority="low">
                        <div class="task-title">Historical Pattern Review</div>
                        <div class="task-deadline">
                            <span class="urgency-indicator low"></span>
                            Due next week
                        </div>
                    </div>
                    <div class="task-item" tabindex="0" data-priority="high">
                        <div class="task-title">Future Scenario Planning</div>
                        <div class="task-deadline">
                            <span class="urgency-indicator high"></span>
                            Due in 4 hours 12 minutes
                        </div>
                    </div>
                </div>
            </div>

            <!-- Historical Timeline Integration -->
            <div class="historical-timeline">
                <h3>Chronological Event Stream</h3>
                <div class="timeline-scroll">
                    <div class="history-event">
                        <div class="event-date">2024-12-15 14:30</div>
                        <div class="event-title">System Architecture Update</div>
                        <div class="event-description">Implemented temporal data structures for enhanced chronological processing.</div>
                    </div>
                    <div class="history-event">
                        <div class="event-date">2024-12-14 09:15</div>
                        <div class="event-title">Time Zone Migration</div>
                        <div class="event-description">Successfully migrated all user timelines to new temporal coordinate system.</div>
                    </div>
                    <div class="history-event">
                        <div class="event-date">2024-12-13 16:45</div>
                        <div class="event-title">Predictive Model Calibration</div>
                        <div class="event-description">Fine-tuned temporal prediction algorithms for improved accuracy.</div>
                    </div>
                    <div class="history-event">
                        <div class="event-date">2024-12-12 11:20</div>
                        <div class="event-title">Collaboration Protocol Update</div>
                        <div class="event-description">Enhanced time-shifted communication channels for global team coordination.</div>
                    </div>
                    <div class="history-event">
                        <div class="event-date">2024-12-11 08:00</div>
                        <div class="event-title">Temporal Workspace Launch</div>
                        <div class="event-description">Initial deployment of time-aware workspace environment.</div>
                    </div>
                </div>
            </div>

            <!-- Future Prediction Interface -->
            <div class="prediction-panel">
                <h3>Temporal Forecasting Matrix</h3>
                <div class="prediction-grid">
                    <div class="prediction-card" tabindex="0">
                        <div class="prediction-metric">94.7%</div>
                        <div class="prediction-label">Task Completion Probability</div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="--confidence-width: 94.7%;"></div>
                        </div>
                    </div>
                    <div class="prediction-card" tabindex="0">
                        <div class="prediction-metric">+23%</div>
                        <div class="prediction-label">Productivity Trend (7 days)</div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="--confidence-width: 78%;"></div>
                        </div>
                    </div>
                    <div class="prediction-card" tabindex="0">
                        <div class="prediction-metric">12.3h</div>
                        <div class="prediction-label">Optimal Work Window</div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="--confidence-width: 89%;"></div>
                        </div>
                    </div>
                    <div class="prediction-card" tabindex="0">
                        <div class="prediction-metric">85.2%</div>
                        <div class="prediction-label">Timeline Synchronization Rate</div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="--confidence-width: 85.2%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Temporal Collaboration Hub -->
            <div class="collaboration-hub">
                <h3>Time-Shifted Communication Streams</h3>
                <div class="collab-streams">
                    <div class="stream-channel">
                        <div class="stream-title">Project Alpha</div>
                        <div class="stream-participants">4 active, 2 time-shifted</div>
                        <div class="temporal-message">
                            <strong>Sarah (EST):</strong> Updated the temporal alignment protocols
                        </div>
                        <div class="temporal-message">
                            <strong>Ken (JST):</strong> Reviewing changes from future perspective
                        </div>
                    </div>
                    <div class="stream-channel">
                        <div class="stream-title">Data Analytics</div>
                        <div class="stream-participants">3 active, 1 time-shifted</div>
                        <div class="temporal-message">
                            <strong>Maria (CET):</strong> Historical patterns show interesting correlation
                        </div>
                        <div class="temporal-message">
                            <strong>AI Assistant:</strong> Predictive models suggest 89% accuracy
                        </div>
                    </div>
                    <div class="stream-channel">
                        <div class="stream-title">Future Planning</div>
                        <div class="stream-participants">2 active, 3 time-shifted</div>
                        <div class="temporal-message">
                            <strong>Alex (PST):</strong> Temporal synchronization complete
                        </div>
                        <div class="temporal-message">
                            <strong>System:</strong> Next checkpoint in 2h 15m
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Temporal Control Panel -->
        <div class="temporal-controls">
            <button class="control-btn active" data-action="present">Present</button>
            <button class="control-btn" data-action="past">Past View</button>
            <button class="control-btn" data-action="future">Future View</button>
            <button class="control-btn" data-action="flow">Time Flow</button>
        </div>
    </main>

    <script>
        // Time-Aware Workspace - Temporal Flux Implementation
        class TemporalWorkspace {
            constructor() {
                this.currentTimeMode = 'present';
                this.timeZones = {
                    'UTC': 0,
                    'EST': -5,
                    'PST': -8,
                    'JST': 9,
                    'CET': 1
                };
                this.timeFlowActive = true;
                this.init();
            }

            init() {
                this.startTimeUpdates();
                this.initializeControls();
                this.initializeTaskInteractions();
                this.initializePredictionUpdates();
                this.initializeCollaborationStreams();
                this.initializeTemporalEffects();
            }

            // Real-time clock synchronization
            startTimeUpdates() {
                this.updateAllTimezones();
                setInterval(() => this.updateAllTimezones(), 1000);
            }

            updateAllTimezones() {
                const now = new Date();
                
                Object.keys(this.timeZones).forEach(timezone => {
                    const offset = this.timeZones[timezone];
                    const localTime = new Date(now.getTime() + (offset * 60 * 60 * 1000));
                    const timeString = localTime.toUTCString().split(' ')[4];
                    
                    const element = document.getElementById(`${timezone.toLowerCase()}-time`);
                    if (element) {
                        element.textContent = timeString;
                        element.style.animation = 'none';
                        element.offsetHeight; // Trigger reflow
                        element.style.animation = 'timePulse 2s ease-in-out infinite';
                    }
                });
            }

            // Temporal mode controls
            initializeControls() {
                const controls = document.querySelectorAll('.control-btn');
                controls.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const action = e.target.dataset.action;
                        this.switchTemporalMode(action);
                        
                        // Update active state
                        controls.forEach(c => c.classList.remove('active'));
                        e.target.classList.add('active');
                    });
                });
            }

            switchTemporalMode(mode) {
                this.currentTimeMode = mode;
                const body = document.body;
                
                // Remove existing mode classes
                body.classList.remove('past-mode', 'future-mode', 'flow-mode');
                
                switch(mode) {
                    case 'past':
                        body.classList.add('past-mode');
                        this.emphasizePastElements();
                        break;
                    case 'future':
                        body.classList.add('future-mode');
                        this.emphasizeFutureElements();
                        break;
                    case 'flow':
                        body.classList.add('flow-mode');
                        this.activateTimeFlow();
                        break;
                    default:
                        this.resetToPresent();
                }

                // Add temporal transition effect
                this.addTemporalTransition();
            }

            emphasizePastElements() {
                const historicalTimeline = document.querySelector('.historical-timeline');
                historicalTimeline.style.transform = 'scale(1.05)';
                historicalTimeline.style.boxShadow = '0 0 30px rgba(139, 69, 19, 0.5)';
                
                document.querySelectorAll('.history-event').forEach((event, index) => {
                    setTimeout(() => {
                        event.style.transform = 'translateX(0)';
                        event.style.opacity = '1';
                        event.style.background = 'rgba(139, 69, 19, 0.6)';
                    }, index * 100);
                });
            }

            emphasizeFutureElements() {
                const predictionPanel = document.querySelector('.prediction-panel');
                predictionPanel.style.transform = 'scale(1.05)';
                predictionPanel.style.boxShadow = '0 0 30px rgba(230, 230, 250, 0.5)';
                
                document.querySelectorAll('.prediction-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.transform = 'translateY(-10px)';
                        card.style.background = 'rgba(230, 230, 250, 0.3)';
                    }, index * 150);
                });
            }

            activateTimeFlow() {
                document.body.style.animation = 'temporalFlux 5s infinite';
                
                // Enhanced flow animations
                document.querySelectorAll('.task-item, .history-event, .prediction-card').forEach((item, index) => {
                    item.style.animation = `timelineFlow ${3 + (index % 3)}s linear infinite`;
                });
            }

            resetToPresent() {
                document.querySelector('.historical-timeline').style.transform = '';
                document.querySelector('.historical-timeline').style.boxShadow = '';
                document.querySelector('.prediction-panel').style.transform = '';
                document.querySelector('.prediction-panel').style.boxShadow = '';
                document.body.style.animation = '';
                
                document.querySelectorAll('.task-item, .history-event, .prediction-card').forEach(item => {
                    item.style.transform = '';
                    item.style.background = '';
                    item.style.animation = '';
                });
            }

            addTemporalTransition() {
                const transition = document.createElement('div');
                transition.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.2), transparent);
                    pointer-events: none;
                    z-index: 1000;
                    animation: temporalShift 1s ease-out;
                `;
                
                document.body.appendChild(transition);
                setTimeout(() => transition.remove(), 1000);
            }

            // Task interaction enhancements
            initializeTaskInteractions() {
                document.querySelectorAll('.task-item').forEach(task => {
                    task.addEventListener('click', () => {
                        this.showTaskDetails(task);
                    });
                    
                    task.addEventListener('mouseenter', () => {
                        task.style.transform = 'translateX(15px) scale(1.02)';
                        task.style.boxShadow = '0 0 25px rgba(0, 255, 255, 0.4)';
                    });
                    
                    task.addEventListener('mouseleave', () => {
                        task.style.transform = 'translateX(10px)';
                        task.style.boxShadow = '';
                    });
                });
            }

            showTaskDetails(task) {
                const title = task.querySelector('.task-title').textContent;
                const deadline = task.querySelector('.task-deadline').textContent;
                
                // Create temporal detail overlay
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(26, 26, 46, 0.95);
                    border: 2px solid var(--present-vibrant);
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 400px;
                    z-index: 1000;
                    animation: taskDetailAppear 0.5s ease-out;
                `;
                
                overlay.innerHTML = `
                    <h3 style="color: var(--present-vibrant); margin-bottom: 15px;">${title}</h3>
                    <p style="color: var(--future-ethereal); margin-bottom: 20px;">${deadline}</p>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="this.parentElement.parentElement.remove()" 
                                style="background: var(--present-vibrant); color: var(--chronometer-bg); 
                                       border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            Close
                        </button>
                    </div>
                `;
                
                document.body.appendChild(overlay);
            }

            // Prediction updates with temporal awareness
            initializePredictionUpdates() {
                setInterval(() => {
                    this.updatePredictions();
                }, 5000);
            }

            updatePredictions() {
                document.querySelectorAll('.prediction-metric').forEach(metric => {
                    const currentValue = parseFloat(metric.textContent);
                    const variation = (Math.random() - 0.5) * 2; // ±1 variation
                    const newValue = Math.max(0, currentValue + variation);
                    
                    if (metric.textContent.includes('%')) {
                        metric.textContent = `${Math.min(100, newValue).toFixed(1)}%`;
                    } else if (metric.textContent.includes('h')) {
                        metric.textContent = `${newValue.toFixed(1)}h`;
                    } else {
                        metric.textContent = `${newValue > 0 ? '+' : ''}${newValue.toFixed(1)}%`;
                    }
                    
                    // Add update animation
                    metric.style.animation = 'none';
                    metric.offsetHeight;
                    metric.style.animation = 'chronoGlow 1s ease-out';
                });
            }

            // Collaboration stream simulation
            initializeCollaborationStreams() {
                const messages = [
                    { user: 'Sarah (EST)', text: 'Temporal sync in progress...' },
                    { user: 'Ken (JST)', text: 'Future analysis complete' },
                    { user: 'Maria (CET)', text: 'Historical data reconciled' },
                    { user: 'Alex (PST)', text: 'Cross-timeline validation passed' },
                    { user: 'AI Assistant', text: 'Prediction accuracy improved to 92%' }
                ];
                
                setInterval(() => {
                    this.addRandomMessage(messages);
                }, 8000);
            }

            addRandomMessage(messages) {
                const streams = document.querySelectorAll('.stream-channel');
                const randomStream = streams[Math.floor(Math.random() * streams.length)];
                const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                
                const messageDiv = document.createElement('div');
                messageDiv.className = 'temporal-message';
                messageDiv.innerHTML = `<strong>${randomMessage.user}:</strong> ${randomMessage.text}`;
                
                randomStream.appendChild(messageDiv);
                
                // Remove old messages to prevent overflow
                const existingMessages = randomStream.querySelectorAll('.temporal-message');
                if (existingMessages.length > 3) {
                    existingMessages[0].remove();
                }
            }

            // Advanced temporal effects
            initializeTemporalEffects() {
                // Add dynamic style updates
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes taskDetailAppear {
                        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                        100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    }
                    
                    .past-mode .history-event {
                        border-left: 4px solid var(--past-sepia) !important;
                        box-shadow: 0 0 15px rgba(139, 69, 19, 0.3) !important;
                    }
                    
                    .future-mode .prediction-card {
                        border: 2px solid var(--future-ethereal) !important;
                        box-shadow: 0 0 20px rgba(230, 230, 250, 0.4) !important;
                    }
                    
                    .flow-mode * {
                        transition: all 0.3s ease !important;
                    }
                `;
                document.head.appendChild(style);
                
                // Initialize particle effects for enhanced immersion
                this.createTemporalParticles();
            }

            createTemporalParticles() {
                const particleContainer = document.createElement('div');
                particleContainer.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    z-index: -1;
                `;
                
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.style.cssText = `
                        position: absolute;
                        width: 2px;
                        height: 2px;
                        background: var(--present-vibrant);
                        border-radius: 50%;
                        opacity: 0.3;
                        animation: temporalFloat ${5 + Math.random() * 10}s linear infinite;
                        top: ${Math.random() * 100}%;
                        left: ${Math.random() * 100}%;
                        animation-delay: ${Math.random() * 5}s;
                    `;
                    particleContainer.appendChild(particle);
                }
                
                document.body.appendChild(particleContainer);
                
                // Add particle animation
                const particleStyle = document.createElement('style');
                particleStyle.textContent = `
                    @keyframes temporalFloat {
                        0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
                        10% { opacity: 0.3; }
                        90% { opacity: 0.3; }
                        100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
                    }
                `;
                document.head.appendChild(particleStyle);
            }
        }

        // Initialize the Temporal Workspace
        document.addEventListener('DOMContentLoaded', () => {
            new TemporalWorkspace();
        });

        // Keyboard shortcuts for temporal navigation
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('[data-action="present"]').click();
                        break;
                    case '2':
                        e.preventDefault();
                        document.querySelector('[data-action="past"]').click();
                        break;
                    case '3':
                        e.preventDefault();
                        document.querySelector('[data-action="future"]').click();
                        break;
                    case '4':
                        e.preventDefault();
                        document.querySelector('[data-action="flow"]').click();
                        break;
                }
            }
        });
    </script>
</body>
</html>