<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEUROMEDIA X - Cyberpunk Holographic Player</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        :root {
            --neon-pink: #ff006e;
            --neon-cyan: #00f5ff;
            --neon-purple: #bd00ff;
            --neon-yellow: #fffc00;
            --neon-green: #39ff14;
            --dark-bg: #0a0a0f;
            --darker-bg: #050508;
            --panel-bg: rgba(10, 10, 15, 0.85);
            --glass-bg: rgba(255, 255, 255, 0.02);
            --text-primary: #ffffff;
            --text-secondary: #a0a0b8;
            --border-color: rgba(0, 245, 255, 0.2);
            --glitch-1: #ff006e;
            --glitch-2: #00f5ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Cyberpunk Grid Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(rgba(0, 245, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 245, 255, 0.03) 1px, transparent 1px),
                radial-gradient(circle at 20% 50%, rgba(255, 0, 110, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(189, 0, 255, 0.1) 0%, transparent 50%);
            background-size: 50px 50px, 50px 50px, 100% 100%, 100% 100%;
            animation: grid-move 20s linear infinite;
            z-index: -1;
        }

        @keyframes grid-move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* Header with Glitch Effect */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header h1 {
            font-size: 4rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 0.3em;
            background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px var(--neon-cyan);
            position: relative;
            animation: text-flicker 3s infinite alternate;
        }

        @keyframes text-flicker {
            0%, 100% { opacity: 1; filter: brightness(1); }
            50% { opacity: 0.95; filter: brightness(1.2); }
        }

        .header h1::before,
        .header h1::after {
            content: 'NEUROMEDIA X';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.8;
        }

        .header h1::before {
            animation: glitch-1 0.5s infinite;
            color: var(--glitch-1);
            z-index: -1;
        }

        .header h1::after {
            animation: glitch-2 0.5s infinite;
            color: var(--glitch-2);
            z-index: -2;
        }

        @keyframes glitch-1 {
            0%, 100% { clip-path: inset(0 0 100% 0); transform: translateX(0); }
            20% { clip-path: inset(20% 0 60% 0); transform: translateX(-5px); }
            40% { clip-path: inset(50% 0 30% 0); transform: translateX(5px); }
            60% { clip-path: inset(70% 0 10% 0); transform: translateX(-2px); }
            80% { clip-path: inset(10% 0 80% 0); transform: translateX(2px); }
        }

        @keyframes glitch-2 {
            0%, 100% { clip-path: inset(100% 0 0 0); transform: translateX(0); }
            20% { clip-path: inset(80% 0 10% 0); transform: translateX(5px); }
            40% { clip-path: inset(30% 0 50% 0); transform: translateX(-5px); }
            60% { clip-path: inset(10% 0 70% 0); transform: translateX(2px); }
            80% { clip-path: inset(60% 0 20% 0); transform: translateX(-2px); }
        }

        /* Main Player Container */
        .media-player {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 
                0 0 50px rgba(0, 245, 255, 0.3),
                inset 0 0 50px rgba(0, 245, 255, 0.05),
                0 0 100px rgba(255, 0, 110, 0.1);
            position: relative;
        }

        /* Holographic Display */
        .holo-display {
            position: relative;
            height: 500px;
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.05), rgba(189, 0, 255, 0.05));
            overflow: hidden;
            border-bottom: 2px solid var(--border-color);
        }

        .holo-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.2), transparent);
            animation: holo-scan 3s linear infinite;
        }

        @keyframes holo-scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Video/Visualizer Container */
        .video-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Audio Visualizer */
        .visualizer {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: 3px;
            height: 200px;
            padding: 0 50px;
        }

        .viz-bar {
            width: 4px;
            background: linear-gradient(to top, var(--neon-pink), var(--neon-cyan));
            border-radius: 2px;
            animation: viz-pulse 1s ease-in-out infinite;
            box-shadow: 0 0 10px var(--neon-cyan);
        }

        @keyframes viz-pulse {
            0%, 100% { height: 20px; }
            50% { height: var(--bar-height, 100px); }
        }

        /* Waveform Display */
        .waveform {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 60px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--border-color);
            border-radius: 30px;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .waveform-progress {
            height: 100%;
            width: 35%;
            background: linear-gradient(90deg, var(--neon-pink), var(--neon-purple));
            position: relative;
            overflow: hidden;
        }

        .waveform-progress::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 3px;
            height: 100%;
            background: var(--neon-cyan);
            box-shadow: 0 0 20px var(--neon-cyan);
            animation: pulse-glow 1s ease-in-out infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Control Panel */
        .control-panel {
            padding: 30px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(10, 10, 20, 0.5));
        }

        /* Playback Controls */
        .playback-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-btn {
            background: transparent;
            border: 2px solid var(--neon-cyan);
            color: var(--neon-cyan);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .control-btn.primary {
            width: 80px;
            height: 80px;
            font-size: 2rem;
            border-color: var(--neon-pink);
            color: var(--neon-pink);
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, var(--neon-cyan), transparent);
            transform: translate(-50%, -50%);
            transition: all 0.3s;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px var(--neon-cyan);
        }

        .control-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .control-btn.primary:hover {
            box-shadow: 0 0 30px var(--neon-pink);
        }

        /* Time Display */
        .time-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .time {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            color: var(--neon-cyan);
            text-shadow: 0 0 10px var(--neon-cyan);
        }

        /* Track Info */
        .track-info {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .track-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .track-artist {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 5px;
        }

        .track-album {
            font-size: 0.9rem;
            color: var(--text-secondary);
            opacity: 0.7;
        }

        /* Quality & Settings Grid */
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .setting-panel {
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s;
        }

        .setting-panel:hover {
            background: rgba(0, 245, 255, 0.05);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.2);
        }

        .setting-title {
            color: var(--neon-yellow);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-title::before {
            content: '▶';
            color: var(--neon-pink);
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        /* Quality Selector */
        .quality-options {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .quality-btn {
            padding: 8px 16px;
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Orbitron', monospace;
            font-size: 0.8rem;
        }

        .quality-btn.active {
            background: linear-gradient(45deg, var(--neon-pink), var(--neon-purple));
            color: var(--text-primary);
            border-color: transparent;
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.5);
        }

        .quality-btn:hover:not(.active) {
            border-color: var(--neon-cyan);
            color: var(--neon-cyan);
        }

        /* Volume Control */
        .volume-control {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .volume-slider {
            flex: 1;
            height: 6px;
            background: rgba(0, 245, 255, 0.2);
            border-radius: 3px;
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .volume-fill {
            height: 100%;
            width: 75%;
            background: linear-gradient(90deg, var(--neon-cyan), var(--neon-pink));
            border-radius: 3px;
            position: relative;
        }

        .volume-handle {
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: var(--neon-cyan);
            border-radius: 50%;
            box-shadow: 0 0 20px var(--neon-cyan);
            cursor: grab;
        }

        /* Playlist */
        .playlist-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
        }

        .playlist-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .playlist-title {
            font-size: 1.3rem;
            color: var(--neon-yellow);
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .playlist-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: var(--glass-bg);
            border: 1px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .playlist-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .playlist-item:hover {
            border-color: var(--neon-cyan);
            transform: translateX(10px);
        }

        .playlist-item:hover::before {
            left: 100%;
        }

        .playlist-item.active {
            background: linear-gradient(135deg, rgba(255, 0, 110, 0.1), rgba(0, 245, 255, 0.1));
            border-color: var(--neon-pink);
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
        }

        .playlist-number {
            width: 30px;
            color: var(--neon-cyan);
            font-weight: 700;
        }

        .playlist-info {
            flex: 1;
            margin-left: 15px;
        }

        .playlist-track {
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .playlist-artist {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .playlist-duration {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* Share Panel */
        .share-panel {
            display: flex;
            gap: 15px;
            justify-content: center;
            padding: 20px;
            background: rgba(189, 0, 255, 0.05);
            border-radius: 15px;
            margin-top: 20px;
        }

        .share-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: transparent;
            border: 2px solid var(--neon-purple);
            color: var(--neon-purple);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 1.2rem;
        }

        .share-btn:hover {
            background: var(--neon-purple);
            color: var(--dark-bg);
            transform: scale(1.1) rotate(360deg);
            box-shadow: 0 0 30px var(--neon-purple);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
                letter-spacing: 0.1em;
            }

            .holo-display {
                height: 300px;
            }

            .control-btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .control-btn.primary {
                width: 65px;
                height: 65px;
                font-size: 1.5rem;
            }

            .track-title {
                font-size: 1.5rem;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(var(--neon-cyan), var(--neon-purple));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(var(--neon-pink), var(--neon-cyan));
        }

        /* Loading Animation */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .loading.active {
            display: block;
        }

        .cyber-loader {
            width: 80px;
            height: 80px;
            border: 3px solid transparent;
            border-top-color: var(--neon-cyan);
            border-right-color: var(--neon-pink);
            border-radius: 50%;
            animation: cyber-spin 1s linear infinite;
        }

        @keyframes cyber-spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 data-text="NEUROMEDIA X">NEUROMEDIA X</h1>
            <p style="color: var(--text-secondary); margin-top: 10px; letter-spacing: 0.2em;">
                HOLOGRAPHIC MEDIA INTERFACE v2.077
            </p>
        </header>

        <div class="media-player">
            <!-- Holographic Display Area -->
            <div class="holo-display">
                <div class="video-container">
                    <!-- Audio Visualizer -->
                    <div class="visualizer" id="visualizer">
                        <!-- Visualizer bars will be generated by JavaScript -->
                    </div>
                </div>
                
                <!-- Waveform Progress -->
                <div class="waveform">
                    <div class="waveform-progress" id="waveformProgress"></div>
                </div>
                
                <!-- Loading Animation -->
                <div class="loading" id="loading">
                    <div class="cyber-loader"></div>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <!-- Track Information -->
                <div class="track-info">
                    <h2 class="track-title" id="trackTitle">Neon Dreams</h2>
                    <p class="track-artist" id="trackArtist">Cyber Collective</p>
                    <p class="track-album" id="trackAlbum">Digital Horizons EP</p>
                </div>

                <!-- Time Display -->
                <div class="time-display">
                    <span class="time" id="currentTime">01:47</span>
                    <span class="time" id="totalTime">04:32</span>
                </div>

                <!-- Playback Controls -->
                <div class="playback-controls">
                    <button class="control-btn" onclick="mediaplayer.previousTrack()" aria-label="Previous">
                        ⏮
                    </button>
                    <button class="control-btn" onclick="mediaplayer.skipBackward()" aria-label="Rewind">
                        ⏪
                    </button>
                    <button class="control-btn primary" id="playPauseBtn" onclick="mediaplayer.togglePlayback()" aria-label="Play/Pause">
                        ▶
                    </button>
                    <button class="control-btn" onclick="mediaplayer.skipForward()" aria-label="Fast Forward">
                        ⏩
                    </button>
                    <button class="control-btn" onclick="mediaplayer.nextTrack()" aria-label="Next">
                        ⏭
                    </button>
                </div>

                <!-- Settings Grid -->
                <div class="settings-grid">
                    <!-- Quality Selector -->
                    <div class="setting-panel">
                        <h3 class="setting-title">Neural Stream Quality</h3>
                        <div class="quality-options">
                            <button class="quality-btn" onclick="mediaplayer.setQuality('128')">128K</button>
                            <button class="quality-btn" onclick="mediaplayer.setQuality('256')">256K</button>
                            <button class="quality-btn active" onclick="mediaplayer.setQuality('320')">320K</button>
                            <button class="quality-btn" onclick="mediaplayer.setQuality('FLAC')">FLAC</button>
                            <button class="quality-btn" onclick="mediaplayer.setQuality('HD')">HD</button>
                        </div>
                    </div>

                    <!-- Volume Control -->
                    <div class="setting-panel">
                        <h3 class="setting-title">Sonic Amplitude</h3>
                        <div class="volume-control">
                            <span style="color: var(--neon-cyan);">🔊</span>
                            <div class="volume-slider" onclick="mediaplayer.setVolume(event)">
                                <div class="volume-fill" id="volumeFill">
                                    <div class="volume-handle"></div>
                                </div>
                            </div>
                            <span id="volumeLevel" style="color: var(--neon-cyan); min-width: 40px;">75%</span>
                        </div>
                    </div>

                    <!-- Effects -->
                    <div class="setting-panel">
                        <h3 class="setting-title">Cybernetic Effects</h3>
                        <div class="quality-options">
                            <button class="quality-btn" onclick="mediaplayer.toggleEffect('bass')">BASS+</button>
                            <button class="quality-btn" onclick="mediaplayer.toggleEffect('3d')">3D</button>
                            <button class="quality-btn" onclick="mediaplayer.toggleEffect('reverb')">REVERB</button>
                            <button class="quality-btn" onclick="mediaplayer.toggleEffect('echo')">ECHO</button>
                        </div>
                    </div>
                </div>

                <!-- Playlist -->
                <div class="playlist-section">
                    <div class="playlist-header">
                        <h3 class="playlist-title">Neural Playlist</h3>
                        <span style="color: var(--text-secondary);">12 Tracks</span>
                    </div>
                    <div id="playlist">
                        <!-- Playlist items will be generated by JavaScript -->
                    </div>
                </div>

                <!-- Share Panel -->
                <div class="share-panel">
                    <button class="share-btn" onclick="mediaplayer.share('neural')" aria-label="Share to Neural Network">
                        🧠
                    </button>
                    <button class="share-btn" onclick="mediaplayer.share('hologram')" aria-label="Send as Hologram">
                        💎
                    </button>
                    <button class="share-btn" onclick="mediaplayer.share('quantum')" aria-label="Quantum Share">
                        ⚛️
                    </button>
                    <button class="share-btn" onclick="mediaplayer.share('matrix')" aria-label="Upload to Matrix">
                        🌐
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class CyberpunkMediaPlayer {
            constructor() {
                this.isPlaying = false;
                this.currentTrack = 0;
                this.volume = 75;
                this.quality = '320';
                this.effects = new Set();
                this.currentTime = 107; // 1:47 in seconds
                this.totalTime = 272; // 4:32 in seconds
                
                this.tracks = [
                    { title: "Neon Dreams", artist: "Cyber Collective", album: "Digital Horizons EP", duration: "4:32" },
                    { title: "Chrome Hearts", artist: "Synthetic Soul", album: "Binary Love", duration: "3:45" },
                    { title: "Holographic Rain", artist: "Neural Network", album: "AI Awakening", duration: "5:21" },
                    { title: "Digital Ghost", artist: "Phantom Protocol", album: "Encrypted Memories", duration: "4:15" },
                    { title: "Laser Boulevard", artist: "Neon Riders", album: "Street Samurai", duration: "3:58" },
                    { title: "Quantum Drift", artist: "Time Hackers", album: "Paradox Engine", duration: "6:03" },
                    { title: "Silicon Dreams", artist: "Android Lullaby", album: "Electric Sheep", duration: "4:47" },
                    { title: "Cybernetic Love", artist: "Digital Emotion", album: "Synthetic Feelings", duration: "3:33" },
                    { title: "Matrix Meditation", artist: "Zen Hacker", album: "Code Enlightenment", duration: "7:12" },
                    { title: "Neon Noir", artist: "Shadow Runner", album: "Dark City", duration: "4:41" },
                    { title: "Binary Sunset", artist: "Data Stream", album: "Digital Horizon", duration: "5:15" },
                    { title: "Electric Evolution", artist: "Cyber Darwin", album: "Tech Selection", duration: "4:28" }
                ];
                
                this.init();
            }

            init() {
                this.generateVisualizer();
                this.generatePlaylist();
                this.startAnimations();
                this.updateTimeDisplay();
            }

            generateVisualizer() {
                const visualizer = document.getElementById('visualizer');
                const barCount = 60;
                
                for (let i = 0; i < barCount; i++) {
                    const bar = document.createElement('div');
                    bar.className = 'viz-bar';
                    const height = Math.random() * 150 + 50;
                    bar.style.setProperty('--bar-height', `${height}px`);
                    bar.style.animationDelay = `${i * 0.05}s`;
                    bar.style.animationDuration = `${0.5 + Math.random() * 1}s`;
                    visualizer.appendChild(bar);
                }
            }

            generatePlaylist() {
                const playlist = document.getElementById('playlist');
                playlist.innerHTML = '';
                
                this.tracks.forEach((track, index) => {
                    const item = document.createElement('div');
                    item.className = `playlist-item ${index === this.currentTrack ? 'active' : ''}`;
                    item.onclick = () => this.selectTrack(index);
                    
                    item.innerHTML = `
                        <span class="playlist-number">${String(index + 1).padStart(2, '0')}</span>
                        <div class="playlist-info">
                            <div class="playlist-track">${track.title}</div>
                            <div class="playlist-artist">${track.artist}</div>
                        </div>
                        <span class="playlist-duration">${track.duration}</span>
                    `;
                    
                    playlist.appendChild(item);
                });
            }

            togglePlayback() {
                this.isPlaying = !this.isPlaying;
                const btn = document.getElementById('playPauseBtn');
                btn.textContent = this.isPlaying ? '⏸' : '▶';
                
                if (this.isPlaying) {
                    this.startPlayback();
                    this.showLoadingBriefly();
                } else {
                    this.pausePlayback();
                }
            }

            startPlayback() {
                // Animate visualizer more actively
                const bars = document.querySelectorAll('.viz-bar');
                bars.forEach(bar => {
                    bar.style.animationPlayState = 'running';
                });
                
                // Start progress animation
                this.progressInterval = setInterval(() => {
                    if (this.currentTime < this.totalTime) {
                        this.currentTime++;
                        this.updateProgress();
                        this.updateTimeDisplay();
                    } else {
                        this.nextTrack();
                    }
                }, 1000);
            }

            pausePlayback() {
                const bars = document.querySelectorAll('.viz-bar');
                bars.forEach(bar => {
                    bar.style.animationPlayState = 'paused';
                });
                
                clearInterval(this.progressInterval);
            }

            updateProgress() {
                const progress = (this.currentTime / this.totalTime) * 100;
                document.getElementById('waveformProgress').style.width = `${progress}%`;
            }

            updateTimeDisplay() {
                const formatTime = (seconds) => {
                    const mins = Math.floor(seconds / 60);
                    const secs = seconds % 60;
                    return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
                };
                
                document.getElementById('currentTime').textContent = formatTime(this.currentTime);
                document.getElementById('totalTime').textContent = formatTime(this.totalTime);
            }

            selectTrack(index) {
                this.currentTrack = index;
                const track = this.tracks[index];
                
                document.getElementById('trackTitle').textContent = track.title;
                document.getElementById('trackArtist').textContent = track.artist;
                document.getElementById('trackAlbum').textContent = track.album;
                
                // Reset time
                this.currentTime = 0;
                const [mins, secs] = track.duration.split(':');
                this.totalTime = parseInt(mins) * 60 + parseInt(secs);
                
                this.updateProgress();
                this.updateTimeDisplay();
                this.generatePlaylist();
                
                if (this.isPlaying) {
                    this.showLoadingBriefly();
                }
            }

            previousTrack() {
                if (this.currentTrack > 0) {
                    this.selectTrack(this.currentTrack - 1);
                } else {
                    this.selectTrack(this.tracks.length - 1);
                }
            }

            nextTrack() {
                if (this.currentTrack < this.tracks.length - 1) {
                    this.selectTrack(this.currentTrack + 1);
                } else {
                    this.selectTrack(0);
                }
            }

            skipBackward() {
                this.currentTime = Math.max(0, this.currentTime - 10);
                this.updateProgress();
                this.updateTimeDisplay();
            }

            skipForward() {
                this.currentTime = Math.min(this.totalTime, this.currentTime + 10);
                this.updateProgress();
                this.updateTimeDisplay();
            }

            setQuality(quality) {
                this.quality = quality;
                const buttons = document.querySelectorAll('.quality-options .quality-btn');
                buttons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.textContent === quality) {
                        btn.classList.add('active');
                    }
                });
                
                this.showNotification(`Stream quality: ${quality}`);
            }

            setVolume(event) {
                const slider = event.currentTarget;
                const rect = slider.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
                
                this.volume = Math.round(percentage);
                document.getElementById('volumeFill').style.width = `${this.volume}%`;
                document.getElementById('volumeLevel').textContent = `${this.volume}%`;
            }

            toggleEffect(effect) {
                const btn = event.target;
                if (this.effects.has(effect)) {
                    this.effects.delete(effect);
                    btn.classList.remove('active');
                } else {
                    this.effects.add(effect);
                    btn.classList.add('active');
                }
                
                this.showNotification(`${effect.toUpperCase()} ${this.effects.has(effect) ? 'enabled' : 'disabled'}`);
            }

            share(platform) {
                const messages = {
                    neural: "Uploading to Neural Network...",
                    hologram: "Generating holographic projection...",
                    quantum: "Initiating quantum entanglement...",
                    matrix: "Connecting to the Matrix..."
                };
                
                this.showNotification(messages[platform]);
                
                // Visual feedback
                const btn = event.target.closest('.share-btn');
                btn.style.transform = 'scale(1.2) rotate(720deg)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 600);
            }

            showLoadingBriefly() {
                const loading = document.getElementById('loading');
                loading.classList.add('active');
                setTimeout(() => {
                    loading.classList.remove('active');
                }, 800);
            }

            showNotification(message) {
                // Create a cyberpunk-style notification
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, rgba(0, 245, 255, 0.9), rgba(255, 0, 110, 0.9));
                    color: white;
                    padding: 15px 25px;
                    border-radius: 25px;
                    font-family: 'Orbitron', monospace;
                    font-size: 0.9rem;
                    z-index: 1000;
                    animation: slideInRight 0.5s ease, slideOutRight 0.5s ease 2s forwards;
                    box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
                `;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            startAnimations() {
                // Add dynamic glow effects
                setInterval(() => {
                    if (this.isPlaying) {
                        const bars = document.querySelectorAll('.viz-bar');
                        bars.forEach(bar => {
                            const height = Math.random() * 150 + 50;
                            bar.style.setProperty('--bar-height', `${height}px`);
                        });
                    }
                }, 500);
            }
        }

        // Initialize the media player
        let mediaplayer;
        document.addEventListener('DOMContentLoaded', () => {
            mediaplayer = new CyberpunkMediaPlayer();
        });

        // Add notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (!mediaplayer) return;
            
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    mediaplayer.togglePlayback();
                    break;
                case 'ArrowLeft':
                    mediaplayer.skipBackward();
                    break;
                case 'ArrowRight':
                    mediaplayer.skipForward();
                    break;
                case 'ArrowUp':
                    mediaplayer.volume = Math.min(100, mediaplayer.volume + 5);
                    document.getElementById('volumeFill').style.width = `${mediaplayer.volume}%`;
                    document.getElementById('volumeLevel').textContent = `${mediaplayer.volume}%`;
                    break;
                case 'ArrowDown':
                    mediaplayer.volume = Math.max(0, mediaplayer.volume - 5);
                    document.getElementById('volumeFill').style.width = `${mediaplayer.volume}%`;
                    document.getElementById('volumeLevel').textContent = `${mediaplayer.volume}%`;
                    break;
            }
        });
    </script>
</body>
</html>