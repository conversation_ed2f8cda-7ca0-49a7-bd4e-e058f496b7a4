<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playful Input Intelligence - UI Hybrid 29</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #4facfe 75%, #00f2fe 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            animation: backgroundShift 10s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .form-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: visible;
        }

        h1 {
            text-align: center;
            color: #764ba2;
            margin-bottom: 30px;
            font-size: 2em;
            animation: titleBounce 2s ease-in-out infinite;
        }

        @keyframes titleBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .input-group {
            position: relative;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .input-wrapper.focused {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(118, 75, 162, 0.3);
        }

        .input-wrapper.success {
            animation: successBounce 0.6s ease-out;
        }

        @keyframes successBounce {
            0% { transform: scale(1); }
            30% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .input-wrapper.error {
            animation: errorShake 0.5s ease-in-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        label {
            display: block;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        input {
            width: 100%;
            padding: 15px 20px;
            border: none;
            background: transparent;
            font-size: 16px;
            color: #333;
            outline: none;
            transition: all 0.3s ease;
        }

        .helper-character {
            position: absolute;
            width: 60px;
            height: 60px;
            right: -80px;
            top: 50%;
            transform: translateY(-50%);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            opacity: 0;
            pointer-events: none;
        }

        .helper-character.show {
            opacity: 1;
            right: -70px;
        }

        .helper-character.celebrate {
            animation: celebrate 0.6s ease-out;
        }

        @keyframes celebrate {
            0% { transform: translateY(-50%) rotate(0deg); }
            50% { transform: translateY(-70%) rotate(360deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        .helper-body {
            width: 100%;
            height: 100%;
            background: #ffd93d;
            border-radius: 50%;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .helper-face {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
        }

        .helper-eyes {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }

        .eye {
            width: 8px;
            height: 8px;
            background: #333;
            border-radius: 50%;
            animation: blink 4s infinite;
        }

        @keyframes blink {
            0%, 45%, 55%, 100% { transform: scaleY(1); }
            50% { transform: scaleY(0.1); }
        }

        .helper-mouth {
            position: absolute;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 10px;
            border-bottom: 3px solid #333;
            border-radius: 0 0 20px 20px;
            transition: all 0.3s ease;
        }

        .helper-character.happy .helper-mouth {
            width: 25px;
            height: 12px;
            border-bottom-width: 4px;
        }

        .helper-character.worried .helper-mouth {
            width: 15px;
            height: 5px;
            border-radius: 0 0 15px 15px;
            transform: translateX(-50%) rotate(180deg);
            bottom: 20%;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #4facfe);
            background-size: 300% 100%;
            width: 0;
            transition: width 0.3s ease;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        .validation-hints {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .validation-hints.show {
            opacity: 1;
            transform: translateY(0);
        }

        .validation-hint {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .hint-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .hint-icon.pending {
            background: #e0e0e0;
            color: #666;
        }

        .hint-icon.valid {
            background: #4caf50;
            color: white;
            animation: checkPop 0.4s ease-out;
        }

        .hint-icon.invalid {
            background: #f44336;
            color: white;
            animation: xPop 0.4s ease-out;
        }

        @keyframes checkPop {
            0% { transform: scale(0); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }

        @keyframes xPop {
            0% { transform: scale(0) rotate(0deg); }
            50% { transform: scale(1.3) rotate(180deg); }
            100% { transform: scale(1) rotate(180deg); }
        }

        .autocomplete-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 10px;
            opacity: 0;
            transform: translateY(-20px) scale(0.9);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            pointer-events: none;
            z-index: 10;
        }

        .autocomplete-suggestions.show {
            opacity: 1;
            transform: translateY(0) scale(1);
            pointer-events: auto;
        }

        .suggestion {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .suggestion:hover {
            background: #f0f0f0;
            padding-left: 30px;
        }

        .suggestion::before {
            content: '→';
            position: absolute;
            left: 10px;
            opacity: 0;
            transition: all 0.2s ease;
        }

        .suggestion:hover::before {
            opacity: 1;
        }

        .format-preview {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            font-size: 12px;
            color: #999;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .format-preview.show {
            opacity: 1;
        }

        .confetti {
            position: fixed;
            pointer-events: none;
            z-index: 1000;
        }

        .confetti-piece {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ffd93d;
            animation: confettiFall 1s ease-out forwards;
        }

        @keyframes confettiFall {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100px) rotate(720deg);
                opacity: 0;
            }
        }

        .submit-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
        }

        .submit-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(118, 75, 162, 0.4);
        }

        .submit-button:active {
            transform: translateY(0);
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: scale(0);
            animation: rippleEffect 0.6s ease-out;
        }

        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>🎉 Fun Form Friend 🎉</h1>
        
        <form id="playfulForm">
            <div class="input-group" data-type="email">
                <label for="email">Email Address</label>
                <div class="input-wrapper">
                    <input type="text" id="email" name="email" placeholder="<EMAIL>" autocomplete="off">
                    <div class="progress-bar"></div>
                    <div class="format-preview"></div>
                </div>
                <div class="helper-character">
                    <div class="helper-body">
                        <div class="helper-face">
                            <div class="helper-eyes">
                                <div class="eye"></div>
                                <div class="eye"></div>
                            </div>
                            <div class="helper-mouth"></div>
                        </div>
                    </div>
                </div>
                <div class="validation-hints">
                    <div class="validation-hint" data-rule="@">
                        <span class="hint-icon pending">@</span>
                        <span>Contains @ symbol</span>
                    </div>
                    <div class="validation-hint" data-rule="domain">
                        <span class="hint-icon pending">.</span>
                        <span>Valid domain</span>
                    </div>
                </div>
                <div class="autocomplete-suggestions"></div>
            </div>

            <div class="input-group" data-type="phone">
                <label for="phone">Phone Number</label>
                <div class="input-wrapper">
                    <input type="text" id="phone" name="phone" placeholder="(*************" autocomplete="off">
                    <div class="progress-bar"></div>
                    <div class="format-preview"></div>
                </div>
                <div class="helper-character">
                    <div class="helper-body" style="background: #4fc3f7;">
                        <div class="helper-face">
                            <div class="helper-eyes">
                                <div class="eye"></div>
                                <div class="eye"></div>
                            </div>
                            <div class="helper-mouth"></div>
                        </div>
                    </div>
                </div>
                <div class="validation-hints">
                    <div class="validation-hint" data-rule="digits">
                        <span class="hint-icon pending">📱</span>
                        <span>10 digits</span>
                    </div>
                    <div class="validation-hint" data-rule="format">
                        <span class="hint-icon pending">✨</span>
                        <span>Proper format</span>
                    </div>
                </div>
            </div>

            <div class="input-group" data-type="creditcard">
                <label for="creditcard">Credit Card</label>
                <div class="input-wrapper">
                    <input type="text" id="creditcard" name="creditcard" placeholder="1234 5678 9012 3456" autocomplete="off">
                    <div class="progress-bar"></div>
                    <div class="format-preview"></div>
                </div>
                <div class="helper-character">
                    <div class="helper-body" style="background: #81c784;">
                        <div class="helper-face">
                            <div class="helper-eyes">
                                <div class="eye"></div>
                                <div class="eye"></div>
                            </div>
                            <div class="helper-mouth"></div>
                        </div>
                    </div>
                </div>
                <div class="validation-hints">
                    <div class="validation-hint" data-rule="length">
                        <span class="hint-icon pending">💳</span>
                        <span>16 digits</span>
                    </div>
                    <div class="validation-hint" data-rule="luhn">
                        <span class="hint-icon pending">🔒</span>
                        <span>Valid card number</span>
                    </div>
                </div>
            </div>

            <div class="input-group" data-type="username">
                <label for="username">Username</label>
                <div class="input-wrapper">
                    <input type="text" id="username" name="username" placeholder="cooluser123" autocomplete="off">
                    <div class="progress-bar"></div>
                    <div class="format-preview"></div>
                </div>
                <div class="helper-character">
                    <div class="helper-body" style="background: #ff7043;">
                        <div class="helper-face">
                            <div class="helper-eyes">
                                <div class="eye"></div>
                                <div class="eye"></div>
                            </div>
                            <div class="helper-mouth"></div>
                        </div>
                    </div>
                </div>
                <div class="validation-hints">
                    <div class="validation-hint" data-rule="length">
                        <span class="hint-icon pending">📏</span>
                        <span>3-20 characters</span>
                    </div>
                    <div class="validation-hint" data-rule="alphanumeric">
                        <span class="hint-icon pending">🔤</span>
                        <span>Letters & numbers only</span>
                    </div>
                </div>
                <div class="autocomplete-suggestions"></div>
            </div>

            <button type="submit" class="submit-button">
                Submit with Joy! 🚀
            </button>
        </form>
    </div>

    <script>
        class PlayfulInput {
            constructor(inputGroup) {
                this.group = inputGroup;
                this.input = inputGroup.querySelector('input');
                this.wrapper = inputGroup.querySelector('.input-wrapper');
                this.helper = inputGroup.querySelector('.helper-character');
                this.progressBar = inputGroup.querySelector('.progress-bar');
                this.hints = inputGroup.querySelector('.validation-hints');
                this.suggestions = inputGroup.querySelector('.autocomplete-suggestions');
                this.formatPreview = inputGroup.querySelector('.format-preview');
                this.type = inputGroup.dataset.type;
                
                this.init();
            }

            init() {
                this.input.addEventListener('focus', () => this.handleFocus());
                this.input.addEventListener('blur', () => this.handleBlur());
                this.input.addEventListener('input', () => this.handleInput());
                
                if (this.suggestions) {
                    this.setupAutocomplete();
                }
                
                this.setupValidation();
            }

            handleFocus() {
                this.wrapper.classList.add('focused');
                this.helper.classList.add('show');
                if (this.hints) {
                    this.hints.classList.add('show');
                }
                this.playSound('pop');
            }

            handleBlur() {
                this.wrapper.classList.remove('focused');
                if (this.input.value === '') {
                    this.helper.classList.remove('show');
                    if (this.hints) {
                        this.hints.classList.remove('show');
                    }
                }
            }

            handleInput() {
                const value = this.input.value;
                const progress = this.calculateProgress(value);
                
                this.progressBar.style.width = `${progress}%`;
                
                if (this.type === 'phone') {
                    this.formatPhone();
                } else if (this.type === 'creditcard') {
                    this.formatCreditCard();
                }
                
                this.validate();
                this.updateHelper(progress);
                
                if (this.suggestions && this.type !== 'phone' && this.type !== 'creditcard') {
                    this.showSuggestions(value);
                }
            }

            formatPhone() {
                let value = this.input.value.replace(/\D/g, '');
                if (value.length > 0) {
                    if (value.length <= 3) {
                        value = `(${value}`;
                    } else if (value.length <= 6) {
                        value = `(${value.slice(0, 3)}) ${value.slice(3)}`;
                    } else {
                        value = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
                    }
                }
                this.input.value = value;
            }

            formatCreditCard() {
                let value = this.input.value.replace(/\s/g, '');
                let formattedValue = '';
                
                for (let i = 0; i < value.length && i < 16; i++) {
                    if (i > 0 && i % 4 === 0) {
                        formattedValue += ' ';
                    }
                    formattedValue += value[i];
                }
                
                this.input.value = formattedValue;
                
                // Detect card type
                if (value.startsWith('4')) {
                    this.showFormatPreview('Visa');
                } else if (value.startsWith('5')) {
                    this.showFormatPreview('Mastercard');
                } else if (value.startsWith('3')) {
                    this.showFormatPreview('Amex');
                }
            }

            showFormatPreview(text) {
                this.formatPreview.textContent = text;
                this.formatPreview.classList.add('show');
            }

            calculateProgress(value) {
                if (this.type === 'email') {
                    const hasAt = value.includes('@');
                    const hasDot = value.includes('.') && value.indexOf('.') > value.indexOf('@');
                    return hasAt && hasDot ? 100 : hasAt ? 50 : value.length > 0 ? 25 : 0;
                } else if (this.type === 'phone') {
                    const digits = value.replace(/\D/g, '');
                    return Math.min((digits.length / 10) * 100, 100);
                } else if (this.type === 'creditcard') {
                    const digits = value.replace(/\s/g, '');
                    return Math.min((digits.length / 16) * 100, 100);
                } else if (this.type === 'username') {
                    const validLength = value.length >= 3 && value.length <= 20;
                    const validChars = /^[a-zA-Z0-9_]*$/.test(value);
                    return validLength && validChars ? 100 : value.length > 0 ? 50 : 0;
                }
                return 0;
            }

            validate() {
                const value = this.input.value;
                const hints = this.group.querySelectorAll('.validation-hint');
                
                hints.forEach(hint => {
                    const rule = hint.dataset.rule;
                    const icon = hint.querySelector('.hint-icon');
                    let isValid = false;
                    
                    if (this.type === 'email') {
                        if (rule === '@') {
                            isValid = value.includes('@');
                        } else if (rule === 'domain') {
                            isValid = /\.[a-z]{2,}$/i.test(value);
                        }
                    } else if (this.type === 'phone') {
                        const digits = value.replace(/\D/g, '');
                        if (rule === 'digits') {
                            isValid = digits.length === 10;
                        } else if (rule === 'format') {
                            isValid = /^\(\d{3}\) \d{3}-\d{4}$/.test(value);
                        }
                    } else if (this.type === 'creditcard') {
                        const digits = value.replace(/\s/g, '');
                        if (rule === 'length') {
                            isValid = digits.length === 16;
                        } else if (rule === 'luhn') {
                            isValid = this.luhnCheck(digits);
                        }
                    } else if (this.type === 'username') {
                        if (rule === 'length') {
                            isValid = value.length >= 3 && value.length <= 20;
                        } else if (rule === 'alphanumeric') {
                            isValid = /^[a-zA-Z0-9_]+$/.test(value);
                        }
                    }
                    
                    icon.classList.remove('pending', 'valid', 'invalid');
                    if (value.length > 0) {
                        icon.classList.add(isValid ? 'valid' : 'invalid');
                        icon.textContent = isValid ? '✓' : '✗';
                    } else {
                        icon.classList.add('pending');
                        icon.textContent = hint.textContent.trim().charAt(0);
                    }
                });
            }

            luhnCheck(digits) {
                if (digits.length !== 16) return false;
                
                let sum = 0;
                let isEven = false;
                
                for (let i = digits.length - 1; i >= 0; i--) {
                    let digit = parseInt(digits[i]);
                    
                    if (isEven) {
                        digit *= 2;
                        if (digit > 9) {
                            digit -= 9;
                        }
                    }
                    
                    sum += digit;
                    isEven = !isEven;
                }
                
                return sum % 10 === 0;
            }

            updateHelper(progress) {
                this.helper.classList.remove('happy', 'worried');
                
                if (progress === 100) {
                    this.helper.classList.add('happy', 'celebrate');
                    this.wrapper.classList.add('success');
                    this.createConfetti();
                    this.playSound('success');
                    
                    setTimeout(() => {
                        this.helper.classList.remove('celebrate');
                        this.wrapper.classList.remove('success');
                    }, 600);
                } else if (progress > 50) {
                    this.helper.classList.add('happy');
                } else if (progress > 0) {
                    this.helper.classList.add('worried');
                }
            }

            setupAutocomplete() {
                const suggestionData = {
                    email: ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'icloud.com'],
                    username: ['_gamer', '_pro', '_master', '_ninja', '_wizard', '123', '2024', '_cool']
                };
                
                if (suggestionData[this.type]) {
                    this.input.addEventListener('input', () => {
                        this.showSuggestions(this.input.value);
                    });
                }
            }

            showSuggestions(value) {
                if (!this.suggestions) return;
                
                const suggestionData = {
                    email: ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'icloud.com'],
                    username: ['_gamer', '_pro', '_master', '_ninja', '_wizard', '123', '2024', '_cool']
                };
                
                const data = suggestionData[this.type];
                if (!data || value.length < 3) {
                    this.suggestions.classList.remove('show');
                    return;
                }
                
                let suggestions = [];
                
                if (this.type === 'email' && value.includes('@') && !value.includes('.')) {
                    const [user, partial] = value.split('@');
                    suggestions = data
                        .filter(domain => domain.startsWith(partial))
                        .map(domain => `${user}@${domain}`);
                } else if (this.type === 'username') {
                    suggestions = data.map(suffix => value + suffix);
                }
                
                if (suggestions.length > 0) {
                    this.suggestions.innerHTML = suggestions
                        .slice(0, 3)
                        .map(s => `<div class="suggestion">${s}</div>`)
                        .join('');
                    
                    this.suggestions.classList.add('show');
                    
                    this.suggestions.querySelectorAll('.suggestion').forEach(el => {
                        el.addEventListener('click', () => {
                            this.input.value = el.textContent;
                            this.suggestions.classList.remove('show');
                            this.handleInput();
                            this.playSound('select');
                        });
                    });
                } else {
                    this.suggestions.classList.remove('show');
                }
            }

            createConfetti() {
                const colors = ['#ffd93d', '#6bcf7f', '#e74c3c', '#3498db', '#9b59b6'];
                const confettiCount = 20;
                
                for (let i = 0; i < confettiCount; i++) {
                    setTimeout(() => {
                        const confetti = document.createElement('div');
                        confetti.className = 'confetti';
                        
                        const piece = document.createElement('div');
                        piece.className = 'confetti-piece';
                        piece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                        piece.style.left = Math.random() * 100 + '%';
                        piece.style.top = Math.random() * 100 + '%';
                        
                        confetti.appendChild(piece);
                        document.body.appendChild(confetti);
                        
                        setTimeout(() => confetti.remove(), 1000);
                    }, i * 50);
                }
            }

            playSound(type) {
                // Sound effects would be implemented here
                // Using Web Audio API or audio elements
            }
        }

        // Initialize all input groups
        document.querySelectorAll('.input-group').forEach(group => {
            new PlayfulInput(group);
        });

        // Form submission
        document.getElementById('playfulForm').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const button = e.target.querySelector('.submit-button');
            const rect = button.getBoundingClientRect();
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.width = ripple.style.height = '40px';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            
            button.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
            
            // Create celebration
            document.querySelectorAll('.helper-character').forEach(helper => {
                helper.classList.add('celebrate');
                setTimeout(() => helper.classList.remove('celebrate'), 600);
            });
            
            // Success message
            alert('🎉 Form submitted with maximum joy! 🎉');
        });

        // Add some ambient animation
        setInterval(() => {
            const helpers = document.querySelectorAll('.helper-character.show:not(.celebrate)');
            const randomHelper = helpers[Math.floor(Math.random() * helpers.length)];
            
            if (randomHelper) {
                randomHelper.style.transform = 'translateY(-50%) rotate(-5deg)';
                setTimeout(() => {
                    randomHelper.style.transform = 'translateY(-50%) rotate(5deg)';
                    setTimeout(() => {
                        randomHelper.style.transform = 'translateY(-50%) rotate(0deg)';
                    }, 200);
                }, 200);
            }
        }, 3000);
    </script>
</body>
</html>