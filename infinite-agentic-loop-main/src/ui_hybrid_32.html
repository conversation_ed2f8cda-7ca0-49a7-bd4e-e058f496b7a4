<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ocean Depths File Explorer - Hybrid UI 32</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #001a33 0%, #002244 20%, #003366 50%, #004488 80%, #0055aa 100%);
            color: #e0f4ff;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Underwater caustic light effect */
        .caustics {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.3;
            mix-blend-mode: screen;
            background: 
                radial-gradient(ellipse at 20% 30%, rgba(100, 200, 255, 0.3) 0%, transparent 40%),
                radial-gradient(ellipse at 80% 60%, rgba(150, 220, 255, 0.2) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 80%, rgba(180, 240, 255, 0.25) 0%, transparent 45%);
            animation: causticWave 20s ease-in-out infinite;
        }

        @keyframes causticWave {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(-20px, 10px) scale(1.1); }
            66% { transform: translate(15px, -15px) scale(0.95); }
        }

        /* Bubble particles */
        .bubbles {
            position: fixed;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .bubble {
            position: absolute;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(200, 230, 255, 0.4));
            border-radius: 50%;
            animation: floatUp 8s infinite ease-in-out;
        }

        @keyframes floatUp {
            0% {
                transform: translateY(100vh) translateX(0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
                transform: translateY(90vh) translateX(10px) scale(1);
            }
            90% {
                opacity: 0.3;
                transform: translateY(-10vh) translateX(-20px) scale(1.2);
            }
            100% {
                opacity: 0;
                transform: translateY(-20vh) translateX(-30px) scale(1.5);
            }
        }

        /* Main container */
        .ocean-explorer {
            height: 100vh;
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            gap: 20px;
            padding: 20px;
            position: relative;
            z-index: 10;
        }

        /* Depth navigation panel */
        .depth-nav {
            background: linear-gradient(180deg, rgba(0, 40, 80, 0.9) 0%, rgba(0, 30, 60, 0.8) 100%);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(100, 200, 255, 0.3);
            overflow-y: auto;
            position: relative;
        }

        .depth-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 60, 120, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(100, 200, 255, 0.4);
        }

        .depth-meter {
            width: 50px;
            height: 150px;
            background: linear-gradient(180deg, #00ccff 0%, #0088cc 50%, #004488 100%);
            border-radius: 25px;
            position: relative;
            overflow: hidden;
        }

        .depth-level {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(100, 200, 255, 0.6) 100%);
            transition: height 0.5s ease-out;
            border-radius: 0 0 25px 25px;
        }

        .depth-info {
            flex: 1;
        }

        .depth-info h3 {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .depth-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ddff;
            text-shadow: 0 0 10px rgba(0, 221, 255, 0.5);
        }

        /* Folder structure with ocean zones */
        .ocean-zone {
            margin-bottom: 15px;
        }

        .zone-header {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background: rgba(0, 80, 160, 0.3);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .zone-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(100, 200, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .zone-header:hover::before {
            left: 100%;
        }

        .zone-header:hover {
            background: rgba(0, 100, 200, 0.4);
            transform: translateX(5px);
        }

        .zone-icon {
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, rgba(100, 200, 255, 0.8) 0%, rgba(0, 100, 200, 0.4) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .zone-items {
            margin-left: 50px;
            margin-top: 10px;
            display: none;
        }

        .zone-items.active {
            display: block;
            animation: depthReveal 0.5s ease-out;
        }

        @keyframes depthReveal {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            margin-bottom: 5px;
            background: rgba(0, 60, 120, 0.2);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-item:hover {
            background: rgba(0, 100, 200, 0.3);
            transform: translateX(10px);
            box-shadow: 0 0 20px rgba(100, 200, 255, 0.3);
        }

        .file-item.selected {
            background: rgba(0, 150, 255, 0.4);
            border: 1px solid rgba(100, 200, 255, 0.6);
        }

        /* Sonar search panel */
        .sonar-search {
            background: radial-gradient(circle at center, rgba(0, 60, 120, 0.9) 0%, rgba(0, 40, 80, 0.8) 100%);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(100, 200, 255, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .search-input-container {
            width: 100%;
            margin-bottom: 30px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            background: rgba(0, 40, 80, 0.6);
            border: 2px solid rgba(100, 200, 255, 0.4);
            border-radius: 30px;
            color: #e0f4ff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #00ddff;
            box-shadow: 0 0 30px rgba(0, 221, 255, 0.4);
            background: rgba(0, 60, 120, 0.7);
        }

        .search-button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #00ddff 0%, #0099cc 100%);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .search-button:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 0 20px rgba(0, 221, 255, 0.6);
        }

        .search-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        /* Sonar display */
        .sonar-display {
            width: 300px;
            height: 300px;
            position: relative;
            margin-bottom: 30px;
        }

        .sonar-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px solid rgba(100, 200, 255, 0.3);
            border-radius: 50%;
            pointer-events: none;
        }

        .sonar-ping {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(0, 221, 255, 0.8);
            opacity: 0;
            pointer-events: none;
        }

        .sonar-ping.active {
            animation: sonarPing 2s ease-out;
        }

        @keyframes sonarPing {
            0% {
                width: 20px;
                height: 20px;
                opacity: 1;
                box-shadow: 0 0 0 0 rgba(0, 221, 255, 0.8);
            }
            100% {
                width: 300px;
                height: 300px;
                opacity: 0;
                box-shadow: 0 0 0 20px rgba(0, 221, 255, 0);
            }
        }

        .sonar-blip {
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #00ff88 0%, #00cc66 100%);
            border-radius: 50%;
            opacity: 0;
            animation: blipAppear 0.5s ease-out forwards;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        @keyframes blipAppear {
            0% {
                opacity: 0;
                transform: scale(0);
            }
            50% {
                opacity: 1;
                transform: scale(1.5);
            }
            100% {
                opacity: 0.8;
                transform: scale(1);
            }
        }

        /* Preview bubbles */
        .preview-area {
            background: linear-gradient(180deg, rgba(0, 50, 100, 0.9) 0%, rgba(0, 40, 80, 0.8) 100%);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(100, 200, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .preview-bubble {
            position: absolute;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.9), rgba(100, 200, 255, 0.6));
            border-radius: 50%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: all 0.5s ease;
            animation: bubbleFloat 4s ease-in-out infinite;
            box-shadow: 
                inset 0 10px 20px rgba(255, 255, 255, 0.5),
                0 5px 15px rgba(0, 100, 200, 0.3);
        }

        @keyframes bubbleFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-10px) rotate(5deg); }
            75% { transform: translateY(10px) rotate(-5deg); }
        }

        .preview-bubble:hover {
            transform: scale(1.1);
            box-shadow: 
                inset 0 10px 30px rgba(255, 255, 255, 0.7),
                0 10px 30px rgba(0, 150, 255, 0.5);
        }

        .bubble-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .bubble-title {
            font-size: 14px;
            font-weight: bold;
            color: #003366;
            margin-bottom: 5px;
        }

        .bubble-info {
            font-size: 12px;
            color: #004488;
            opacity: 0.8;
        }

        /* Coral decorations */
        .coral-decoration {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 150px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 150"><path d="M0,150 Q100,100 200,120 T400,100 Q500,80 600,110 T800,90 Q900,70 1000,100 T1200,80 L1200,150 Z" fill="rgba(0,80,160,0.3)"/><path d="M0,150 Q150,120 300,130 T600,110 Q750,90 900,120 T1200,100 L1200,150 Z" fill="rgba(0,60,120,0.4)"/></svg>') no-repeat bottom;
            background-size: cover;
            pointer-events: none;
        }

        /* Loading animation */
        .loading-fish {
            position: fixed;
            width: 60px;
            height: 30px;
            background: linear-gradient(90deg, #00ddff 0%, #0099cc 50%, #006699 100%);
            border-radius: 50% 0 50% 0;
            opacity: 0;
            pointer-events: none;
        }

        .loading-fish.swimming {
            animation: swimAcross 3s ease-in-out;
        }

        @keyframes swimAcross {
            0% {
                left: -60px;
                top: 50%;
                opacity: 0;
                transform: translateY(-50%) scaleX(1);
            }
            10% {
                opacity: 1;
            }
            50% {
                transform: translateY(-50%) scaleX(1) rotate(5deg);
            }
            51% {
                transform: translateY(-50%) scaleX(-1) rotate(5deg);
            }
            90% {
                opacity: 1;
            }
            100% {
                left: 100%;
                top: 50%;
                opacity: 0;
                transform: translateY(-50%) scaleX(-1);
            }
        }

        /* Bioluminescent glow effects */
        .glow-effect {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
            animation: glowPulse 3s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.2);
            }
        }

        /* Status bar */
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: rgba(0, 40, 80, 0.9);
            border-top: 1px solid rgba(100, 200, 255, 0.3);
            display: flex;
            align-items: center;
            padding: 0 20px;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-right: 30px;
            font-size: 14px;
            color: rgba(224, 244, 255, 0.8);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="caustics"></div>
    <div class="bubbles" id="bubbleContainer"></div>
    
    <div class="ocean-explorer">
        <!-- Depth Navigation Panel -->
        <div class="depth-nav">
            <div class="depth-indicator">
                <div class="depth-meter">
                    <div class="depth-level" id="depthLevel" style="height: 30%;"></div>
                </div>
                <div class="depth-info">
                    <h3>Current Depth</h3>
                    <div class="depth-value" id="depthValue">300m</div>
                </div>
            </div>
            
            <div class="ocean-zone">
                <div class="zone-header" onclick="toggleZone(this)">
                    <div class="zone-icon">🌊</div>
                    <div>
                        <div style="font-weight: bold;">Surface Waters</div>
                        <div style="font-size: 12px; opacity: 0.7;">0-200m • Recent Files</div>
                    </div>
                </div>
                <div class="zone-items">
                    <div class="file-item" data-file="presentation.pptx">
                        <span>📊</span>
                        <span>Q4 Presentation.pptx</span>
                    </div>
                    <div class="file-item" data-file="report.pdf">
                        <span>📄</span>
                        <span>Annual Report.pdf</span>
                    </div>
                    <div class="file-item" data-file="data.xlsx">
                        <span>📈</span>
                        <span>Analytics Data.xlsx</span>
                    </div>
                </div>
            </div>
            
            <div class="ocean-zone">
                <div class="zone-header" onclick="toggleZone(this)">
                    <div class="zone-icon">🐟</div>
                    <div>
                        <div style="font-weight: bold;">Twilight Zone</div>
                        <div style="font-size: 12px; opacity: 0.7;">200-1000m • Projects</div>
                    </div>
                </div>
                <div class="zone-items">
                    <div class="file-item" data-file="webapp.zip">
                        <span>💼</span>
                        <span>WebApp Project.zip</span>
                    </div>
                    <div class="file-item" data-file="designs.sketch">
                        <span>🎨</span>
                        <span>UI Designs.sketch</span>
                    </div>
                    <div class="file-item" data-file="database.sql">
                        <span>🗄️</span>
                        <span>Database Schema.sql</span>
                    </div>
                </div>
            </div>
            
            <div class="ocean-zone">
                <div class="zone-header" onclick="toggleZone(this)">
                    <div class="zone-icon">🦑</div>
                    <div>
                        <div style="font-weight: bold;">Midnight Zone</div>
                        <div style="font-size: 12px; opacity: 0.7;">1000-4000m • Archives</div>
                    </div>
                </div>
                <div class="zone-items">
                    <div class="file-item" data-file="backup2023.tar">
                        <span>📦</span>
                        <span>Backup 2023.tar</span>
                    </div>
                    <div class="file-item" data-file="legacy.zip">
                        <span>🗂️</span>
                        <span>Legacy System.zip</span>
                    </div>
                    <div class="file-item" data-file="docs.pdf">
                        <span>📚</span>
                        <span>Documentation.pdf</span>
                    </div>
                </div>
            </div>
            
            <div class="ocean-zone">
                <div class="zone-header" onclick="toggleZone(this)">
                    <div class="zone-icon">🐙</div>
                    <div>
                        <div style="font-weight: bold;">Abyssal Zone</div>
                        <div style="font-size: 12px; opacity: 0.7;">4000-6000m • System</div>
                    </div>
                </div>
                <div class="zone-items">
                    <div class="file-item" data-file="system.log">
                        <span>⚙️</span>
                        <span>System.log</span>
                    </div>
                    <div class="file-item" data-file="config.json">
                        <span>🔧</span>
                        <span>Config.json</span>
                    </div>
                    <div class="file-item" data-file="kernel.img">
                        <span>💾</span>
                        <span>Kernel.img</span>
                    </div>
                </div>
            </div>
            
            <div class="ocean-zone">
                <div class="zone-header" onclick="toggleZone(this)">
                    <div class="zone-icon">🌋</div>
                    <div>
                        <div style="font-weight: bold;">Hadal Zone</div>
                        <div style="font-size: 12px; opacity: 0.7;">6000m+ • Deep Storage</div>
                    </div>
                </div>
                <div class="zone-items">
                    <div class="file-item" data-file="ancient.dat">
                        <span>🗿</span>
                        <span>Ancient.dat</span>
                    </div>
                    <div class="file-item" data-file="core.bin">
                        <span>🔮</span>
                        <span>Core.bin</span>
                    </div>
                    <div class="file-item" data-file="mystery.enc">
                        <span>🔐</span>
                        <span>Mystery.enc</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sonar Search Panel -->
        <div class="sonar-search">
            <div class="search-input-container">
                <input type="text" class="search-input" id="searchInput" placeholder="Deploy sonar ping...">
                <button class="search-button" onclick="performSonarSearch()">
                    <span style="color: white;">🔍</span>
                </button>
            </div>
            
            <div class="sonar-display" id="sonarDisplay">
                <div class="sonar-ring" style="width: 100px; height: 100px;"></div>
                <div class="sonar-ring" style="width: 200px; height: 200px;"></div>
                <div class="sonar-ring" style="width: 300px; height: 300px;"></div>
                <div class="sonar-ping" id="sonarPing"></div>
            </div>
            
            <div style="width: 100%; max-height: 300px; overflow-y: auto;">
                <div id="searchResults" style="display: flex; flex-direction: column; gap: 10px;">
                    <!-- Search results will appear here -->
                </div>
            </div>
            
            <div class="coral-decoration"></div>
        </div>
        
        <!-- Preview Area -->
        <div class="preview-area" id="previewArea">
            <h2 style="text-align: center; margin-bottom: 20px; color: #00ddff; text-shadow: 0 0 20px rgba(0, 221, 255, 0.5);">
                Discovery Chamber
            </h2>
            <div id="previewBubbles" style="position: relative; height: 400px;">
                <!-- Preview bubbles will appear here -->
            </div>
            
            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px;">
                <div style="background: rgba(0, 60, 120, 0.6); padding: 15px; border-radius: 15px; border: 1px solid rgba(100, 200, 255, 0.4);">
                    <h3 style="font-size: 14px; margin-bottom: 10px; color: #00ddff;">Current Selection</h3>
                    <div id="selectionInfo" style="font-size: 12px; opacity: 0.8;">
                        No file selected. Click on files to preview.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-item">
            <div class="status-indicator"></div>
            <span>System Online</span>
        </div>
        <div class="status-item">
            <span>📡</span>
            <span id="sonarStatus">Sonar Ready</span>
        </div>
        <div class="status-item">
            <span>🌡️</span>
            <span>Pressure: <span id="pressure">1.0</span> ATM</span>
        </div>
        <div class="status-item">
            <span>💧</span>
            <span>Depth: <span id="currentDepth">300</span>m</span>
        </div>
    </div>
    
    <!-- Loading Fish -->
    <div class="loading-fish" id="loadingFish"></div>
    
    <!-- Glow Effects -->
    <div class="glow-effect" style="width: 100px; height: 100px; background: radial-gradient(circle, rgba(0, 255, 136, 0.4) 0%, transparent 70%); top: 20%; left: 10%;"></div>
    <div class="glow-effect" style="width: 150px; height: 150px; background: radial-gradient(circle, rgba(0, 150, 255, 0.3) 0%, transparent 70%); top: 60%; right: 15%; animation-delay: 1.5s;"></div>
    <div class="glow-effect" style="width: 80px; height: 80px; background: radial-gradient(circle, rgba(255, 100, 200, 0.3) 0%, transparent 70%); bottom: 30%; left: 40%; animation-delay: 0.8s;"></div>
    
    <script>
        // Ocean file system data
        const oceanFiles = {
            'Surface Waters': [
                { name: 'Q4 Presentation.pptx', icon: '📊', size: '4.2 MB', modified: '2 hours ago', type: 'presentation' },
                { name: 'Annual Report.pdf', icon: '📄', size: '1.8 MB', modified: '1 day ago', type: 'document' },
                { name: 'Analytics Data.xlsx', icon: '📈', size: '2.5 MB', modified: '3 hours ago', type: 'spreadsheet' },
                { name: 'Meeting Notes.doc', icon: '📝', size: '256 KB', modified: '30 minutes ago', type: 'document' },
                { name: 'Team Photo.jpg', icon: '📸', size: '3.1 MB', modified: '2 days ago', type: 'image' }
            ],
            'Twilight Zone': [
                { name: 'WebApp Project.zip', icon: '💼', size: '45.6 MB', modified: '1 week ago', type: 'archive' },
                { name: 'UI Designs.sketch', icon: '🎨', size: '12.3 MB', modified: '3 days ago', type: 'design' },
                { name: 'Database Schema.sql', icon: '🗄️', size: '45 KB', modified: '5 days ago', type: 'database' },
                { name: 'API Documentation.md', icon: '📖', size: '128 KB', modified: '4 days ago', type: 'document' },
                { name: 'Test Results.xml', icon: '🧪', size: '89 KB', modified: '2 days ago', type: 'data' }
            ],
            'Midnight Zone': [
                { name: 'Backup 2023.tar', icon: '📦', size: '2.4 GB', modified: '1 month ago', type: 'archive' },
                { name: 'Legacy System.zip', icon: '🗂️', size: '856 MB', modified: '3 months ago', type: 'archive' },
                { name: 'Documentation.pdf', icon: '📚', size: '15.2 MB', modified: '2 months ago', type: 'document' },
                { name: 'Old Codebase.tar.gz', icon: '💾', size: '456 MB', modified: '6 months ago', type: 'archive' },
                { name: 'Archive Photos.zip', icon: '🖼️', size: '1.2 GB', modified: '4 months ago', type: 'archive' }
            ],
            'Abyssal Zone': [
                { name: 'System.log', icon: '⚙️', size: '45 MB', modified: '1 hour ago', type: 'log' },
                { name: 'Config.json', icon: '🔧', size: '12 KB', modified: '1 week ago', type: 'config' },
                { name: 'Kernel.img', icon: '💾', size: '125 MB', modified: '2 weeks ago', type: 'system' },
                { name: 'Error.dump', icon: '🚨', size: '89 MB', modified: '3 days ago', type: 'log' },
                { name: 'Registry.dat', icon: '📋', size: '34 MB', modified: '1 month ago', type: 'system' }
            ],
            'Hadal Zone': [
                { name: 'Ancient.dat', icon: '🗿', size: '???', modified: 'Unknown', type: 'mystery' },
                { name: 'Core.bin', icon: '🔮', size: '???', modified: 'Eternal', type: 'mystery' },
                { name: 'Mystery.enc', icon: '🔐', size: '???', modified: 'Timeless', type: 'encrypted' },
                { name: 'Void.null', icon: '⚫', size: '0 B', modified: 'Never', type: 'mystery' },
                { name: 'Origin.genesis', icon: '🌌', size: '∞', modified: 'Beginning', type: 'mystery' }
            ]
        };
        
        let currentDepth = 300;
        let selectedFiles = [];
        
        // Initialize bubbles
        function createBubble() {
            const bubble = document.createElement('div');
            bubble.className = 'bubble';
            const size = Math.random() * 20 + 10;
            bubble.style.width = size + 'px';
            bubble.style.height = size + 'px';
            bubble.style.left = Math.random() * 100 + '%';
            bubble.style.animationDelay = Math.random() * 8 + 's';
            bubble.style.animationDuration = (Math.random() * 4 + 6) + 's';
            return bubble;
        }
        
        // Create initial bubbles
        const bubbleContainer = document.getElementById('bubbleContainer');
        for (let i = 0; i < 20; i++) {
            bubbleContainer.appendChild(createBubble());
        }
        
        // Add new bubbles periodically
        setInterval(() => {
            if (bubbleContainer.children.length < 30) {
                bubbleContainer.appendChild(createBubble());
            }
            // Remove old bubbles
            const bubbles = bubbleContainer.querySelectorAll('.bubble');
            bubbles.forEach(bubble => {
                const rect = bubble.getBoundingClientRect();
                if (rect.bottom < 0) {
                    bubble.remove();
                }
            });
        }, 2000);
        
        // Toggle ocean zones
        function toggleZone(header) {
            const items = header.nextElementSibling;
            items.classList.toggle('active');
            
            // Update depth based on zone
            const zoneName = header.querySelector('div:nth-child(2) > div').textContent;
            const depths = {
                'Surface Waters': 100,
                'Twilight Zone': 600,
                'Midnight Zone': 2500,
                'Abyssal Zone': 5000,
                'Hadal Zone': 8000
            };
            
            if (depths[zoneName]) {
                updateDepth(depths[zoneName]);
            }
        }
        
        // Update depth indicator
        function updateDepth(newDepth) {
            currentDepth = newDepth;
            const depthPercent = Math.min((newDepth / 10000) * 100, 100);
            document.getElementById('depthLevel').style.height = depthPercent + '%';
            document.getElementById('depthValue').textContent = newDepth + 'm';
            document.getElementById('currentDepth').textContent = newDepth;
            
            // Update pressure
            const pressure = 1 + (newDepth / 1000);
            document.getElementById('pressure').textContent = pressure.toFixed(1);
            
            // Update background darkness
            const darkness = Math.min(newDepth / 8000, 0.8);
            document.body.style.filter = `brightness(${1 - darkness * 0.5})`;
        }
        
        // File selection
        document.addEventListener('click', function(e) {
            if (e.target.closest('.file-item')) {
                const fileItem = e.target.closest('.file-item');
                fileItem.classList.toggle('selected');
                
                const fileName = fileItem.querySelector('span:last-child').textContent;
                const fileIcon = fileItem.querySelector('span:first-child').textContent;
                
                if (fileItem.classList.contains('selected')) {
                    createPreviewBubble(fileName, fileIcon);
                    selectedFiles.push(fileName);
                } else {
                    removePreviewBubble(fileName);
                    selectedFiles = selectedFiles.filter(f => f !== fileName);
                }
                
                updateSelectionInfo();
            }
        });
        
        // Create preview bubble
        function createPreviewBubble(fileName, icon) {
            const previewArea = document.getElementById('previewBubbles');
            const bubble = document.createElement('div');
            bubble.className = 'preview-bubble';
            bubble.setAttribute('data-file', fileName);
            
            const size = Math.random() * 60 + 100;
            bubble.style.width = size + 'px';
            bubble.style.height = size + 'px';
            bubble.style.left = Math.random() * (previewArea.offsetWidth - size) + 'px';
            bubble.style.top = Math.random() * (previewArea.offsetHeight - size) + 'px';
            bubble.style.animationDelay = Math.random() * 2 + 's';
            
            bubble.innerHTML = `
                <div class="bubble-icon">${icon}</div>
                <div class="bubble-title">${fileName.split('.')[0]}</div>
                <div class="bubble-info">.${fileName.split('.').pop()}</div>
            `;
            
            previewArea.appendChild(bubble);
            
            // Animate bubble appearance
            setTimeout(() => {
                bubble.style.opacity = '1';
                bubble.style.transform = 'scale(1)';
            }, 50);
        }
        
        // Remove preview bubble
        function removePreviewBubble(fileName) {
            const bubble = document.querySelector(`[data-file="${fileName}"]`);
            if (bubble) {
                bubble.style.opacity = '0';
                bubble.style.transform = 'scale(0)';
                setTimeout(() => bubble.remove(), 500);
            }
        }
        
        // Update selection info
        function updateSelectionInfo() {
            const info = document.getElementById('selectionInfo');
            if (selectedFiles.length === 0) {
                info.textContent = 'No file selected. Click on files to preview.';
            } else {
                info.innerHTML = `<strong>${selectedFiles.length} file(s) selected:</strong><br>${selectedFiles.join(', ')}`;
            }
        }
        
        // Sonar search
        function performSonarSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sonarPing = document.getElementById('sonarPing');
            const sonarDisplay = document.getElementById('sonarDisplay');
            const resultsContainer = document.getElementById('searchResults');
            const loadingFish = document.getElementById('loadingFish');
            
            if (!searchTerm) return;
            
            // Update status
            document.getElementById('sonarStatus').textContent = 'Scanning...';
            
            // Show loading fish
            loadingFish.classList.add('swimming');
            setTimeout(() => loadingFish.classList.remove('swimming'), 3000);
            
            // Trigger sonar ping
            sonarPing.classList.remove('active');
            void sonarPing.offsetWidth; // Force reflow
            sonarPing.classList.add('active');
            
            // Clear previous results
            resultsContainer.innerHTML = '';
            const existingBlips = sonarDisplay.querySelectorAll('.sonar-blip');
            existingBlips.forEach(blip => blip.remove());
            
            // Search through all files
            setTimeout(() => {
                const results = [];
                Object.entries(oceanFiles).forEach(([zone, files]) => {
                    files.forEach(file => {
                        if (file.name.toLowerCase().includes(searchTerm) || 
                            file.type.toLowerCase().includes(searchTerm)) {
                            results.push({ ...file, zone });
                        }
                    });
                });
                
                // Display results
                results.forEach((result, index) => {
                    // Add blip to sonar
                    setTimeout(() => {
                        const blip = document.createElement('div');
                        blip.className = 'sonar-blip';
                        const angle = (index / results.length) * 360;
                        const distance = Math.random() * 120 + 30;
                        const x = 150 + Math.cos(angle * Math.PI / 180) * distance;
                        const y = 150 + Math.sin(angle * Math.PI / 180) * distance;
                        blip.style.left = x + 'px';
                        blip.style.top = y + 'px';
                        sonarDisplay.appendChild(blip);
                    }, 500 + index * 200);
                    
                    // Add result item
                    const resultItem = document.createElement('div');
                    resultItem.className = 'file-item';
                    resultItem.innerHTML = `
                        <span>${result.icon}</span>
                        <div style="flex: 1;">
                            <div>${result.name}</div>
                            <div style="font-size: 11px; opacity: 0.6;">${result.zone} • ${result.size}</div>
                        </div>
                    `;
                    resultsContainer.appendChild(resultItem);
                });
                
                // Update status
                document.getElementById('sonarStatus').textContent = `Found ${results.length} items`;
                
                if (results.length === 0) {
                    resultsContainer.innerHTML = '<div style="text-align: center; opacity: 0.6;">No treasures found in these depths...</div>';
                }
            }, 1000);
        }
        
        // Keyboard shortcuts
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSonarSearch();
            }
        });
        
        // Ambient animations
        setInterval(() => {
            // Random depth changes
            if (Math.random() < 0.1) {
                const variation = (Math.random() - 0.5) * 50;
                updateDepth(Math.max(0, Math.min(10000, currentDepth + variation)));
            }
        }, 5000);
        
        // Initialize
        updateDepth(300);
    </script>
</body>
</html>