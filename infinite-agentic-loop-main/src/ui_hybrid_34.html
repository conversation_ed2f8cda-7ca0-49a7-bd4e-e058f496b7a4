<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wabi-Sabi Navigation Garden</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: #f5f2ed;
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* Rice Paper Texture Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(ellipse at 20% 30%, rgba(188, 180, 164, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 70%, rgba(188, 180, 164, 0.08) 0%, transparent 50%),
                radial-gradient(ellipse at 10% 90%, rgba(188, 180, 164, 0.06) 0%, transparent 40%);
            pointer-events: none;
            opacity: 0.8;
        }

        /* Subtle Paper Fibers */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                repeating-linear-gradient(90deg, transparent, transparent 3px, rgba(188, 180, 164, 0.03) 3px, rgba(188, 180, 164, 0.03) 6px),
                repeating-linear-gradient(0deg, transparent, transparent 3px, rgba(188, 180, 164, 0.03) 3px, rgba(188, 180, 164, 0.03) 6px);
            pointer-events: none;
        }

        /* Main Container - Garden Layout */
        .garden-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 1;
        }

        /* Title with Calligraphy Style */
        .garden-title {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .garden-title h1 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #2c2c2c;
            letter-spacing: 0.2em;
            position: relative;
            display: inline-block;
        }

        .garden-title h1::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 2px;
            background: #2c2c2c;
            opacity: 0.2;
            border-radius: 1px;
        }

        /* Garden Path Navigation */
        .garden-path {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 60px;
            position: relative;
        }

        /* Stepping Stones Navigation */
        .stepping-stones {
            position: relative;
        }

        .stone {
            display: block;
            padding: 20px 30px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(188, 180, 164, 0.3);
            border-radius: 50% 40% 60% 30% / 40% 50% 30% 60%;
            text-decoration: none;
            color: #2c2c2c;
            font-size: 0.95rem;
            letter-spacing: 0.05em;
            position: relative;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .stone::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -30px;
            transform: translateY(-50%);
            width: 20px;
            height: 1px;
            background: rgba(188, 180, 164, 0.3);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stone:hover {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 5px 20px rgba(188, 180, 164, 0.2);
        }

        .stone:hover::before {
            opacity: 1;
        }

        .stone.active {
            background: rgba(255, 255, 255, 0.95);
            color: #1a1a1a;
            font-weight: 500;
        }

        /* Zen Garden Content Area */
        .content-garden {
            position: relative;
        }

        /* Haiku Content Cards */
        .haiku-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(188, 180, 164, 0.2);
            border-radius: 2px;
            padding: 40px;
            margin-bottom: 30px;
            position: relative;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        /* Ink Wash Effect */
        .haiku-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(188, 180, 164, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: scale(0);
            transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .haiku-card:hover::before {
            transform: scale(1.5);
        }

        .haiku-card h2 {
            font-size: 1.8rem;
            font-weight: 300;
            color: #2c2c2c;
            margin-bottom: 20px;
            letter-spacing: 0.1em;
        }

        .haiku-text {
            font-size: 1.1rem;
            line-height: 2;
            color: #4a4a4a;
            font-style: italic;
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border-left: 2px solid rgba(188, 180, 164, 0.2);
            border-right: 2px solid rgba(188, 180, 164, 0.2);
        }

        .haiku-content {
            font-size: 1rem;
            line-height: 1.8;
            color: #4a4a4a;
            margin-bottom: 20px;
        }

        /* Brush Stroke Animation */
        .brush-stroke {
            position: absolute;
            width: 100px;
            height: 2px;
            background: #2c2c2c;
            opacity: 0.1;
            transform-origin: left center;
            animation: brushDraw 3s ease-in-out infinite;
        }

        @keyframes brushDraw {
            0% {
                transform: scaleX(0);
                opacity: 0;
            }
            50% {
                transform: scaleX(1);
                opacity: 0.1;
            }
            100% {
                transform: scaleX(0);
                opacity: 0;
            }
        }

        /* Floating Elements - Cherry Blossoms */
        .sakura {
            position: fixed;
            width: 10px;
            height: 10px;
            background: rgba(255, 192, 203, 0.3);
            border-radius: 50% 0;
            animation: float 10s infinite linear;
            pointer-events: none;
        }

        @keyframes float {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.5;
            }
            90% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(calc(100vh + 100px)) rotate(720deg);
                opacity: 0;
            }
        }

        /* Tea Ceremony Interaction */
        .tea-ceremony {
            position: fixed;
            bottom: 40px;
            right: 40px;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(188, 180, 164, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .tea-ceremony:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(188, 180, 164, 0.3);
        }

        .tea-bowl {
            width: 30px;
            height: 20px;
            border: 2px solid #2c2c2c;
            border-radius: 0 0 15px 15px;
            border-top: none;
            position: relative;
        }

        .tea-bowl::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: #2c2c2c;
            border-radius: 50%;
            opacity: 0;
            animation: steam 2s infinite;
        }

        @keyframes steam {
            0%, 100% {
                opacity: 0;
                transform: translateX(-50%) translateY(0);
            }
            50% {
                opacity: 0.5;
                transform: translateX(-50%) translateY(-10px);
            }
        }

        /* Mindful Transitions */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 1s ease forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Natural Imperfections */
        .imperfection {
            position: absolute;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(188, 180, 164, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .garden-path {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .stepping-stones {
                display: flex;
                overflow-x: auto;
                padding-bottom: 10px;
                margin-bottom: 30px;
            }

            .stone {
                flex-shrink: 0;
                margin-right: 15px;
                margin-bottom: 0;
            }

            .haiku-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="garden-container">
        <div class="garden-title fade-in">
            <h1>侘寂</h1>
            <p style="margin-top: 10px; color: #666; font-size: 0.9rem; letter-spacing: 0.1em;">WABI-SABI NAVIGATION</p>
        </div>

        <div class="garden-path">
            <!-- Stepping Stones Navigation -->
            <nav class="stepping-stones fade-in" style="animation-delay: 0.2s;">
                <a href="#beginning" class="stone active">Beginning</a>
                <a href="#simplicity" class="stone">Simplicity</a>
                <a href="#imperfection" class="stone">Imperfection</a>
                <a href="#impermanence" class="stone">Impermanence</a>
                <a href="#contemplation" class="stone">Contemplation</a>
            </nav>

            <!-- Content Garden -->
            <div class="content-garden">
                <div id="beginning" class="haiku-card fade-in" style="animation-delay: 0.4s;">
                    <div class="brush-stroke" style="top: 20px; right: 40px;"></div>
                    <h2>The Beginning</h2>
                    <div class="haiku-text">
                        Empty bowl awaits<br>
                        Morning light through paper walls<br>
                        Tea ceremony starts
                    </div>
                    <p class="haiku-content">
                        In the philosophy of wabi-sabi, we find beauty in the imperfect, impermanent, and incomplete. 
                        This navigation garden invites you to explore concepts through a mindful journey, where each 
                        step reveals new understanding.
                    </p>
                    <div class="imperfection" style="bottom: 20px; right: 30px;"></div>
                </div>

                <div id="simplicity" class="haiku-card fade-in" style="animation-delay: 0.6s;">
                    <div class="brush-stroke" style="top: 40px; left: 60px; transform: rotate(-5deg);"></div>
                    <h2>Embracing Simplicity</h2>
                    <div class="haiku-text">
                        Single blade of grass<br>
                        Bends with wind, returns to form<br>
                        Strength in gentleness
                    </div>
                    <p class="haiku-content">
                        Simplicity is not merely the absence of clutter, but the presence of clarity. In our digital 
                        gardens, we cultivate spaces where every element serves a purpose, where negative space 
                        speaks as loudly as content.
                    </p>
                    <div class="imperfection" style="top: 50px; left: 40px;"></div>
                </div>

                <div id="imperfection" class="haiku-card fade-in" style="animation-delay: 0.8s;">
                    <div class="brush-stroke" style="bottom: 30px; right: 50px; transform: rotate(3deg);"></div>
                    <h2>Beauty in Imperfection</h2>
                    <div class="haiku-text">
                        Cracked ceramic bowl<br>
                        Gold fills the broken spaces<br>
                        More precious than new
                    </div>
                    <p class="haiku-content">
                        Like kintsugi, the art of golden repair, our imperfections become part of our story. 
                        The asymmetry in design, the natural variations in texture—these are not flaws but 
                        features that give character and authenticity.
                    </p>
                    <div class="imperfection" style="top: 30px; right: 60px;"></div>
                </div>

                <div id="impermanence" class="haiku-card fade-in" style="animation-delay: 1s;">
                    <div class="brush-stroke" style="top: 60px; left: 40px; transform: rotate(-2deg);"></div>
                    <h2>Accepting Impermanence</h2>
                    <div class="haiku-text">
                        Cherry blossoms fall<br>
                        Brief beauty more precious still<br>
                        Memory remains
                    </div>
                    <p class="haiku-content">
                        Nothing is permanent, and therein lies the beauty. Each interaction, each moment of 
                        engagement is unique and fleeting. We design not for eternity, but for the present 
                        moment, knowing that change is the only constant.
                    </p>
                    <div class="imperfection" style="bottom: 40px; left: 50px;"></div>
                </div>

                <div id="contemplation" class="haiku-card fade-in" style="animation-delay: 1.2s;">
                    <div class="brush-stroke" style="bottom: 50px; right: 30px; transform: rotate(1deg);"></div>
                    <h2>Space for Contemplation</h2>
                    <div class="haiku-text">
                        Silent meditation<br>
                        Between breaths, infinity<br>
                        Understanding blooms
                    </div>
                    <p class="haiku-content">
                        In the spaces between, in the pauses and the silence, we find room for contemplation. 
                        This navigation garden is not meant to be rushed through, but experienced mindfully, 
                        allowing each concept to settle like sediment in still water.
                    </p>
                    <div class="imperfection" style="top: 20px; left: 70px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tea Ceremony Button -->
    <div class="tea-ceremony" title="Take a moment to pause">
        <div class="tea-bowl"></div>
    </div>

    <!-- Floating Sakura Petals -->
    <div class="sakura" style="left: 10%; animation-delay: 0s;"></div>
    <div class="sakura" style="left: 30%; animation-delay: 2s;"></div>
    <div class="sakura" style="left: 50%; animation-delay: 4s;"></div>
    <div class="sakura" style="left: 70%; animation-delay: 6s;"></div>
    <div class="sakura" style="left: 90%; animation-delay: 8s;"></div>

    <script>
        // Smooth scroll behavior
        document.querySelectorAll('.stone').forEach(stone => {
            stone.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                // Update active state
                document.querySelectorAll('.stone').forEach(s => s.classList.remove('active'));
                this.classList.add('active');
                
                // Smooth scroll
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // Add subtle animation to the target card
                targetElement.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    targetElement.style.transform = 'scale(1)';
                }, 300);
            });
        });

        // Tea ceremony meditation
        const teaCeremony = document.querySelector('.tea-ceremony');
        let meditating = false;

        teaCeremony.addEventListener('click', function() {
            if (!meditating) {
                meditating = true;
                document.body.style.transition = 'filter 1s ease';
                document.body.style.filter = 'sepia(0.2)';
                
                // Create meditation overlay
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(245, 242, 237, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    opacity: 0;
                    transition: opacity 1s ease;
                `;
                
                overlay.innerHTML = `
                    <div style="text-align: center;">
                        <p style="font-size: 1.5rem; color: #2c2c2c; letter-spacing: 0.2em; margin-bottom: 20px;">一期一会</p>
                        <p style="font-size: 1rem; color: #666; letter-spacing: 0.1em;">One time, one meeting</p>
                        <p style="font-size: 0.9rem; color: #999; margin-top: 40px;">Click to continue...</p>
                    </div>
                `;
                
                document.body.appendChild(overlay);
                
                setTimeout(() => {
                    overlay.style.opacity = '1';
                }, 100);
                
                overlay.addEventListener('click', function() {
                    overlay.style.opacity = '0';
                    document.body.style.filter = 'none';
                    setTimeout(() => {
                        overlay.remove();
                        meditating = false;
                    }, 1000);
                });
            }
        });

        // Intersection Observer for fade-in animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.haiku-card').forEach(card => {
            observer.observe(card);
        });

        // Add natural imperfections on load
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.haiku-card');
            cards.forEach((card, index) => {
                // Slightly rotate each card for natural asymmetry
                const rotation = (Math.random() - 0.5) * 0.5;
                card.style.transform = `rotate(${rotation}deg)`;
                
                // Add subtle box shadow variations
                const shadowIntensity = 0.1 + Math.random() * 0.1;
                card.style.boxShadow = `0 2px 10px rgba(188, 180, 164, ${shadowIntensity})`;
            });
        });

        // Dynamic brush strokes
        setInterval(() => {
            const brushStrokes = document.querySelectorAll('.brush-stroke');
            brushStrokes.forEach(stroke => {
                const delay = Math.random() * 2;
                stroke.style.animationDelay = `${delay}s`;
            });
        }, 5000);
    </script>
</body>
</html>