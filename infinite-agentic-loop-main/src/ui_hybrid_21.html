<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organic Nature Search Hub - Hybrid UI Component</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7e6 0%, #e8f0d6 50%, #dde8cc 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        /* Animated background elements */
        .nature-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .leaf {
            position: absolute;
            width: 40px;
            height: 40px;
            opacity: 0.1;
            animation: float 20s infinite ease-in-out;
        }

        .leaf::before {
            content: '🍃';
            font-size: 30px;
            position: absolute;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(10px) rotate(180deg); }
            75% { transform: translateY(-15px) rotate(270deg); }
        }

        /* Main container */
        .search-ecosystem {
            max-width: 900px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        /* Search bar with organic styling */
        .search-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 25px;
            box-shadow: 0 10px 40px rgba(76, 114, 29, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .search-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(139, 195, 74, 0.05) 0%, transparent 70%);
            animation: ripple 15s infinite ease-out;
        }

        @keyframes ripple {
            0% { transform: scale(0.8); opacity: 0.5; }
            50% { transform: scale(1.2); opacity: 0.3; }
            100% { transform: scale(0.8); opacity: 0.5; }
        }

        .search-wrapper {
            position: relative;
            z-index: 1;
        }

        .search-input-group {
            position: relative;
            display: flex;
            align-items: center;
            background: #f9fdf5;
            border: 2px solid #c5d9b4;
            border-radius: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .search-input-group:focus-within {
            border-color: #7cb342;
            box-shadow: 0 0 0 4px rgba(124, 179, 66, 0.1);
        }

        .search-icon {
            padding: 0 20px;
            color: #689f38;
            font-size: 20px;
        }

        .search-input {
            flex: 1;
            padding: 15px 0;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
            color: #2e4a1c;
        }

        .search-input::placeholder {
            color: #8ba673;
        }

        /* Filter tags */
        .filter-tags {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .filter-tag {
            background: linear-gradient(135deg, #dcedc8 0%, #c5e1a5 100%);
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: #33691e;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .filter-tag::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(124, 179, 66, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .filter-tag:hover::before {
            width: 100px;
            height: 100px;
        }

        .filter-tag.active {
            background: linear-gradient(135deg, #8bc34a 0%, #689f38 100%);
            color: white;
        }

        /* Autocomplete dropdown */
        .autocomplete-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px;
            margin-top: 10px;
            box-shadow: 0 10px 30px rgba(76, 114, 29, 0.15);
            overflow: hidden;
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
            transform-origin: top center;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .autocomplete-dropdown.show {
            opacity: 1;
            transform: translateY(0) scale(1);
            pointer-events: auto;
        }

        .autocomplete-item {
            padding: 12px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .autocomplete-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(139, 195, 74, 0.1) 0%, transparent 100%);
            transition: width 0.3s ease;
        }

        .autocomplete-item:hover::before {
            width: 100%;
        }

        .autocomplete-item-icon {
            width: 30px;
            height: 30px;
            background: #e8f5e9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        /* Recent searches with vine animation */
        .recent-searches {
            margin-top: 30px;
            opacity: 0;
            animation: growIn 0.6s ease forwards;
            animation-delay: 0.3s;
        }

        @keyframes growIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .recent-searches h3 {
            color: #4a6f31;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .recent-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .recent-item {
            background: rgba(255, 255, 255, 0.7);
            padding: 12px 16px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .recent-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #8bc34a 0%, #c5e1a5 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
        }

        .recent-item:hover::after {
            transform: scaleX(1);
        }

        /* Results preview with bloom animation */
        .results-preview {
            margin-top: 30px;
            opacity: 0;
        }

        .results-preview.show {
            animation: bloomIn 0.8s ease forwards;
        }

        @keyframes bloomIn {
            0% {
                opacity: 0;
                transform: scale(0.8) rotate(5deg);
            }
            50% {
                transform: scale(1.05) rotate(-2deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        .result-card {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 20px rgba(76, 114, 29, 0.1);
            position: relative;
            overflow: hidden;
            transform-origin: left center;
            animation: leafUnfold 0.6s ease forwards;
            opacity: 0;
        }

        .result-card:nth-child(1) { animation-delay: 0.1s; }
        .result-card:nth-child(2) { animation-delay: 0.2s; }
        .result-card:nth-child(3) { animation-delay: 0.3s; }

        @keyframes leafUnfold {
            0% {
                opacity: 0;
                transform: translateX(-20px) rotateY(-90deg);
            }
            100% {
                opacity: 1;
                transform: translateX(0) rotateY(0deg);
            }
        }

        .result-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(139, 195, 74, 0.05) 0%, transparent 70%);
            animation: pulse 4s infinite ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.2); opacity: 0.3; }
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 12px;
        }

        .result-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #c5e1a5 0%, #aed581 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .result-title {
            font-size: 18px;
            color: #2e4a1c;
            font-weight: 600;
        }

        .result-description {
            color: #5d7a48;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .result-meta {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #8ba673;
        }

        /* Water flow loading animation */
        .loading-indicator {
            display: none;
            justify-content: center;
            margin: 20px 0;
        }

        .loading-indicator.show {
            display: flex;
        }

        .water-drop {
            width: 10px;
            height: 10px;
            background: #7cb342;
            border-radius: 50%;
            margin: 0 3px;
            animation: waterFlow 1.4s infinite ease-in-out;
        }

        .water-drop:nth-child(1) { animation-delay: -0.32s; }
        .water-drop:nth-child(2) { animation-delay: -0.16s; }
        .water-drop:nth-child(3) { animation-delay: 0; }

        @keyframes waterFlow {
            0%, 80%, 100% {
                transform: scale(0.8) translateY(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2) translateY(-20px);
                opacity: 1;
            }
        }

        /* Seasonal color variations */
        .spring { --season-primary: #8bc34a; --season-secondary: #c5e1a5; }
        .summer { --season-primary: #66bb6a; --season-secondary: #a5d6a7; }
        .autumn { --season-primary: #ff7043; --season-secondary: #ffab91; }
        .winter { --season-primary: #5c6bc0; --season-secondary: #9fa8da; }
    </style>
</head>
<body>
    <!-- Animated nature background -->
    <div class="nature-bg" id="natureBg"></div>

    <div class="search-ecosystem">
        <div class="search-container">
            <div class="search-wrapper">
                <!-- Main search bar -->
                <div class="search-input-group">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" id="searchInput" placeholder="Search through nature's knowledge...">
                </div>

                <!-- Filter tags -->
                <div class="filter-tags">
                    <button class="filter-tag active" data-filter="all">🌿 All</button>
                    <button class="filter-tag" data-filter="plants">🌱 Plants</button>
                    <button class="filter-tag" data-filter="animals">🦋 Animals</button>
                    <button class="filter-tag" data-filter="weather">☀️ Weather</button>
                    <button class="filter-tag" data-filter="seasons">🍂 Seasons</button>
                </div>

                <!-- Autocomplete dropdown -->
                <div class="autocomplete-dropdown" id="autocompleteDropdown">
                    <div class="autocomplete-item">
                        <div class="autocomplete-item-icon">🌸</div>
                        <span>Spring flowers blooming patterns</span>
                    </div>
                    <div class="autocomplete-item">
                        <div class="autocomplete-item-icon">🌳</div>
                        <span>Oak tree growth cycles</span>
                    </div>
                    <div class="autocomplete-item">
                        <div class="autocomplete-item-icon">🐝</div>
                        <span>Bee pollination techniques</span>
                    </div>
                    <div class="autocomplete-item">
                        <div class="autocomplete-item-icon">💧</div>
                        <span>Water conservation in plants</span>
                    </div>
                </div>
            </div>

            <!-- Recent searches -->
            <div class="recent-searches">
                <h3>🌿 Recent Explorations</h3>
                <div class="recent-list">
                    <div class="recent-item">
                        <span>Photosynthesis process</span>
                        <span style="color: #8ba673; font-size: 14px;">2 hours ago</span>
                    </div>
                    <div class="recent-item">
                        <span>Butterfly migration patterns</span>
                        <span style="color: #8ba673; font-size: 14px;">Yesterday</span>
                    </div>
                    <div class="recent-item">
                        <span>Seasonal plant care</span>
                        <span style="color: #8ba673; font-size: 14px;">3 days ago</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading indicator -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="water-drop"></div>
            <div class="water-drop"></div>
            <div class="water-drop"></div>
        </div>

        <!-- Results preview -->
        <div class="results-preview" id="resultsPreview">
            <div class="result-card">
                <div class="result-header">
                    <div class="result-icon">🌺</div>
                    <h3 class="result-title">Understanding Flower Pollination</h3>
                </div>
                <p class="result-description">
                    Discover the intricate dance between flowers and their pollinators. Learn how different species have evolved unique strategies to ensure successful reproduction through color, scent, and timing.
                </p>
                <div class="result-meta">
                    <span>📚 Educational</span>
                    <span>⏱️ 5 min read</span>
                    <span>🌟 4.8 rating</span>
                </div>
            </div>

            <div class="result-card">
                <div class="result-header">
                    <div class="result-icon">🌲</div>
                    <h3 class="result-title">Forest Ecosystems Explained</h3>
                </div>
                <p class="result-description">
                    Explore the complex relationships within forest ecosystems. From the canopy to the forest floor, understand how each layer supports diverse life forms and maintains ecological balance.
                </p>
                <div class="result-meta">
                    <span>🎥 Video</span>
                    <span>⏱️ 12 min watch</span>
                    <span>🌟 4.9 rating</span>
                </div>
            </div>

            <div class="result-card">
                <div class="result-header">
                    <div class="result-icon">🦋</div>
                    <h3 class="result-title">Monarch Butterfly Migration</h3>
                </div>
                <p class="result-description">
                    Follow the incredible journey of monarch butterflies as they travel thousands of miles. Learn about their navigation methods, breeding cycles, and conservation efforts.
                </p>
                <div class="result-meta">
                    <span>🗺️ Interactive</span>
                    <span>⏱️ 8 min explore</span>
                    <span>🌟 5.0 rating</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Create floating leaves in background
        function createLeaves() {
            const bg = document.getElementById('natureBg');
            for (let i = 0; i < 8; i++) {
                const leaf = document.createElement('div');
                leaf.className = 'leaf';
                leaf.style.left = Math.random() * 100 + '%';
                leaf.style.top = Math.random() * 100 + '%';
                leaf.style.animationDelay = Math.random() * 20 + 's';
                leaf.style.animationDuration = (20 + Math.random() * 10) + 's';
                bg.appendChild(leaf);
            }
        }

        createLeaves();

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const autocompleteDropdown = document.getElementById('autocompleteDropdown');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const resultsPreview = document.getElementById('resultsPreview');

        // Autocomplete suggestions data
        const suggestions = {
            plants: ['Rose care tips', 'Succulent watering guide', 'Indoor plant species', 'Garden design ideas'],
            animals: ['Bird watching basics', 'Butterfly gardens', 'Wildlife photography', 'Animal habitats'],
            weather: ['Cloud formation types', 'Weather patterns', 'Climate zones', 'Storm tracking'],
            seasons: ['Spring gardening', 'Summer plant care', 'Autumn leaves', 'Winter preparations']
        };

        // Show autocomplete on focus
        searchInput.addEventListener('focus', () => {
            autocompleteDropdown.classList.add('show');
        });

        // Hide autocomplete on blur (with delay for clicking)
        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                autocompleteDropdown.classList.remove('show');
            }, 200);
        });

        // Search input handler
        searchInput.addEventListener('input', (e) => {
            const value = e.target.value;
            if (value.length > 2) {
                // Update autocomplete based on input
                updateAutocomplete(value);
            }
        });

        // Perform search on Enter
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.target.value.trim()) {
                performSearch(e.target.value);
            }
        });

        // Filter tag functionality
        const filterTags = document.querySelectorAll('.filter-tag');
        let activeFilter = 'all';

        filterTags.forEach(tag => {
            tag.addEventListener('click', () => {
                filterTags.forEach(t => t.classList.remove('active'));
                tag.classList.add('active');
                activeFilter = tag.dataset.filter;
                
                // Trigger search with current input and filter
                if (searchInput.value.trim()) {
                    performSearch(searchInput.value);
                }
            });
        });

        // Autocomplete item clicks
        document.querySelectorAll('.autocomplete-item').forEach(item => {
            item.addEventListener('click', () => {
                searchInput.value = item.querySelector('span').textContent;
                performSearch(searchInput.value);
            });
        });

        // Recent search item clicks
        document.querySelectorAll('.recent-item').forEach(item => {
            item.addEventListener('click', () => {
                searchInput.value = item.querySelector('span').textContent;
                performSearch(searchInput.value);
            });
        });

        // Update autocomplete suggestions
        function updateAutocomplete(query) {
            // In a real app, this would fetch suggestions from an API
            const dropdown = document.getElementById('autocompleteDropdown');
            dropdown.innerHTML = '';
            
            // Generate contextual suggestions
            const icons = ['🌸', '🌳', '🐝', '💧', '🌿', '🦋'];
            const contextualSuggestions = [
                `${query} in spring`,
                `${query} care guide`,
                `${query} identification`,
                `How to grow ${query}`,
                `${query} facts`,
                `Best ${query} varieties`
            ];
            
            contextualSuggestions.slice(0, 4).forEach((suggestion, index) => {
                const item = document.createElement('div');
                item.className = 'autocomplete-item';
                item.innerHTML = `
                    <div class="autocomplete-item-icon">${icons[index]}</div>
                    <span>${suggestion}</span>
                `;
                item.addEventListener('click', () => {
                    searchInput.value = suggestion;
                    performSearch(suggestion);
                });
                dropdown.appendChild(item);
            });
        }

        // Perform search with loading and results animation
        function performSearch(query) {
            // Hide autocomplete
            autocompleteDropdown.classList.remove('show');
            
            // Show loading
            loadingIndicator.classList.add('show');
            resultsPreview.classList.remove('show');
            
            // Simulate API call
            setTimeout(() => {
                loadingIndicator.classList.remove('show');
                
                // Generate dynamic results based on query and filter
                generateResults(query, activeFilter);
                
                // Show results with bloom animation
                resultsPreview.classList.add('show');
                
                // Add to recent searches
                addToRecentSearches(query);
            }, 1500);
        }

        // Generate dynamic search results
        function generateResults(query, filter) {
            const resultsContainer = document.getElementById('resultsPreview');
            resultsContainer.innerHTML = '';
            
            // Sample result templates based on filter
            const resultTemplates = {
                plants: [
                    { icon: '🌺', title: 'Plant Care Guide', type: '📚 Guide' },
                    { icon: '🌱', title: 'Growing Tips', type: '🎥 Video' },
                    { icon: '🌿', title: 'Species Information', type: '📊 Database' }
                ],
                animals: [
                    { icon: '🦋', title: 'Wildlife Observation', type: '📷 Gallery' },
                    { icon: '🐝', title: 'Animal Behavior', type: '🎥 Documentary' },
                    { icon: '🦜', title: 'Species Guide', type: '📚 Reference' }
                ],
                weather: [
                    { icon: '☀️', title: 'Weather Patterns', type: '📊 Data' },
                    { icon: '🌧️', title: 'Climate Analysis', type: '📈 Report' },
                    { icon: '⛈️', title: 'Storm Tracking', type: '🗺️ Live Map' }
                ],
                all: [
                    { icon: '🌸', title: 'Nature Discovery', type: '🔍 Explore' },
                    { icon: '🌳', title: 'Ecosystem Guide', type: '📚 Learn' },
                    { icon: '💧', title: 'Environmental Topics', type: '🌍 Discover' }
                ]
            };
            
            const templates = resultTemplates[filter] || resultTemplates.all;
            
            templates.forEach((template, index) => {
                const card = document.createElement('div');
                card.className = 'result-card';
                card.style.animationDelay = `${index * 0.1}s`;
                
                card.innerHTML = `
                    <div class="result-header">
                        <div class="result-icon">${template.icon}</div>
                        <h3 class="result-title">${template.title}: ${query}</h3>
                    </div>
                    <p class="result-description">
                        Comprehensive information about ${query}. Explore detailed insights, expert knowledge, and practical tips 
                        to deepen your understanding of this fascinating aspect of nature.
                    </p>
                    <div class="result-meta">
                        <span>${template.type}</span>
                        <span>⏱️ ${5 + index * 3} min</span>
                        <span>🌟 ${(4.5 + Math.random() * 0.5).toFixed(1)} rating</span>
                    </div>
                `;
                
                resultsContainer.appendChild(card);
            });
        }

        // Add to recent searches
        function addToRecentSearches(query) {
            const recentList = document.querySelector('.recent-list');
            const existingItems = Array.from(recentList.children);
            
            // Check if already exists
            const exists = existingItems.some(item => 
                item.querySelector('span').textContent === query
            );
            
            if (!exists) {
                // Create new recent item
                const newItem = document.createElement('div');
                newItem.className = 'recent-item';
                newItem.innerHTML = `
                    <span>${query}</span>
                    <span style="color: #8ba673; font-size: 14px;">Just now</span>
                `;
                
                // Add click handler
                newItem.addEventListener('click', () => {
                    searchInput.value = query;
                    performSearch(query);
                });
                
                // Insert at beginning
                recentList.insertBefore(newItem, recentList.firstChild);
                
                // Remove last item if more than 3
                if (recentList.children.length > 3) {
                    recentList.removeChild(recentList.lastChild);
                }
            }
        }

        // Seasonal theme changer (demo)
        function applySeason() {
            const seasons = ['spring', 'summer', 'autumn', 'winter'];
            const currentMonth = new Date().getMonth();
            let season = 'spring';
            
            if (currentMonth >= 2 && currentMonth <= 4) season = 'spring';
            else if (currentMonth >= 5 && currentMonth <= 7) season = 'summer';
            else if (currentMonth >= 8 && currentMonth <= 10) season = 'autumn';
            else season = 'winter';
            
            document.body.className = season;
        }

        // Initialize seasonal theme
        applySeason();
    </script>
</body>
</html>