<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Handcrafted Paper Media Player</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Caveat:wght@400;600&display=swap');

        :root {
            --paper-cream: #f4f1eb;
            --paper-white: #faf8f3;
            --paper-brown: #8b7355;
            --paper-sepia: #704214;
            --ink-blue: #2c3e50;
            --ink-black: #1a1a1a;
            --torn-shadow: rgba(139, 115, 85, 0.3);
            --paper-texture: linear-gradient(45deg, transparent 40%, rgba(139, 115, 85, 0.05) 50%, transparent 60%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Kalam', cursive;
            background: linear-gradient(135deg, #e8ddc7 0%, #f4f1eb 50%, #ede4d3 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            color: var(--ink-black);
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(139, 115, 85, 0.03) 1px, transparent 1px),
                radial-gradient(circle at 80% 80%, rgba(139, 115, 85, 0.03) 1px, transparent 1px);
            background-size: 40px 40px;
            pointer-events: none;
            z-index: -1;
        }

        main {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        h1 {
            font-family: 'Caveat', cursive;
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--paper-sepia);
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(139, 115, 85, 0.2);
            transform: rotate(-1deg);
        }

        .hybrid-component {
            background: var(--paper-white);
            border-radius: 20px;
            padding: 30px;
            position: relative;
            box-shadow: 
                0 10px 30px rgba(139, 115, 85, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            background-image: var(--paper-texture);
            transform: rotate(0.5deg);
        }

        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, transparent 30%, rgba(139, 115, 85, 0.1) 50%, transparent 70%);
            border-radius: 25px;
            z-index: -1;
        }

        .media-player {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            min-height: 600px;
        }

        .main-player {
            background: var(--paper-cream);
            border-radius: 15px;
            padding: 25px;
            position: relative;
            box-shadow: 
                inset 0 0 20px rgba(139, 115, 85, 0.1),
                0 5px 15px rgba(139, 115, 85, 0.2);
            border: 3px solid var(--paper-brown);
            border-style: dashed;
            transform: rotate(-0.3deg);
        }

        .now-playing {
            text-align: center;
            margin-bottom: 25px;
            padding: 20px;
            background: var(--paper-white);
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(139, 115, 85, 0.2);
            position: relative;
            overflow: hidden;
        }

        .now-playing::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--paper-brown), var(--paper-sepia), var(--paper-brown));
        }

        .track-title {
            font-family: 'Caveat', cursive;
            font-size: 1.8rem;
            color: var(--ink-blue);
            margin-bottom: 5px;
            transform: rotate(-0.2deg);
        }

        .track-artist {
            font-size: 1rem;
            color: var(--paper-sepia);
            font-weight: 300;
        }

        .visualizer {
            height: 80px;
            margin: 20px 0;
            display: flex;
            align-items: end;
            justify-content: center;
            gap: 3px;
            background: var(--paper-cream);
            border-radius: 8px;
            padding: 10px;
            position: relative;
            overflow: hidden;
        }

        .visualizer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                90deg,
                transparent 0px,
                rgba(139, 115, 85, 0.05) 1px,
                transparent 2px,
                transparent 10px
            );
        }

        .bar {
            width: 6px;
            background: linear-gradient(to top, var(--paper-sepia), var(--paper-brown));
            border-radius: 3px 3px 0 0;
            transition: height 0.1s ease;
            position: relative;
            box-shadow: 0 -2px 4px rgba(139, 115, 85, 0.3);
        }

        .progress-container {
            margin: 20px 0;
            position: relative;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--paper-brown);
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(139, 115, 85, 0.3);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--paper-sepia), var(--ink-blue));
            border-radius: 4px;
            width: 35%;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            right: -6px;
            top: -4px;
            width: 16px;
            height: 16px;
            background: var(--paper-white);
            border: 2px solid var(--paper-sepia);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(139, 115, 85, 0.4);
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: var(--paper-sepia);
            margin-top: 5px;
            font-family: 'Caveat', cursive;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 25px 0;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: var(--paper-white);
            color: var(--ink-blue);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            box-shadow: 
                0 4px 8px rgba(139, 115, 85, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            position: relative;
            transform: rotate(-1deg);
        }

        .control-btn:hover {
            transform: rotate(-1deg) translateY(-2px);
            box-shadow: 
                0 6px 12px rgba(139, 115, 85, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .control-btn.play {
            width: 60px;
            height: 60px;
            background: var(--paper-sepia);
            color: var(--paper-white);
            transform: rotate(0deg);
        }

        .control-btn:active {
            transform: rotate(-1deg) translateY(1px);
            box-shadow: 
                0 2px 4px rgba(139, 115, 85, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
            padding: 15px;
            background: var(--paper-white);
            border-radius: 8px;
            box-shadow: inset 0 2px 4px rgba(139, 115, 85, 0.1);
        }

        .volume-slider {
            flex: 1;
            height: 6px;
            background: var(--paper-brown);
            border-radius: 3px;
            position: relative;
            cursor: pointer;
        }

        .volume-fill {
            height: 100%;
            background: var(--paper-sepia);
            border-radius: 3px;
            width: 70%;
            position: relative;
        }

        .volume-fill::after {
            content: '';
            position: absolute;
            right: -5px;
            top: -3px;
            width: 12px;
            height: 12px;
            background: var(--paper-white);
            border: 2px solid var(--paper-sepia);
            border-radius: 50%;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .paper-section {
            background: var(--paper-white);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.2);
            position: relative;
            border: 2px solid var(--paper-brown);
            border-style: solid;
            transform: rotate(0.3deg);
        }

        .paper-section::before {
            content: '';
            position: absolute;
            top: -3px;
            left: 10px;
            right: 10px;
            height: 2px;
            background: var(--paper-brown);
            opacity: 0.3;
        }

        .section-title {
            font-family: 'Caveat', cursive;
            font-size: 1.3rem;
            color: var(--paper-sepia);
            margin-bottom: 15px;
            text-align: center;
            transform: rotate(-0.5deg);
        }

        .playlist {
            max-height: 200px;
            overflow-y: auto;
        }

        .playlist-item {
            padding: 10px;
            margin: 5px 0;
            background: var(--paper-cream);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            transform: rotate(-0.1deg);
        }

        .playlist-item:hover {
            background: var(--paper-white);
            border-left-color: var(--paper-sepia);
            transform: rotate(-0.1deg) translateX(3px);
        }

        .playlist-item.active {
            background: var(--paper-sepia);
            color: var(--paper-white);
            border-left-color: var(--ink-blue);
        }

        .playlist-item-title {
            font-weight: 400;
            font-size: 0.9rem;
            margin-bottom: 2px;
        }

        .playlist-item-artist {
            font-size: 0.8rem;
            opacity: 0.7;
            font-weight: 300;
        }

        .quality-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .quality-btn {
            padding: 6px 12px;
            border: 2px solid var(--paper-brown);
            background: var(--paper-cream);
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            color: var(--paper-sepia);
            transition: all 0.3s ease;
            font-family: 'Kalam', cursive;
            transform: rotate(-0.2deg);
        }

        .quality-btn:hover {
            background: var(--paper-white);
            transform: rotate(-0.2deg) scale(1.05);
        }

        .quality-btn.active {
            background: var(--paper-sepia);
            color: var(--paper-white);
            border-color: var(--ink-blue);
        }

        .sharing-options {
            display: flex;
            justify-content: space-around;
            gap: 10px;
        }

        .share-btn {
            width: 40px;
            height: 40px;
            border: 2px solid var(--paper-brown);
            background: var(--paper-white);
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--paper-sepia);
            transition: all 0.3s ease;
            transform: rotate(1deg);
        }

        .share-btn:hover {
            background: var(--paper-sepia);
            color: var(--paper-white);
            transform: rotate(1deg) scale(1.1);
        }

        .paper-note {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: #fffacd;
            padding: 15px;
            border-radius: 0 10px 0 10px;
            box-shadow: 0 3px 8px rgba(139, 115, 85, 0.4);
            font-family: 'Caveat', cursive;
            font-size: 0.9rem;
            color: var(--paper-sepia);
            max-width: 200px;
            transform: rotate(3deg);
            border: 1px dashed var(--paper-brown);
        }

        @media (max-width: 768px) {
            .media-player {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .hybrid-component {
                padding: 20px;
                transform: rotate(0deg);
            }
            
            .main-player {
                transform: rotate(0deg);
            }
            
            .controls {
                gap: 10px;
            }
            
            .control-btn {
                width: 45px;
                height: 45px;
            }
            
            .control-btn.play {
                width: 55px;
                height: 55px;
            }
        }

        /* Custom scrollbar */
        .playlist::-webkit-scrollbar {
            width: 8px;
        }

        .playlist::-webkit-scrollbar-track {
            background: var(--paper-cream);
            border-radius: 4px;
        }

        .playlist::-webkit-scrollbar-thumb {
            background: var(--paper-brown);
            border-radius: 4px;
        }

        .playlist::-webkit-scrollbar-thumb:hover {
            background: var(--paper-sepia);
        }
    </style>
</head>
<body>
    <main>
        <h1>Media Player - Handcrafted Paper Theme</h1>
        <div class="hybrid-component">
            <div class="media-player">
                <div class="main-player">
                    <div class="now-playing">
                        <div class="track-title" id="trackTitle">Acoustic Memories</div>
                        <div class="track-artist" id="trackArtist">Paper Trail Sessions</div>
                    </div>

                    <div class="visualizer" id="visualizer">
                        <!-- Bars will be generated by JavaScript -->
                    </div>

                    <div class="progress-container">
                        <div class="progress-bar" id="progressBar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="time-display">
                            <span id="currentTime">2:34</span>
                            <span id="totalTime">5:47</span>
                        </div>
                    </div>

                    <div class="controls">
                        <button class="control-btn" id="prevBtn">⏮</button>
                        <button class="control-btn" id="rewindBtn">⏪</button>
                        <button class="control-btn play" id="playBtn">▶</button>
                        <button class="control-btn" id="forwardBtn">⏩</button>
                        <button class="control-btn" id="nextBtn">⏭</button>
                    </div>

                    <div class="volume-control">
                        <span>🔊</span>
                        <div class="volume-slider" id="volumeSlider">
                            <div class="volume-fill" id="volumeFill"></div>
                        </div>
                        <span id="volumeValue">70%</span>
                    </div>
                </div>

                <div class="sidebar">
                    <div class="paper-section">
                        <div class="section-title">Playlist</div>
                        <div class="playlist" id="playlist">
                            <!-- Playlist items will be generated by JavaScript -->
                        </div>
                    </div>

                    <div class="paper-section">
                        <div class="section-title">Quality</div>
                        <div class="quality-selector" id="qualitySelector">
                            <button class="quality-btn" data-quality="128">128k</button>
                            <button class="quality-btn active" data-quality="320">320k</button>
                            <button class="quality-btn" data-quality="lossless">Lossless</button>
                        </div>
                    </div>

                    <div class="paper-section">
                        <div class="section-title">Share</div>
                        <div class="sharing-options">
                            <button class="share-btn" title="Copy Link">🔗</button>
                            <button class="share-btn" title="Share on Social">📱</button>
                            <button class="share-btn" title="Email">✉️</button>
                            <button class="share-btn" title="Download">💾</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="paper-note">
                "Music is the poetry of the air, captured on handmade paper..."
            </div>
        </div>
    </main>

    <script>
        // Media Player State
        const state = {
            isPlaying: false,
            currentTrack: 0,
            volume: 70,
            progress: 35,
            quality: '320',
            playlist: [
                { title: "Acoustic Memories", artist: "Paper Trail Sessions", duration: "5:47" },
                { title: "Vintage Echoes", artist: "Handwritten Harmonies", duration: "4:23" },
                { title: "Textured Melodies", artist: "Craft Corner Collective", duration: "6:12" },
                { title: "Sepia Serenade", artist: "Analog Hearts", duration: "3:58" },
                { title: "Paper Moon Rising", artist: "Folk & Fiber", duration: "5:31" },
                { title: "Inked Impressions", artist: "Manuscript Music", duration: "4:47" }
            ]
        };

        // Initialize the interface
        function init() {
            createVisualizer();
            createPlaylist();
            setupEventListeners();
            updateDisplay();
        }

        // Create visualizer bars
        function createVisualizer() {
            const visualizer = document.getElementById('visualizer');
            for (let i = 0; i < 40; i++) {
                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.height = Math.random() * 60 + 10 + 'px';
                visualizer.appendChild(bar);
            }
        }

        // Create playlist items
        function createPlaylist() {
            const playlist = document.getElementById('playlist');
            state.playlist.forEach((track, index) => {
                const item = document.createElement('div');
                item.className = `playlist-item ${index === state.currentTrack ? 'active' : ''}`;
                item.innerHTML = `
                    <div class="playlist-item-title">${track.title}</div>
                    <div class="playlist-item-artist">${track.artist} • ${track.duration}</div>
                `;
                item.addEventListener('click', () => selectTrack(index));
                playlist.appendChild(item);
            });
        }

        // Setup event listeners
        function setupEventListeners() {
            // Play/Pause button
            document.getElementById('playBtn').addEventListener('click', togglePlay);

            // Control buttons
            document.getElementById('prevBtn').addEventListener('click', () => changeTrack(-1));
            document.getElementById('nextBtn').addEventListener('click', () => changeTrack(1));
            document.getElementById('rewindBtn').addEventListener('click', () => seekRelative(-10));
            document.getElementById('forwardBtn').addEventListener('click', () => seekRelative(10));

            // Progress bar
            document.getElementById('progressBar').addEventListener('click', handleProgressClick);

            // Volume control
            document.getElementById('volumeSlider').addEventListener('click', handleVolumeClick);

            // Quality selector
            document.querySelectorAll('.quality-btn').forEach(btn => {
                btn.addEventListener('click', (e) => setQuality(e.target.dataset.quality));
            });

            // Share buttons
            document.querySelectorAll('.share-btn').forEach(btn => {
                btn.addEventListener('click', handleShare);
            });
        }

        // Toggle play/pause
        function togglePlay() {
            state.isPlaying = !state.isPlaying;
            const playBtn = document.getElementById('playBtn');
            playBtn.textContent = state.isPlaying ? '⏸' : '▶';
            
            if (state.isPlaying) {
                startVisualizer();
            } else {
                stopVisualizer();
            }
        }

        // Change track
        function changeTrack(direction) {
            state.currentTrack = Math.max(0, Math.min(state.playlist.length - 1, state.currentTrack + direction));
            updateDisplay();
            updatePlaylist();
        }

        // Select specific track
        function selectTrack(index) {
            state.currentTrack = index;
            updateDisplay();
            updatePlaylist();
            if (!state.isPlaying) togglePlay();
        }

        // Handle progress bar click
        function handleProgressClick(e) {
            const rect = e.target.getBoundingClientRect();
            const percent = (e.clientX - rect.left) / rect.width * 100;
            state.progress = Math.max(0, Math.min(100, percent));
            updateProgress();
        }

        // Handle volume click
        function handleVolumeClick(e) {
            const rect = e.target.getBoundingClientRect();
            const percent = (e.clientX - rect.left) / rect.width * 100;
            state.volume = Math.max(0, Math.min(100, percent));
            updateVolume();
        }

        // Seek relative
        function seekRelative(seconds) {
            // Simulate seeking by adjusting progress
            const currentSeconds = (state.progress / 100) * 347; // 5:47 = 347 seconds
            const newSeconds = Math.max(0, Math.min(347, currentSeconds + seconds));
            state.progress = (newSeconds / 347) * 100;
            updateProgress();
        }

        // Set quality
        function setQuality(quality) {
            state.quality = quality;
            document.querySelectorAll('.quality-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.quality === quality);
            });
            
            // Simulate quality change effect
            showPaperNote(`Quality changed to ${quality === 'lossless' ? 'Lossless' : quality + 'kbps'}`);
        }

        // Handle share
        function handleShare(e) {
            const title = e.target.getAttribute('title');
            showPaperNote(`${title} feature activated!`);
            
            // Add visual feedback
            e.target.style.transform = 'rotate(1deg) scale(1.2)';
            setTimeout(() => {
                e.target.style.transform = 'rotate(1deg) scale(1.1)';
            }, 200);
        }

        // Update display
        function updateDisplay() {
            const track = state.playlist[state.currentTrack];
            document.getElementById('trackTitle').textContent = track.title;
            document.getElementById('trackArtist').textContent = track.artist;
            document.getElementById('totalTime').textContent = track.duration;
        }

        // Update playlist display
        function updatePlaylist() {
            document.querySelectorAll('.playlist-item').forEach((item, index) => {
                item.classList.toggle('active', index === state.currentTrack);
            });
        }

        // Update progress display
        function updateProgress() {
            document.getElementById('progressFill').style.width = state.progress + '%';
            
            // Update current time based on progress
            const totalSeconds = 347; // 5:47
            const currentSeconds = (state.progress / 100) * totalSeconds;
            const minutes = Math.floor(currentSeconds / 60);
            const seconds = Math.floor(currentSeconds % 60);
            document.getElementById('currentTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        // Update volume display
        function updateVolume() {
            document.getElementById('volumeFill').style.width = state.volume + '%';
            document.getElementById('volumeValue').textContent = Math.round(state.volume) + '%';
        }

        // Start visualizer animation
        function startVisualizer() {
            const bars = document.querySelectorAll('.bar');
            const animateBar = (bar) => {
                const height = Math.random() * 60 + 10;
                bar.style.height = height + 'px';
            };

            state.visualizerInterval = setInterval(() => {
                bars.forEach((bar, index) => {
                    setTimeout(() => animateBar(bar), index * 20);
                });
            }, 200);
        }

        // Stop visualizer animation
        function stopVisualizer() {
            if (state.visualizerInterval) {
                clearInterval(state.visualizerInterval);
            }
        }

        // Show paper note notification
        function showPaperNote(message) {
            const note = document.querySelector('.paper-note');
            const originalText = note.textContent;
            
            note.textContent = message;
            note.style.background = '#e6f3ff';
            note.style.transform = 'rotate(3deg) scale(1.1)';
            
            setTimeout(() => {
                note.textContent = originalText;
                note.style.background = '#fffacd';
                note.style.transform = 'rotate(3deg) scale(1)';
            }, 2000);
        }

        // Simulate progress updates when playing
        setInterval(() => {
            if (state.isPlaying && state.progress < 100) {
                state.progress += 0.5;
                updateProgress();
            }
        }, 1000);

        // Initialize the media player
        init();

        // Add some paper-like interactions
        document.querySelectorAll('.paper-section').forEach(section => {
            section.addEventListener('mouseenter', () => {
                section.style.transform = 'rotate(0.3deg) scale(1.02)';
                section.style.boxShadow = '0 6px 16px rgba(139, 115, 85, 0.3)';
            });
            
            section.addEventListener('mouseleave', () => {
                section.style.transform = 'rotate(0.3deg) scale(1)';
                section.style.boxShadow = '0 4px 12px rgba(139, 115, 85, 0.2)';
            });
        });

        // Add paper texture animation
        let textureOffset = 0;
        setInterval(() => {
            textureOffset += 0.5;
            document.documentElement.style.setProperty('--paper-texture', 
                `linear-gradient(${45 + textureOffset * 0.1}deg, transparent 40%, rgba(139, 115, 85, 0.05) 50%, transparent 60%)`
            );
        }, 100);
    </script>
</body>
</html>