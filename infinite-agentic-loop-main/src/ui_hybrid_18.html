<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steampunk Victorian Adaptive Workspace</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap');

        :root {
            --brass-primary: #b8860b;
            --brass-light: #daa520;
            --brass-dark: #8b6914;
            --copper-primary: #b87333;
            --iron-dark: #2f2f2f;
            --iron-medium: #4a4a4a;
            --sepia-light: #f4e4bc;
            --sepia-medium: #d2b48c;
            --mahogany: #8b0000;
            --parchment: #f5f5dc;
            --ink: #1a1a1a;
            --steam: #e6e6fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Crimson Text', serif;
            background: linear-gradient(135deg, var(--sepia-light) 0%, var(--sepia-medium) 100%);
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(184, 134, 11, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(139, 0, 0, 0.1) 0%, transparent 50%),
                url('data:image/svg+xml,<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="gears" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="3" fill="none" stroke="rgba(184,134,11,0.1)" stroke-width="0.5"/><path d="M25 15 L30 20 L25 25 L20 20 Z" fill="rgba(184,134,11,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23gears)"/></svg>');
            min-height: 100vh;
            color: var(--ink);
            overflow-x: hidden;
        }

        main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--mahogany);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
        }

        h1::before, h1::after {
            content: '⚙';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: var(--brass-primary);
            animation: rotate 8s linear infinite;
        }

        h1::before {
            left: -3rem;
        }

        h1::after {
            right: -3rem;
            animation-direction: reverse;
        }

        .hybrid-component {
            background: linear-gradient(145deg, var(--parchment) 0%, #f0e6d2 100%);
            border: 3px solid var(--brass-primary);
            border-radius: 15px;
            box-shadow: 
                0 0 30px rgba(184, 134, 11, 0.3),
                inset 0 0 20px rgba(184, 134, 11, 0.1);
            position: relative;
            overflow: hidden;
        }

        .hybrid-component::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--brass-dark), var(--brass-light), var(--copper-primary), var(--brass-light), var(--brass-dark));
            animation: shimmer 3s ease-in-out infinite;
        }

        .workshop-header {
            background: linear-gradient(135deg, var(--brass-primary) 0%, var(--copper-primary) 100%);
            padding: 1.5rem;
            border-bottom: 2px solid var(--brass-dark);
            position: relative;
        }

        .workshop-title {
            font-family: 'Cinzel', serif;
            font-size: 1.8rem;
            color: var(--parchment);
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .main-interface {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: auto 1fr;
            height: 80vh;
            gap: 1px;
        }

        .control-panel {
            background: linear-gradient(145deg, var(--iron-medium) 0%, var(--iron-dark) 100%);
            padding: 1.5rem;
            border-right: 2px solid var(--brass-primary);
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .main-workspace {
            background: var(--parchment);
            padding: 2rem;
            position: relative;
            overflow-y: auto;
        }

        .side-panel {
            background: linear-gradient(145deg, var(--sepia-medium) 0%, var(--sepia-light) 100%);
            padding: 1.5rem;
            border-left: 2px solid var(--brass-primary);
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .panel-section {
            background: rgba(245, 245, 220, 0.8);
            border: 2px solid var(--brass-primary);
            border-radius: 8px;
            padding: 1rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .panel-section:hover {
            box-shadow: 0 0 15px rgba(184, 134, 11, 0.4);
            transform: translateY(-2px);
        }

        .section-title {
            font-family: 'Cinzel', serif;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--mahogany);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .gear-icon {
            width: 16px;
            height: 16px;
            color: var(--brass-primary);
            animation: rotate 4s linear infinite;
        }

        .task-item, .calendar-event, .note-item, .file-item {
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid var(--brass-light);
            border-radius: 5px;
            padding: 0.5rem;
            margin: 0.3rem 0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .task-item:hover, .calendar-event:hover, .note-item:hover, .file-item:hover {
            background: rgba(184, 134, 11, 0.1);
            border-color: var(--brass-primary);
            transform: translateX(5px);
        }

        .priority-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            animation: pulse 2s ease-in-out infinite;
        }

        .high-priority { background: var(--mahogany); }
        .medium-priority { background: var(--brass-primary); }
        .low-priority { background: var(--copper-primary); }

        .workshop-tabs {
            display: flex;
            background: var(--brass-primary);
            border-bottom: 2px solid var(--brass-dark);
        }

        .tab-button {
            flex: 1;
            padding: 1rem;
            border: none;
            background: transparent;
            color: var(--parchment);
            font-family: 'Cinzel', serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .tab-button.active {
            background: var(--parchment);
            color: var(--mahogany);
        }

        .tab-content {
            display: none;
            height: 100%;
        }

        .tab-content.active {
            display: block;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: var(--brass-primary);
            border: 2px solid var(--brass-primary);
            border-radius: 8px;
            overflow: hidden;
        }

        .calendar-day {
            background: var(--parchment);
            padding: 0.5rem;
            min-height: 60px;
            border: 1px solid var(--brass-light);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-day:hover {
            background: rgba(184, 134, 11, 0.1);
        }

        .day-number {
            font-weight: 600;
            color: var(--mahogany);
        }

        .note-editor {
            background: var(--parchment);
            border: 2px solid var(--brass-primary);
            border-radius: 8px;
            padding: 1rem;
            min-height: 200px;
            font-family: 'Crimson Text', serif;
            font-size: 1rem;
            color: var(--ink);
            resize: vertical;
            background-image: repeating-linear-gradient(
                transparent,
                transparent 24px,
                rgba(184, 134, 11, 0.3) 24px,
                rgba(184, 134, 11, 0.3) 25px
            );
            line-height: 25px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 1rem;
        }

        .file-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(145deg, var(--brass-light), var(--brass-primary));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--parchment);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(184, 134, 11, 0.5);
        }

        .communication-hub {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid var(--brass-primary);
            border-radius: 8px;
            padding: 1rem;
            height: 200px;
            overflow-y: auto;
        }

        .message {
            background: rgba(184, 134, 11, 0.1);
            border-left: 3px solid var(--brass-primary);
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 0 5px 5px 0;
        }

        .environment-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .control-knob {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: radial-gradient(circle, var(--brass-light) 0%, var(--brass-primary) 70%, var(--brass-dark) 100%);
            border: 3px solid var(--brass-dark);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-knob:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(184, 134, 11, 0.6);
        }

        .control-knob::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 15px;
            background: var(--mahogany);
            border-radius: 2px;
        }

        .steam-effect {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, var(--steam) 0%, transparent 70%);
            border-radius: 50%;
            animation: steam 3s ease-in-out infinite;
        }

        .mechanical-border {
            border: 2px solid var(--brass-primary);
            border-radius: 8px;
            position: relative;
        }

        .mechanical-border::before, .mechanical-border::after {
            content: '⚙';
            position: absolute;
            font-size: 0.8rem;
            color: var(--brass-primary);
            animation: rotate 6s linear infinite;
        }

        .mechanical-border::before {
            top: -10px;
            left: -10px;
        }

        .mechanical-border::after {
            bottom: -10px;
            right: -10px;
            animation-direction: reverse;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
            50% { opacity: 0.7; transform: translateY(-50%) scale(1.2); }
        }

        @keyframes shimmer {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes steam {
            0%, 100% { opacity: 0.3; transform: translateY(0) scale(1); }
            50% { opacity: 0.7; transform: translateY(-10px) scale(1.2); }
        }

        .adaptive-suggestion {
            background: linear-gradient(135deg, rgba(139, 0, 0, 0.1) 0%, rgba(184, 134, 11, 0.1) 100%);
            border: 1px dashed var(--mahogany);
            border-radius: 5px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            font-style: italic;
            color: var(--mahogany);
        }

        @media (max-width: 1200px) {
            .main-interface {
                grid-template-columns: 250px 1fr;
                grid-template-rows: auto auto 1fr;
            }

            .side-panel {
                grid-column: 1 / -1;
                border-left: none;
                border-top: 2px solid var(--brass-primary);
            }
        }

        @media (max-width: 768px) {
            .main-interface {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto 1fr;
            }

            .control-panel {
                border-right: none;
                border-bottom: 2px solid var(--brass-primary);
            }

            .workshop-tabs {
                flex-wrap: wrap;
            }

            .tab-button {
                flex: 1 1 50%;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Adaptive Workspace - Steampunk Victorian Theme</h1>
        <div class="hybrid-component">
            <div class="workshop-header">
                <h2 class="workshop-title">Victorian Mechanical Workshop Interface</h2>
                <div class="steam-effect"></div>
            </div>
            
            <div class="main-interface">
                <div class="control-panel">
                    <div class="panel-section mechanical-border">
                        <h3 class="section-title">
                            <span class="gear-icon">⚙</span>
                            Priority Tasks
                        </h3>
                        <div class="task-item">
                            <span>Complete quarterly invention report</span>
                            <div class="priority-indicator high-priority"></div>
                        </div>
                        <div class="task-item">
                            <span>Calibrate chronometer mechanisms</span>
                            <div class="priority-indicator medium-priority"></div>
                        </div>
                        <div class="task-item">
                            <span>Review apprentice blueprints</span>
                            <div class="priority-indicator low-priority"></div>
                        </div>
                        <div class="adaptive-suggestion">
                            💡 Based on your pattern, schedule calibration before 2 PM for optimal precision
                        </div>
                    </div>

                    <div class="panel-section mechanical-border">
                        <h3 class="section-title">
                            <span class="gear-icon">⚙</span>
                            Environment Controls
                        </h3>
                        <div class="environment-controls">
                            <div>
                                <label style="font-size: 0.9rem; color: var(--mahogany);">Lighting</label>
                                <div class="control-knob" data-control="lighting"></div>
                            </div>
                            <div>
                                <label style="font-size: 0.9rem; color: var(--mahogany);">Steam Pressure</label>
                                <div class="control-knob" data-control="pressure"></div>
                            </div>
                            <div>
                                <label style="font-size: 0.9rem; color: var(--mahogany);">Temperature</label>
                                <div class="control-knob" data-control="temperature"></div>
                            </div>
                            <div>
                                <label style="font-size: 0.9rem; color: var(--mahogany);">Noise Level</label>
                                <div class="control-knob" data-control="noise"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="main-workspace">
                    <div class="workshop-tabs">
                        <button class="tab-button active" data-tab="calendar">Chronometer Schedule</button>
                        <button class="tab-button" data-tab="notes">Laboratory Journal</button>
                        <button class="tab-button" data-tab="files">Blueprint Archive</button>
                        <button class="tab-button" data-tab="communication">Telegraph Hub</button>
                    </div>

                    <div class="tab-content active" id="calendar">
                        <h3 style="font-family: 'Cinzel', serif; color: var(--mahogany); margin: 1rem 0;">Workshop Schedule - December 2024</h3>
                        <div class="calendar-grid">
                            <div class="calendar-day"><span class="day-number">1</span></div>
                            <div class="calendar-day"><span class="day-number">2</span></div>
                            <div class="calendar-day"><span class="day-number">3</span></div>
                            <div class="calendar-day"><span class="day-number">4</span></div>
                            <div class="calendar-day"><span class="day-number">5</span></div>
                            <div class="calendar-day"><span class="day-number">6</span></div>
                            <div class="calendar-day"><span class="day-number">7</span></div>
                            <div class="calendar-day">
                                <span class="day-number">8</span>
                                <div style="font-size: 0.7rem; color: var(--mahogany);">Steam Engine Maintenance</div>
                            </div>
                            <div class="calendar-day"><span class="day-number">9</span></div>
                            <div class="calendar-day"><span class="day-number">10</span></div>
                            <div class="calendar-day"><span class="day-number">11</span></div>
                            <div class="calendar-day">
                                <span class="day-number">12</span>
                                <div style="font-size: 0.7rem; color: var(--mahogany);">Guild Meeting</div>
                            </div>
                            <div class="calendar-day"><span class="day-number">13</span></div>
                            <div class="calendar-day"><span class="day-number">14</span></div>
                            <div class="calendar-day">
                                <span class="day-number">15</span>
                                <div style="font-size: 0.7rem; color: var(--mahogany);">Invention Expo</div>
                            </div>
                            <div class="calendar-day"><span class="day-number">16</span></div>
                            <div class="calendar-day"><span class="day-number">17</span></div>
                            <div class="calendar-day"><span class="day-number">18</span></div>
                            <div class="calendar-day"><span class="day-number">19</span></div>
                            <div class="calendar-day"><span class="day-number">20</span></div>
                            <div class="calendar-day"><span class="day-number">21</span></div>
                            <div class="calendar-day"><span class="day-number">22</span></div>
                            <div class="calendar-day"><span class="day-number">23</span></div>
                            <div class="calendar-day"><span class="day-number">24</span></div>
                            <div class="calendar-day"><span class="day-number">25</span></div>
                            <div class="calendar-day"><span class="day-number">26</span></div>
                            <div class="calendar-day"><span class="day-number">27</span></div>
                            <div class="calendar-day"><span class="day-number">28</span></div>
                            <div class="calendar-day"><span class="day-number">29</span></div>
                            <div class="calendar-day"><span class="day-number">30</span></div>
                            <div class="calendar-day"><span class="day-number">31</span></div>
                        </div>
                        <div class="adaptive-suggestion" style="margin-top: 1rem;">
                            📅 Your most productive invention sessions occur Tuesday-Thursday mornings. Consider scheduling complex work then.
                        </div>
                    </div>

                    <div class="tab-content" id="notes">
                        <h3 style="font-family: 'Cinzel', serif; color: var(--mahogany); margin: 1rem 0;">Laboratory Research Journal</h3>
                        <textarea class="note-editor" placeholder="Record your discoveries and observations in this mechanical journal...

Entry Date: December 7th, 1887

Today's experiments with the clockwork automation yielded fascinating results. The precision gearing mechanism shows promise for..."></textarea>
                        <div class="adaptive-suggestion" style="margin-top: 1rem;">
                            📝 Your notes often reference pressure calculations. Would you like quick access to your conversion tables?
                        </div>
                    </div>

                    <div class="tab-content" id="files">
                        <h3 style="font-family: 'Cinzel', serif; color: var(--mahogany); margin: 1rem 0;">Blueprint & Patent Archive</h3>
                        <div class="file-grid">
                            <div class="file-icon" title="Steam Engine Schematics">📋</div>
                            <div class="file-icon" title="Clockwork Mechanisms">⚙️</div>
                            <div class="file-icon" title="Pressure Calculations">📊</div>
                            <div class="file-icon" title="Patent Applications">📜</div>
                            <div class="file-icon" title="Guild Correspondence">✉️</div>
                            <div class="file-icon" title="Invention Sketches">✏️</div>
                            <div class="file-icon" title="Material Specifications">📐</div>
                            <div class="file-icon" title="Test Results">🔬</div>
                        </div>
                        <div class="adaptive-suggestion" style="margin-top: 1rem;">
                            📁 Files accessed together: Steam Engine Schematics + Pressure Calculations. Auto-group these?
                        </div>
                    </div>

                    <div class="tab-content" id="communication">
                        <h3 style="font-family: 'Cinzel', serif; color: var(--mahogany); margin: 1rem 0;">Telegraph Communication Hub</h3>
                        <div class="communication-hub">
                            <div class="message">
                                <strong>From: Guild Master Thompson</strong><br>
                                <em>10:30 AM</em><br>
                                Your latest clockwork innovation has been approved for the exhibition. Magnificent work, old chap!
                            </div>
                            <div class="message">
                                <strong>From: Apprentice Wilson</strong><br>
                                <em>11:15 AM</em><br>
                                The steam pressure readings are ready for your review. Shall I prepare the calibration tools?
                            </div>
                            <div class="message">
                                <strong>From: Patent Office</strong><br>
                                <em>2:45 PM</em><br>
                                Application #VIC-1887-0247 requires additional technical specifications. Please submit by Friday.
                            </div>
                        </div>
                        <div style="margin-top: 1rem;">
                            <input type="text" placeholder="Compose telegraph message..." style="width: 100%; padding: 0.5rem; border: 2px solid var(--brass-primary); border-radius: 5px; font-family: 'Crimson Text', serif;">
                        </div>
                        <div class="adaptive-suggestion" style="margin-top: 1rem;">
                            📨 Guild messages often require formal responses. Use template: "Most esteemed colleague..."?
                        </div>
                    </div>
                </div>

                <div class="side-panel">
                    <div class="panel-section mechanical-border">
                        <h3 class="section-title">
                            <span class="gear-icon">⚙</span>
                            Today's Agenda
                        </h3>
                        <div class="calendar-event">
                            <strong>9:00 AM</strong><br>
                            Workshop Maintenance
                        </div>
                        <div class="calendar-event">
                            <strong>11:30 AM</strong><br>
                            Apprentice Training
                        </div>
                        <div class="calendar-event">
                            <strong>2:00 PM</strong><br>
                            Guild Council Meeting
                        </div>
                        <div class="calendar-event">
                            <strong>4:30 PM</strong><br>
                            Patent Review Session
                        </div>
                    </div>

                    <div class="panel-section mechanical-border">
                        <h3 class="section-title">
                            <span class="gear-icon">⚙</span>
                            Quick Notes
                        </h3>
                        <div class="note-item">
                            Remember: Brass expansion coefficient at high temperatures
                        </div>
                        <div class="note-item">
                            Order more spring steel from Sheffield suppliers
                        </div>
                        <div class="note-item">
                            Test new gear ratio: 3.7:1 shows promise
                        </div>
                        <div class="adaptive-suggestion">
                            🔧 You frequently note material specifications. Create a quick-access materials database?
                        </div>
                    </div>

                    <div class="panel-section mechanical-border">
                        <h3 class="section-title">
                            <span class="gear-icon">⚙</span>
                            Recent Files
                        </h3>
                        <div class="file-item">📋 Steam_Engine_Mark_VII.blueprint</div>
                        <div class="file-item">⚙️ Clockwork_Precision_Tests.data</div>
                        <div class="file-item">📜 Patent_Application_247.document</div>
                        <div class="file-item">📊 Pressure_Analysis_Dec.report</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tab switching functionality
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all tabs and contents
                document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                button.classList.add('active');
                document.getElementById(button.dataset.tab).classList.add('active');
                
                // Adaptive learning simulation
                setTimeout(() => {
                    addAdaptiveSuggestion(button.dataset.tab);
                }, 1000);
            });
        });

        // Environment control knobs
        document.querySelectorAll('.control-knob').forEach(knob => {
            let rotation = 0;
            knob.addEventListener('click', (e) => {
                rotation += 45;
                knob.style.transform = `rotate(${rotation}deg)`;
                
                // Simulate environment change
                const control = knob.dataset.control;
                showEnvironmentFeedback(control, rotation);
            });
        });

        // Calendar day interaction
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.addEventListener('click', () => {
                const existingEvent = day.querySelector('div:not(.day-number)');
                if (!existingEvent) {
                    const event = document.createElement('div');
                    event.style.fontSize = '0.7rem';
                    event.style.color = 'var(--mahogany)';
                    event.textContent = 'New appointment';
                    event.contentEditable = true;
                    day.appendChild(event);
                    event.focus();
                }
            });
        });

        // Task interaction with mechanical feedback
        document.querySelectorAll('.task-item, .calendar-event, .note-item, .file-item').forEach(item => {
            item.addEventListener('click', () => {
                item.style.transform = 'translateX(10px)';
                setTimeout(() => {
                    item.style.transform = 'translateX(5px)';
                }, 150);
                
                // Simulate mechanical click sound
                playMechanicalSound();
            });
        });

        // Communication input enhancement
        const messageInput = document.querySelector('input[placeholder*="telegraph"]');
        if (messageInput) {
            messageInput.addEventListener('focus', () => {
                messageInput.style.boxShadow = '0 0 15px rgba(184, 134, 11, 0.4)';
            });
            
            messageInput.addEventListener('blur', () => {
                messageInput.style.boxShadow = 'none';
            });
            
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    sendTelegraphMessage(messageInput.value);
                    messageInput.value = '';
                }
            });
        }

        // File organization with drag simulation
        document.querySelectorAll('.file-icon').forEach(icon => {
            icon.addEventListener('mousedown', () => {
                icon.style.cursor = 'grabbing';
                icon.style.opacity = '0.8';
            });
            
            icon.addEventListener('mouseup', () => {
                icon.style.cursor = 'pointer';
                icon.style.opacity = '1';
            });
        });

        // Adaptive workspace learning
        function addAdaptiveSuggestion(context) {
            const suggestions = {
                calendar: "⏰ You often schedule meetings after 2 PM. Block morning hours for focused work?",
                notes: "📖 Your research notes could benefit from automatic cross-referencing. Enable smart linking?",
                files: "🗂️ Frequently accessed blueprints detected. Create a 'Daily Essentials' quick access panel?",
                communication: "💬 Response pattern analysis: You prefer formal language in guild correspondence."
            };
            
            const existingSuggestion = document.querySelector(`#${context} .adaptive-suggestion`);
            if (existingSuggestion && Math.random() > 0.7) {
                existingSuggestion.innerHTML = suggestions[context] || existingSuggestion.innerHTML;
                existingSuggestion.style.animation = 'pulse 1s ease-in-out';
                setTimeout(() => {
                    existingSuggestion.style.animation = '';
                }, 1000);
            }
        }

        function showEnvironmentFeedback(control, rotation) {
            const feedbackMessages = {
                lighting: rotation > 180 ? "Bright gas lamps engaged" : "Dim candlelight mode",
                pressure: rotation > 180 ? "High pressure steam ready" : "Low pressure conservation mode",
                temperature: rotation > 180 ? "Workshop heating increased" : "Cool working conditions",
                noise: rotation > 180 ? "Full machinery operation" : "Quiet contemplation mode"
            };
            
            // Create temporary feedback
            const feedback = document.createElement('div');
            feedback.textContent = feedbackMessages[control];
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--brass-primary);
                color: var(--parchment);
                padding: 0.5rem 1rem;
                border-radius: 5px;
                z-index: 1000;
                font-family: 'Cinzel', serif;
                box-shadow: 0 0 15px rgba(184, 134, 11, 0.5);
            `;
            
            document.body.appendChild(feedback);
            setTimeout(() => {
                feedback.remove();
            }, 2000);
        }

        function playMechanicalSound() {
            // Simulate mechanical click with visual feedback
            const steamEffect = document.querySelector('.steam-effect');
            if (steamEffect) {
                steamEffect.style.animation = 'none';
                setTimeout(() => {
                    steamEffect.style.animation = 'steam 3s ease-in-out infinite';
                }, 50);
            }
        }

        function sendTelegraphMessage(message) {
            if (message.trim()) {
                const messageContainer = document.querySelector('.communication-hub');
                const newMessage = document.createElement('div');
                newMessage.className = 'message';
                newMessage.innerHTML = `
                    <strong>To: Workshop Colleagues</strong><br>
                    <em>${new Date().toLocaleTimeString()}</em><br>
                    ${message}
                `;
                messageContainer.appendChild(newMessage);
                messageContainer.scrollTop = messageContainer.scrollHeight;
                
                // Simulate telegraph transmission effect
                newMessage.style.opacity = '0';
                newMessage.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    newMessage.style.transition = 'all 0.5s ease';
                    newMessage.style.opacity = '1';
                    newMessage.style.transform = 'translateY(0)';
                }, 100);
            }
        }

        // Automatic workspace adaptation based on time
        function adaptWorkspaceToTime() {
            const hour = new Date().getHours();
            const knobs = document.querySelectorAll('.control-knob');
            
            if (hour < 9 || hour > 18) {
                // Evening/night mode - dim lighting, quiet
                knobs[0].style.transform = 'rotate(45deg)'; // Dim lighting
                knobs[3].style.transform = 'rotate(45deg)'; // Quiet mode
            } else if (hour >= 9 && hour < 12) {
                // Morning - bright lighting, active
                knobs[0].style.transform = 'rotate(135deg)'; // Bright lighting
                knobs[1].style.transform = 'rotate(135deg)'; // High pressure
            }
        }

        // Initialize adaptive features
        document.addEventListener('DOMContentLoaded', () => {
            adaptWorkspaceToTime();
            
            // Simulate user activity patterns
            setInterval(() => {
                if (Math.random() > 0.95) {
                    const suggestions = document.querySelectorAll('.adaptive-suggestion');
                    const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
                    if (randomSuggestion) {
                        randomSuggestion.style.animation = 'pulse 1s ease-in-out';
                        setTimeout(() => {
                            randomSuggestion.style.animation = '';
                        }, 1000);
                    }
                }
            }, 30000);
        });

        // Keyboard shortcuts for workshop efficiency
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('[data-tab="calendar"]').click();
                        break;
                    case '2':
                        e.preventDefault();
                        document.querySelector('[data-tab="notes"]').click();
                        break;
                    case '3':
                        e.preventDefault();
                        document.querySelector('[data-tab="files"]').click();
                        break;
                    case '4':
                        e.preventDefault();
                        document.querySelector('[data-tab="communication"]').click();
                        break;
                }
            }
        });
    </script>
</body>
</html>