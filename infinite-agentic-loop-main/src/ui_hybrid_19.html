<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crystalline Future Multi-Modal Interface</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Orbitron', 'Arial', sans-serif;
            background: radial-gradient(circle at 30% 70%, #0a0a1a 0%, #1a1a2e 30%, #16213e 60%, #0f3460 100%);
            color: #e0f3ff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        /* Animated crystal background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolygon points='30,5 45,25 30,45 15,25' stroke='%2300d4ff' stroke-width='0.5' fill='none' opacity='0.3'/%3E%3C/svg%3E"),
                url("data:image/svg+xml,%3Csvg width='40' height='40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20,2 L35,20 L20,38 L5,20 Z' stroke='%2380e6ff' stroke-width='0.3' fill='none' opacity='0.2'/%3E%3C/svg%3E");
            background-size: 120px 120px, 80px 80px;
            background-position: 0 0, 40px 40px;
            animation: crystallineFloat 20s linear infinite, prismaticShift 15s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes crystallineFloat {
            0% { transform: translateX(0) translateY(0) rotate(0deg); }
            100% { transform: translateX(-60px) translateY(-40px) rotate(360deg); }
        }

        @keyframes prismaticShift {
            0%, 100% { 
                filter: hue-rotate(0deg) brightness(1) contrast(1);
                opacity: 0.3;
            }
            25% { 
                filter: hue-rotate(90deg) brightness(1.2) contrast(1.1);
                opacity: 0.4;
            }
            50% { 
                filter: hue-rotate(180deg) brightness(1.1) contrast(1.2);
                opacity: 0.5;
            }
            75% { 
                filter: hue-rotate(270deg) brightness(1.3) contrast(1.1);
                opacity: 0.4;
            }
        }

        main {
            width: 100%;
            max-width: 900px;
            position: relative;
            z-index: 1;
        }

        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 40px;
            font-size: 2.8em;
            font-weight: 300;
            position: relative;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            letter-spacing: 2px;
        }

        h1::before,
        h1::after {
            content: '◊';
            position: absolute;
            top: 50%;
            transform: translateY(-50%) scale(1.5);
            color: #80e6ff;
            animation: crystallinePulse 3s ease-in-out infinite;
        }

        h1::before {
            left: -50px;
            animation-delay: 0s;
        }

        h1::after {
            right: -50px;
            animation-delay: 1.5s;
        }

        @keyframes crystallinePulse {
            0%, 100% { 
                transform: translateY(-50%) scale(1.5) rotate(0deg);
                opacity: 0.6;
            }
            50% { 
                transform: translateY(-50%) scale(2) rotate(45deg);
                opacity: 1;
                text-shadow: 0 0 30px rgba(128, 230, 255, 0.8);
            }
        }

        .hybrid-component {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(0, 212, 255, 0.1) 50%, rgba(255, 255, 255, 0.05) 100%);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 0 60px rgba(0, 212, 255, 0.1);
            position: relative;
            overflow: visible;
        }

        /* Crystal corner decorations */
        .hybrid-component::before,
        .hybrid-component::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #00d4ff, #80e6ff);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            animation: cornerCrystalRotate 8s linear infinite;
        }

        .hybrid-component::before {
            top: -10px;
            left: -10px;
        }

        .hybrid-component::after {
            bottom: -10px;
            right: -10px;
            animation-delay: 4s;
        }

        @keyframes cornerCrystalRotate {
            0% { 
                transform: rotate(0deg) scale(1);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            }
            50% { 
                transform: rotate(180deg) scale(1.2);
                box-shadow: 0 0 40px rgba(128, 230, 255, 0.8);
            }
            100% { 
                transform: rotate(360deg) scale(1);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            }
        }

        /* Multi-Modal Interface Layout */
        .interface-grid {
            display: grid;
            grid-template-areas: 
                "voice gesture eye"
                "touch ai adaptive"
                "status status status";
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 25px;
            margin-bottom: 30px;
        }

        /* Modal Interface Cards */
        .modal-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(0, 212, 255, 0.05) 100%);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .modal-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.1) 50%, transparent 100%);
            transition: left 0.6s ease-out;
        }

        .modal-card:hover::before {
            left: 100%;
        }

        .modal-card:hover {
            transform: translateY(-3px) scale(1.02);
            border-color: rgba(0, 212, 255, 0.4);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
        }

        .modal-card.active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(128, 230, 255, 0.1) 100%);
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }

        /* Voice Control */
        .voice-control {
            grid-area: voice;
        }

        .voice-visualizer {
            width: 100%;
            height: 60px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .voice-bars {
            display: flex;
            align-items: end;
            justify-content: center;
            height: 100%;
            gap: 3px;
            padding: 10px;
        }

        .voice-bar {
            width: 4px;
            background: linear-gradient(to top, #00d4ff, #80e6ff);
            border-radius: 2px;
            height: 20%;
            transition: height 0.1s ease-out;
        }

        .voice-control.active .voice-bar {
            animation: voiceWave 0.5s ease-in-out infinite alternate;
        }

        @keyframes voiceWave {
            0% { height: 20%; }
            100% { height: 80%; }
        }

        .voice-bar:nth-child(1) { animation-delay: 0.1s; }
        .voice-bar:nth-child(2) { animation-delay: 0.2s; }
        .voice-bar:nth-child(3) { animation-delay: 0.3s; }
        .voice-bar:nth-child(4) { animation-delay: 0.4s; }
        .voice-bar:nth-child(5) { animation-delay: 0.5s; }

        /* Gesture Recognition */
        .gesture-control {
            grid-area: gesture;
        }

        .gesture-canvas {
            width: 100%;
            height: 80px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
            cursor: crosshair;
        }

        .gesture-trail {
            position: absolute;
            pointer-events: none;
            width: 6px;
            height: 6px;
            background: radial-gradient(circle, #00d4ff, transparent);
            border-radius: 50%;
            animation: trailFade 1s ease-out forwards;
        }

        @keyframes trailFade {
            0% { 
                opacity: 1; 
                transform: scale(1);
                box-shadow: 0 0 10px #00d4ff;
            }
            100% { 
                opacity: 0; 
                transform: scale(0.1);
                box-shadow: 0 0 20px transparent;
            }
        }

        /* Eye Tracking */
        .eye-tracking {
            grid-area: eye;
        }

        .eye-display {
            width: 100%;
            height: 80px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .eye-focus {
            position: absolute;
            width: 12px;
            height: 12px;
            background: radial-gradient(circle, #00d4ff, #80e6ff);
            border-radius: 50%;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
            transition: all 0.2s ease-out;
            animation: eyeBlink 3s ease-in-out infinite;
        }

        @keyframes eyeBlink {
            0%, 95%, 100% { 
                opacity: 1; 
                transform: scale(1);
            }
            97.5% { 
                opacity: 0.3; 
                transform: scale(0.8);
            }
        }

        /* Touch Interface */
        .touch-interface {
            grid-area: touch;
        }

        .touch-surface {
            width: 100%;
            height: 80px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .touch-ripple {
            position: absolute;
            border: 2px solid #00d4ff;
            border-radius: 50%;
            animation: rippleExpand 1s ease-out forwards;
            pointer-events: none;
        }

        @keyframes rippleExpand {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                width: 100px;
                height: 100px;
                opacity: 0;
            }
        }

        /* AI Prediction */
        .ai-prediction {
            grid-area: ai;
        }

        .ai-brain {
            width: 100%;
            height: 80px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .neural-network {
            position: absolute;
            inset: 10px;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(0, 212, 255, 0.3) 2px, transparent 3px),
                radial-gradient(circle at 80% 20%, rgba(128, 230, 255, 0.3) 2px, transparent 3px),
                radial-gradient(circle at 60% 70%, rgba(0, 212, 255, 0.3) 2px, transparent 3px),
                radial-gradient(circle at 30% 80%, rgba(128, 230, 255, 0.3) 2px, transparent 3px);
            background-size: 20px 20px;
            animation: neuralPulse 2s ease-in-out infinite;
        }

        @keyframes neuralPulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        /* Adaptive Feedback */
        .adaptive-feedback {
            grid-area: adaptive;
        }

        .feedback-prism {
            width: 100%;
            height: 80px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .prism-face {
            position: absolute;
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #00d4ff, #80e6ff, #00d4ff);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: prismRotate 4s linear infinite;
        }

        @keyframes prismRotate {
            0% { 
                transform: translate(-50%, -50%) rotate(0deg);
                filter: hue-rotate(0deg);
            }
            25% { 
                transform: translate(-50%, -50%) rotate(90deg);
                filter: hue-rotate(90deg);
            }
            50% { 
                transform: translate(-50%, -50%) rotate(180deg);
                filter: hue-rotate(180deg);
            }
            75% { 
                transform: translate(-50%, -50%) rotate(270deg);
                filter: hue-rotate(270deg);
            }
            100% { 
                transform: translate(-50%, -50%) rotate(360deg);
                filter: hue-rotate(360deg);
            }
        }

        /* Status Display */
        .status-display {
            grid-area: status;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .status-crystal {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #00d4ff, #80e6ff);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            margin: 0 10px;
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { 
                transform: scale(1);
                filter: brightness(1);
            }
            50% { 
                transform: scale(1.2);
                filter: brightness(1.5);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            }
        }

        /* Card Headers */
        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: #00d4ff;
            font-weight: 500;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-icon {
            font-size: 18px;
            filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.5));
        }

        /* Interactive buttons */
        .modal-btn {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(128, 230, 255, 0.1) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: #e0f3ff;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease-out;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modal-btn:hover {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(128, 230, 255, 0.2) 100%);
            border-color: rgba(0, 212, 255, 0.5);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }

        .modal-btn.active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.4) 0%, rgba(128, 230, 255, 0.3) 100%);
            border-color: rgba(0, 212, 255, 0.7);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
        }

        /* Mode switching animation */
        .mode-transition {
            position: absolute;
            inset: 0;
            background: radial-gradient(circle, rgba(0, 212, 255, 0.3), transparent);
            opacity: 0;
            pointer-events: none;
            animation: modeSwitch 0.5s ease-out;
        }

        @keyframes modeSwitch {
            0% { 
                opacity: 0; 
                transform: scale(0.5);
            }
            50% { 
                opacity: 1; 
                transform: scale(1.2);
            }
            100% { 
                opacity: 0; 
                transform: scale(2);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .interface-grid {
                grid-template-areas: 
                    "voice"
                    "gesture"
                    "eye"
                    "touch"
                    "ai"
                    "adaptive"
                    "status";
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2.2em;
            }
            
            h1::before,
            h1::after {
                display: none;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Multi-Modal Interface - Crystalline Future Theme</h1>
        
        <div class="hybrid-component">
            <div class="interface-grid">
                <!-- Voice Control -->
                <div class="modal-card voice-control" id="voiceCard">
                    <div class="card-header">
                        <span class="card-icon">🎤</span>
                        <span>Voice Control</span>
                    </div>
                    <div class="voice-visualizer">
                        <div class="voice-bars">
                            <div class="voice-bar"></div>
                            <div class="voice-bar"></div>
                            <div class="voice-bar"></div>
                            <div class="voice-bar"></div>
                            <div class="voice-bar"></div>
                            <div class="voice-bar"></div>
                            <div class="voice-bar"></div>
                        </div>
                    </div>
                    <button class="modal-btn" id="voiceBtn">Activate Voice</button>
                </div>

                <!-- Gesture Recognition -->
                <div class="modal-card gesture-control" id="gestureCard">
                    <div class="card-header">
                        <span class="card-icon">👋</span>
                        <span>Gesture Input</span>
                    </div>
                    <div class="gesture-canvas" id="gestureCanvas"></div>
                    <button class="modal-btn" id="gestureBtn">Enable Gestures</button>
                </div>

                <!-- Eye Tracking -->
                <div class="modal-card eye-tracking" id="eyeCard">
                    <div class="card-header">
                        <span class="card-icon">👁️</span>
                        <span>Eye Tracking</span>
                    </div>
                    <div class="eye-display" id="eyeDisplay">
                        <div class="eye-focus" id="eyeFocus"></div>
                    </div>
                    <button class="modal-btn" id="eyeBtn">Start Tracking</button>
                </div>

                <!-- Touch Interface -->
                <div class="modal-card touch-interface" id="touchCard">
                    <div class="card-header">
                        <span class="card-icon">✋</span>
                        <span>Touch Input</span>
                    </div>
                    <div class="touch-surface" id="touchSurface"></div>
                    <button class="modal-btn" id="touchBtn">Touch Mode</button>
                </div>

                <!-- AI Prediction -->
                <div class="modal-card ai-prediction" id="aiCard">
                    <div class="card-header">
                        <span class="card-icon">🧠</span>
                        <span>AI Prediction</span>
                    </div>
                    <div class="ai-brain">
                        <div class="neural-network"></div>
                    </div>
                    <button class="modal-btn" id="aiBtn">AI Assist</button>
                </div>

                <!-- Adaptive Feedback -->
                <div class="modal-card adaptive-feedback" id="adaptiveCard">
                    <div class="card-header">
                        <span class="card-icon">🔄</span>
                        <span>Adaptive System</span>
                    </div>
                    <div class="feedback-prism">
                        <div class="prism-face"></div>
                    </div>
                    <button class="modal-btn" id="adaptiveBtn">Adaptive Mode</button>
                </div>

                <!-- Status Display -->
                <div class="status-display">
                    <div class="card-header">
                        <span class="card-icon">💎</span>
                        <span>System Status</span>
                    </div>
                    <div id="statusMessage">
                        <span class="status-crystal"></span>
                        Multi-Modal Interface Ready
                        <span class="status-crystal"></span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Crystalline Future Multi-Modal Interface Controller
        class CrystallineInterface {
            constructor() {
                this.activeModals = new Set();
                this.isListening = false;
                this.gestureHistory = [];
                this.eyePosition = { x: 50, y: 40 };
                this.aiState = 'idle';
                this.adaptiveLevel = 1;
                
                this.initializeInterface();
                this.startCrystallineAnimations();
            }

            initializeInterface() {
                // Voice Control
                const voiceBtn = document.getElementById('voiceBtn');
                const voiceCard = document.getElementById('voiceCard');
                
                voiceBtn.addEventListener('click', () => {
                    this.toggleVoiceControl();
                });

                // Gesture Recognition
                const gestureCanvas = document.getElementById('gestureCanvas');
                const gestureBtn = document.getElementById('gestureBtn');
                
                gestureBtn.addEventListener('click', () => {
                    this.toggleGestureControl();
                });

                gestureCanvas.addEventListener('mousemove', (e) => {
                    if (this.activeModals.has('gesture')) {
                        this.handleGestureInput(e);
                    }
                });

                // Eye Tracking
                const eyeBtn = document.getElementById('eyeBtn');
                
                eyeBtn.addEventListener('click', () => {
                    this.toggleEyeTracking();
                });

                // Touch Interface
                const touchSurface = document.getElementById('touchSurface');
                const touchBtn = document.getElementById('touchBtn');
                
                touchBtn.addEventListener('click', () => {
                    this.toggleTouchInterface();
                });

                touchSurface.addEventListener('click', (e) => {
                    if (this.activeModals.has('touch')) {
                        this.handleTouchInput(e);
                    }
                });

                // AI Prediction
                const aiBtn = document.getElementById('aiBtn');
                
                aiBtn.addEventListener('click', () => {
                    this.toggleAIPrediction();
                });

                // Adaptive Feedback
                const adaptiveBtn = document.getElementById('adaptiveBtn');
                
                adaptiveBtn.addEventListener('click', () => {
                    this.toggleAdaptiveFeedback();
                });

                // Global keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    this.handleKeyboardShortcuts(e);
                });
            }

            toggleVoiceControl() {
                const card = document.getElementById('voiceCard');
                const btn = document.getElementById('voiceBtn');
                
                if (this.activeModals.has('voice')) {
                    this.activeModals.delete('voice');
                    card.classList.remove('active');
                    btn.classList.remove('active');
                    btn.textContent = 'Activate Voice';
                    this.updateStatus('Voice control deactivated');
                } else {
                    this.activeModals.add('voice');
                    card.classList.add('active');
                    btn.classList.add('active');
                    btn.textContent = 'Voice Active';
                    this.createModeTransition(card);
                    this.updateStatus('Voice control activated - Listening...');
                    this.simulateVoiceActivity();
                }
                
                this.updateAdaptiveSystem();
            }

            toggleGestureControl() {
                const card = document.getElementById('gestureCard');
                const btn = document.getElementById('gestureBtn');
                
                if (this.activeModals.has('gesture')) {
                    this.activeModals.delete('gesture');
                    card.classList.remove('active');
                    btn.classList.remove('active');
                    btn.textContent = 'Enable Gestures';
                    this.updateStatus('Gesture recognition disabled');
                } else {
                    this.activeModals.add('gesture');
                    card.classList.add('active');
                    btn.classList.add('active');
                    btn.textContent = 'Gestures Active';
                    this.createModeTransition(card);
                    this.updateStatus('Gesture recognition enabled - Move cursor to interact');
                }
                
                this.updateAdaptiveSystem();
            }

            toggleEyeTracking() {
                const card = document.getElementById('eyeCard');
                const btn = document.getElementById('eyeBtn');
                
                if (this.activeModals.has('eye')) {
                    this.activeModals.delete('eye');
                    card.classList.remove('active');
                    btn.classList.remove('active');
                    btn.textContent = 'Start Tracking';
                    this.updateStatus('Eye tracking disabled');
                } else {
                    this.activeModals.add('eye');
                    card.classList.add('active');
                    btn.classList.add('active');
                    btn.textContent = 'Tracking Eyes';
                    this.createModeTransition(card);
                    this.updateStatus('Eye tracking enabled - Focus detected');
                    this.startEyeTracking();
                }
                
                this.updateAdaptiveSystem();
            }

            toggleTouchInterface() {
                const card = document.getElementById('touchCard');
                const btn = document.getElementById('touchBtn');
                
                if (this.activeModals.has('touch')) {
                    this.activeModals.delete('touch');
                    card.classList.remove('active');
                    btn.classList.remove('active');
                    btn.textContent = 'Touch Mode';
                    this.updateStatus('Touch interface disabled');
                } else {
                    this.activeModals.add('touch');
                    card.classList.add('active');
                    btn.classList.add('active');
                    btn.textContent = 'Touch Active';
                    this.createModeTransition(card);
                    this.updateStatus('Touch interface enabled - Tap surface to interact');
                }
                
                this.updateAdaptiveSystem();
            }

            toggleAIPrediction() {
                const card = document.getElementById('aiCard');
                const btn = document.getElementById('aiBtn');
                
                if (this.activeModals.has('ai')) {
                    this.activeModals.delete('ai');
                    card.classList.remove('active');
                    btn.classList.remove('active');
                    btn.textContent = 'AI Assist';
                    this.aiState = 'idle';
                    this.updateStatus('AI prediction disabled');
                } else {
                    this.activeModals.add('ai');
                    card.classList.add('active');
                    btn.classList.add('active');
                    btn.textContent = 'AI Thinking';
                    this.createModeTransition(card);
                    this.aiState = 'learning';
                    this.updateStatus('AI prediction enabled - Learning patterns...');
                    this.simulateAIThinking();
                }
                
                this.updateAdaptiveSystem();
            }

            toggleAdaptiveFeedback() {
                const card = document.getElementById('adaptiveCard');
                const btn = document.getElementById('adaptiveBtn');
                
                if (this.activeModals.has('adaptive')) {
                    this.activeModals.delete('adaptive');
                    card.classList.remove('active');
                    btn.classList.remove('active');
                    btn.textContent = 'Adaptive Mode';
                    this.updateStatus('Adaptive feedback disabled');
                } else {
                    this.activeModals.add('adaptive');
                    card.classList.add('active');
                    btn.classList.add('active');
                    btn.textContent = 'Adapting';
                    this.createModeTransition(card);
                    this.updateStatus('Adaptive feedback enabled - System learning preferences');
                }
                
                this.updateAdaptiveSystem();
            }

            handleGestureInput(e) {
                const rect = e.target.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // Create gesture trail
                const trail = document.createElement('div');
                trail.className = 'gesture-trail';
                trail.style.left = x + 'px';
                trail.style.top = y + 'px';
                e.target.appendChild(trail);
                
                // Store gesture data
                this.gestureHistory.push({ x, y, timestamp: Date.now() });
                
                // Clean up old trails
                setTimeout(() => {
                    if (trail.parentNode) {
                        trail.parentNode.removeChild(trail);
                    }
                }, 1000);
                
                // Clean up old gesture data
                const now = Date.now();
                this.gestureHistory = this.gestureHistory.filter(point => 
                    now - point.timestamp < 2000
                );
                
                this.updateStatus(`Gesture detected - ${this.gestureHistory.length} points tracked`);
            }

            handleTouchInput(e) {
                const rect = e.target.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // Create ripple effect
                const ripple = document.createElement('div');
                ripple.className = 'touch-ripple';
                ripple.style.left = (x - 50) + 'px';
                ripple.style.top = (y - 50) + 'px';
                e.target.appendChild(ripple);
                
                // Clean up ripple
                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 1000);
                
                this.updateStatus(`Touch registered at coordinates (${Math.round(x)}, ${Math.round(y)})`);
            }

            handleKeyboardShortcuts(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case '1':
                            e.preventDefault();
                            this.toggleVoiceControl();
                            break;
                        case '2':
                            e.preventDefault();
                            this.toggleGestureControl();
                            break;
                        case '3':
                            e.preventDefault();
                            this.toggleEyeTracking();
                            break;
                        case '4':
                            e.preventDefault();
                            this.toggleTouchInterface();
                            break;
                        case '5':
                            e.preventDefault();
                            this.toggleAIPrediction();
                            break;
                        case '6':
                            e.preventDefault();
                            this.toggleAdaptiveFeedback();
                            break;
                    }
                }
            }

            simulateVoiceActivity() {
                if (!this.activeModals.has('voice')) return;
                
                const voiceBars = document.querySelectorAll('.voice-bar');
                voiceBars.forEach((bar, index) => {
                    setTimeout(() => {
                        bar.style.height = Math.random() * 80 + 20 + '%';
                    }, index * 100);
                });
                
                setTimeout(() => {
                    this.simulateVoiceActivity();
                }, 500);
            }

            startEyeTracking() {
                if (!this.activeModals.has('eye')) return;
                
                const eyeFocus = document.getElementById('eyeFocus');
                const eyeDisplay = document.getElementById('eyeDisplay');
                const rect = eyeDisplay.getBoundingClientRect();
                
                // Simulate natural eye movement
                this.eyePosition.x += (Math.random() - 0.5) * 10;
                this.eyePosition.y += (Math.random() - 0.5) * 10;
                
                // Keep within bounds
                this.eyePosition.x = Math.max(6, Math.min(rect.width - 6, this.eyePosition.x));
                this.eyePosition.y = Math.max(6, Math.min(rect.height - 6, this.eyePosition.y));
                
                eyeFocus.style.left = this.eyePosition.x + 'px';
                eyeFocus.style.top = this.eyePosition.y + 'px';
                
                setTimeout(() => {
                    this.startEyeTracking();
                }, 200);
            }

            simulateAIThinking() {
                if (!this.activeModals.has('ai')) return;
                
                const states = ['learning', 'analyzing', 'predicting', 'optimizing'];
                this.aiState = states[Math.floor(Math.random() * states.length)];
                
                this.updateStatus(`AI ${this.aiState} - ${Math.floor(Math.random() * 100)}% confidence`);
                
                setTimeout(() => {
                    this.simulateAIThinking();
                }, 2000);
            }

            updateAdaptiveSystem() {
                const activeCount = this.activeModals.size;
                this.adaptiveLevel = Math.min(activeCount + 1, 5);
                
                if (this.activeModals.has('adaptive')) {
                    const adaptiveCard = document.getElementById('adaptiveCard');
                    const prism = adaptiveCard.querySelector('.prism-face');
                    
                    // Adjust prism rotation speed based on activity
                    prism.style.animationDuration = (6 - this.adaptiveLevel) + 's';
                    
                    this.updateStatus(`Adaptive system level ${this.adaptiveLevel} - ${activeCount} modalities active`);
                }
            }

            createModeTransition(card) {
                const transition = document.createElement('div');
                transition.className = 'mode-transition';
                card.appendChild(transition);
                
                setTimeout(() => {
                    if (transition.parentNode) {
                        transition.parentNode.removeChild(transition);
                    }
                }, 500);
            }

            updateStatus(message) {
                const statusMessage = document.getElementById('statusMessage');
                statusMessage.innerHTML = `
                    <span class="status-crystal"></span>
                    ${message}
                    <span class="status-crystal"></span>
                `;
            }

            startCrystallineAnimations() {
                // Add subtle breathing effect to the main component
                const component = document.querySelector('.hybrid-component');
                setInterval(() => {
                    const scale = 1 + Math.sin(Date.now() * 0.001) * 0.01;
                    component.style.transform = `scale(${scale})`;
                }, 50);
                
                // Create floating crystal particles
                this.createFloatingCrystals();
            }

            createFloatingCrystals() {
                setInterval(() => {
                    const crystal = document.createElement('div');
                    crystal.style.position = 'fixed';
                    crystal.style.width = '4px';
                    crystal.style.height = '4px';
                    crystal.style.background = '#00d4ff';
                    crystal.style.clipPath = 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)';
                    crystal.style.left = Math.random() * window.innerWidth + 'px';
                    crystal.style.top = window.innerHeight + 'px';
                    crystal.style.pointerEvents = 'none';
                    crystal.style.zIndex = '0';
                    crystal.style.animation = 'floatUp 8s linear forwards, sparkle 2s ease-in-out infinite';
                    
                    document.body.appendChild(crystal);
                    
                    setTimeout(() => {
                        if (crystal.parentNode) {
                            crystal.parentNode.removeChild(crystal);
                        }
                    }, 8000);
                }, 2000);
            }
        }

        // Add floating animation keyframes
        const floatingStyles = `
            @keyframes floatUp {
                0% { transform: translateY(0) rotate(0deg); opacity: 0; }
                10% { opacity: 1; }
                90% { opacity: 1; }
                100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
            }
            
            @keyframes sparkle {
                0%, 100% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.5); }
                50% { box-shadow: 0 0 15px rgba(0, 212, 255, 1); }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = floatingStyles;
        document.head.appendChild(styleSheet);

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', () => {
            new CrystallineInterface();
        });
    </script>
</body>
</html>