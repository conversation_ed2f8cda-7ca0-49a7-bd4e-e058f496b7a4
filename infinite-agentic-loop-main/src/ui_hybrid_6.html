<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Minimalism Input Intelligence</title>
    <style>
        /* Digital Minimalism Core Variables */
        :root {
            --primary-color: #000000;
            --secondary-color: #333333;
            --accent-color: #666666;
            --background-color: #ffffff;
            --surface-color: #fafafa;
            --error-color: #ff0000;
            --success-color: #00ff00;
            --warning-color: #ffaa00;
            
            --font-primary: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-mono: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
            
            --spacing-unit: 8px;
            --border-radius: 2px;
            --animation-duration: 200ms;
            --animation-easing: cubic-bezier(0.23, 1, 0.32, 1);
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background-color: var(--background-color);
            color: var(--primary-color);
            line-height: 1.4;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: calc(var(--spacing-unit) * 4);
        }

        main {
            width: 100%;
            max-width: 480px;
        }

        h1 {
            font-size: 18px;
            font-weight: 400;
            letter-spacing: 0.5px;
            margin-bottom: calc(var(--spacing-unit) * 6);
            text-align: center;
            color: var(--secondary-color);
        }

        /* Hybrid Component Container */
        .hybrid-component {
            position: relative;
            width: 100%;
        }

        .input-intelligence {
            position: relative;
            background: var(--surface-color);
            border: 1px solid transparent;
            border-radius: var(--border-radius);
            transition: all var(--animation-duration) var(--animation-easing);
        }

        .input-intelligence:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 1px var(--primary-color);
        }

        .input-intelligence.has-error {
            border-color: var(--error-color);
            box-shadow: 0 0 0 1px var(--error-color);
        }

        .input-intelligence.has-success {
            border-color: var(--success-color);
            box-shadow: 0 0 0 1px var(--success-color);
        }

        /* Main Input Field */
        .input-field {
            position: relative;
            padding: calc(var(--spacing-unit) * 3);
        }

        .input-field input {
            width: 100%;
            border: none;
            outline: none;
            background: transparent;
            font-family: var(--font-primary);
            font-size: 16px;
            font-weight: 300;
            color: var(--primary-color);
            letter-spacing: 0.3px;
        }

        .input-field input::placeholder {
            color: var(--accent-color);
            transition: opacity var(--animation-duration) var(--animation-easing);
        }

        .input-field input:focus::placeholder {
            opacity: 0;
        }

        /* Label System */
        .input-label {
            position: absolute;
            top: calc(var(--spacing-unit) * 3);
            left: calc(var(--spacing-unit) * 3);
            font-size: 16px;
            font-weight: 300;
            color: var(--accent-color);
            pointer-events: none;
            transition: all var(--animation-duration) var(--animation-easing);
            letter-spacing: 0.3px;
        }

        .input-label.focused {
            top: calc(var(--spacing-unit) * 1);
            font-size: 11px;
            font-weight: 400;
            letter-spacing: 0.8px;
            text-transform: uppercase;
            color: var(--secondary-color);
        }

        /* Validation System */
        .validation-container {
            position: absolute;
            right: calc(var(--spacing-unit) * 3);
            top: calc(var(--spacing-unit) * 3);
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .validation-icon {
            width: 16px;
            height: 16px;
            opacity: 0;
            transition: opacity var(--animation-duration) var(--animation-easing);
        }

        .validation-icon.visible {
            opacity: 1;
        }

        .validation-icon.success {
            background: linear-gradient(45deg, var(--success-color), var(--success-color));
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='3'%3E%3Cpolyline points='20,6 9,17 4,12'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
            mask-size: contain;
        }

        .validation-icon.error {
            background: linear-gradient(45deg, var(--error-color), var(--error-color));
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='3'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
            mask-size: contain;
        }

        .validation-icon.loading {
            width: 12px;
            height: 12px;
            border: 1px solid var(--accent-color);
            border-top: 1px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Help System */
        .help-system {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--background-color);
            border: 1px solid var(--surface-color);
            border-top: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            opacity: 0;
            transform: translateY(-4px);
            transition: all var(--animation-duration) var(--animation-easing);
            pointer-events: none;
            z-index: 10;
        }

        .help-system.visible {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .help-content {
            padding: calc(var(--spacing-unit) * 2);
            font-size: 12px;
            font-weight: 300;
            color: var(--secondary-color);
            line-height: 1.5;
            letter-spacing: 0.2px;
        }

        .help-examples {
            margin-top: calc(var(--spacing-unit) * 1);
            font-family: var(--font-mono);
            font-size: 11px;
            color: var(--accent-color);
            letter-spacing: 0;
        }

        /* Autocomplete System */
        .autocomplete-system {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--background-color);
            border: 1px solid var(--surface-color);
            border-top: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            max-height: 200px;
            overflow-y: auto;
            opacity: 0;
            transform: translateY(-4px);
            transition: all var(--animation-duration) var(--animation-easing);
            pointer-events: none;
            z-index: 20;
        }

        .autocomplete-system.visible {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .autocomplete-item {
            padding: calc(var(--spacing-unit) * 2);
            font-size: 14px;
            font-weight: 300;
            color: var(--secondary-color);
            cursor: pointer;
            transition: background-color var(--animation-duration) var(--animation-easing);
            border-bottom: 1px solid var(--surface-color);
            letter-spacing: 0.2px;
        }

        .autocomplete-item:last-child {
            border-bottom: none;
        }

        .autocomplete-item:hover,
        .autocomplete-item.selected {
            background-color: var(--surface-color);
        }

        .autocomplete-highlight {
            color: var(--primary-color);
            font-weight: 400;
        }

        /* Format Indicator */
        .format-indicator {
            position: absolute;
            bottom: calc(var(--spacing-unit) * 1);
            right: calc(var(--spacing-unit) * 3);
            font-size: 10px;
            font-weight: 400;
            letter-spacing: 1px;
            text-transform: uppercase;
            color: var(--accent-color);
            opacity: 0;
            transition: opacity var(--animation-duration) var(--animation-easing);
        }

        .format-indicator.visible {
            opacity: 1;
        }

        /* Progress Indicator */
        .progress-indicator {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 1px;
            background: var(--primary-color);
            width: 0%;
            transition: width var(--animation-duration) var(--animation-easing);
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            body {
                padding: calc(var(--spacing-unit) * 2);
            }
            
            main {
                max-width: 100%;
            }
            
            h1 {
                font-size: 16px;
                margin-bottom: calc(var(--spacing-unit) * 4);
            }
        }

        /* Demo Examples */
        .demo-section {
            margin-top: calc(var(--spacing-unit) * 8);
            padding-top: calc(var(--spacing-unit) * 4);
            border-top: 1px solid var(--surface-color);
        }

        .demo-label {
            font-size: 12px;
            font-weight: 400;
            letter-spacing: 0.8px;
            text-transform: uppercase;
            color: var(--accent-color);
            margin-bottom: calc(var(--spacing-unit) * 2);
        }

        .demo-input {
            margin-bottom: calc(var(--spacing-unit) * 4);
        }
    </style>
</head>
<body>
    <main>
        <h1>Input Intelligence - Digital Minimalism Theme</h1>
        
        <div class="hybrid-component">
            <!-- Primary Email Input with Full Intelligence -->
            <div class="input-intelligence" id="email-input">
                <div class="input-field">
                    <label class="input-label" for="email">Email Address</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email"
                        autocomplete="email"
                        spellcheck="false"
                        data-type="email"
                        aria-describedby="email-help"
                    >
                    <div class="validation-container">
                        <div class="validation-icon" id="email-validation"></div>
                    </div>
                    <div class="format-indicator" id="email-format"></div>
                </div>
                
                <div class="help-system" id="email-help">
                    <div class="help-content">
                        Enter a valid email address for account verification.
                        <div class="help-examples">
                            Examples: <EMAIL>, <EMAIL>
                        </div>
                    </div>
                </div>
                
                <div class="autocomplete-system" id="email-autocomplete">
                    <!-- Dynamic autocomplete suggestions -->
                </div>
                
                <div class="progress-indicator" id="email-progress"></div>
            </div>
        </div>

        <!-- Demo Section -->
        <div class="demo-section">
            <div class="demo-label">Additional Examples</div>
            
            <!-- Phone Number Input -->
            <div class="demo-input">
                <div class="input-intelligence" id="phone-input">
                    <div class="input-field">
                        <label class="input-label" for="phone">Phone Number</label>
                        <input 
                            type="tel" 
                            id="phone" 
                            name="phone"
                            autocomplete="tel"
                            data-type="phone"
                            aria-describedby="phone-help"
                        >
                        <div class="validation-container">
                            <div class="validation-icon" id="phone-validation"></div>
                        </div>
                        <div class="format-indicator" id="phone-format"></div>
                    </div>
                    
                    <div class="help-system" id="phone-help">
                        <div class="help-content">
                            Enter your phone number for SMS verification.
                            <div class="help-examples">
                                Formats: (*************, ******-123-4567, ************
                            </div>
                        </div>
                    </div>
                    
                    <div class="autocomplete-system" id="phone-autocomplete"></div>
                    <div class="progress-indicator" id="phone-progress"></div>
                </div>
            </div>

            <!-- Date Input -->
            <div class="demo-input">
                <div class="input-intelligence" id="date-input">
                    <div class="input-field">
                        <label class="input-label" for="date">Date of Birth</label>
                        <input 
                            type="text" 
                            id="date" 
                            name="date"
                            autocomplete="bday"
                            data-type="date"
                            aria-describedby="date-help"
                        >
                        <div class="validation-container">
                            <div class="validation-icon" id="date-validation"></div>
                        </div>
                        <div class="format-indicator" id="date-format"></div>
                    </div>
                    
                    <div class="help-system" id="date-help">
                        <div class="help-content">
                            Enter your date of birth for age verification.
                            <div class="help-examples">
                                Formats: MM/DD/YYYY, MM-DD-YYYY, Month DD, YYYY
                            </div>
                        </div>
                    </div>
                    
                    <div class="autocomplete-system" id="date-autocomplete"></div>
                    <div class="progress-indicator" id="date-progress"></div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Digital Minimalism Input Intelligence System
        class InputIntelligence {
            constructor(container) {
                this.container = container;
                this.input = container.querySelector('input');
                this.label = container.querySelector('.input-label');
                this.validation = container.querySelector('.validation-icon');
                this.help = container.querySelector('.help-system');
                this.autocomplete = container.querySelector('.autocomplete-system');
                this.formatIndicator = container.querySelector('.format-indicator');
                this.progress = container.querySelector('.progress-indicator');
                
                this.type = this.input.dataset.type;
                this.isValid = false;
                this.isValidating = false;
                this.selectedIndex = -1;
                this.suggestions = [];
                
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.setupFormatting();
                this.loadSuggestions();
            }
            
            bindEvents() {
                // Focus and blur events
                this.input.addEventListener('focus', () => this.handleFocus());
                this.input.addEventListener('blur', () => this.handleBlur());
                
                // Input events
                this.input.addEventListener('input', () => this.handleInput());
                this.input.addEventListener('keydown', (e) => this.handleKeydown(e));
                
                // Help system
                this.input.addEventListener('focus', () => this.showHelp());
                this.input.addEventListener('blur', () => {
                    setTimeout(() => this.hideHelp(), 150);
                });
            }
            
            handleFocus() {
                this.label.classList.add('focused');
                this.updateProgress(25);
            }
            
            handleBlur() {
                if (!this.input.value) {
                    this.label.classList.remove('focused');
                    this.updateProgress(0);
                }
                this.hideAutocomplete();
            }
            
            handleInput() {
                const value = this.input.value;
                
                // Format the input
                this.formatInput(value);
                
                // Validate
                this.validateInput(value);
                
                // Show autocomplete
                this.showAutocomplete(value);
                
                // Update progress
                this.updateProgress(this.calculateProgress(value));
                
                // Keep label focused if there's content
                if (value) {
                    this.label.classList.add('focused');
                } else if (!this.input.matches(':focus')) {
                    this.label.classList.remove('focused');
                }
            }
            
            handleKeydown(e) {
                if (this.autocomplete.classList.contains('visible')) {
                    switch (e.key) {
                        case 'ArrowDown':
                            e.preventDefault();
                            this.selectNext();
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            this.selectPrevious();
                            break;
                        case 'Enter':
                            e.preventDefault();
                            this.selectCurrent();
                            break;
                        case 'Escape':
                            this.hideAutocomplete();
                            break;
                    }
                }
            }
            
            formatInput(value) {
                let formatted = value;
                
                switch (this.type) {
                    case 'phone':
                        formatted = this.formatPhone(value);
                        break;
                    case 'date':
                        formatted = this.formatDate(value);
                        break;
                    case 'email':
                        formatted = value.toLowerCase();
                        break;
                }
                
                if (formatted !== value) {
                    const cursorPos = this.input.selectionStart;
                    this.input.value = formatted;
                    this.input.setSelectionRange(cursorPos, cursorPos);
                }
                
                this.updateFormatIndicator(formatted);
            }
            
            formatPhone(value) {
                const digits = value.replace(/\D/g, '');
                if (digits.length <= 3) return digits;
                if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
                return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
            }
            
            formatDate(value) {
                const digits = value.replace(/\D/g, '');
                if (digits.length <= 2) return digits;
                if (digits.length <= 4) return `${digits.slice(0, 2)}/${digits.slice(2)}`;
                return `${digits.slice(0, 2)}/${digits.slice(2, 4)}/${digits.slice(4, 8)}`;
            }
            
            validateInput(value) {
                this.isValidating = true;
                this.showValidation('loading');
                
                setTimeout(() => {
                    const isValid = this.checkValidity(value);
                    this.isValid = isValid;
                    this.isValidating = false;
                    
                    this.container.classList.toggle('has-error', !isValid && value.length > 0);
                    this.container.classList.toggle('has-success', isValid && value.length > 0);
                    
                    if (value.length > 0) {
                        this.showValidation(isValid ? 'success' : 'error');
                    } else {
                        this.hideValidation();
                    }
                }, 300);
            }
            
            checkValidity(value) {
                if (!value) return false;
                
                switch (this.type) {
                    case 'email':
                        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                    case 'phone':
                        const digits = value.replace(/\D/g, '');
                        return digits.length === 10;
                    case 'date':
                        const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
                        const match = value.match(dateRegex);
                        if (!match) return false;
                        
                        const month = parseInt(match[1]);
                        const day = parseInt(match[2]);
                        const year = parseInt(match[3]);
                        
                        return month >= 1 && month <= 12 && 
                               day >= 1 && day <= 31 && 
                               year >= 1900 && year <= new Date().getFullYear();
                    default:
                        return value.length > 0;
                }
            }
            
            showValidation(type) {
                this.validation.className = `validation-icon visible ${type}`;
            }
            
            hideValidation() {
                this.validation.className = 'validation-icon';
            }
            
            showHelp() {
                this.help.classList.add('visible');
            }
            
            hideHelp() {
                this.help.classList.remove('visible');
            }
            
            showAutocomplete(value) {
                if (!value || value.length < 2) {
                    this.hideAutocomplete();
                    return;
                }
                
                const filtered = this.suggestions.filter(suggestion => 
                    suggestion.toLowerCase().includes(value.toLowerCase())
                );
                
                if (filtered.length === 0) {
                    this.hideAutocomplete();
                    return;
                }
                
                this.renderAutocomplete(filtered, value);
                this.autocomplete.classList.add('visible');
                this.selectedIndex = -1;
            }
            
            hideAutocomplete() {
                this.autocomplete.classList.remove('visible');
                this.selectedIndex = -1;
            }
            
            renderAutocomplete(suggestions, query) {
                const html = suggestions.map((suggestion, index) => {
                    const highlighted = suggestion.replace(
                        new RegExp(`(${query})`, 'gi'),
                        '<span class="autocomplete-highlight">$1</span>'
                    );
                    
                    return `<div class="autocomplete-item" data-index="${index}">${highlighted}</div>`;
                }).join('');
                
                this.autocomplete.innerHTML = html;
                
                // Bind click events
                this.autocomplete.querySelectorAll('.autocomplete-item').forEach((item, index) => {
                    item.addEventListener('click', () => {
                        this.selectedIndex = index;
                        this.selectCurrent();
                    });
                });
            }
            
            selectNext() {
                const items = this.autocomplete.querySelectorAll('.autocomplete-item');
                if (items.length === 0) return;
                
                this.selectedIndex = (this.selectedIndex + 1) % items.length;
                this.updateSelection();
            }
            
            selectPrevious() {
                const items = this.autocomplete.querySelectorAll('.autocomplete-item');
                if (items.length === 0) return;
                
                this.selectedIndex = this.selectedIndex <= 0 ? items.length - 1 : this.selectedIndex - 1;
                this.updateSelection();
            }
            
            updateSelection() {
                const items = this.autocomplete.querySelectorAll('.autocomplete-item');
                items.forEach((item, index) => {
                    item.classList.toggle('selected', index === this.selectedIndex);
                });
            }
            
            selectCurrent() {
                const items = this.autocomplete.querySelectorAll('.autocomplete-item');
                if (this.selectedIndex >= 0 && items[this.selectedIndex]) {
                    const suggestion = items[this.selectedIndex].textContent;
                    this.input.value = suggestion;
                    this.hideAutocomplete();
                    this.handleInput();
                    this.input.focus();
                }
            }
            
            updateFormatIndicator(value) {
                if (!value) {
                    this.formatIndicator.classList.remove('visible');
                    return;
                }
                
                let format = '';
                switch (this.type) {
                    case 'email':
                        format = 'Email';
                        break;
                    case 'phone':
                        format = 'Phone';
                        break;
                    case 'date':
                        format = 'Date';
                        break;
                }
                
                this.formatIndicator.textContent = format;
                this.formatIndicator.classList.add('visible');
            }
            
            calculateProgress(value) {
                if (!value) return 0;
                
                const baseProgress = Math.min((value.length / 10) * 50, 50);
                const validityProgress = this.isValid ? 50 : 0;
                
                return baseProgress + validityProgress;
            }
            
            updateProgress(percentage) {
                this.progress.style.width = `${percentage}%`;
            }
            
            setupFormatting() {
                // Format-specific setup
                switch (this.type) {
                    case 'phone':
                        this.input.setAttribute('maxlength', '14');
                        break;
                    case 'date':
                        this.input.setAttribute('maxlength', '10');
                        break;
                }
            }
            
            loadSuggestions() {
                // Mock suggestions for demo
                switch (this.type) {
                    case 'email':
                        this.suggestions = [
                            '<EMAIL>',
                            '<EMAIL>',
                            '<EMAIL>',
                            '<EMAIL>',
                            '<EMAIL>'
                        ];
                        break;
                    case 'phone':
                        this.suggestions = [
                            '(*************',
                            '(*************',
                            '(*************'
                        ];
                        break;
                    case 'date':
                        this.suggestions = [
                            '01/15/1990',
                            '03/22/1985',
                            '12/05/1992'
                        ];
                        break;
                }
            }
        }
        
        // Initialize all input intelligence components
        document.addEventListener('DOMContentLoaded', () => {
            const inputs = document.querySelectorAll('.input-intelligence');
            inputs.forEach(input => new InputIntelligence(input));
        });
    </script>
</body>
</html>