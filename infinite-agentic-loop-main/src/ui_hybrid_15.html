<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Art Deco Communication Center</title>
    <style>
        /* Art Deco Communication Center Styles */
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Cinzel:wght@400;600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --gold-primary: #d4af37;
            --gold-dark: #b8941f;
            --gold-light: #f4e4a6;
            --black-deco: #1a1a1a;
            --cream: #f5f5dc;
            --emerald: #50c878;
            --ruby: #9b111e;
            --shadow-deep: 0 8px 32px rgba(0, 0, 0, 0.4);
            --shadow-elegant: 0 4px 16px rgba(212, 175, 55, 0.3);
            --gradient-gold: linear-gradient(135deg, #d4af37 0%, #f4e4a6 50%, #b8941f 100%);
            --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }
        
        body {
            font-family: 'Playfair Display', serif;
            background: var(--gradient-dark);
            min-height: 100vh;
            color: var(--cream);
            overflow-x: hidden;
        }
        
        main {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            font-family: 'Cinzel', serif;
            font-size: 3rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 3rem;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            letter-spacing: 3px;
        }
        
        h1::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 4px;
            background: var(--gradient-gold);
            border-radius: 2px;
        }
        
        h1::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 4px;
            background: var(--gradient-gold);
            border-radius: 2px;
        }
        
        .hybrid-component {
            display: grid;
            grid-template-columns: 300px 1fr 320px;
            grid-template-rows: auto 1fr;
            gap: 2rem;
            height: 80vh;
            background: rgba(26, 26, 26, 0.9);
            border: 3px solid var(--gold-primary);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-deep);
            position: relative;
            overflow: hidden;
        }
        
        .hybrid-component::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(212, 175, 55, 0.05) 2px,
                    rgba(212, 175, 55, 0.05) 4px
                );
            pointer-events: none;
            z-index: 1;
        }
        
        .status-bar {
            grid-column: 1 / -1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--gradient-gold);
            color: var(--black-deco);
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            box-shadow: var(--shadow-elegant);
            z-index: 2;
            position: relative;
        }
        
        .status-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--emerald);
            animation: pulse 2s infinite;
        }
        
        .status-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        
        .notification-badge {
            background: var(--ruby);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }
        
        .contacts-panel {
            background: rgba(245, 245, 220, 0.1);
            border: 2px solid var(--gold-dark);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            z-index: 2;
            position: relative;
        }
        
        .panel-header {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gold-primary);
            margin-bottom: 1.5rem;
            text-align: center;
            position: relative;
        }
        
        .panel-header::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: var(--gold-primary);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }
        
        .contact-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .contact-item:hover::before {
            left: 100%;
        }
        
        .contact-item:hover {
            background: rgba(212, 175, 55, 0.2);
            border-color: var(--gold-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-elegant);
        }
        
        .contact-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-gold);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: var(--black-deco);
            position: relative;
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--cream);
        }
        
        .contact-status {
            font-size: 0.8rem;
            color: var(--gold-light);
        }
        
        .chat-main {
            background: rgba(245, 245, 220, 0.05);
            border: 2px solid var(--gold-dark);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            backdrop-filter: blur(10px);
            z-index: 2;
            position: relative;
        }
        
        .chat-header {
            background: var(--gradient-gold);
            color: var(--black-deco);
            padding: 1.5rem;
            border-radius: 13px 13px 0 0;
            display: flex;
            align-items: center;
            gap: 1rem;
            font-weight: 600;
        }
        
        .chat-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--black-deco);
            color: var(--gold-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .chat-info h3 {
            margin-bottom: 0.25rem;
            font-family: 'Cinzel', serif;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .message {
            max-width: 70%;
            padding: 1rem 1.5rem;
            border-radius: 20px;
            position: relative;
            animation: slideIn 0.3s ease;
        }
        
        .message-sent {
            align-self: flex-end;
            background: var(--gradient-gold);
            color: var(--black-deco);
            border-bottom-right-radius: 5px;
        }
        
        .message-received {
            align-self: flex-start;
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.4);
            color: var(--cream);
            border-bottom-left-radius: 5px;
        }
        
        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 0.5rem;
        }
        
        .chat-input {
            padding: 1.5rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .input-field {
            flex: 1;
            background: rgba(245, 245, 220, 0.1);
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 25px;
            padding: 1rem 1.5rem;
            color: var(--cream);
            font-family: inherit;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .input-field::placeholder {
            color: rgba(245, 245, 220, 0.5);
        }
        
        .input-field:focus {
            border-color: var(--gold-primary);
            box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
        }
        
        .send-btn {
            background: var(--gradient-gold);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-elegant);
        }
        
        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
        }
        
        .notifications-panel {
            background: rgba(245, 245, 220, 0.1);
            border: 2px solid var(--gold-dark);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            z-index: 2;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .notification-item {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .notification-item:hover {
            background: rgba(212, 175, 55, 0.2);
            transform: translateX(5px);
        }
        
        .notification-type {
            font-size: 0.8rem;
            color: var(--gold-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .notification-content {
            color: var(--cream);
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        
        .notification-time {
            font-size: 0.7rem;
            color: var(--gold-light);
        }
        
        .message-history {
            background: rgba(245, 245, 220, 0.05);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-bottom: 1px solid rgba(212, 175, 55, 0.2);
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .history-item:hover {
            background: rgba(212, 175, 55, 0.1);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .controls-section {
            margin-top: auto;
            padding-top: 1rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
        }
        
        .quick-actions {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .action-btn {
            flex: 1;
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.4);
            border-radius: 8px;
            padding: 0.5rem;
            color: var(--cream);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.8rem;
        }
        
        .action-btn:hover {
            background: rgba(212, 175, 55, 0.3);
            transform: translateY(-1px);
        }
        
        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 1fr auto;
                height: auto;
                min-height: 80vh;
            }
            
            .contacts-panel,
            .notifications-panel {
                max-height: 300px;
                overflow-y: auto;
            }
        }
        
        @media (max-width: 768px) {
            main {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .hybrid-component {
                padding: 1rem;
                gap: 1rem;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
        
        /* Art Deco geometric patterns */
        .deco-corner {
            position: absolute;
            width: 40px;
            height: 40px;
            background: var(--gradient-gold);
            clip-path: polygon(0 0, 100% 0, 0 100%);
        }
        
        .deco-corner.top-left {
            top: 0;
            left: 0;
        }
        
        .deco-corner.top-right {
            top: 0;
            right: 0;
            transform: rotate(90deg);
        }
        
        .deco-corner.bottom-left {
            bottom: 0;
            left: 0;
            transform: rotate(-90deg);
        }
        
        .deco-corner.bottom-right {
            bottom: 0;
            right: 0;
            transform: rotate(180deg);
        }
        
        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        /* Focus styles */
        button:focus,
        .contact-item:focus,
        .input-field:focus {
            outline: 3px solid var(--gold-primary);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <main>
        <h1>Communication Center - Art Deco Theme</h1>
        <div class="hybrid-component">
            <!-- Art Deco corner decorations -->
            <div class="deco-corner top-left"></div>
            <div class="deco-corner top-right"></div>
            <div class="deco-corner bottom-left"></div>
            <div class="deco-corner bottom-right"></div>
            
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <div class="user-status">
                        <div class="status-indicator" aria-label="Online status"></div>
                        <span>Eleanor Gatsby</span>
                    </div>
                    <div class="current-time" id="currentTime"></div>
                </div>
                <div class="status-right">
                    <div class="notification-badge" id="notificationCount">3</div>
                    <button class="action-btn" onclick="toggleAllPanels()">Toggle All</button>
                </div>
            </div>
            
            <!-- Contacts Panel -->
            <div class="contacts-panel">
                <h2 class="panel-header">Contacts</h2>
                <div class="contacts-list" id="contactsList">
                    <div class="contact-item" onclick="selectContact('jay')" tabindex="0" role="button" aria-label="Chat with Jay Buchanan">
                        <div class="contact-avatar">JB</div>
                        <div class="contact-info">
                            <div class="contact-name">Jay Buchanan</div>
                            <div class="contact-status">Available</div>
                        </div>
                    </div>
                    <div class="contact-item" onclick="selectContact('daisy')" tabindex="0" role="button" aria-label="Chat with Daisy Fay">
                        <div class="contact-avatar">DF</div>
                        <div class="contact-info">
                            <div class="contact-name">Daisy Fay</div>
                            <div class="contact-status">Busy</div>
                        </div>
                    </div>
                    <div class="contact-item" onclick="selectContact('nick')" tabindex="0" role="button" aria-label="Chat with Nick Carraway">
                        <div class="contact-avatar">NC</div>
                        <div class="contact-info">
                            <div class="contact-name">Nick Carraway</div>
                            <div class="contact-status">Away</div>
                        </div>
                    </div>
                    <div class="contact-item" onclick="selectContact('jordan')" tabindex="0" role="button" aria-label="Chat with Jordan Baker">
                        <div class="contact-avatar">JB</div>
                        <div class="contact-info">
                            <div class="contact-name">Jordan Baker</div>
                            <div class="contact-status">Available</div>
                        </div>
                    </div>
                </div>
                
                <div class="message-history">
                    <h3 style="color: var(--gold-primary); margin-bottom: 1rem; font-size: 1rem;">Recent Conversations</h3>
                    <div class="history-item" onclick="loadConversation('jay')" tabindex="0" role="button">
                        <span>Jay Buchanan</span>
                        <span style="font-size: 0.8rem; color: var(--gold-light);">2:30 PM</span>
                    </div>
                    <div class="history-item" onclick="loadConversation('daisy')" tabindex="0" role="button">
                        <span>Daisy Fay</span>
                        <span style="font-size: 0.8rem; color: var(--gold-light);">1:15 PM</span>
                    </div>
                    <div class="history-item" onclick="loadConversation('nick')" tabindex="0" role="button">
                        <span>Nick Carraway</span>
                        <span style="font-size: 0.8rem; color: var(--gold-light);">Yesterday</span>
                    </div>
                </div>
            </div>
            
            <!-- Chat Main Area -->
            <div class="chat-main">
                <div class="chat-header">
                    <div class="chat-avatar" id="chatAvatar">JB</div>
                    <div class="chat-info">
                        <h3 id="chatName">Jay Buchanan</h3>
                        <div id="chatStatus">Available • Last seen 2 minutes ago</div>
                    </div>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message message-received">
                        <div>Old sport, are you planning to attend the soirée this evening?</div>
                        <div class="message-time">2:28 PM</div>
                    </div>
                    <div class="message message-sent">
                        <div>Indeed! The preparations are quite magnificent. Your parties are legendary, Jay.</div>
                        <div class="message-time">2:29 PM</div>
                    </div>
                    <div class="message message-received">
                        <div>Excellent! The orchestra will be playing all evening, and the champagne is from the finest vintages.</div>
                        <div class="message-time">2:30 PM</div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <input type="text" class="input-field" id="messageInput" placeholder="Type your message with Art Deco elegance..." onkeypress="handleKeyPress(event)">
                    <button class="send-btn" onclick="sendMessage()" aria-label="Send message">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Notifications Panel -->
            <div class="notifications-panel">
                <h2 class="panel-header">Notifications</h2>
                <div class="notifications-list" id="notificationsList">
                    <div class="notification-item">
                        <div class="notification-type">New Message</div>
                        <div class="notification-content">Jordan Baker sent you a message about tomorrow's tennis match</div>
                        <div class="notification-time">5 minutes ago</div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-type">Status Update</div>
                        <div class="notification-content">Daisy Fay is now available for chat</div>
                        <div class="notification-time">12 minutes ago</div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-type">Event Reminder</div>
                        <div class="notification-content">Gatsby's Grand Ball starts in 2 hours</div>
                        <div class="notification-time">30 minutes ago</div>
                    </div>
                </div>
                
                <div class="controls-section">
                    <div class="quick-actions">
                        <button class="action-btn" onclick="markAllRead()">Mark All Read</button>
                        <button class="action-btn" onclick="clearNotifications()">Clear</button>
                    </div>
                    
                    <div class="quick-actions">
                        <button class="action-btn" onclick="composeQuickMessage()">Quick Compose</button>
                        <button class="action-btn" onclick="showSettings()">Settings</button>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script>
        // Communication Center State Management
        let currentContact = 'jay';
        let conversations = {
            jay: [
                { type: 'received', content: 'Old sport, are you planning to attend the soirée this evening?', time: '2:28 PM' },
                { type: 'sent', content: 'Indeed! The preparations are quite magnificent. Your parties are legendary, Jay.', time: '2:29 PM' },
                { type: 'received', content: 'Excellent! The orchestra will be playing all evening, and the champagne is from the finest vintages.', time: '2:30 PM' }
            ],
            daisy: [
                { type: 'received', content: 'Eleanor, darling, what are your thoughts on tonight\'s festivities?', time: '1:15 PM' },
                { type: 'sent', content: 'They promise to be absolutely divine, Daisy. Your presence will make them perfect.', time: '1:16 PM' }
            ],
            nick: [
                { type: 'sent', content: 'Nick, have you seen the extraordinary preparations Jay has made?', time: 'Yesterday 8:30 PM' },
                { type: 'received', content: 'Indeed, Eleanor. The attention to detail is remarkable.', time: 'Yesterday 8:32 PM' }
            ],
            jordan: [
                { type: 'received', content: 'Shall we have our tennis match tomorrow morning?', time: '1:45 PM' }
            ]
        };
        
        let contacts = {
            jay: { name: 'Jay Buchanan', avatar: 'JB', status: 'Available • Last seen 2 minutes ago' },
            daisy: { name: 'Daisy Fay', avatar: 'DF', status: 'Busy • In a meeting' },
            nick: { name: 'Nick Carraway', avatar: 'NC', status: 'Away • Be back soon' },
            jordan: { name: 'Jordan Baker', avatar: 'JB', status: 'Available • Active now' }
        };
        
        let notifications = [
            { type: 'New Message', content: 'Jordan Baker sent you a message about tomorrow\'s tennis match', time: '5 minutes ago' },
            { type: 'Status Update', content: 'Daisy Fay is now available for chat', time: '12 minutes ago' },
            { type: 'Event Reminder', content: 'Gatsby\'s Grand Ball starts in 2 hours', time: '30 minutes ago' }
        ];
        
        // Initialize the application
        function init() {
            updateTime();
            setInterval(updateTime, 1000);
            updateNotificationCount();
            selectContact(currentContact);
        }
        
        // Time management
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // Contact management
        function selectContact(contactId) {
            currentContact = contactId;
            const contact = contacts[contactId];
            const conversation = conversations[contactId] || [];
            
            // Update chat header
            document.getElementById('chatAvatar').textContent = contact.avatar;
            document.getElementById('chatName').textContent = contact.name;
            document.getElementById('chatStatus').textContent = contact.status;
            
            // Update chat messages
            displayMessages(conversation);
            
            // Visual feedback for selected contact
            document.querySelectorAll('.contact-item').forEach(item => {
                item.style.background = 'rgba(212, 175, 55, 0.1)';
            });
            
            // Highlight selected contact (simplified approach)
            event?.target?.closest('.contact-item')?.style?.setProperty('background', 'rgba(212, 175, 55, 0.3)');
        }
        
        function displayMessages(messages) {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.innerHTML = '';
            
            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message message-${message.type}`;
                messageDiv.innerHTML = `
                    <div>${message.content}</div>
                    <div class="message-time">${message.time}</div>
                `;
                messagesContainer.appendChild(messageDiv);
            });
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Message handling
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            
            if (content) {
                const now = new Date();
                const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                // Add message to conversation
                if (!conversations[currentContact]) {
                    conversations[currentContact] = [];
                }
                conversations[currentContact].push({
                    type: 'sent',
                    content: content,
                    time: timeString
                });
                
                // Clear input
                input.value = '';
                
                // Update display
                displayMessages(conversations[currentContact]);
                
                // Simulate response (Art Deco themed)
                setTimeout(() => {
                    simulateResponse();
                }, 1000 + Math.random() * 2000);
            }
        }
        
        function simulateResponse() {
            const responses = [
                "How marvelously elegant of you to say so!",
                "Indeed, old sport, quite the remarkable observation.",
                "Absolutely divine! Your wit is as sharp as ever.",
                "Splendid! The evening promises to be magnificent.",
                "How wonderfully sophisticated. I couldn't agree more."
            ];
            
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            conversations[currentContact].push({
                type: 'received',
                content: randomResponse,
                time: timeString
            });
            
            displayMessages(conversations[currentContact]);
            
            // Add notification
            addNotification('New Message', `${contacts[currentContact].name} replied to your message`, 'Just now');
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Notification management
        function addNotification(type, content, time) {
            notifications.unshift({ type, content, time });
            updateNotificationsList();
            updateNotificationCount();
        }
        
        function updateNotificationsList() {
            const notificationsList = document.getElementById('notificationsList');
            notificationsList.innerHTML = '';
            
            notifications.slice(0, 5).forEach(notification => {
                const notificationDiv = document.createElement('div');
                notificationDiv.className = 'notification-item';
                notificationDiv.innerHTML = `
                    <div class="notification-type">${notification.type}</div>
                    <div class="notification-content">${notification.content}</div>
                    <div class="notification-time">${notification.time}</div>
                `;
                notificationsList.appendChild(notificationDiv);
            });
        }
        
        function updateNotificationCount() {
            const count = notifications.length;
            document.getElementById('notificationCount').textContent = count;
        }
        
        function markAllRead() {
            notifications = [];
            updateNotificationsList();
            updateNotificationCount();
            showFeedback('All notifications marked as read');
        }
        
        function clearNotifications() {
            notifications = [];
            updateNotificationsList();
            updateNotificationCount();
            showFeedback('All notifications cleared');
        }
        
        // Conversation history
        function loadConversation(contactId) {
            selectContact(contactId);
            showFeedback(`Loaded conversation with ${contacts[contactId].name}`);
        }
        
        // Quick actions
        function composeQuickMessage() {
            const recipient = prompt('To whom shall you send this elegant message?');
            if (recipient) {
                const message = prompt(`Compose your message to ${recipient}:`);
                if (message) {
                    addNotification('Message Sent', `Quick message sent to ${recipient}`, 'Just now');
                    showFeedback('Quick message sent with Art Deco flair');
                }
            }
        }
        
        function showSettings() {
            alert('Communication Settings:\n\n• Art Deco Theme: Enabled\n• Elegant Animations: On\n• Sophisticated Sounds: Classical\n• Golden Ratio Layout: Active\n• Luxury Mode: Premium');
        }
        
        function toggleAllPanels() {
            const panels = document.querySelectorAll('.contacts-panel, .notifications-panel');
            const isHidden = panels[0].style.display === 'none';
            
            panels.forEach(panel => {
                panel.style.display = isHidden ? 'block' : 'none';
            });
            
            showFeedback(isHidden ? 'All panels revealed' : 'All panels concealed');
        }
        
        // Feedback system
        function showFeedback(message) {
            // Create elegant feedback notification
            const feedback = document.createElement('div');
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--gradient-gold);
                color: var(--black-deco);
                padding: 1rem 2rem;
                border-radius: 10px;
                box-shadow: var(--shadow-elegant);
                z-index: 1000;
                font-weight: 600;
                animation: slideIn 0.3s ease;
            `;
            feedback.textContent = message;
            document.body.appendChild(feedback);
            
            setTimeout(() => {
                feedback.style.animation = 'slideIn 0.3s ease reverse';
                setTimeout(() => feedback.remove(), 300);
            }, 2000);
        }
        
        // Enhanced interactions
        document.addEventListener('DOMContentLoaded', () => {
            init();
            
            // Add elegant hover effects
            document.querySelectorAll('.contact-item, .notification-item, .action-btn').forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = 'var(--shadow-elegant)';
                });
                
                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            });
            
            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                            e.preventDefault();
                            const contactIds = ['jay', 'daisy', 'nick', 'jordan'];
                            selectContact(contactIds[parseInt(e.key) - 1]);
                            break;
                        case 'Enter':
                            e.preventDefault();
                            sendMessage();
                            break;
                    }
                }
            });
        });
        
        // Initialize notifications display
        updateNotificationsList();
    </script>
</body>
</html>