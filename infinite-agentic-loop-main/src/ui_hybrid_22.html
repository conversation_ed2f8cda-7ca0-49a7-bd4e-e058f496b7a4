<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEXUS Media Player - Cyberpunk Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            user-select: none;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* Animated cyberpunk background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, #00ffff22 0%, transparent 50%),
                radial-gradient(circle at 80% 50%, #ff00ff22 0%, transparent 50%),
                radial-gradient(circle at 50% 20%, #ffff0022 0%, transparent 50%);
            animation: bgPulse 10s ease-in-out infinite;
            z-index: -2;
        }

        /* Grid overlay */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(0deg, transparent 24%, rgba(0, 255, 255, 0.05) 25%, rgba(0, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 255, 0.05) 75%, rgba(0, 255, 255, 0.05) 76%, transparent 77%, transparent),
                linear-gradient(90deg, transparent 24%, rgba(0, 255, 255, 0.05) 25%, rgba(0, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 255, 0.05) 75%, rgba(0, 255, 255, 0.05) 76%, transparent 77%, transparent);
            background-size: 50px 50px;
            z-index: -1;
        }

        @keyframes bgPulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .player-container {
            width: 90vw;
            max-width: 1200px;
            height: 90vh;
            max-height: 700px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            border: 2px solid #00ffff;
            border-radius: 0;
            overflow: hidden;
            position: relative;
            box-shadow: 
                0 0 50px rgba(0, 255, 255, 0.5),
                inset 0 0 50px rgba(0, 255, 255, 0.1);
            display: grid;
            grid-template-rows: auto 1fr auto;
        }

        /* Glitch effect */
        @keyframes glitch {
            0%, 100% { 
                transform: translate(0);
                filter: hue-rotate(0deg);
            }
            20% {
                transform: translate(-2px, 2px);
                filter: hue-rotate(90deg);
            }
            40% {
                transform: translate(-2px, -2px);
                filter: hue-rotate(180deg);
            }
            60% {
                transform: translate(2px, 2px);
                filter: hue-rotate(270deg);
            }
            80% {
                transform: translate(2px, -2px);
                filter: hue-rotate(360deg);
            }
        }

        .glitch {
            animation: glitch 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-bottom: 1px solid #00ffff;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: 'NEXUS MEDIA PLAYER v2.077';
            position: absolute;
            top: 5px;
            left: 20px;
            font-size: 10px;
            color: #00ffff;
            opacity: 0.5;
            letter-spacing: 2px;
        }

        .title {
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: 4px;
            color: #00ffff;
            text-shadow: 
                0 0 10px #00ffff,
                0 0 20px #00ffff,
                0 0 30px #00ffff;
            margin-top: 10px;
        }

        /* Main content area */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            height: 100%;
            overflow: hidden;
        }

        /* Visualizer area */
        .visualizer-section {
            position: relative;
            background: #000;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .visualizer {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .visualizer-bar {
            position: absolute;
            bottom: 0;
            width: 2%;
            background: linear-gradient(to top, #00ffff, #ff00ff, #ffff00);
            transform-origin: bottom;
            transition: height 0.1s ease;
            opacity: 0.8;
            box-shadow: 0 0 10px currentColor;
        }

        /* Video display */
        .video-display {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ffff;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #00ffff;
            text-shadow: 0 0 20px currentColor;
        }

        .video-display.active {
            display: flex;
        }

        /* Playlist section */
        .playlist-section {
            background: rgba(0, 0, 0, 0.8);
            border-left: 1px solid #00ffff;
            display: flex;
            flex-direction: column;
        }

        .playlist-header {
            padding: 15px;
            border-bottom: 1px solid #00ffff;
            background: rgba(0, 255, 255, 0.1);
        }

        .playlist-header h3 {
            color: #00ffff;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-size: 14px;
        }

        .playlist {
            flex: 1;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #00ffff #000;
        }

        .playlist::-webkit-scrollbar {
            width: 8px;
        }

        .playlist::-webkit-scrollbar-track {
            background: #000;
        }

        .playlist::-webkit-scrollbar-thumb {
            background: #00ffff;
            border-radius: 0;
        }

        .track-item {
            padding: 15px;
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .track-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .track-item:hover::before {
            left: 100%;
        }

        .track-item:hover {
            background: rgba(0, 255, 255, 0.1);
            padding-left: 25px;
        }

        .track-item.active {
            background: rgba(0, 255, 255, 0.2);
            border-left: 3px solid #00ffff;
        }

        .track-title {
            color: #fff;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .track-artist {
            color: #00ffff;
            font-size: 12px;
            opacity: 0.7;
        }

        .track-duration {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #ffff00;
            font-size: 12px;
        }

        /* Controls section */
        .controls-section {
            background: rgba(0, 0, 0, 0.9);
            border-top: 1px solid #00ffff;
            padding: 20px;
        }

        .progress-container {
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, #00ffff, transparent);
            animation: scanline 2s linear infinite;
        }

        @keyframes scanline {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #ff00ff, #ffff00);
            width: 0%;
            transition: width 0.1s linear;
            position: relative;
            z-index: 1;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 12px;
            color: #00ffff;
        }

        .controls-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .playback-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .control-btn {
            background: none;
            border: 1px solid #00ffff;
            color: #00ffff;
            width: 50px;
            height: 50px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            font-size: 20px;
        }

        .control-btn.main {
            width: 60px;
            height: 60px;
            font-size: 24px;
            border-color: #ff00ff;
            color: #ff00ff;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(0, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .control-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px currentColor;
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .volume-slider {
            width: 100px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
            cursor: pointer;
        }

        .volume-fill {
            height: 100%;
            background: #00ffff;
            width: 70%;
            transition: width 0.1s ease;
        }

        .extra-controls {
            display: flex;
            gap: 10px;
        }

        .control-small {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }

        /* Quality selector */
        .quality-selector {
            position: relative;
        }

        .quality-dropdown {
            position: absolute;
            bottom: 100%;
            right: 0;
            background: #000;
            border: 1px solid #00ffff;
            display: none;
            min-width: 100px;
        }

        .quality-dropdown.active {
            display: block;
        }

        .quality-option {
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quality-option:hover {
            background: rgba(0, 255, 255, 0.2);
        }

        .quality-option.active {
            color: #ffff00;
        }

        /* Share menu */
        .share-menu {
            position: absolute;
            bottom: 100%;
            right: 0;
            background: #000;
            border: 1px solid #00ffff;
            display: none;
            padding: 10px;
        }

        .share-menu.active {
            display: block;
        }

        .share-option {
            padding: 8px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .share-option:hover {
            background: rgba(0, 255, 255, 0.2);
            transform: translateX(5px);
        }

        /* Cyberpunk text effects */
        .cyber-text {
            position: relative;
            display: inline-block;
        }

        .cyber-text::before,
        .cyber-text::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.8;
        }

        .cyber-text::before {
            color: #ff00ff;
            z-index: -1;
            animation: cyberGlitch1 0.5s infinite;
        }

        .cyber-text::after {
            color: #00ffff;
            z-index: -2;
            animation: cyberGlitch2 0.5s infinite;
        }

        @keyframes cyberGlitch1 {
            0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
            20% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, 2px); }
            40% { clip-path: inset(50% 0 20% 0); transform: translate(2px, -2px); }
            60% { clip-path: inset(10% 0 80% 0); transform: translate(-2px, -2px); }
            80% { clip-path: inset(80% 0 10% 0); transform: translate(2px, 2px); }
        }

        @keyframes cyberGlitch2 {
            0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
            20% { clip-path: inset(80% 0 10% 0); transform: translate(2px, -2px); }
            40% { clip-path: inset(10% 0 80% 0); transform: translate(-2px, 2px); }
            60% { clip-path: inset(50% 0 20% 0); transform: translate(2px, 2px); }
            80% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, -2px); }
        }

        /* Current track info */
        .current-track {
            text-align: center;
            margin-bottom: 15px;
        }

        .current-title {
            font-size: 18px;
            color: #fff;
            margin-bottom: 5px;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .current-artist {
            font-size: 14px;
            color: #00ffff;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="header">
            <h1 class="title cyber-text" data-text="NEXUS MEDIA SYSTEM">NEXUS MEDIA SYSTEM</h1>
        </div>

        <div class="main-content">
            <div class="visualizer-section">
                <div class="visualizer" id="visualizer"></div>
                <div class="video-display" id="videoDisplay">
                    <span>VIDEO MODE</span>
                </div>
            </div>

            <div class="playlist-section">
                <div class="playlist-header">
                    <h3>TRANSMISSION LOG</h3>
                </div>
                <div class="playlist" id="playlist"></div>
            </div>
        </div>

        <div class="controls-section">
            <div class="current-track">
                <div class="current-title" id="currentTitle">INITIALIZING...</div>
                <div class="current-artist" id="currentArtist">SYSTEM READY</div>
            </div>

            <div class="progress-container">
                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="time-display">
                    <span id="currentTime">00:00</span>
                    <span id="totalTime">00:00</span>
                </div>
            </div>

            <div class="controls-main">
                <div class="playback-controls">
                    <button class="control-btn" id="prevBtn">⏮</button>
                    <button class="control-btn main" id="playBtn">▶</button>
                    <button class="control-btn" id="nextBtn">⏭</button>
                </div>

                <div class="volume-control">
                    <button class="control-btn control-small" id="muteBtn">🔊</button>
                    <div class="volume-slider" id="volumeSlider">
                        <div class="volume-fill" id="volumeFill"></div>
                    </div>
                </div>

                <div class="extra-controls">
                    <button class="control-btn control-small" id="shuffleBtn">🔀</button>
                    <button class="control-btn control-small" id="repeatBtn">🔁</button>
                    <div class="quality-selector">
                        <button class="control-btn control-small" id="qualityBtn">HD</button>
                        <div class="quality-dropdown" id="qualityDropdown">
                            <div class="quality-option" data-quality="4K">4K</div>
                            <div class="quality-option active" data-quality="1080p">1080p</div>
                            <div class="quality-option" data-quality="720p">720p</div>
                            <div class="quality-option" data-quality="480p">480p</div>
                        </div>
                    </div>
                    <div class="share-container">
                        <button class="control-btn control-small" id="shareBtn">📤</button>
                        <div class="share-menu" id="shareMenu">
                            <div class="share-option">Copy Link</div>
                            <div class="share-option">Neural Link</div>
                            <div class="share-option">Quantum Share</div>
                            <div class="share-option">Matrix Upload</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Playlist data
        const tracks = [
            { id: 1, title: "Neon Dreams", artist: "Cyber Collective", duration: "3:45", type: "audio" },
            { id: 2, title: "Digital Rain", artist: "Matrix Runners", duration: "4:12", type: "audio" },
            { id: 3, title: "Ghost Protocol", artist: "Neural Network", duration: "5:03", type: "video" },
            { id: 4, title: "Blade Symphony", artist: "Replicant Echo", duration: "3:28", type: "audio" },
            { id: 5, title: "Chrome Hearts", artist: "Android Dreams", duration: "4:55", type: "audio" },
            { id: 6, title: "Cyber City Lights", artist: "Neon Pulse", duration: "6:21", type: "video" },
            { id: 7, title: "Data Stream", artist: "Binary Code", duration: "3:17", type: "audio" },
            { id: 8, title: "Hologram Love", artist: "Virtual Hearts", duration: "4:33", type: "audio" },
            { id: 9, title: "Mainframe Dreams", artist: "System Override", duration: "5:45", type: "video" },
            { id: 10, title: "Electric Shadows", artist: "Noir Tech", duration: "3:52", type: "audio" },
            { id: 11, title: "Quantum Leap", artist: "Time Hackers", duration: "4:08", type: "audio" },
            { id: 12, title: "Neural Interface", artist: "Mind Link", duration: "7:12", type: "video" }
        ];

        // Player state
        let currentTrackIndex = 0;
        let isPlaying = false;
        let currentTime = 0;
        let duration = 0;
        let volume = 0.7;
        let isMuted = false;
        let isShuffled = false;
        let repeatMode = 'none'; // none, one, all
        let visualizerInterval;

        // DOM elements
        const playBtn = document.getElementById('playBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const currentTimeEl = document.getElementById('currentTime');
        const totalTimeEl = document.getElementById('totalTime');
        const volumeSlider = document.getElementById('volumeSlider');
        const volumeFill = document.getElementById('volumeFill');
        const muteBtn = document.getElementById('muteBtn');
        const shuffleBtn = document.getElementById('shuffleBtn');
        const repeatBtn = document.getElementById('repeatBtn');
        const playlist = document.getElementById('playlist');
        const visualizer = document.getElementById('visualizer');
        const videoDisplay = document.getElementById('videoDisplay');
        const currentTitle = document.getElementById('currentTitle');
        const currentArtist = document.getElementById('currentArtist');
        const qualityBtn = document.getElementById('qualityBtn');
        const qualityDropdown = document.getElementById('qualityDropdown');
        const shareBtn = document.getElementById('shareBtn');
        const shareMenu = document.getElementById('shareMenu');

        // Initialize visualizer bars
        function initVisualizer() {
            for (let i = 0; i < 50; i++) {
                const bar = document.createElement('div');
                bar.className = 'visualizer-bar';
                bar.style.left = `${i * 2}%`;
                visualizer.appendChild(bar);
            }
        }

        // Animate visualizer
        function animateVisualizer() {
            const bars = visualizer.querySelectorAll('.visualizer-bar');
            bars.forEach(bar => {
                const height = Math.random() * 70 + 30;
                bar.style.height = `${height}%`;
                bar.style.opacity = Math.random() * 0.5 + 0.5;
            });
        }

        // Create playlist
        function createPlaylist() {
            playlist.innerHTML = '';
            tracks.forEach((track, index) => {
                const trackEl = document.createElement('div');
                trackEl.className = 'track-item';
                if (index === currentTrackIndex) trackEl.classList.add('active');
                
                trackEl.innerHTML = `
                    <div class="track-title">${track.title}</div>
                    <div class="track-artist">${track.artist}</div>
                    <div class="track-duration">${track.duration}</div>
                `;
                
                trackEl.addEventListener('click', () => loadTrack(index));
                playlist.appendChild(trackEl);
            });
        }

        // Load track
        function loadTrack(index) {
            currentTrackIndex = index;
            const track = tracks[currentTrackIndex];
            
            // Add glitch effect
            document.querySelector('.player-container').classList.add('glitch');
            setTimeout(() => {
                document.querySelector('.player-container').classList.remove('glitch');
            }, 300);
            
            // Update UI
            currentTitle.textContent = track.title;
            currentArtist.textContent = track.artist;
            
            // Parse duration
            const [minutes, seconds] = track.duration.split(':').map(Number);
            duration = minutes * 60 + seconds;
            totalTimeEl.textContent = track.duration;
            currentTime = 0;
            updateProgress();
            
            // Update playlist active state
            document.querySelectorAll('.track-item').forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });
            
            // Show/hide video display
            videoDisplay.classList.toggle('active', track.type === 'video');
            
            if (isPlaying) {
                play();
            }
        }

        // Play/pause
        function togglePlay() {
            if (isPlaying) {
                pause();
            } else {
                play();
            }
        }

        function play() {
            isPlaying = true;
            playBtn.textContent = '⏸';
            startPlayback();
            
            // Start visualizer
            visualizerInterval = setInterval(animateVisualizer, 100);
        }

        function pause() {
            isPlaying = false;
            playBtn.textContent = '▶';
            stopPlayback();
            
            // Stop visualizer
            clearInterval(visualizerInterval);
        }

        // Playback simulation
        let playbackInterval;
        
        function startPlayback() {
            playbackInterval = setInterval(() => {
                if (currentTime < duration) {
                    currentTime += 0.1;
                    updateProgress();
                } else {
                    handleTrackEnd();
                }
            }, 100);
        }

        function stopPlayback() {
            clearInterval(playbackInterval);
        }

        function updateProgress() {
            const progress = (currentTime / duration) * 100;
            progressFill.style.width = `${progress}%`;
            
            const minutes = Math.floor(currentTime / 60);
            const seconds = Math.floor(currentTime % 60);
            currentTimeEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function handleTrackEnd() {
            if (repeatMode === 'one') {
                currentTime = 0;
                play();
            } else if (repeatMode === 'all' || currentTrackIndex < tracks.length - 1) {
                nextTrack();
            } else {
                pause();
            }
        }

        // Navigation
        function prevTrack() {
            if (currentTrackIndex > 0) {
                loadTrack(currentTrackIndex - 1);
            } else if (repeatMode === 'all') {
                loadTrack(tracks.length - 1);
            }
        }

        function nextTrack() {
            if (isShuffled) {
                const nextIndex = Math.floor(Math.random() * tracks.length);
                loadTrack(nextIndex);
            } else if (currentTrackIndex < tracks.length - 1) {
                loadTrack(currentTrackIndex + 1);
            } else if (repeatMode === 'all') {
                loadTrack(0);
            }
        }

        // Progress bar seeking
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = x / rect.width;
            currentTime = percentage * duration;
            updateProgress();
        });

        // Volume control
        volumeSlider.addEventListener('click', (e) => {
            const rect = volumeSlider.getBoundingClientRect();
            const x = e.clientX - rect.left;
            volume = x / rect.width;
            updateVolume();
        });

        function updateVolume() {
            volumeFill.style.width = `${volume * 100}%`;
            muteBtn.textContent = volume === 0 || isMuted ? '🔇' : '🔊';
        }

        function toggleMute() {
            isMuted = !isMuted;
            updateVolume();
        }

        // Shuffle and repeat
        function toggleShuffle() {
            isShuffled = !isShuffled;
            shuffleBtn.style.color = isShuffled ? '#ffff00' : '#00ffff';
        }

        function toggleRepeat() {
            const modes = ['none', 'all', 'one'];
            const currentIndex = modes.indexOf(repeatMode);
            repeatMode = modes[(currentIndex + 1) % modes.length];
            
            repeatBtn.style.color = repeatMode === 'none' ? '#00ffff' : '#ffff00';
            repeatBtn.textContent = repeatMode === 'one' ? '🔂' : '🔁';
        }

        // Quality selector
        qualityBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            qualityDropdown.classList.toggle('active');
            shareMenu.classList.remove('active');
        });

        document.querySelectorAll('.quality-option').forEach(option => {
            option.addEventListener('click', (e) => {
                document.querySelectorAll('.quality-option').forEach(opt => opt.classList.remove('active'));
                option.classList.add('active');
                qualityBtn.textContent = option.dataset.quality;
                qualityDropdown.classList.remove('active');
            });
        });

        // Share menu
        shareBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            shareMenu.classList.toggle('active');
            qualityDropdown.classList.remove('active');
        });

        document.querySelectorAll('.share-option').forEach(option => {
            option.addEventListener('click', () => {
                // Add glitch effect on share
                option.classList.add('glitch');
                setTimeout(() => {
                    option.classList.remove('glitch');
                    shareMenu.classList.remove('active');
                }, 300);
            });
        });

        // Close dropdowns on outside click
        document.addEventListener('click', () => {
            qualityDropdown.classList.remove('active');
            shareMenu.classList.remove('active');
        });

        // Event listeners
        playBtn.addEventListener('click', togglePlay);
        prevBtn.addEventListener('click', prevTrack);
        nextBtn.addEventListener('click', nextTrack);
        muteBtn.addEventListener('click', toggleMute);
        shuffleBtn.addEventListener('click', toggleShuffle);
        repeatBtn.addEventListener('click', toggleRepeat);

        // Initialize
        initVisualizer();
        createPlaylist();
        loadTrack(0);
        updateVolume();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    togglePlay();
                    break;
                case 'ArrowLeft':
                    prevTrack();
                    break;
                case 'ArrowRight':
                    nextTrack();
                    break;
                case 'ArrowUp':
                    volume = Math.min(1, volume + 0.1);
                    updateVolume();
                    break;
                case 'ArrowDown':
                    volume = Math.max(0, volume - 0.1);
                    updateVolume();
                    break;
                case 'm':
                    toggleMute();
                    break;
                case 's':
                    toggleShuffle();
                    break;
                case 'r':
                    toggleRepeat();
                    break;
            }
        });

        // Add startup animation
        setTimeout(() => {
            document.querySelector('.player-container').style.opacity = '0';
            setTimeout(() => {
                document.querySelector('.player-container').style.opacity = '1';
                document.querySelector('.player-container').classList.add('glitch');
                setTimeout(() => {
                    document.querySelector('.player-container').classList.remove('glitch');
                }, 500);
            }, 100);
        }, 100);
    </script>
</body>
</html>