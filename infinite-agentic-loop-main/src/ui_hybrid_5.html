<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zen Philosophy Navigation Center</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Zen color palette - calm, muted tones */
            --zen-white: #fafaf8;
            --zen-sand: #e8e3da;
            --zen-stone: #b8b2a6;
            --zen-moss: #7a8672;
            --zen-water: #6b7f8c;
            --zen-shadow: #4a4a48;
            --zen-ink: #2c2c2a;
            
            /* Breathing animation timing */
            --breathe-in: 3s;
            --breathe-out: 4s;
            --transition-flow: cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--zen-white) 0%, var(--zen-sand) 100%);
            color: var(--zen-ink);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        main {
            width: 100%;
            max-width: 1200px;
        }

        h1 {
            text-align: center;
            font-weight: 300;
            font-size: 2rem;
            color: var(--zen-shadow);
            margin-bottom: 3rem;
            letter-spacing: 0.1em;
        }

        /* Navigation Center Container */
        .navigation-center {
            background: var(--zen-white);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        /* Zen decoration - subtle circle pattern */
        .navigation-center::before {
            content: '';
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, transparent 60%, var(--zen-sand) 100%);
            top: -150px;
            right: -150px;
            opacity: 0.3;
            animation: breathe var(--breathe-in) ease-in-out infinite alternate;
        }

        @keyframes breathe {
            0% { transform: scale(1); opacity: 0.3; }
            100% { transform: scale(1.1); opacity: 0.2; }
        }

        /* Top Section - Search and Actions */
        .nav-top {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
        }

        /* Search as Mindful Exploration */
        .search-container {
            flex: 1;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1.5rem;
            border: 2px solid transparent;
            background: var(--zen-sand);
            border-radius: 30px;
            font-size: 1rem;
            color: var(--zen-ink);
            transition: all 0.4s var(--transition-flow);
        }

        .search-input:focus {
            outline: none;
            background: var(--zen-white);
            border-color: var(--zen-stone);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .search-input::placeholder {
            color: var(--zen-stone);
        }

        .search-icon {
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--zen-stone);
            pointer-events: none;
            transition: color 0.4s var(--transition-flow);
        }

        .search-input:focus + .search-icon {
            color: var(--zen-moss);
        }

        /* Quick Actions as Zen Shortcuts */
        .quick-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-stone {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--zen-sand);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--zen-shadow);
            transition: all 0.3s var(--transition-flow);
            position: relative;
            overflow: hidden;
        }

        .action-stone:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background: var(--zen-stone);
            color: var(--zen-white);
        }

        .action-stone::after {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at center, var(--zen-water) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        .action-stone:active::after {
            opacity: 0.3;
            animation: ripple 0.6s ease-out;
        }

        @keyframes ripple {
            0% { transform: scale(0); }
            100% { transform: scale(2); }
        }

        /* Breadcrumbs as Stepping Path */
        .stepping-path {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: linear-gradient(90deg, var(--zen-sand) 0%, transparent 100%);
            border-radius: 10px;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--zen-stone) transparent;
        }

        .path-stone {
            white-space: nowrap;
            color: var(--zen-shadow);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s var(--transition-flow);
            position: relative;
        }

        .path-stone:hover {
            background: var(--zen-white);
            color: var(--zen-moss);
        }

        .path-divider {
            color: var(--zen-stone);
            font-size: 0.8rem;
        }

        .path-stone.current {
            background: var(--zen-moss);
            color: var(--zen-white);
        }

        /* Tabs as Meditation Stones */
        .meditation-stones {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--zen-sand);
            border-radius: 15px;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--zen-stone) transparent;
        }

        .stone-tab {
            padding: 1rem 2rem;
            border: none;
            background: var(--zen-white);
            border-radius: 30px;
            cursor: pointer;
            color: var(--zen-shadow);
            font-size: 1rem;
            white-space: nowrap;
            transition: all 0.4s var(--transition-flow);
            position: relative;
            overflow: hidden;
        }

        .stone-tab::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, var(--zen-moss) 0%, var(--zen-water) 100%);
            opacity: 0;
            transition: opacity 0.4s var(--transition-flow);
        }

        .stone-tab:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }

        .stone-tab.active {
            color: var(--zen-white);
        }

        .stone-tab.active::before {
            opacity: 1;
        }

        .stone-tab span {
            position: relative;
            z-index: 1;
        }

        /* Content Area */
        .content-garden {
            background: var(--zen-white);
            border-radius: 15px;
            padding: 2rem;
            min-height: 300px;
            position: relative;
            overflow: hidden;
        }

        .content-section {
            display: none;
            animation: fadeInUp 0.6s var(--transition-flow);
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* State Memory Indicator */
        .consciousness-indicator {
            position: absolute;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--zen-sand);
            border-radius: 20px;
            font-size: 0.9rem;
            color: var(--zen-shadow);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.4s var(--transition-flow);
        }

        .consciousness-indicator.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .consciousness-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--zen-moss);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.4; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* Search Results Dropdown */
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            margin-top: 0.5rem;
            background: var(--zen-white);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow-y: auto;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s var(--transition-flow);
            z-index: 10;
        }

        .search-results.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .search-result {
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: background 0.2s ease;
            border-bottom: 1px solid var(--zen-sand);
        }

        .search-result:last-child {
            border-bottom: none;
        }

        .search-result:hover {
            background: var(--zen-sand);
        }

        .search-result-title {
            font-weight: 500;
            color: var(--zen-ink);
            margin-bottom: 0.25rem;
        }

        .search-result-path {
            font-size: 0.85rem;
            color: var(--zen-stone);
        }

        /* Tooltip for actions */
        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(-5px);
            background: var(--zen-ink);
            color: var(--zen-white);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.85rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s var(--transition-flow);
            pointer-events: none;
        }

        .action-stone:hover .tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-10px);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .nav-top {
                flex-direction: column;
            }

            .quick-actions {
                width: 100%;
                justify-content: center;
            }

            .meditation-stones {
                gap: 0.5rem;
                padding: 0.75rem;
            }

            .stone-tab {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
            }

            .consciousness-indicator {
                bottom: 1rem;
                right: 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Navigation Center - Zen Philosophy Theme</h1>
        
        <div class="navigation-center">
            <!-- Top Section with Search and Quick Actions -->
            <div class="nav-top">
                <div class="search-container">
                    <input 
                        type="text" 
                        class="search-input" 
                        placeholder="Begin your mindful exploration..."
                        id="searchInput"
                    >
                    <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    <div class="search-results" id="searchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
                
                <div class="quick-actions">
                    <button class="action-stone" data-action="home">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                        <span class="tooltip">Return to beginning</span>
                    </button>
                    <button class="action-stone" data-action="bookmark">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                        </svg>
                        <span class="tooltip">Mark this moment</span>
                    </button>
                    <button class="action-stone" data-action="settings">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M12 1v6m0 6v6m11-11h-6m-6 0H1"></path>
                        </svg>
                        <span class="tooltip">Adjust your path</span>
                    </button>
                </div>
            </div>

            <!-- Breadcrumbs as Stepping Path -->
            <div class="stepping-path" id="steppingPath">
                <a href="#" class="path-stone" data-path="root">Garden</a>
                <span class="path-divider">›</span>
                <a href="#" class="path-stone" data-path="meditation">Meditation</a>
                <span class="path-divider">›</span>
                <a href="#" class="path-stone current" data-path="breathing">Breathing</a>
            </div>

            <!-- Tabs as Meditation Stones -->
            <div class="meditation-stones" id="meditationStones">
                <button class="stone-tab active" data-tab="breathing">
                    <span>Breathing</span>
                </button>
                <button class="stone-tab" data-tab="mindfulness">
                    <span>Mindfulness</span>
                </button>
                <button class="stone-tab" data-tab="movement">
                    <span>Movement</span>
                </button>
                <button class="stone-tab" data-tab="reflection">
                    <span>Reflection</span>
                </button>
                <button class="stone-tab" data-tab="wisdom">
                    <span>Wisdom</span>
                </button>
            </div>

            <!-- Content Area -->
            <div class="content-garden">
                <div class="content-section active" data-content="breathing">
                    <h2>The Art of Breathing</h2>
                    <p>In the garden of consciousness, breath is the first step on the path to awareness. Each inhale brings new possibilities, each exhale releases what no longer serves.</p>
                    <br>
                    <p>Begin with simple observation. Notice the natural rhythm without forcing change. Like water finding its level, let your breath find its own peaceful cadence.</p>
                    <br>
                    <p>Practice the 4-7-8 technique: Inhale for 4 counts, hold for 7, exhale for 8. This ancient pattern calms the mind and prepares the spirit for deeper meditation.</p>
                </div>

                <div class="content-section" data-content="mindfulness">
                    <h2>Present Moment Awareness</h2>
                    <p>Mindfulness is the art of being fully present. Like a still pond reflecting the sky, the mindful mind mirrors reality without distortion.</p>
                    <br>
                    <p>Start with your senses. What do you see, hear, feel in this exact moment? Each sensation is a doorway to presence, a bridge from thinking to being.</p>
                    <br>
                    <p>When thoughts arise, observe them like clouds passing through an empty sky. No need to grasp or push away - simply witness and return to now.</p>
                </div>

                <div class="content-section" data-content="movement">
                    <h2>Meditation in Motion</h2>
                    <p>Movement meditation transforms everyday actions into spiritual practice. Walking, stretching, even washing dishes can become paths to enlightenment.</p>
                    <br>
                    <p>Try walking meditation: Each step deliberate, each movement conscious. Feel the earth beneath your feet, the air around your body. This is moving zen.</p>
                    <br>
                    <p>Gentle yoga or tai chi brings harmony between body and mind. Flow like water, bend like bamboo - strength through flexibility, power through peace.</p>
                </div>

                <div class="content-section" data-content="reflection">
                    <h2>The Mirror of Self</h2>
                    <p>Reflection deepens understanding. Like a mountain lake that reveals both surface and depths, contemplation shows us who we truly are.</p>
                    <br>
                    <p>Journal your thoughts without judgment. Let words flow like a stream, carrying insights from the depths of consciousness to the light of awareness.</p>
                    <br>
                    <p>Ask yourself: What am I grateful for today? What lessons has life offered? In quiet reflection, wisdom emerges naturally, like flowers blooming in their season.</p>
                </div>

                <div class="content-section" data-content="wisdom">
                    <h2>Ancient Teachings</h2>
                    <p>"The pine teaches silence, the rock teaches patience, the water teaches persistence." These natural teachers surround us, offering lessons to those who listen.</p>
                    <br>
                    <p>Zen masters remind us: "Before enlightenment, chop wood, carry water. After enlightenment, chop wood, carry water." Profound truth lives in simple actions.</p>
                    <br>
                    <p>Study the wisdom traditions not as dogma but as maps. Each tradition offers a different path up the same mountain. Find the way that resonates with your spirit.</p>
                </div>
            </div>

            <!-- State Memory Indicator -->
            <div class="consciousness-indicator" id="consciousnessIndicator">
                <span class="consciousness-dot"></span>
                <span>State remembered</span>
            </div>
        </div>
    </main>

    <script>
        // State Management - Conscious Awareness
        class NavigationState {
            constructor() {
                this.state = this.loadState() || {
                    currentTab: 'breathing',
                    breadcrumbs: ['root', 'meditation', 'breathing'],
                    searchHistory: [],
                    bookmarks: [],
                    lastVisit: Date.now()
                };
                this.saveStateDebounced = this.debounce(this.saveState.bind(this), 1000);
            }

            loadState() {
                try {
                    const saved = localStorage.getItem('zenNavState');
                    return saved ? JSON.parse(saved) : null;
                } catch (e) {
                    return null;
                }
            }

            saveState() {
                try {
                    localStorage.setItem('zenNavState', JSON.stringify(this.state));
                    this.showConsciousnessIndicator();
                } catch (e) {
                    console.error('Could not save state:', e);
                }
            }

            showConsciousnessIndicator() {
                const indicator = document.getElementById('consciousnessIndicator');
                indicator.classList.add('visible');
                setTimeout(() => {
                    indicator.classList.remove('visible');
                }, 2000);
            }

            updateTab(tabName) {
                this.state.currentTab = tabName;
                this.saveStateDebounced();
            }

            updateBreadcrumbs(path) {
                const index = this.state.breadcrumbs.indexOf(path);
                if (index > -1) {
                    this.state.breadcrumbs = this.state.breadcrumbs.slice(0, index + 1);
                } else {
                    this.state.breadcrumbs.push(path);
                }
                this.saveStateDebounced();
            }

            addSearchTerm(term) {
                if (!this.state.searchHistory.includes(term)) {
                    this.state.searchHistory.unshift(term);
                    this.state.searchHistory = this.state.searchHistory.slice(0, 10);
                    this.saveStateDebounced();
                }
            }

            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }

        // Initialize Navigation State
        const navState = new NavigationState();

        // Tab Navigation - Meditation Stones
        const tabs = document.querySelectorAll('.stone-tab');
        const contents = document.querySelectorAll('.content-section');

        function switchTab(tabName) {
            tabs.forEach(tab => {
                if (tab.dataset.tab === tabName) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });

            contents.forEach(content => {
                if (content.dataset.content === tabName) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });

            navState.updateTab(tabName);
            updateBreadcrumbsForTab(tabName);
        }

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                switchTab(tab.dataset.tab);
            });
        });

        // Breadcrumb Navigation - Stepping Path
        function updateBreadcrumbsForTab(tabName) {
            const pathElement = document.getElementById('steppingPath');
            const newPath = ['root', 'meditation', tabName];
            
            pathElement.innerHTML = newPath.map((step, index) => {
                const isLast = index === newPath.length - 1;
                const stone = `<a href="#" class="path-stone ${isLast ? 'current' : ''}" data-path="${step}">${step.charAt(0).toUpperCase() + step.slice(1)}</a>`;
                const divider = isLast ? '' : '<span class="path-divider">›</span>';
                return stone + divider;
            }).join('');

            // Re-attach breadcrumb listeners
            attachBreadcrumbListeners();
            navState.updateBreadcrumbs(tabName);
        }

        function attachBreadcrumbListeners() {
            document.querySelectorAll('.path-stone').forEach(stone => {
                stone.addEventListener('click', (e) => {
                    e.preventDefault();
                    const path = stone.dataset.path;
                    if (path !== 'root' && path !== 'meditation') {
                        switchTab(path);
                    }
                });
            });
        }

        // Search Functionality - Mindful Exploration
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');

        const searchableContent = [
            { title: 'Breathing Techniques', path: 'Meditation > Breathing', tab: 'breathing' },
            { title: '4-7-8 Breathing Method', path: 'Meditation > Breathing > Techniques', tab: 'breathing' },
            { title: 'Present Moment Practice', path: 'Meditation > Mindfulness', tab: 'mindfulness' },
            { title: 'Walking Meditation', path: 'Meditation > Movement', tab: 'movement' },
            { title: 'Tai Chi Basics', path: 'Meditation > Movement > Forms', tab: 'movement' },
            { title: 'Journaling for Insight', path: 'Meditation > Reflection', tab: 'reflection' },
            { title: 'Zen Teachings', path: 'Meditation > Wisdom', tab: 'wisdom' },
            { title: 'Natural Teachers', path: 'Meditation > Wisdom > Nature', tab: 'wisdom' }
        ];

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase().trim();
            
            if (query.length > 2) {
                const results = searchableContent.filter(item => 
                    item.title.toLowerCase().includes(query) ||
                    item.path.toLowerCase().includes(query)
                );

                if (results.length > 0) {
                    displaySearchResults(results);
                    searchResults.classList.add('visible');
                } else {
                    searchResults.classList.remove('visible');
                }
            } else {
                searchResults.classList.remove('visible');
            }
        });

        searchInput.addEventListener('blur', (e) => {
            setTimeout(() => {
                searchResults.classList.remove('visible');
                if (e.target.value.trim()) {
                    navState.addSearchTerm(e.target.value.trim());
                }
            }, 200);
        });

        function displaySearchResults(results) {
            searchResults.innerHTML = results.map(result => `
                <div class="search-result" data-tab="${result.tab}">
                    <div class="search-result-title">${result.title}</div>
                    <div class="search-result-path">${result.path}</div>
                </div>
            `).join('');

            // Add click handlers to results
            searchResults.querySelectorAll('.search-result').forEach(result => {
                result.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                    const tab = result.dataset.tab;
                    switchTab(tab);
                    searchInput.value = '';
                    searchResults.classList.remove('visible');
                });
            });
        }

        // Quick Actions - Zen Shortcuts
        const actionButtons = document.querySelectorAll('.action-stone');

        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const action = button.dataset.action;
                
                switch(action) {
                    case 'home':
                        switchTab('breathing');
                        searchInput.value = '';
                        break;
                    case 'bookmark':
                        const currentTab = navState.state.currentTab;
                        if (!navState.state.bookmarks.includes(currentTab)) {
                            navState.state.bookmarks.push(currentTab);
                            navState.saveState();
                            button.style.color = 'var(--zen-moss)';
                        }
                        break;
                    case 'settings':
                        // In a real app, this would open settings
                        alert('Settings would open here - adjust your meditation preferences');
                        break;
                }
            });
        });

        // Restore state on load
        window.addEventListener('load', () => {
            // Restore last tab
            if (navState.state.currentTab) {
                switchTab(navState.state.currentTab);
            }

            // Highlight bookmarked items
            if (navState.state.bookmarks.includes(navState.state.currentTab)) {
                const bookmarkButton = document.querySelector('[data-action="bookmark"]');
                bookmarkButton.style.color = 'var(--zen-moss)';
            }

            // Initialize breadcrumb listeners
            attachBreadcrumbListeners();
        });

        // Smooth scroll for tab container
        const tabContainer = document.querySelector('.meditation-stones');
        let isDown = false;
        let startX;
        let scrollLeft;

        tabContainer.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('stone-tab')) return;
            isDown = true;
            startX = e.pageX - tabContainer.offsetLeft;
            scrollLeft = tabContainer.scrollLeft;
        });

        tabContainer.addEventListener('mouseleave', () => {
            isDown = false;
        });

        tabContainer.addEventListener('mouseup', () => {
            isDown = false;
        });

        tabContainer.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - tabContainer.offsetLeft;
            const walk = (x - startX) * 2;
            tabContainer.scrollLeft = scrollLeft - walk;
        });
    </script>
</body>
</html>