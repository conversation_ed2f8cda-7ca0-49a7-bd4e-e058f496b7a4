<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Minimalism Action Controller - Iteration 30</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
            background: #FAFAFA;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #000;
        }

        .container {
            max-width: 800px;
            width: 100%;
        }

        .header {
            margin-bottom: 80px;
            text-align: center;
        }

        .header h1 {
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 12px;
            color: #666;
            letter-spacing: 0.5px;
        }

        .controllers-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 60px;
            margin-bottom: 60px;
        }

        @media (max-width: 768px) {
            .controllers-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
        }

        .action-controller {
            position: relative;
            background: #fff;
            border: 1px solid #E5E5E5;
            padding: 32px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .action-controller:hover {
            border-color: #000;
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
        }

        .controller-label {
            font-size: 11px;
            letter-spacing: 1px;
            text-transform: uppercase;
            color: #666;
            margin-bottom: 24px;
            display: block;
        }

        .action-button {
            width: 100%;
            height: 48px;
            background: #000;
            color: #fff;
            border: none;
            font-size: 13px;
            letter-spacing: 0.5px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .action-button:hover:not(:disabled) {
            background: #222;
        }

        .action-button:disabled {
            cursor: not-allowed;
            opacity: 0.3;
        }

        .action-button.delete {
            background: #fff;
            color: #000;
            border: 1px solid #000;
        }

        .action-button.delete:hover:not(:disabled) {
            background: #FF3B30;
            color: #fff;
            border-color: #FF3B30;
        }

        .action-button.submit {
            background: #000;
        }

        .action-button.archive {
            background: #fff;
            color: #666;
            border: 1px solid #E5E5E5;
        }

        .action-button.archive:hover:not(:disabled) {
            border-color: #000;
            color: #000;
        }

        /* Button States */
        .button-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .button-content.hidden {
            opacity: 0;
            transform: translateY(10px);
        }

        /* Loading State */
        .loading-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .loading-state.active {
            opacity: 1;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 4px;
            height: 4px;
            background: currentColor;
            border-radius: 50%;
            animation: loadingPulse 1.4s ease-in-out infinite;
        }

        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes loadingPulse {
            0%, 80%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            40% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        /* Confirmation State */
        .confirmation-state {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .confirmation-state.active {
            opacity: 1;
            pointer-events: all;
        }

        .confirmation-content {
            text-align: center;
        }

        .confirmation-text {
            font-size: 12px;
            margin-bottom: 16px;
            color: #666;
        }

        .confirmation-actions {
            display: flex;
            gap: 8px;
        }

        .confirm-btn, .cancel-btn {
            padding: 8px 16px;
            font-size: 11px;
            letter-spacing: 0.5px;
            border: 1px solid #E5E5E5;
            background: #fff;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .confirm-btn {
            background: #000;
            color: #fff;
            border-color: #000;
        }

        .confirm-btn:hover {
            background: #222;
        }

        .cancel-btn:hover {
            border-color: #000;
        }

        /* Success/Error States */
        .feedback-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .feedback-state.active {
            opacity: 1;
        }

        .feedback-icon {
            width: 24px;
            height: 24px;
            margin: 0 auto 8px;
        }

        .feedback-text {
            font-size: 11px;
            letter-spacing: 0.5px;
            text-align: center;
        }

        /* Progress Bar */
        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #E5E5E5;
            overflow: hidden;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .progress-bar.active {
            opacity: 1;
        }

        .progress-fill {
            height: 100%;
            background: #000;
            width: 0%;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* SVG Icons */
        .icon-check {
            stroke: #34C759;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .icon-error {
            stroke: #FF3B30;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        /* Advanced Controllers */
        .multi-action-controller {
            grid-column: span 2;
            display: flex;
            gap: 40px;
            align-items: center;
            padding: 40px;
        }

        @media (max-width: 768px) {
            .multi-action-controller {
                grid-column: span 1;
                flex-direction: column;
                align-items: stretch;
            }
        }

        .action-info {
            flex: 1;
        }

        .action-title {
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .action-description {
            font-size: 13px;
            color: #666;
            line-height: 1.6;
        }

        .action-controls {
            display: flex;
            gap: 12px;
        }

        .secondary-button {
            padding: 12px 24px;
            font-size: 12px;
            letter-spacing: 0.5px;
            background: #fff;
            color: #666;
            border: 1px solid #E5E5E5;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .secondary-button:hover {
            border-color: #000;
            color: #000;
        }

        .primary-button {
            padding: 12px 24px;
            font-size: 12px;
            letter-spacing: 0.5px;
            background: #000;
            color: #fff;
            border: 1px solid #000;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .primary-button:hover {
            background: #222;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Action Controller</h1>
            <p>Digital Minimalism × Iteration 30</p>
        </div>

        <div class="controllers-grid">
            <!-- Save Controller -->
            <div class="action-controller">
                <span class="controller-label">Save</span>
                <button class="action-button save" data-action="save">
                    <span class="button-content">Save Changes</span>
                    <div class="loading-state">
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                    <div class="feedback-state success">
                        <svg class="feedback-icon" viewBox="0 0 24 24">
                            <path class="icon-check" d="M20 6L9 17l-5-5"/>
                        </svg>
                        <div class="feedback-text">Saved</div>
                    </div>
                    <div class="feedback-state error">
                        <svg class="feedback-icon" viewBox="0 0 24 24">
                            <circle class="icon-error" cx="12" cy="12" r="10"/>
                            <line class="icon-error" x1="12" y1="8" x2="12" y2="12"/>
                            <line class="icon-error" x1="12" y1="16" x2="12.01" y2="16"/>
                        </svg>
                        <div class="feedback-text">Failed</div>
                    </div>
                </button>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <!-- Delete Controller -->
            <div class="action-controller">
                <span class="controller-label">Delete</span>
                <button class="action-button delete" data-action="delete">
                    <span class="button-content">Delete Item</span>
                    <div class="loading-state">
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                    <div class="confirmation-state">
                        <div class="confirmation-content">
                            <div class="confirmation-text">Are you sure?</div>
                            <div class="confirmation-actions">
                                <button class="cancel-btn">Cancel</button>
                                <button class="confirm-btn">Delete</button>
                            </div>
                        </div>
                    </div>
                    <div class="feedback-state success">
                        <svg class="feedback-icon" viewBox="0 0 24 24">
                            <path class="icon-check" d="M20 6L9 17l-5-5"/>
                        </svg>
                        <div class="feedback-text">Deleted</div>
                    </div>
                </button>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <!-- Submit Controller -->
            <div class="action-controller">
                <span class="controller-label">Submit</span>
                <button class="action-button submit" data-action="submit">
                    <span class="button-content">Submit Form</span>
                    <div class="loading-state">
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                    <div class="feedback-state success">
                        <svg class="feedback-icon" viewBox="0 0 24 24">
                            <path class="icon-check" d="M20 6L9 17l-5-5"/>
                        </svg>
                        <div class="feedback-text">Submitted</div>
                    </div>
                    <div class="feedback-state error">
                        <svg class="feedback-icon" viewBox="0 0 24 24">
                            <circle class="icon-error" cx="12" cy="12" r="10"/>
                            <line class="icon-error" x1="12" y1="8" x2="12" y2="12"/>
                            <line class="icon-error" x1="12" y1="16" x2="12.01" y2="16"/>
                        </svg>
                        <div class="feedback-text">Error</div>
                    </div>
                </button>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <!-- Archive Controller -->
            <div class="action-controller">
                <span class="controller-label">Archive</span>
                <button class="action-button archive" data-action="archive">
                    <span class="button-content">Archive</span>
                    <div class="loading-state">
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                    <div class="feedback-state success">
                        <svg class="feedback-icon" viewBox="0 0 24 24">
                            <path class="icon-check" d="M20 6L9 17l-5-5"/>
                        </svg>
                        <div class="feedback-text">Archived</div>
                    </div>
                </button>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <!-- Multi-Action Controller -->
            <div class="action-controller multi-action-controller">
                <div class="action-info">
                    <h3 class="action-title">Export Document</h3>
                    <p class="action-description">Generate and download a comprehensive report with all current data and analytics.</p>
                </div>
                <div class="action-controls">
                    <button class="secondary-button" data-action="preview">Preview</button>
                    <button class="primary-button" data-action="export">Export PDF</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ActionController {
            constructor(button) {
                this.button = button;
                this.controller = button.closest('.action-controller');
                this.action = button.dataset.action;
                this.isProcessing = false;
                
                this.elements = {
                    buttonContent: button.querySelector('.button-content'),
                    loadingState: button.querySelector('.loading-state'),
                    confirmationState: button.querySelector('.confirmation-state'),
                    successState: button.querySelector('.feedback-state.success'),
                    errorState: button.querySelector('.feedback-state.error'),
                    progressBar: this.controller.querySelector('.progress-bar'),
                    progressFill: this.controller.querySelector('.progress-fill')
                };

                this.init();
            }

            init() {
                this.button.addEventListener('click', () => this.handleClick());
                
                if (this.elements.confirmationState) {
                    const cancelBtn = this.elements.confirmationState.querySelector('.cancel-btn');
                    const confirmBtn = this.elements.confirmationState.querySelector('.confirm-btn');
                    
                    cancelBtn?.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.hideConfirmation();
                    });
                    
                    confirmBtn?.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.executeAction();
                    });
                }
            }

            handleClick() {
                if (this.isProcessing) return;
                
                if (this.action === 'delete' && this.elements.confirmationState) {
                    this.showConfirmation();
                } else {
                    this.executeAction();
                }
            }

            showConfirmation() {
                this.elements.buttonContent.classList.add('hidden');
                this.elements.confirmationState.classList.add('active');
            }

            hideConfirmation() {
                this.elements.confirmationState.classList.remove('active');
                setTimeout(() => {
                    this.elements.buttonContent.classList.remove('hidden');
                }, 300);
            }

            async executeAction() {
                this.isProcessing = true;
                this.button.disabled = true;

                // Hide confirmation if visible
                if (this.elements.confirmationState?.classList.contains('active')) {
                    this.elements.confirmationState.classList.remove('active');
                }

                // Start loading
                this.elements.buttonContent.classList.add('hidden');
                if (this.elements.loadingState) {
                    this.elements.loadingState.classList.add('active');
                }
                
                // Show progress bar
                if (this.elements.progressBar) {
                    this.elements.progressBar.classList.add('active');
                    this.animateProgress();
                }

                // Simulate API call
                const duration = this.getActionDuration();
                const success = Math.random() > 0.2; // 80% success rate

                await this.delay(duration);

                // Hide loading
                if (this.elements.loadingState) {
                    this.elements.loadingState.classList.remove('active');
                }

                // Show result
                if (success && this.elements.successState) {
                    this.elements.successState.classList.add('active');
                    await this.delay(1500);
                    this.elements.successState.classList.remove('active');
                } else if (!success && this.elements.errorState) {
                    this.elements.errorState.classList.add('active');
                    await this.delay(2000);
                    this.elements.errorState.classList.remove('active');
                }

                // Reset
                await this.delay(300);
                this.elements.buttonContent.classList.remove('hidden');
                this.button.disabled = false;
                this.isProcessing = false;

                // Hide progress bar
                if (this.elements.progressBar) {
                    this.elements.progressBar.classList.remove('active');
                    this.elements.progressFill.style.width = '0%';
                }
            }

            animateProgress() {
                if (!this.elements.progressFill) return;
                
                const duration = this.getActionDuration();
                const steps = 20;
                const stepDuration = duration / steps;
                let currentStep = 0;

                const interval = setInterval(() => {
                    currentStep++;
                    const progress = (currentStep / steps) * 100;
                    this.elements.progressFill.style.width = `${progress}%`;
                    
                    if (currentStep >= steps) {
                        clearInterval(interval);
                    }
                }, stepDuration);
            }

            getActionDuration() {
                const durations = {
                    save: 1500,
                    delete: 2000,
                    submit: 2500,
                    archive: 1000,
                    export: 3000,
                    preview: 1000
                };
                return durations[this.action] || 1500;
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Initialize all action controllers
        document.querySelectorAll('.action-button, .primary-button, .secondary-button').forEach(button => {
            new ActionController(button);
        });

        // Add hover effects for multi-action controller
        document.querySelectorAll('.multi-action-controller').forEach(controller => {
            controller.addEventListener('mouseenter', () => {
                controller.style.borderColor = '#000';
            });
            
            controller.addEventListener('mouseleave', () => {
                controller.style.borderColor = '#E5E5E5';
            });
        });
    </script>
</body>
</html>