<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playful Animation Content Card</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Nunito:wght@400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', sans-serif;
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FECA57);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            padding: 2rem;
            overflow-x: hidden;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        main {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            font-family: 'Fredoka One', cursive;
            font-size: 3rem;
            text-align: center;
            color: #fff;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            margin-bottom: 3rem;
            animation: bounce 2s ease-in-out infinite alternate;
        }

        @keyframes bounce {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-10px); }
        }

        .hybrid-component {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.1),
                0 0 0 1px rgba(255,255,255,0.5);
            backdrop-filter: blur(10px);
            transform: perspective(1000px) rotateX(2deg);
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .hybrid-component:hover {
            transform: perspective(1000px) rotateX(0deg) translateY(-10px);
            box-shadow: 
                0 30px 60px rgba(0,0,0,0.15),
                0 0 0 1px rgba(255,255,255,0.8);
        }

        .content-card {
            position: relative;
            overflow: hidden;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid #FF6B6B;
            border-image: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1) 1;
        }

        .card-title {
            font-family: 'Fredoka One', cursive;
            font-size: 1.8rem;
            color: #2C3E50;
            animation: wiggle 3s ease-in-out infinite;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(1deg); }
            75% { transform: rotate(-1deg); }
        }

        .favorite-btn {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            transform-origin: center;
        }

        .favorite-btn:hover {
            animation: heartBeat 0.6s ease-in-out;
        }

        .favorite-btn.favorited {
            color: #FF6B6B;
            animation: favoriteExplosion 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.3); }
        }

        @keyframes favoriteExplosion {
            0% { transform: scale(1); }
            50% { transform: scale(1.5) rotate(10deg); }
            100% { transform: scale(1); }
        }

        .content-preview {
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            color: white;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            overflow: hidden;
        }

        .content-preview::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 20%, transparent 21%);
            background-size: 30px 30px;
            animation: floatPattern 20s linear infinite;
        }

        @keyframes floatPattern {
            0% { transform: translate(0, 0); }
            100% { transform: translate(30px, 30px); }
        }

        .content-preview:hover {
            transform: scale(1.05) rotateZ(1deg);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .preview-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .preview-title {
            font-family: 'Fredoka One', cursive;
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: colorShift 4s ease-in-out infinite;
        }

        @keyframes colorShift {
            0%, 100% { color: #fff; }
            33% { color: #FFE066; }
            66% { color: #FF6B6B; }
        }

        .preview-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .metadata {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            color: white;
        }

        .metadata-item {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            animation: float 3s ease-in-out infinite;
            backdrop-filter: blur(5px);
        }

        .metadata-item:nth-child(1) { animation-delay: 0s; }
        .metadata-item:nth-child(2) { animation-delay: 0.5s; }
        .metadata-item:nth-child(3) { animation-delay: 1s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-family: 'Nunito', sans-serif;
            font-weight: 700;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transition: all 0.5s ease;
            transform: translate(-50%, -50%);
        }

        .action-btn:hover::before {
            width: 200px;
            height: 200px;
        }

        .action-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 25px rgba(0,0,0,0.2);
        }

        .action-btn:active {
            transform: translateY(-2px) scale(0.98);
            animation: squeeze 0.2s ease;
        }

        @keyframes squeeze {
            0%, 100% { transform: scale(0.98); }
            50% { transform: scale(0.95); }
        }

        .share-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .share-title {
            font-family: 'Fredoka One', cursive;
            font-size: 1.3rem;
            color: #2C3E50;
            margin-bottom: 1rem;
            text-align: center;
        }

        .share-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .share-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
            position: relative;
        }

        .share-btn.twitter { background: linear-gradient(135deg, #1DA1F2, #0d8bd9); color: white; }
        .share-btn.facebook { background: linear-gradient(135deg, #4267B2, #365899); color: white; }
        .share-btn.linkedin { background: linear-gradient(135deg, #0077B5, #005885); color: white; }
        .share-btn.email { background: linear-gradient(135deg, #FF6B6B, #ee5a52); color: white; }

        .share-btn:hover {
            transform: scale(1.2) rotateZ(10deg);
            box-shadow: 0 15px 25px rgba(0,0,0,0.2);
        }

        .share-btn:active {
            animation: shareWiggle 0.5s ease;
        }

        @keyframes shareWiggle {
            0%, 100% { transform: scale(1.2) rotateZ(10deg); }
            25% { transform: scale(1.3) rotateZ(-5deg); }
            75% { transform: scale(1.1) rotateZ(15deg); }
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 25px;
            padding: 3rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.5) rotateY(180deg);
            transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 30px 60px rgba(0,0,0,0.3);
        }

        .modal.active .modal-content {
            transform: scale(1) rotateY(0deg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid #FF6B6B;
        }

        .modal-title {
            font-family: 'Fredoka One', cursive;
            font-size: 2rem;
            color: #2C3E50;
        }

        .close-btn {
            background: #FF6B6B;
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .close-btn:hover {
            transform: scale(1.2) rotate(90deg);
            background: #ee5a52;
        }

        .modal-body {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2C3E50;
        }

        /* Animation Classes */
        .bounce-in {
            animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            h1 { font-size: 2rem; }
            .hybrid-component { padding: 1rem; margin: 0 1rem; }
            .action-buttons { flex-direction: column; }
            .action-btn { width: 100%; }
            .share-buttons { justify-content: center; }
            .card-title { font-size: 1.4rem; }
        }

        /* Success/Error Messages */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            z-index: 2000;
            transform: translateX(400px);
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .message.show {
            transform: translateX(0);
        }

        .message.success {
            background: linear-gradient(135deg, #96CEB4, #79a88a);
        }

        .message.error {
            background: linear-gradient(135deg, #FF6B6B, #ee5a52);
        }
    </style>
</head>
<body>
    <main>
        <h1>Content Card - Playful Animation Theme</h1>
        <div class="hybrid-component">
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">Amazing Adventure Story</h2>
                    <button class="favorite-btn" id="favoriteBtn" aria-label="Add to favorites">🤍</button>
                </div>

                <div class="content-preview" id="contentPreview">
                    <div class="preview-content">
                        <h3 class="preview-title">🎪 The Magical Carousel</h3>
                        <p class="preview-description">
                            Join Luna and her enchanted friends as they discover a mysterious carousel 
                            that comes to life at midnight, leading them on an extraordinary adventure 
                            through wonderland where dreams become reality...
                        </p>
                    </div>
                </div>

                <div class="metadata">
                    <div class="metadata-item">📚 Fantasy</div>
                    <div class="metadata-item">⏱️ 15 min read</div>
                    <div class="metadata-item">⭐ 4.8/5</div>
                    <div class="metadata-item">👥 1.2k readers</div>
                </div>

                <div class="action-buttons">
                    <button class="action-btn" id="readBtn">📖 Read Full Story</button>
                    <button class="action-btn" id="previewBtn">👀 Quick Preview</button>
                    <button class="action-btn" id="downloadBtn">💾 Download</button>
                    <button class="action-btn" id="bookmarkBtn">🔖 Save for Later</button>
                </div>

                <div class="share-section">
                    <h3 class="share-title">🎉 Share the Magic!</h3>
                    <div class="share-buttons">
                        <button class="share-btn twitter" data-platform="twitter" aria-label="Share on Twitter">🐦</button>
                        <button class="share-btn facebook" data-platform="facebook" aria-label="Share on Facebook">📘</button>
                        <button class="share-btn linkedin" data-platform="linkedin" aria-label="Share on LinkedIn">💼</button>
                        <button class="share-btn email" data-platform="email" aria-label="Share via Email">📧</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Story Preview</h3>
                <button class="close-btn" id="closeBtn" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <p>The old carousel stood silent in the moonlight, its painted horses frozen mid-gallop. Luna pressed her face against the cold iron gates of the abandoned amusement park, her breath forming small clouds in the chilly night air.</p>
                
                <p>"They say it comes alive at midnight," whispered her friend Marcus, adjusting his glasses nervously. "My grandmother told me stories about children who rode the carousel and never came back... or came back changed."</p>
                
                <p>Luna's eyes sparkled with curiosity rather than fear. She had always been drawn to mysteries, to the spaces between what was real and what was possible. As the clock tower in the distance began to chime twelve, something extraordinary happened...</p>
                
                <p>The carousel began to glow with a soft, ethereal light. The painted horses started to move, their wooden bodies transforming into living, breathing creatures of starlight and dreams. Music filled the air - not the tinny mechanical tune of an old music box, but a symphony of wonder that seemed to come from the very heart of magic itself.</p>
                
                <p><em>Click "Read Full Story" to continue this magical adventure and discover what happens when Luna and Marcus step through the gates into a world where anything is possible...</em></p>
            </div>
        </div>
    </div>

    <script>
        // Global state
        const state = {
            isFavorited: false,
            isShared: false,
            modalOpen: false
        };

        // DOM elements
        const favoriteBtn = document.getElementById('favoriteBtn');
        const contentPreview = document.getElementById('contentPreview');
        const modal = document.getElementById('modal');
        const closeBtn = document.getElementById('closeBtn');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        
        // Action buttons
        const readBtn = document.getElementById('readBtn');
        const previewBtn = document.getElementById('previewBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        
        // Share buttons
        const shareButtons = document.querySelectorAll('.share-btn');

        // Favorite functionality
        favoriteBtn.addEventListener('click', function() {
            state.isFavorited = !state.isFavorited;
            
            if (state.isFavorited) {
                this.textContent = '❤️';
                this.classList.add('favorited');
                showMessage('Added to favorites! 💖', 'success');
                
                // Create floating hearts effect
                createFloatingHearts();
            } else {
                this.textContent = '🤍';
                this.classList.remove('favorited');
                showMessage('Removed from favorites', 'error');
            }
            
            // Add bounce animation
            this.classList.add('bounce-in');
            setTimeout(() => this.classList.remove('bounce-in'), 600);
        });

        // Content preview click - open modal
        contentPreview.addEventListener('click', function() {
            openModal('Story Preview', modalBody.innerHTML);
            this.classList.add('bounce-in');
            setTimeout(() => this.classList.remove('bounce-in'), 600);
        });

        // Action button functionality
        readBtn.addEventListener('click', function() {
            showMessage('Opening full story... 📖✨', 'success');
            addButtonBounce(this);
            
            setTimeout(() => {
                openModal('The Magical Carousel - Full Story', `
                    <div style="text-align: center; padding: 2rem;">
                        <h3 style="color: #FF6B6B; font-family: 'Fredoka One', cursive; margin-bottom: 1rem;">🎪 Chapter 1: The Midnight Discovery</h3>
                        <p>In a full version, you would read the complete magical adventure story here. This would include all chapters, beautiful illustrations, and interactive elements that bring the story to life!</p>
                        <div style="margin-top: 2rem;">
                            <button class="action-btn" onclick="showMessage('Feature coming soon! 🚀', 'success')" style="margin: 0.5rem;">📖 Continue Reading</button>
                            <button class="action-btn" onclick="showMessage('Bookmarked! 📚', 'success')" style="margin: 0.5rem;">🔖 Bookmark Page</button>
                        </div>
                    </div>
                `);
            }, 500);
        });

        previewBtn.addEventListener('click', function() {
            showMessage('Loading preview... 👀✨', 'success');
            addButtonBounce(this);
            openModal('Quick Preview', modalBody.innerHTML);
        });

        downloadBtn.addEventListener('click', function() {
            showMessage('Download started! 💾🎉', 'success');
            addButtonBounce(this);
            
            // Simulate download
            setTimeout(() => {
                showMessage('Story downloaded successfully! 📚✨', 'success');
            }, 2000);
        });

        bookmarkBtn.addEventListener('click', function() {
            showMessage('Saved for later! 🔖💫', 'success');
            addButtonBounce(this);
            
            // Change button text temporarily
            const originalText = this.textContent;
            this.textContent = '✅ Saved!';
            this.style.background = 'linear-gradient(135deg, #96CEB4, #79a88a)';
            
            setTimeout(() => {
                this.textContent = originalText;
                this.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }, 2000);
        });

        // Share functionality
        shareButtons.forEach(button => {
            button.addEventListener('click', function() {
                const platform = this.dataset.platform;
                const title = 'Amazing Adventure Story - The Magical Carousel';
                const url = window.location.href;
                
                addButtonBounce(this);
                
                switch(platform) {
                    case 'twitter':
                        showMessage('Shared on Twitter! 🐦💙', 'success');
                        // In real app: window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`);
                        break;
                    case 'facebook':
                        showMessage('Shared on Facebook! 📘💙', 'success');
                        // In real app: window.open(`https://facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`);
                        break;
                    case 'linkedin':
                        showMessage('Shared on LinkedIn! 💼✨', 'success');
                        // In real app: window.open(`https://linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`);
                        break;
                    case 'email':
                        showMessage('Email composer opened! 📧💌', 'success');
                        // In real app: window.location.href = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}`;
                        break;
                }
                
                // Create share explosion effect
                createShareExplosion(this);
            });
        });

        // Modal functionality
        function openModal(title, content) {
            modalTitle.textContent = title;
            if (typeof content === 'string') {
                modalBody.innerHTML = content;
            }
            modal.classList.add('active');
            state.modalOpen = true;
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            modal.classList.remove('active');
            state.modalOpen = false;
            
            // Restore body scroll
            document.body.style.overflow = 'auto';
        }

        closeBtn.addEventListener('click', closeModal);

        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && state.modalOpen) {
                closeModal();
            }
        });

        // Utility functions
        function showMessage(text, type) {
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.body.appendChild(message);
            
            setTimeout(() => message.classList.add('show'), 100);
            
            setTimeout(() => {
                message.classList.remove('show');
                setTimeout(() => message.remove(), 500);
            }, 3000);
        }

        function addButtonBounce(button) {
            button.classList.add('bounce-in');
            setTimeout(() => button.classList.remove('bounce-in'), 600);
        }

        function createFloatingHearts() {
            const hearts = ['💖', '💕', '💗', '💝', '❤️'];
            
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const heart = document.createElement('div');
                    heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
                    heart.style.cssText = `
                        position: fixed;
                        font-size: 2rem;
                        pointer-events: none;
                        z-index: 3000;
                        left: ${favoriteBtn.getBoundingClientRect().left + Math.random() * 100 - 50}px;
                        top: ${favoriteBtn.getBoundingClientRect().top}px;
                        animation: floatUp 2s ease-out forwards;
                    `;
                    
                    document.body.appendChild(heart);
                    
                    setTimeout(() => heart.remove(), 2000);
                }, i * 200);
            }
        }

        function createShareExplosion(button) {
            const emojis = ['✨', '🎉', '💫', '⭐', '🌟'];
            
            for (let i = 0; i < 6; i++) {
                setTimeout(() => {
                    const emoji = document.createElement('div');
                    emoji.textContent = emojis[Math.floor(Math.random() * emojis.length)];
                    emoji.style.cssText = `
                        position: fixed;
                        font-size: 1.5rem;
                        pointer-events: none;
                        z-index: 3000;
                        left: ${button.getBoundingClientRect().left + button.offsetWidth / 2}px;
                        top: ${button.getBoundingClientRect().top + button.offsetHeight / 2}px;
                        animation: explode 1s ease-out forwards;
                        transform-origin: center;
                    `;
                    
                    document.body.appendChild(emoji);
                    
                    setTimeout(() => emoji.remove(), 1000);
                }, i * 100);
            }
        }

        // Add custom CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes floatUp {
                0% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-100px) scale(1.5);
                }
            }
            
            @keyframes explode {
                0% {
                    opacity: 1;
                    transform: scale(1) translate(0, 0);
                }
                100% {
                    opacity: 0;
                    transform: scale(1.5) translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px);
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize with fun entrance animation
        window.addEventListener('load', function() {
            const component = document.querySelector('.hybrid-component');
            component.classList.add('bounce-in');
            
            // Stagger animation for elements
            const elements = document.querySelectorAll('.metadata-item, .action-btn, .share-btn');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.classList.add('bounce-in');
                    setTimeout(() => el.classList.remove('bounce-in'), 600);
                }, index * 100);
            });
        });

        // Add playful hover sounds (visual feedback)
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = '';
                }, 10);
            });
        });

        // Random playful elements
        setInterval(() => {
            const title = document.querySelector('.card-title');
            if (Math.random() < 0.1) { // 10% chance every interval
                title.classList.add('shake');
                setTimeout(() => title.classList.remove('shake'), 500);
            }
        }, 10000);

        // Interactive cursor effects for enhanced playfulness
        document.addEventListener('mousemove', function(e) {
            if (Math.random() < 0.002) { // Very rare sparkle effect
                const sparkle = document.createElement('div');
                sparkle.textContent = '✨';
                sparkle.style.cssText = `
                    position: fixed;
                    pointer-events: none;
                    z-index: 1000;
                    left: ${e.clientX}px;
                    top: ${e.clientY}px;
                    font-size: 1rem;
                    animation: fadeOut 1s ease-out forwards;
                `;
                document.body.appendChild(sparkle);
                setTimeout(() => sparkle.remove(), 1000);
            }
        });

        // Add fadeOut animation
        const fadeOutStyle = document.createElement('style');
        fadeOutStyle.textContent = `
            @keyframes fadeOut {
                0% { opacity: 1; transform: scale(1); }
                100% { opacity: 0; transform: scale(2); }
            }
        `;
        document.head.appendChild(fadeOutStyle);
    </script>
</body>
</html>