<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architectural Brutalism Dashboard Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Brutalist color palette - raw concrete aesthetics */
            --concrete-light: #e8e6e3;
            --concrete-medium: #c8c4bd;
            --concrete-dark: #9a9590;
            --concrete-shadow: #6b6660;
            --concrete-deep: #4a453f;
            --steel-black: #2c2a26;
            --warning-orange: #d35400;
            --accent-blue: #3498db;
            --success-green: #27ae60;
            --error-red: #e74c3c;
            
            /* Brutalist typography */
            --font-primary: 'Arial Black', 'Helvetica Bold', sans-serif;
            --font-secondary: 'Courier New', monospace;
            
            /* Massive shadows */
            --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.4);
            --shadow-brutal: 0 16px 64px rgba(0, 0, 0, 0.6);
            --shadow-inset: inset 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(145deg, var(--concrete-medium) 0%, var(--concrete-dark) 100%);
            color: var(--steel-black);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        /* Concrete texture overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(0,0,0,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0,0,0,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        main {
            width: 100%;
            max-width: 1400px;
            position: relative;
            z-index: 2;
        }

        h1 {
            text-align: center;
            font-size: 3rem;
            font-weight: 900;
            color: var(--steel-black);
            margin-bottom: 40px;
            text-transform: uppercase;
            letter-spacing: 0.2em;
            text-shadow: 4px 4px 0px var(--concrete-shadow);
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 8px;
            background: var(--steel-black);
            box-shadow: var(--shadow-heavy);
        }

        /* Main Dashboard Container - Monolithic Block */
        .hybrid-component {
            background: var(--concrete-light);
            border: none;
            box-shadow: var(--shadow-brutal);
            position: relative;
            overflow: hidden;
            transform: perspective(1000px) rotateX(2deg);
        }

        /* Concrete block structure */
        .hybrid-component::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(90deg, 
                var(--concrete-shadow) 0%, 
                var(--concrete-dark) 10%, 
                var(--concrete-light) 20%, 
                var(--concrete-light) 80%, 
                var(--concrete-dark) 90%, 
                var(--concrete-shadow) 100%);
            box-shadow: var(--shadow-inset);
        }

        /* Dashboard Header - Imposing Command Center */
        .dashboard-header {
            background: var(--steel-black);
            padding: 30px 40px;
            margin-bottom: 0;
            position: relative;
            box-shadow: var(--shadow-inset);
        }

        .header-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .dashboard-title {
            color: var(--concrete-light);
            font-size: 1.8rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 0.15em;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        /* Brutalist Buttons - Massive Concrete Blocks */
        .brutal-button {
            background: var(--concrete-medium);
            border: none;
            padding: 15px 25px;
            font-family: var(--font-primary);
            font-weight: 900;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: var(--steel-black);
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            box-shadow: var(--shadow-heavy);
            min-width: 120px;
        }

        .brutal-button:hover {
            background: var(--concrete-dark);
            transform: translateY(-4px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.5);
        }

        .brutal-button:active {
            transform: translateY(2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
        }

        .brutal-button.active {
            background: var(--warning-orange);
            color: white;
            box-shadow: 0 8px 32px rgba(211, 84, 0, 0.4);
        }

        /* Main Dashboard Grid - Modular Blocks */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 0;
            padding: 40px;
            background: var(--concrete-light);
        }

        /* Chart Section - Dominant Visual Block */
        .chart-section {
            background: var(--concrete-medium);
            padding: 30px;
            position: relative;
            box-shadow: var(--shadow-inset);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 4px solid var(--steel-black);
            padding-bottom: 15px;
        }

        .chart-title {
            font-size: 1.4rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: var(--steel-black);
        }

        .chart-container {
            height: 400px;
            background: var(--concrete-light);
            border: 4px solid var(--steel-black);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-inset);
        }

        /* Brutalist Chart Visualization */
        .chart-canvas {
            width: 100%;
            height: 100%;
            position: relative;
            background: linear-gradient(180deg, 
                var(--concrete-light) 0%, 
                var(--concrete-medium) 50%, 
                var(--concrete-dark) 100%);
        }

        .chart-bars {
            display: flex;
            align-items: flex-end;
            height: 100%;
            padding: 20px;
            gap: 10px;
        }

        .chart-bar {
            flex: 1;
            background: var(--steel-black);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
        }

        .chart-bar:hover {
            background: var(--warning-orange);
            transform: scaleY(1.1);
        }

        .chart-bar::before {
            content: attr(data-value);
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-family: var(--font-secondary);
            font-weight: bold;
            font-size: 12px;
            color: var(--steel-black);
        }

        /* Controls Panel - Fortress-like Sidebar */
        .controls-panel {
            background: var(--concrete-dark);
            padding: 0;
            position: relative;
            box-shadow: var(--shadow-inset);
        }

        .control-section {
            border-bottom: 4px solid var(--steel-black);
            padding: 25px;
            position: relative;
        }

        .control-section:last-child {
            border-bottom: none;
        }

        .control-header {
            font-size: 1.1rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: var(--steel-black);
            margin-bottom: 20px;
            position: relative;
        }

        .control-header::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background: var(--warning-orange);
        }

        /* Filters - Industrial Controls */
        .filter-grid {
            display: grid;
            gap: 12px;
        }

        .filter-item {
            background: var(--concrete-light);
            border: 2px solid var(--concrete-shadow);
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: var(--font-secondary);
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.05em;
            position: relative;
        }

        .filter-item:hover {
            background: var(--concrete-medium);
            border-color: var(--steel-black);
            transform: translateX(4px);
        }

        .filter-item.active {
            background: var(--warning-orange);
            border-color: var(--warning-orange);
            color: white;
            box-shadow: 4px 4px 0 var(--concrete-shadow);
        }

        /* Export Controls - Heavy Machinery */
        .export-controls {
            display: grid;
            gap: 12px;
        }

        .export-button {
            background: var(--steel-black);
            color: var(--concrete-light);
            border: none;
            padding: 15px;
            font-family: var(--font-primary);
            font-weight: 900;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.1em;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .export-button:hover {
            background: var(--warning-orange);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        .export-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .export-button:hover::before {
            left: 100%;
        }

        /* Settings Panel - Industrial Configuration */
        .settings-grid {
            display: grid;
            gap: 15px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--concrete-light);
            padding: 12px;
            border-left: 4px solid var(--steel-black);
        }

        .setting-label {
            font-family: var(--font-secondary);
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            color: var(--steel-black);
        }

        .setting-toggle {
            width: 50px;
            height: 24px;
            background: var(--concrete-shadow);
            border: none;
            border-radius: 0;
            position: relative;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .setting-toggle.active {
            background: var(--warning-orange);
        }

        .setting-toggle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: var(--concrete-light);
            transition: transform 0.2s ease;
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setting-toggle.active::before {
            transform: translateX(26px);
        }

        /* Alert System - Warning Fortress */
        .alert-system {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .alert-block {
            background: var(--error-red);
            color: white;
            padding: 20px 25px;
            font-family: var(--font-primary);
            font-weight: 900;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.1em;
            box-shadow: var(--shadow-heavy);
            border-left: 8px solid var(--steel-black);
            min-width: 300px;
            position: relative;
            transform: translateX(400px);
            animation: slideIn 0.5s ease forwards;
        }

        .alert-block.success {
            background: var(--success-green);
        }

        .alert-block.warning {
            background: var(--warning-orange);
        }

        .alert-block.info {
            background: var(--accent-blue);
        }

        @keyframes slideIn {
            to {
                transform: translateX(0);
            }
        }

        .alert-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            font-weight: 900;
            cursor: pointer;
            opacity: 0.7;
        }

        .alert-close:hover {
            opacity: 1;
        }

        /* Refresh Status - Mechanical Indicator */
        .refresh-status {
            position: absolute;
            top: 20px;
            left: 40px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: var(--steel-black);
            color: var(--concrete-light);
            padding: 10px 20px;
            font-family: var(--font-secondary);
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }

        .refresh-indicator {
            width: 12px;
            height: 12px;
            background: var(--success-green);
            animation: pulse 2s ease-in-out infinite;
        }

        .refresh-indicator.loading {
            background: var(--warning-orange);
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive Brutalism */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .controls-panel {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .alert-system {
                right: 10px;
                top: 10px;
            }
            
            .alert-block {
                min-width: 250px;
            }
        }

        @media (max-width: 768px) {
            .header-controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .brutal-button {
                min-width: 100px;
                padding: 12px 18px;
                font-size: 12px;
            }
            
            .controls-panel {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Dashboard Widget - Architectural Brutalism Theme</h1>
        
        <div class="hybrid-component">
            <!-- Dashboard Header - Command Center -->
            <div class="dashboard-header">
                <div class="header-controls">
                    <h2 class="dashboard-title">System Analytics</h2>
                    <div class="header-actions">
                        <button class="brutal-button active" id="realTimeBtn">Live Data</button>
                        <button class="brutal-button" id="historicalBtn">Historical</button>
                        <button class="brutal-button" id="refreshBtn">Refresh</button>
                        <button class="brutal-button" id="settingsBtn">Settings</button>
                    </div>
                </div>
            </div>

            <!-- Refresh Status Indicator -->
            <div class="refresh-status" id="refreshStatus">
                <div class="refresh-indicator" id="refreshIndicator"></div>
                <span id="refreshText">Live Feed Active</span>
            </div>

            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Chart Section - Main Visual -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3 class="chart-title">Performance Metrics</h3>
                        <div class="header-actions">
                            <button class="brutal-button" id="chartTypeBtn">Bar Chart</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="chart-canvas">
                            <div class="chart-bars" id="chartBars">
                                <div class="chart-bar" data-value="85" style="height: 85%;"></div>
                                <div class="chart-bar" data-value="92" style="height: 92%;"></div>
                                <div class="chart-bar" data-value="78" style="height: 78%;"></div>
                                <div class="chart-bar" data-value="96" style="height: 96%;"></div>
                                <div class="chart-bar" data-value="89" style="height: 89%;"></div>
                                <div class="chart-bar" data-value="73" style="height: 73%;"></div>
                                <div class="chart-bar" data-value="91" style="height: 91%;"></div>
                                <div class="chart-bar" data-value="87" style="height: 87%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controls Panel - Configuration Fortress -->
                <div class="controls-panel">
                    <!-- Filters Section -->
                    <div class="control-section">
                        <h4 class="control-header">Data Filters</h4>
                        <div class="filter-grid">
                            <div class="filter-item active" data-filter="all">All Systems</div>
                            <div class="filter-item" data-filter="cpu">CPU Usage</div>
                            <div class="filter-item" data-filter="memory">Memory</div>
                            <div class="filter-item" data-filter="network">Network</div>
                            <div class="filter-item" data-filter="storage">Storage</div>
                            <div class="filter-item" data-filter="security">Security</div>
                        </div>
                    </div>

                    <!-- Export Section -->
                    <div class="control-section">
                        <h4 class="control-header">Export Data</h4>
                        <div class="export-controls">
                            <button class="export-button" id="exportPdf">PDF Report</button>
                            <button class="export-button" id="exportCsv">CSV Data</button>
                            <button class="export-button" id="exportJson">JSON Export</button>
                            <button class="export-button" id="exportImg">Image</button>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="control-section">
                        <h4 class="control-header">Configuration</h4>
                        <div class="settings-grid">
                            <div class="setting-item">
                                <span class="setting-label">Auto Refresh</span>
                                <button class="setting-toggle active" id="autoRefreshToggle"></button>
                            </div>
                            <div class="setting-item">
                                <span class="setting-label">Alerts</span>
                                <button class="setting-toggle active" id="alertsToggle"></button>
                            </div>
                            <div class="setting-item">
                                <span class="setting-label">Grid Lines</span>
                                <button class="setting-toggle" id="gridLinesToggle"></button>
                            </div>
                            <div class="setting-item">
                                <span class="setting-label">Dark Mode</span>
                                <button class="setting-toggle" id="darkModeToggle"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert System - Warning Blocks -->
        <div class="alert-system" id="alertSystem">
            <!-- Alerts will be dynamically added here -->
        </div>
    </main>

    <script>
        // Brutalist Dashboard State Management
        class BrutalistDashboard {
            constructor() {
                this.state = {
                    currentView: 'realtime',
                    activeFilters: new Set(['all']),
                    autoRefresh: true,
                    alertsEnabled: true,
                    refreshInterval: null,
                    data: this.generateMockData()
                };
                
                this.initializeComponents();
                this.startAutoRefresh();
            }

            generateMockData() {
                return Array.from({ length: 8 }, () => ({
                    value: Math.floor(Math.random() * 40) + 60,
                    timestamp: Date.now(),
                    category: 'system'
                }));
            }

            initializeComponents() {
                this.setupHeaderControls();
                this.setupFilters();
                this.setupExportControls();
                this.setupSettings();
                this.setupChartInteractions();
            }

            setupHeaderControls() {
                // Real-time vs Historical toggle
                document.getElementById('realTimeBtn').addEventListener('click', () => {
                    this.switchView('realtime');
                    this.updateActiveButton('realTimeBtn');
                });

                document.getElementById('historicalBtn').addEventListener('click', () => {
                    this.switchView('historical');
                    this.updateActiveButton('historicalBtn');
                });

                // Manual refresh
                document.getElementById('refreshBtn').addEventListener('click', () => {
                    this.refreshData();
                });

                // Settings toggle
                document.getElementById('settingsBtn').addEventListener('click', () => {
                    this.showAlert('Settings panel activated', 'info');
                });
            }

            setupFilters() {
                document.querySelectorAll('.filter-item').forEach(filter => {
                    filter.addEventListener('click', () => {
                        const filterType = filter.dataset.filter;
                        
                        if (filterType === 'all') {
                            // Clear other filters and activate 'all'
                            this.state.activeFilters.clear();
                            this.state.activeFilters.add('all');
                            document.querySelectorAll('.filter-item').forEach(f => f.classList.remove('active'));
                            filter.classList.add('active');
                        } else {
                            // Toggle individual filter
                            filter.classList.toggle('active');
                            
                            if (filter.classList.contains('active')) {
                                this.state.activeFilters.add(filterType);
                                this.state.activeFilters.delete('all');
                            } else {
                                this.state.activeFilters.delete(filterType);
                            }
                            
                            // Update 'all' filter
                            const allFilter = document.querySelector('[data-filter="all"]');
                            allFilter.classList.remove('active');
                            
                            // If no filters selected, activate 'all'
                            if (this.state.activeFilters.size === 0) {
                                this.state.activeFilters.add('all');
                                allFilter.classList.add('active');
                            }
                        }
                        
                        this.filterData();
                        this.showAlert(`Filter applied: ${filterType.toUpperCase()}`, 'success');
                    });
                });
            }

            setupExportControls() {
                const exportButtons = {
                    'exportPdf': 'PDF Report generated',
                    'exportCsv': 'CSV data exported',
                    'exportJson': 'JSON export complete',
                    'exportImg': 'Chart image saved'
                };

                Object.entries(exportButtons).forEach(([id, message]) => {
                    document.getElementById(id).addEventListener('click', () => {
                        this.simulateExport(message);
                    });
                });
            }

            setupSettings() {
                // Auto-refresh toggle
                document.getElementById('autoRefreshToggle').addEventListener('click', (e) => {
                    this.toggleSetting(e.target, 'autoRefresh');
                    if (this.state.autoRefresh) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                });

                // Alerts toggle
                document.getElementById('alertsToggle').addEventListener('click', (e) => {
                    this.toggleSetting(e.target, 'alertsEnabled');
                });

                // Grid lines toggle
                document.getElementById('gridLinesToggle').addEventListener('click', (e) => {
                    this.toggleSetting(e.target, 'gridLines');
                    this.toggleGridLines();
                });

                // Dark mode toggle
                document.getElementById('darkModeToggle').addEventListener('click', (e) => {
                    this.toggleSetting(e.target, 'darkMode');
                    this.toggleDarkMode();
                });
            }

            setupChartInteractions() {
                // Chart type switching
                document.getElementById('chartTypeBtn').addEventListener('click', () => {
                    this.showAlert('Chart type functionality - coming soon', 'info');
                });

                // Bar hover effects are handled by CSS
                document.querySelectorAll('.chart-bar').forEach(bar => {
                    bar.addEventListener('click', () => {
                        const value = bar.dataset.value;
                        this.showAlert(`Data point selected: ${value}%`, 'info');
                    });
                });
            }

            switchView(view) {
                this.state.currentView = view;
                this.updateRefreshStatus();
                
                if (view === 'realtime') {
                    this.startAutoRefresh();
                    this.showAlert('Switched to real-time data', 'success');
                } else {
                    this.stopAutoRefresh();
                    this.showAlert('Switched to historical data', 'info');
                }
            }

            updateActiveButton(activeId) {
                document.querySelectorAll('.dashboard-header .brutal-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById(activeId).classList.add('active');
            }

            refreshData() {
                const indicator = document.getElementById('refreshIndicator');
                const text = document.getElementById('refreshText');
                
                indicator.classList.add('loading');
                text.textContent = 'Refreshing...';
                
                setTimeout(() => {
                    this.state.data = this.generateMockData();
                    this.updateChart();
                    
                    indicator.classList.remove('loading');
                    text.textContent = this.state.currentView === 'realtime' ? 'Live Feed Active' : 'Data Updated';
                    
                    this.showAlert('Data refreshed successfully', 'success');
                }, 1500);
            }

            updateChart() {
                const bars = document.querySelectorAll('.chart-bar');
                this.state.data.forEach((dataPoint, index) => {
                    if (bars[index]) {
                        bars[index].style.height = `${dataPoint.value}%`;
                        bars[index].dataset.value = dataPoint.value;
                    }
                });
            }

            filterData() {
                // Simulate data filtering
                const filteredData = this.state.data.map(item => ({
                    ...item,
                    value: Math.floor(Math.random() * 40) + 60
                }));
                
                this.state.data = filteredData;
                this.updateChart();
            }

            toggleSetting(element, setting) {
                element.classList.toggle('active');
                this.state[setting] = element.classList.contains('active');
                
                const action = this.state[setting] ? 'enabled' : 'disabled';
                this.showAlert(`${setting.replace(/([A-Z])/g, ' $1').toLowerCase()} ${action}`, 'info');
            }

            toggleGridLines() {
                const chartCanvas = document.querySelector('.chart-canvas');
                chartCanvas.style.backgroundImage = this.state.gridLines 
                    ? 'linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px), linear-gradient(180deg, rgba(0,0,0,0.1) 1px, transparent 1px)'
                    : 'none';
                chartCanvas.style.backgroundSize = this.state.gridLines ? '50px 50px' : 'auto';
            }

            toggleDarkMode() {
                // Simplified dark mode toggle
                document.body.style.filter = this.state.darkMode ? 'invert(1) hue-rotate(180deg)' : 'none';
            }

            startAutoRefresh() {
                if (this.state.autoRefresh && this.state.currentView === 'realtime') {
                    this.state.refreshInterval = setInterval(() => {
                        this.refreshData();
                    }, 10000); // Refresh every 10 seconds
                }
            }

            stopAutoRefresh() {
                if (this.state.refreshInterval) {
                    clearInterval(this.state.refreshInterval);
                    this.state.refreshInterval = null;
                }
            }

            updateRefreshStatus() {
                const text = document.getElementById('refreshText');
                text.textContent = this.state.currentView === 'realtime' ? 'Live Feed Active' : 'Historical Mode';
            }

            simulateExport(message) {
                this.showAlert('Preparing export...', 'info');
                setTimeout(() => {
                    this.showAlert(message, 'success');
                }, 2000);
            }

            showAlert(message, type = 'info') {
                if (!this.state.alertsEnabled) return;
                
                const alertSystem = document.getElementById('alertSystem');
                const alert = document.createElement('div');
                alert.className = `alert-block ${type}`;
                alert.innerHTML = `
                    ${message}
                    <button class="alert-close">&times;</button>
                `;
                
                alertSystem.appendChild(alert);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.style.animation = 'slideIn 0.5s ease reverse';
                        setTimeout(() => alert.remove(), 500);
                    }
                }, 5000);
                
                // Manual close
                alert.querySelector('.alert-close').addEventListener('click', () => {
                    alert.style.animation = 'slideIn 0.5s ease reverse';
                    setTimeout(() => alert.remove(), 500);
                });
            }
        }

        // Initialize the Brutalist Dashboard
        document.addEventListener('DOMContentLoaded', () => {
            const dashboard = new BrutalistDashboard();
            
            // Initial welcome alert
            setTimeout(() => {
                dashboard.showAlert('Brutalist Dashboard System Online', 'success');
            }, 1000);
        });

        // Add mechanical sound effects on interactions (CSS animations provide visual feedback)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('brutal-button') || 
                e.target.classList.contains('filter-item') || 
                e.target.classList.contains('export-button')) {
                // Visual feedback is handled by CSS hover/active states
                // In a real implementation, you could add sound effects here
            }
        });
    </script>
</body>
</html>