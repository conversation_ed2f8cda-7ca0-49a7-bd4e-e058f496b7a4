<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zen Form Wizard - Meditation Retreat Booking</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #2c3e50;
        }

        .wizard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
            padding: 60px;
            position: relative;
            overflow: hidden;
        }

        .wizard-container::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(147, 197, 253, 0.1) 0%, transparent 70%);
            animation: breathe 8s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.05) rotate(180deg); }
        }

        .wizard-header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
            z-index: 1;
        }

        .wizard-title {
            font-size: 2.5em;
            color: #34495e;
            margin-bottom: 10px;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .wizard-subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
            font-style: italic;
        }

        .progress-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 60px;
            position: relative;
        }

        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e0e6ed;
            z-index: 0;
        }

        .progress-line-active {
            position: absolute;
            top: 20px;
            left: 0;
            height: 2px;
            background: linear-gradient(90deg, #93c5fd 0%, #60a5fa 100%);
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
        }

        .progress-step {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e6ed;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            transition: all 0.4s ease;
            position: relative;
        }

        .step-circle::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(147, 197, 253, 0.3);
            transform: scale(0);
            transition: transform 0.4s ease;
        }

        .progress-step.active .step-circle {
            background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
            color: white;
            transform: scale(1.1);
            animation: pulse 2s ease-in-out infinite;
        }

        .progress-step.active .step-circle::after {
            transform: scale(1.5);
            animation: ripple 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
        }

        @keyframes ripple {
            0%, 100% { transform: scale(1.5); opacity: 0; }
            50% { transform: scale(2); opacity: 0.3; }
        }

        .progress-step.completed .step-circle {
            background: linear-gradient(135deg, #86efac 0%, #4ade80 100%);
            color: white;
        }

        .step-label {
            margin-top: 10px;
            font-size: 0.9em;
            color: #7f8c8d;
            transition: color 0.3s ease;
        }

        .progress-step.active .step-label,
        .progress-step.completed .step-label {
            color: #2c3e50;
            font-weight: 500;
        }

        .form-content {
            min-height: 400px;
            position: relative;
        }

        .form-step {
            position: absolute;
            width: 100%;
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
        }

        .form-step.active {
            opacity: 1;
            transform: translateX(0);
            pointer-events: all;
        }

        .form-group {
            margin-bottom: 30px;
        }

        .form-label {
            display: block;
            margin-bottom: 10px;
            color: #34495e;
            font-size: 1.1em;
            font-weight: 300;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            font-family: inherit;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #93c5fd;
            background: white;
            box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .radio-group,
        .checkbox-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .radio-option,
        .checkbox-option {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .radio-option:hover,
        .checkbox-option:hover {
            background: rgba(147, 197, 253, 0.1);
        }

        .radio-option input,
        .checkbox-option input {
            margin-right: 10px;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .validation-message {
            margin-top: 8px;
            font-size: 0.9em;
            color: #e74c3c;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .validation-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .validation-message.success {
            color: #4ade80;
        }

        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            gap: 20px;
        }

        .btn {
            padding: 15px 40px;
            border: none;
            border-radius: 30px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-prev {
            background: #e0e6ed;
            color: #34495e;
        }

        .btn-prev:hover {
            background: #cbd5e1;
        }

        .btn-next {
            background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
            color: white;
            margin-left: auto;
        }

        .btn-next:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(96, 165, 250, 0.3);
        }

        .btn-submit {
            background: linear-gradient(135deg, #86efac 0%, #4ade80 100%);
            color: white;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(74, 222, 128, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .save-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .save-indicator.show {
            opacity: 1;
        }

        .save-icon {
            width: 20px;
            height: 20px;
            border: 2px solid #4ade80;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .save-indicator.saved .save-icon {
            animation: none;
            border-color: #4ade80;
            background: #4ade80;
            position: relative;
        }

        .save-indicator.saved .save-icon::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
        }

        .completion-message {
            text-align: center;
            padding: 40px;
        }

        .completion-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #86efac 0%, #4ade80 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3em;
            color: white;
            animation: breathe 3s ease-in-out infinite;
        }

        .completion-text {
            font-size: 1.5em;
            color: #34495e;
            margin-bottom: 20px;
        }

        .completion-subtext {
            color: #7f8c8d;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .wizard-container {
                padding: 40px 20px;
            }

            .wizard-title {
                font-size: 2em;
            }

            .progress-container {
                gap: 10px;
            }

            .step-label {
                font-size: 0.8em;
            }

            .form-navigation {
                flex-wrap: wrap;
            }

            .btn {
                flex: 1;
                min-width: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="save-indicator" id="saveIndicator">
            <div class="save-icon"></div>
            <span>Saving...</span>
        </div>

        <div class="wizard-header">
            <h1 class="wizard-title">Journey to Inner Peace</h1>
            <p class="wizard-subtitle">Book your meditation retreat experience</p>
        </div>

        <div class="progress-container">
            <div class="progress-line"></div>
            <div class="progress-line-active" id="progressLineActive"></div>
            
            <div class="progress-step active" data-step="1">
                <div class="step-circle">1</div>
                <div class="step-label">Intention</div>
            </div>
            <div class="progress-step" data-step="2">
                <div class="step-circle">2</div>
                <div class="step-label">Journey</div>
            </div>
            <div class="progress-step" data-step="3">
                <div class="step-circle">3</div>
                <div class="step-label">Practice</div>
            </div>
            <div class="progress-step" data-step="4">
                <div class="step-circle">4</div>
                <div class="step-label">Reflection</div>
            </div>
        </div>

        <div class="form-content">
            <!-- Step 1: Intention -->
            <div class="form-step active" data-step="1">
                <div class="form-group">
                    <label class="form-label">Your name, dear traveler</label>
                    <input type="text" class="form-input" id="fullName" placeholder="Enter your full name">
                    <div class="validation-message" id="fullNameError">Please share your name with us</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Email for your journey details</label>
                    <input type="email" class="form-input" id="email" placeholder="<EMAIL>">
                    <div class="validation-message" id="emailError">A valid email helps us guide your path</div>
                </div>

                <div class="form-group">
                    <label class="form-label">What brings you to this retreat?</label>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="intention" value="stress-relief">
                            <span>Finding calm in chaos</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="intention" value="self-discovery">
                            <span>Journey of self-discovery</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="intention" value="spiritual-growth">
                            <span>Deepening spiritual practice</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="intention" value="healing">
                            <span>Healing and restoration</span>
                        </label>
                    </div>
                    <div class="validation-message" id="intentionError">Your intention guides your journey</div>
                </div>
            </div>

            <!-- Step 2: Journey -->
            <div class="form-step" data-step="2">
                <div class="form-group">
                    <label class="form-label">When would you like to begin?</label>
                    <input type="date" class="form-input" id="startDate">
                    <div class="validation-message" id="startDateError">Choose a date for your journey</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Duration of your retreat</label>
                    <select class="form-select" id="duration">
                        <option value="">Select duration</option>
                        <option value="weekend">Weekend Retreat (3 days)</option>
                        <option value="week">Week of Mindfulness (7 days)</option>
                        <option value="extended">Extended Journey (14 days)</option>
                        <option value="immersion">Full Immersion (30 days)</option>
                    </select>
                    <div class="validation-message" id="durationError">Time is a gift - choose wisely</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Preferred accommodation</label>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="accommodation" value="shared">
                            <span>Shared space - Community living</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="accommodation" value="private">
                            <span>Private room - Solitary reflection</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="accommodation" value="nature">
                            <span>Nature cabin - Forest dwelling</span>
                        </label>
                    </div>
                    <div class="validation-message" id="accommodationError">Your sanctuary awaits selection</div>
                </div>
            </div>

            <!-- Step 3: Practice -->
            <div class="form-step" data-step="3">
                <div class="form-group">
                    <label class="form-label">Your meditation experience</label>
                    <select class="form-select" id="experience">
                        <option value="">Share your practice level</option>
                        <option value="beginner">New to the path</option>
                        <option value="occasional">Occasional practitioner</option>
                        <option value="regular">Regular practice</option>
                        <option value="advanced">Seasoned meditator</option>
                    </select>
                    <div class="validation-message" id="experienceError">Every journey begins somewhere</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Practices that call to you</label>
                    <div class="checkbox-group">
                        <label class="checkbox-option">
                            <input type="checkbox" name="practices" value="vipassana">
                            <span>Vipassana meditation</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" name="practices" value="yoga">
                            <span>Mindful yoga</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" name="practices" value="walking">
                            <span>Walking meditation</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" name="practices" value="chanting">
                            <span>Sacred chanting</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" name="practices" value="silence">
                            <span>Noble silence</span>
                        </label>
                    </div>
                    <div class="validation-message" id="practicesError">Select at least one practice for your journey</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Dietary preferences</label>
                    <select class="form-select" id="diet">
                        <option value="">Nourishment for body and soul</option>
                        <option value="vegetarian">Vegetarian</option>
                        <option value="vegan">Vegan</option>
                        <option value="gluten-free">Gluten-free</option>
                        <option value="no-preference">No restrictions</option>
                    </select>
                    <div class="validation-message" id="dietError">Help us prepare your mindful meals</div>
                </div>
            </div>

            <!-- Step 4: Reflection -->
            <div class="form-step" data-step="4">
                <div class="form-group">
                    <label class="form-label">Share your heart's intention</label>
                    <textarea class="form-textarea" id="heartIntention" placeholder="What do you hope to discover or release during this retreat?"></textarea>
                    <div class="validation-message" id="heartIntentionError">Your words help us support your journey</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Any special needs or considerations?</label>
                    <textarea class="form-textarea" id="specialNeeds" placeholder="Physical, emotional, or spiritual considerations we should know about"></textarea>
                </div>

                <div class="form-group">
                    <label class="checkbox-option">
                        <input type="checkbox" id="agreement">
                        <span>I understand this is a journey of self-discovery and commit to approaching it with an open heart</span>
                    </label>
                    <div class="validation-message" id="agreementError">Your commitment opens the door to transformation</div>
                </div>
            </div>

            <!-- Completion Message -->
            <div class="form-step" data-step="complete">
                <div class="completion-message">
                    <div class="completion-icon">🙏</div>
                    <h2 class="completion-text">Your Journey Awaits</h2>
                    <p class="completion-subtext">
                        Thank you for trusting us with your spiritual journey.<br>
                        We will send you a detailed guide to prepare for your retreat.<br>
                        May your path be filled with peace and discovery.
                    </p>
                </div>
            </div>
        </div>

        <div class="form-navigation">
            <button class="btn btn-prev" id="prevBtn" style="display: none;">Previous</button>
            <button class="btn btn-next" id="nextBtn">Continue</button>
            <button class="btn btn-submit" id="submitBtn" style="display: none;">Complete Journey</button>
        </div>
    </div>

    <script>
        class ZenFormWizard {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 4;
                this.formData = this.loadSavedData() || {};
                this.init();
            }

            init() {
                this.attachEventListeners();
                this.updateProgressBar();
                this.restoreFormData();
                this.initializeAutoSave();
            }

            attachEventListeners() {
                document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
                document.getElementById('prevBtn').addEventListener('click', () => this.prevStep());
                document.getElementById('submitBtn').addEventListener('click', () => this.submitForm());

                // Progress step clicks
                document.querySelectorAll('.progress-step').forEach(step => {
                    step.addEventListener('click', (e) => {
                        const stepNum = parseInt(e.currentTarget.dataset.step);
                        if (stepNum < this.currentStep || this.validateStep(stepNum - 1)) {
                            this.goToStep(stepNum);
                        }
                    });
                });

                // Real-time validation
                this.attachValidationListeners();
            }

            attachValidationListeners() {
                // Step 1 validations
                document.getElementById('fullName').addEventListener('blur', (e) => {
                    this.validateField('fullName', e.target.value.trim().length > 0);
                });

                document.getElementById('email').addEventListener('blur', (e) => {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    this.validateField('email', emailRegex.test(e.target.value));
                });

                // Add more validation listeners as needed
            }

            validateField(fieldId, isValid) {
                const errorElement = document.getElementById(`${fieldId}Error`);
                if (isValid) {
                    errorElement.classList.remove('show');
                    errorElement.classList.add('success');
                    setTimeout(() => {
                        errorElement.classList.remove('success');
                    }, 2000);
                } else {
                    errorElement.classList.add('show');
                }
                return isValid;
            }

            validateStep(step) {
                let isValid = true;

                switch(step) {
                    case 1:
                        const fullName = document.getElementById('fullName').value.trim();
                        const email = document.getElementById('email').value.trim();
                        const intention = document.querySelector('input[name="intention"]:checked');
                        
                        if (!this.validateField('fullName', fullName.length > 0)) isValid = false;
                        if (!this.validateField('email', /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))) isValid = false;
                        if (!this.validateField('intention', intention !== null)) isValid = false;
                        break;

                    case 2:
                        const startDate = document.getElementById('startDate').value;
                        const duration = document.getElementById('duration').value;
                        const accommodation = document.querySelector('input[name="accommodation"]:checked');
                        
                        if (!this.validateField('startDate', startDate !== '')) isValid = false;
                        if (!this.validateField('duration', duration !== '')) isValid = false;
                        if (!this.validateField('accommodation', accommodation !== null)) isValid = false;
                        break;

                    case 3:
                        const experience = document.getElementById('experience').value;
                        const practices = document.querySelectorAll('input[name="practices"]:checked');
                        const diet = document.getElementById('diet').value;
                        
                        if (!this.validateField('experience', experience !== '')) isValid = false;
                        if (!this.validateField('practices', practices.length > 0)) isValid = false;
                        if (!this.validateField('diet', diet !== '')) isValid = false;
                        break;

                    case 4:
                        const heartIntention = document.getElementById('heartIntention').value.trim();
                        const agreement = document.getElementById('agreement').checked;
                        
                        if (!this.validateField('heartIntention', heartIntention.length > 10)) isValid = false;
                        if (!this.validateField('agreement', agreement)) isValid = false;
                        break;
                }

                return isValid;
            }

            nextStep() {
                if (this.validateStep(this.currentStep)) {
                    this.saveFormData();
                    if (this.currentStep < this.totalSteps) {
                        this.currentStep++;
                        this.updateUI();
                    }
                }
            }

            prevStep() {
                if (this.currentStep > 1) {
                    this.currentStep--;
                    this.updateUI();
                }
            }

            goToStep(step) {
                this.currentStep = step;
                this.updateUI();
            }

            updateUI() {
                // Update form steps
                document.querySelectorAll('.form-step').forEach(step => {
                    step.classList.remove('active');
                });
                document.querySelector(`.form-step[data-step="${this.currentStep}"]`).classList.add('active');

                // Update progress steps
                document.querySelectorAll('.progress-step').forEach((step, index) => {
                    step.classList.remove('active', 'completed');
                    if (index + 1 < this.currentStep) {
                        step.classList.add('completed');
                    } else if (index + 1 === this.currentStep) {
                        step.classList.add('active');
                    }
                });

                // Update navigation buttons
                document.getElementById('prevBtn').style.display = this.currentStep === 1 ? 'none' : 'block';
                document.getElementById('nextBtn').style.display = this.currentStep === this.totalSteps ? 'none' : 'block';
                document.getElementById('submitBtn').style.display = this.currentStep === this.totalSteps ? 'block' : 'none';

                // Update progress bar
                this.updateProgressBar();
            }

            updateProgressBar() {
                const progressPercentage = ((this.currentStep - 1) / (this.totalSteps - 1)) * 100;
                document.getElementById('progressLineActive').style.width = `${progressPercentage}%`;
            }

            saveFormData() {
                // Collect all form data
                const formElements = document.querySelectorAll('input, select, textarea');
                formElements.forEach(element => {
                    if (element.type === 'radio' || element.type === 'checkbox') {
                        if (element.checked) {
                            if (element.type === 'checkbox') {
                                if (!this.formData[element.name]) {
                                    this.formData[element.name] = [];
                                }
                                if (!this.formData[element.name].includes(element.value)) {
                                    this.formData[element.name].push(element.value);
                                }
                            } else {
                                this.formData[element.name] = element.value;
                            }
                        }
                    } else if (element.value) {
                        this.formData[element.id] = element.value;
                    }
                });

                // Save to localStorage
                localStorage.setItem('zenFormData', JSON.stringify(this.formData));
                this.showSaveIndicator();
            }

            loadSavedData() {
                const saved = localStorage.getItem('zenFormData');
                return saved ? JSON.parse(saved) : null;
            }

            restoreFormData() {
                if (!this.formData) return;

                Object.keys(this.formData).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = this.formData[key];
                    } else {
                        // Handle radio/checkbox
                        if (Array.isArray(this.formData[key])) {
                            this.formData[key].forEach(value => {
                                const checkbox = document.querySelector(`input[name="${key}"][value="${value}"]`);
                                if (checkbox) checkbox.checked = true;
                            });
                        } else {
                            const radio = document.querySelector(`input[name="${key}"][value="${this.formData[key]}"]`);
                            if (radio) radio.checked = true;
                        }
                    }
                });
            }

            showSaveIndicator() {
                const indicator = document.getElementById('saveIndicator');
                indicator.classList.add('show');
                
                setTimeout(() => {
                    indicator.classList.add('saved');
                    indicator.querySelector('span').textContent = 'Saved';
                }, 500);

                setTimeout(() => {
                    indicator.classList.remove('show', 'saved');
                    indicator.querySelector('span').textContent = 'Saving...';
                }, 2000);
            }

            initializeAutoSave() {
                let saveTimeout;
                document.addEventListener('input', () => {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        this.saveFormData();
                    }, 1000);
                });
            }

            submitForm() {
                if (this.validateStep(this.currentStep)) {
                    this.saveFormData();
                    
                    // Show completion
                    document.querySelectorAll('.form-step').forEach(step => {
                        step.classList.remove('active');
                    });
                    document.querySelector('.form-step[data-step="complete"]').classList.add('active');
                    
                    // Hide navigation
                    document.querySelector('.form-navigation').style.display = 'none';
                    
                    // Clear saved data
                    localStorage.removeItem('zenFormData');
                    
                    // In a real application, you would submit to a server here
                    console.log('Form submitted:', this.formData);
                }
            }
        }

        // Initialize the wizard when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new ZenFormWizard();
        });
    </script>
</body>
</html>