<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MAINFRAME TERMINAL v2.6 - System Data Explorer</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'VT323', monospace;
            background: #000;
            color: #00ff00;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        /* CRT Monitor Effects */
        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                rgba(0, 0, 0, 0.15),
                rgba(0, 0, 0, 0.15) 1px,
                transparent 1px,
                transparent 2px
            );
            pointer-events: none;
            z-index: 1000;
        }
        
        /* Phosphor Glow Effect */
        @keyframes flicker {
            0% { opacity: 0.98; }
            50% { opacity: 1; }
            100% { opacity: 0.99; }
        }
        
        .terminal-container {
            width: 100%;
            height: 100vh;
            padding: 20px;
            background: radial-gradient(ellipse at center, #001100 0%, #000000 100%);
            animation: flicker 0.15s infinite;
            position: relative;
            overflow: auto;
        }
        
        /* ASCII Art Header */
        .header {
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #00ff00, 0 0 20px #00ff00;
        }
        
        .ascii-art {
            font-size: 12px;
            line-height: 1;
            white-space: pre;
            color: #00ff00;
        }
        
        /* Command Line */
        .command-line {
            background: #001100;
            border: 2px solid #00ff00;
            padding: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
        
        .prompt {
            color: #00ff00;
            margin-right: 10px;
        }
        
        #commandInput {
            background: transparent;
            border: none;
            color: #00ff00;
            font-family: 'VT323', monospace;
            font-size: 18px;
            flex: 1;
            outline: none;
        }
        
        #commandInput::placeholder {
            color: #008800;
        }
        
        /* Status Bar */
        .status-bar {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: #002200;
            border: 1px solid #00ff00;
            margin-bottom: 20px;
            font-size: 16px;
        }
        
        .status-item {
            color: #00ff00;
        }
        
        /* Data Table Container */
        .data-container {
            background: #001100;
            border: 2px solid #00ff00;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
        }
        
        /* Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .data-table th,
        .data-table td {
            text-align: left;
            padding: 8px;
            border-bottom: 1px solid #004400;
        }
        
        .data-table th {
            background: #002200;
            color: #00ff00;
            font-weight: bold;
            text-transform: uppercase;
            cursor: pointer;
            position: relative;
        }
        
        .data-table th:hover {
            background: #003300;
            text-shadow: 0 0 5px #00ff00;
        }
        
        .data-table th.sorted::after {
            content: " ▲";
            color: #ffff00;
        }
        
        .data-table th.sorted-desc::after {
            content: " ▼";
            color: #ffff00;
        }
        
        .data-table tr:hover {
            background: #002200;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
        }
        
        .data-table tr.selected {
            background: #003300;
            color: #ffff00;
        }
        
        /* Checkbox Styling */
        .checkbox-cell {
            width: 30px;
        }
        
        input[type="checkbox"] {
            appearance: none;
            width: 16px;
            height: 16px;
            border: 2px solid #00ff00;
            background: transparent;
            cursor: pointer;
            position: relative;
        }
        
        input[type="checkbox"]:checked {
            background: #00ff00;
        }
        
        input[type="checkbox"]:checked::after {
            content: "X";
            position: absolute;
            top: -2px;
            left: 2px;
            color: #000;
            font-weight: bold;
        }
        
        /* Status Indicators */
        .status-active {
            color: #00ff00;
            text-shadow: 0 0 5px #00ff00;
        }
        
        .status-inactive {
            color: #ff0000;
            text-shadow: 0 0 5px #ff0000;
        }
        
        .status-warning {
            color: #ffaa00;
            text-shadow: 0 0 5px #ffaa00;
        }
        
        /* Control Panel */
        .control-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .control-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        /* Buttons */
        .btn {
            background: #002200;
            border: 2px solid #00ff00;
            color: #00ff00;
            padding: 8px 16px;
            font-family: 'VT323', monospace;
            font-size: 16px;
            cursor: pointer;
            text-transform: uppercase;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #00ff00;
            color: #000;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.8);
            text-shadow: none;
        }
        
        .btn:active {
            transform: scale(0.95);
        }
        
        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .page-btn {
            background: transparent;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 4px 8px;
            font-family: 'VT323', monospace;
            font-size: 16px;
            cursor: pointer;
            min-width: 30px;
        }
        
        .page-btn:hover {
            background: #002200;
        }
        
        .page-btn.active {
            background: #00ff00;
            color: #000;
        }
        
        .page-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
        
        /* Search Box */
        .search-box {
            background: #001100;
            border: 1px solid #00ff00;
            padding: 6px 12px;
            color: #00ff00;
            font-family: 'VT323', monospace;
            font-size: 16px;
            width: 200px;
        }
        
        .search-box:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        /* Filter Dropdown */
        .filter-select {
            background: #001100;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 6px 12px;
            font-family: 'VT323', monospace;
            font-size: 16px;
            cursor: pointer;
        }
        
        .filter-select:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        /* Help Text */
        .help-text {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #002200;
            border: 1px solid #00ff00;
            padding: 10px;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
        
        .help-title {
            color: #ffff00;
            margin-bottom: 5px;
        }
        
        /* Loading Animation */
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .loading {
            animation: blink 1s infinite;
        }
        
        /* Export Modal */
        .modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #001100;
            border: 2px solid #00ff00;
            padding: 20px;
            z-index: 2000;
            box-shadow: 0 0 50px rgba(0, 255, 0, 0.8);
        }
        
        .modal-content {
            text-align: center;
        }
        
        .modal h3 {
            color: #ffff00;
            margin-bottom: 15px;
        }
        
        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .control-panel {
                flex-direction: column;
            }
            
            .data-table {
                font-size: 14px;
            }
            
            .help-text {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="terminal-container">
        <!-- ASCII Art Header -->
        <div class="header">
            <pre class="ascii-art">
╔═══════════════════════════════════════════════════════════════════════════════╗
║  __  __    _    ___ _   _ _____ ____      _    __  __ _____   ____  __   __  ║
║ |  \/  |  / \  |_ _| \ | |  ___|  _ \    / \  |  \/  | ____| |___ \ \ \ / /  ║
║ | |\/| | / _ \  | ||  \| | |_  | |_) |  / _ \ | |\/| |  _|     __) | \ V /   ║
║ | |  | |/ ___ \ | || |\  |  _| |  _ <  / ___ \| |  | | |___   / __/ _ | |    ║
║ |_|  |_/_/   \_\___|_| \_|_|   |_| \_\/_/   \_\_|  |_|_____| |_____(_)|_|    ║
║                                                                               ║
║                        SYSTEM DATA EXPLORER v2.6.0                            ║
║                    [AUTHORIZED PERSONNEL ONLY]                                ║
╚═══════════════════════════════════════════════════════════════════════════════╝
            </pre>
        </div>
        
        <!-- Command Line Interface -->
        <div class="command-line">
            <span class="prompt">SYSTEM:/$</span>
            <input type="text" id="commandInput" placeholder="Enter command... (type 'help' for commands)" autocomplete="off">
        </div>
        
        <!-- Status Bar -->
        <div class="status-bar">
            <span class="status-item">RECORDS: <span id="totalRecords">0</span></span>
            <span class="status-item">SELECTED: <span id="selectedCount">0</span></span>
            <span class="status-item">FILTERED: <span id="filteredCount">0</span></span>
            <span class="status-item">PAGE: <span id="currentPage">1</span>/<span id="totalPages">1</span></span>
            <span class="status-item">STATUS: <span id="systemStatus" class="status-active">ONLINE</span></span>
        </div>
        
        <!-- Control Panel -->
        <div class="control-panel">
            <div class="control-group">
                <input type="text" class="search-box" id="searchInput" placeholder="SEARCH...">
                <select class="filter-select" id="filterSelect">
                    <option value="">ALL STATUS</option>
                    <option value="active">ACTIVE</option>
                    <option value="inactive">INACTIVE</option>
                    <option value="warning">WARNING</option>
                </select>
            </div>
            <div class="control-group">
                <button class="btn" onclick="selectAll()">SELECT ALL</button>
                <button class="btn" onclick="deselectAll()">DESELECT</button>
                <button class="btn" onclick="exportSelected()">EXPORT</button>
                <button class="btn" onclick="refreshData()">REFRESH</button>
            </div>
        </div>
        
        <!-- Data Container -->
        <div class="data-container">
            <table class="data-table" id="dataTable">
                <thead>
                    <tr>
                        <th class="checkbox-cell">
                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                        </th>
                        <th onclick="sortTable('id')">ID</th>
                        <th onclick="sortTable('user')">USER</th>
                        <th onclick="sortTable('process')">PROCESS</th>
                        <th onclick="sortTable('cpu')">CPU%</th>
                        <th onclick="sortTable('memory')">MEM(MB)</th>
                        <th onclick="sortTable('status')">STATUS</th>
                        <th onclick="sortTable('timestamp')">TIMESTAMP</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- Data will be inserted here -->
                </tbody>
            </table>
            
            <!-- Pagination -->
            <div class="pagination" id="pagination">
                <!-- Pagination buttons will be inserted here -->
            </div>
        </div>
        
        <!-- Export Modal -->
        <div class="modal" id="exportModal">
            <div class="modal-content">
                <h3>EXPORT DATA</h3>
                <p>Export <span id="exportCount">0</span> selected records?</p>
                <div class="modal-buttons">
                    <button class="btn" onclick="confirmExport('csv')">CSV</button>
                    <button class="btn" onclick="confirmExport('json')">JSON</button>
                    <button class="btn" onclick="confirmExport('txt')">TXT</button>
                    <button class="btn" onclick="closeModal()">CANCEL</button>
                </div>
            </div>
        </div>
        
        <!-- Help Text -->
        <div class="help-text">
            <div class="help-title">COMMANDS:</div>
            <div>:sort [column] - Sort by column</div>
            <div>:filter [status] - Filter by status</div>
            <div>:search [term] - Search records</div>
            <div>:export - Export selected</div>
            <div>:clear - Clear filters</div>
            <div>:help - Show commands</div>
        </div>
    </div>
    
    <script>
        // Sample Data Generation
        const processes = ['SYSTEM', 'KERNEL', 'NETWORK', 'DATABASE', 'AUTH', 'MONITOR', 'BACKUP', 'SYNC', 'CACHE', 'QUEUE'];
        const users = ['ROOT', 'ADMIN', 'SYSTEM', 'SERVICE', 'DAEMON', 'USER001', 'USER002', 'USER003'];
        const statuses = ['active', 'inactive', 'warning'];
        
        let allData = [];
        let filteredData = [];
        let currentSort = { column: null, ascending: true };
        let currentPage = 1;
        const recordsPerPage = 10;
        
        // Generate mock data
        function generateData() {
            allData = [];
            for (let i = 1; i <= 100; i++) {
                allData.push({
                    id: `SYS${String(i).padStart(4, '0')}`,
                    user: users[Math.floor(Math.random() * users.length)],
                    process: processes[Math.floor(Math.random() * processes.length)],
                    cpu: Math.floor(Math.random() * 100),
                    memory: Math.floor(Math.random() * 2048),
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString().slice(0, 19).replace('T', ' '),
                    selected: false
                });
            }
            filteredData = [...allData];
            updateDisplay();
        }
        
        // Update table display
        function updateDisplay() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';
            
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            pageData.forEach(record => {
                const row = document.createElement('tr');
                if (record.selected) row.classList.add('selected');
                
                row.innerHTML = `
                    <td class="checkbox-cell">
                        <input type="checkbox" ${record.selected ? 'checked' : ''} 
                               onchange="toggleSelection('${record.id}')">
                    </td>
                    <td>${record.id}</td>
                    <td>${record.user}</td>
                    <td>${record.process}</td>
                    <td>${record.cpu}</td>
                    <td>${record.memory}</td>
                    <td class="status-${record.status}">${record.status.toUpperCase()}</td>
                    <td>${record.timestamp}</td>
                `;
                tbody.appendChild(row);
            });
            
            updateStatusBar();
            updatePagination();
        }
        
        // Update status bar
        function updateStatusBar() {
            document.getElementById('totalRecords').textContent = allData.length;
            document.getElementById('filteredCount').textContent = filteredData.length;
            document.getElementById('selectedCount').textContent = allData.filter(r => r.selected).length;
            document.getElementById('currentPage').textContent = currentPage;
            document.getElementById('totalPages').textContent = Math.ceil(filteredData.length / recordsPerPage);
        }
        
        // Pagination
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            const totalPages = Math.ceil(filteredData.length / recordsPerPage);
            
            // Previous button
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '<<';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => goToPage(currentPage - 1);
            pagination.appendChild(prevBtn);
            
            // Page numbers
            const maxButtons = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
            let endPage = Math.min(totalPages, startPage + maxButtons - 1);
            
            if (endPage - startPage < maxButtons - 1) {
                startPage = Math.max(1, endPage - maxButtons + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = 'page-btn';
                if (i === currentPage) pageBtn.classList.add('active');
                pageBtn.textContent = i;
                pageBtn.onclick = () => goToPage(i);
                pagination.appendChild(pageBtn);
            }
            
            // Next button
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = '>>';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => goToPage(currentPage + 1);
            pagination.appendChild(nextBtn);
        }
        
        function goToPage(page) {
            const totalPages = Math.ceil(filteredData.length / recordsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                updateDisplay();
            }
        }
        
        // Sorting
        function sortTable(column) {
            const headers = document.querySelectorAll('th');
            headers.forEach(h => {
                h.classList.remove('sorted', 'sorted-desc');
            });
            
            if (currentSort.column === column) {
                currentSort.ascending = !currentSort.ascending;
            } else {
                currentSort.column = column;
                currentSort.ascending = true;
            }
            
            filteredData.sort((a, b) => {
                let valA = a[column];
                let valB = b[column];
                
                if (typeof valA === 'string') {
                    valA = valA.toLowerCase();
                    valB = valB.toLowerCase();
                }
                
                if (valA < valB) return currentSort.ascending ? -1 : 1;
                if (valA > valB) return currentSort.ascending ? 1 : -1;
                return 0;
            });
            
            const headerIndex = Array.from(headers).findIndex(h => h.textContent.toLowerCase().includes(column));
            if (headerIndex > -1) {
                headers[headerIndex].classList.add(currentSort.ascending ? 'sorted' : 'sorted-desc');
            }
            
            currentPage = 1;
            updateDisplay();
        }
        
        // Selection functions
        function toggleSelection(id) {
            const record = allData.find(r => r.id === id);
            if (record) {
                record.selected = !record.selected;
                updateDisplay();
            }
        }
        
        function toggleSelectAll() {
            const checkbox = document.getElementById('selectAllCheckbox');
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            pageData.forEach(record => {
                record.selected = checkbox.checked;
            });
            updateDisplay();
        }
        
        function selectAll() {
            filteredData.forEach(record => {
                record.selected = true;
            });
            updateDisplay();
        }
        
        function deselectAll() {
            allData.forEach(record => {
                record.selected = false;
            });
            document.getElementById('selectAllCheckbox').checked = false;
            updateDisplay();
        }
        
        // Search and filter
        document.getElementById('searchInput').addEventListener('input', function(e) {
            applyFilters();
        });
        
        document.getElementById('filterSelect').addEventListener('change', function(e) {
            applyFilters();
        });
        
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('filterSelect').value;
            
            filteredData = allData.filter(record => {
                const matchesSearch = !searchTerm || 
                    Object.values(record).some(val => 
                        String(val).toLowerCase().includes(searchTerm)
                    );
                
                const matchesStatus = !statusFilter || record.status === statusFilter;
                
                return matchesSearch && matchesStatus;
            });
            
            currentPage = 1;
            updateDisplay();
        }
        
        // Export functions
        function exportSelected() {
            const selectedCount = allData.filter(r => r.selected).length;
            if (selectedCount === 0) {
                alert('NO RECORDS SELECTED FOR EXPORT');
                return;
            }
            document.getElementById('exportCount').textContent = selectedCount;
            document.getElementById('exportModal').style.display = 'block';
        }
        
        function confirmExport(format) {
            const selectedData = allData.filter(r => r.selected);
            let content = '';
            
            switch(format) {
                case 'csv':
                    content = 'ID,USER,PROCESS,CPU%,MEMORY(MB),STATUS,TIMESTAMP\n';
                    selectedData.forEach(r => {
                        content += `${r.id},${r.user},${r.process},${r.cpu},${r.memory},${r.status},${r.timestamp}\n`;
                    });
                    downloadFile('system_data.csv', content, 'text/csv');
                    break;
                    
                case 'json':
                    content = JSON.stringify(selectedData.map(r => {
                        const {selected, ...data} = r;
                        return data;
                    }), null, 2);
                    downloadFile('system_data.json', content, 'application/json');
                    break;
                    
                case 'txt':
                    content = 'SYSTEM DATA EXPORT\n';
                    content += '==================\n\n';
                    selectedData.forEach(r => {
                        content += `ID: ${r.id}\n`;
                        content += `USER: ${r.user}\n`;
                        content += `PROCESS: ${r.process}\n`;
                        content += `CPU: ${r.cpu}%\n`;
                        content += `MEMORY: ${r.memory}MB\n`;
                        content += `STATUS: ${r.status}\n`;
                        content += `TIMESTAMP: ${r.timestamp}\n`;
                        content += '-------------------\n';
                    });
                    downloadFile('system_data.txt', content, 'text/plain');
                    break;
            }
            
            closeModal();
            document.getElementById('systemStatus').textContent = 'EXPORT COMPLETE';
            document.getElementById('systemStatus').className = 'status-warning';
            setTimeout(() => {
                document.getElementById('systemStatus').textContent = 'ONLINE';
                document.getElementById('systemStatus').className = 'status-active';
            }, 2000);
        }
        
        function downloadFile(filename, content, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function closeModal() {
            document.getElementById('exportModal').style.display = 'none';
        }
        
        function refreshData() {
            document.getElementById('systemStatus').textContent = 'REFRESHING...';
            document.getElementById('systemStatus').className = 'status-warning loading';
            
            setTimeout(() => {
                generateData();
                document.getElementById('systemStatus').textContent = 'ONLINE';
                document.getElementById('systemStatus').className = 'status-active';
            }, 1000);
        }
        
        // Command line interface
        document.getElementById('commandInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const command = this.value.trim().toLowerCase();
                processCommand(command);
                this.value = '';
            }
        });
        
        function processCommand(command) {
            const parts = command.split(' ');
            const cmd = parts[0];
            const arg = parts[1];
            
            switch(cmd) {
                case ':sort':
                    if (arg && ['id', 'user', 'process', 'cpu', 'memory', 'status', 'timestamp'].includes(arg)) {
                        sortTable(arg);
                        showCommandResult(`SORTED BY ${arg.toUpperCase()}`);
                    } else {
                        showCommandResult('INVALID SORT COLUMN');
                    }
                    break;
                    
                case ':filter':
                    if (arg && ['active', 'inactive', 'warning'].includes(arg)) {
                        document.getElementById('filterSelect').value = arg;
                        applyFilters();
                        showCommandResult(`FILTERED BY ${arg.toUpperCase()}`);
                    } else if (arg === 'clear') {
                        document.getElementById('filterSelect').value = '';
                        applyFilters();
                        showCommandResult('FILTER CLEARED');
                    } else {
                        showCommandResult('INVALID FILTER');
                    }
                    break;
                    
                case ':search':
                    const searchTerm = parts.slice(1).join(' ');
                    document.getElementById('searchInput').value = searchTerm;
                    applyFilters();
                    showCommandResult(`SEARCHING FOR "${searchTerm}"`);
                    break;
                    
                case ':export':
                    exportSelected();
                    break;
                    
                case ':clear':
                    document.getElementById('searchInput').value = '';
                    document.getElementById('filterSelect').value = '';
                    applyFilters();
                    showCommandResult('FILTERS CLEARED');
                    break;
                    
                case ':help':
                case 'help':
                    showCommandResult('AVAILABLE COMMANDS: :sort, :filter, :search, :export, :clear');
                    break;
                    
                default:
                    showCommandResult('UNKNOWN COMMAND. TYPE "help" FOR AVAILABLE COMMANDS');
            }
        }
        
        function showCommandResult(message) {
            const status = document.getElementById('systemStatus');
            const originalText = status.textContent;
            const originalClass = status.className;
            
            status.textContent = message;
            status.className = 'status-warning';
            
            setTimeout(() => {
                status.textContent = originalText;
                status.className = originalClass;
            }, 2000);
        }
        
        // Initialize
        generateData();
        
        // Add some CRT flicker effect
        setInterval(() => {
            if (Math.random() > 0.95) {
                document.body.style.opacity = '0.95';
                setTimeout(() => {
                    document.body.style.opacity = '1';
                }, 50);
            }
        }, 3000);
    </script>
</body>
</html>