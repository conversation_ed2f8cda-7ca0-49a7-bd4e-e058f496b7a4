<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Industrial File Manager - Hybrid UI Component</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #e0e0e0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .industrial-container {
            width: 95vw;
            max-width: 1400px;
            height: 90vh;
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border-radius: 4px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.8),
                inset 0 2px 4px rgba(255, 255, 255, 0.1),
                inset 0 -2px 4px rgba(0, 0, 0, 0.5);
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .industrial-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 255, 255, 0.03) 2px,
                    rgba(255, 255, 255, 0.03) 4px
                ),
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 255, 255, 0.03) 2px,
                    rgba(255, 255, 255, 0.03) 4px
                );
            pointer-events: none;
        }

        .header-panel {
            height: 60px;
            background: linear-gradient(to bottom, #3a3a3a, #2a2a2a);
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.5),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 4px;
            color: #ff6b35;
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        }

        .status-lights {
            margin-left: auto;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #333;
            box-shadow: 
                inset 0 1px 2px rgba(0, 0, 0, 0.5),
                0 0 4px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .status-light.active {
            background: #00ff41;
            box-shadow: 
                inset 0 1px 2px rgba(0, 0, 0, 0.5),
                0 0 15px rgba(0, 255, 65, 0.8);
        }

        .main-area {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            gap: 20px;
            height: calc(100% - 80px);
        }

        .file-browser {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 4px;
            padding: 15px;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.5),
                0 1px 2px rgba(255, 255, 255, 0.05);
            overflow-y: auto;
        }

        .browser-header {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #888;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        }

        .folder-item, .file-item {
            padding: 10px;
            margin: 5px 0;
            background: #2a2a2a;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            border: 1px solid transparent;
        }

        .folder-item:hover, .file-item:hover {
            background: #333;
            border-color: #ff6b35;
            transform: translateX(5px);
        }

        .folder-item::before {
            content: '📁';
        }

        .file-item::before {
            content: '📄';
        }

        .upload-zone {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 4px;
            padding: 30px;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.5),
                0 1px 2px rgba(255, 255, 255, 0.05);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .drop-area {
            flex: 1;
            border: 3px dashed #444;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            background: rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .drop-area.dragover {
            border-color: #ff6b35;
            background: rgba(255, 107, 53, 0.1);
            transform: scale(1.02);
        }

        .drop-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            position: relative;
        }

        .gear {
            position: absolute;
            border-radius: 50%;
            border: 3px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gear::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: 
                repeating-conic-gradient(
                    from 0deg,
                    #666 0deg 30deg,
                    transparent 30deg 60deg
                );
            border-radius: 50%;
        }

        .gear.large {
            width: 80px;
            height: 80px;
            top: 0;
            left: 0;
            animation: rotate 4s linear infinite;
        }

        .gear.small {
            width: 40px;
            height: 40px;
            bottom: 0;
            right: 0;
            animation: rotate-reverse 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes rotate-reverse {
            from { transform: rotate(0deg); }
            to { transform: rotate(-360deg); }
        }

        .drop-text {
            font-size: 18px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 10px;
        }

        .drop-subtext {
            font-size: 14px;
            color: #666;
        }

        .file-input {
            display: none;
        }

        .progress-container {
            margin-top: 30px;
            min-height: 200px;
        }

        .file-progress {
            background: #2a2a2a;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #333;
            position: relative;
            overflow: hidden;
        }

        .file-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 107, 53, 0.2),
                transparent
            );
            animation: scan 2s linear infinite;
        }

        @keyframes scan {
            to { left: 100%; }
        }

        .file-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .file-name {
            font-size: 14px;
            color: #e0e0e0;
        }

        .file-size {
            font-size: 12px;
            color: #888;
        }

        .progress-bar-container {
            height: 8px;
            background: #1a1a1a;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6b35, #ff8555);
            width: 0%;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent
            );
            animation: shimmer 1s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-status {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }

        .preview-panel {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 4px;
            padding: 20px;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.5),
                0 1px 2px rgba(255, 255, 255, 0.05);
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #888;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        }

        .preview-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #333;
            border-radius: 4px;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
        }

        .preview-image {
            max-width: 100%;
            max-height: 100%;
            border-radius: 2px;
        }

        .preview-placeholder {
            text-align: center;
            color: #666;
        }

        .preview-icon {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.5;
        }

        .file-details {
            margin-top: 20px;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 4px;
            font-size: 12px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #333;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .detail-value {
            color: #e0e0e0;
            font-family: monospace;
        }

        .validation-panel {
            margin-top: 20px;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 4px;
        }

        .validation-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            font-size: 12px;
        }

        .validation-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        .validation-icon.pass {
            background: #00ff41;
            color: #000;
        }

        .validation-icon.fail {
            background: #ff3333;
            color: #fff;
        }

        .validation-icon.warning {
            background: #ffaa00;
            color: #000;
        }

        .controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 10px 20px;
            background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
            border: none;
            border-radius: 4px;
            color: #e0e0e0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.5),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: linear-gradient(145deg, #4a4a4a, #3a3a3a);
            transform: translateY(-2px);
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.5),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
        }

        .control-btn:active {
            transform: translateY(0);
            box-shadow: 
                0 1px 2px rgba(0, 0, 0, 0.5),
                inset 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .control-btn.primary {
            background: linear-gradient(145deg, #ff6b35, #ff5525);
            color: #fff;
        }

        .control-btn.primary:hover {
            background: linear-gradient(145deg, #ff7b45, #ff6535);
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1a1a1a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="industrial-container">
        <div class="header-panel">
            <div class="logo">INDUSTRIAL FM</div>
            <div class="status-lights">
                <div class="status-light" id="powerLight"></div>
                <div class="status-light" id="processLight"></div>
                <div class="status-light" id="networkLight"></div>
            </div>
        </div>

        <div class="main-area">
            <div class="file-browser">
                <div class="browser-header">File System</div>
                <div class="folder-item">Documents</div>
                <div class="folder-item">Projects</div>
                <div class="folder-item">Archives</div>
                <div class="file-item">blueprint_v2.dwg</div>
                <div class="file-item">specs_final.pdf</div>
                <div class="file-item">prototype_3d.stl</div>
                <div class="file-item">assembly_guide.pdf</div>
                <div class="file-item">material_list.xlsx</div>
                <div class="file-item">safety_protocol.doc</div>
            </div>

            <div class="upload-zone">
                <div class="drop-area" id="dropArea">
                    <div class="drop-icon">
                        <div class="gear large"></div>
                        <div class="gear small"></div>
                    </div>
                    <div class="drop-text">Drop Files Here</div>
                    <div class="drop-subtext">or click to browse</div>
                    <input type="file" class="file-input" id="fileInput" multiple>
                </div>

                <div class="progress-container" id="progressContainer"></div>
            </div>

            <div class="preview-panel">
                <div class="preview-header">File Preview</div>
                <div class="preview-content" id="previewContent">
                    <div class="preview-placeholder">
                        <div class="preview-icon">⚙️</div>
                        <div>No file selected</div>
                    </div>
                </div>
                <div class="file-details" id="fileDetails" style="display: none;">
                    <div class="detail-row">
                        <span class="detail-label">Name</span>
                        <span class="detail-value" id="detailName">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Type</span>
                        <span class="detail-value" id="detailType">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Size</span>
                        <span class="detail-value" id="detailSize">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Modified</span>
                        <span class="detail-value" id="detailModified">-</span>
                    </div>
                </div>
                <div class="validation-panel" id="validationPanel" style="display: none;">
                    <div class="validation-item">
                        <div class="validation-icon pass" id="sizeCheck">✓</div>
                        <span>File size within limits</span>
                    </div>
                    <div class="validation-item">
                        <div class="validation-icon pass" id="typeCheck">✓</div>
                        <span>File type supported</span>
                    </div>
                    <div class="validation-item">
                        <div class="validation-icon warning" id="virusCheck">!</div>
                        <span>Security scan pending</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" id="clearBtn">Clear All</button>
            <button class="control-btn primary" id="uploadBtn">Upload Files</button>
        </div>
    </div>

    <script>
        // Status lights initialization
        const statusLights = {
            power: document.getElementById('powerLight'),
            process: document.getElementById('processLight'),
            network: document.getElementById('networkLight')
        };

        // Activate power light immediately
        setTimeout(() => {
            statusLights.power.classList.add('active');
        }, 500);

        // Simulate network connection
        setTimeout(() => {
            statusLights.network.classList.add('active');
        }, 1500);

        // File handling
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('fileInput');
        const progressContainer = document.getElementById('progressContainer');
        const previewContent = document.getElementById('previewContent');
        const fileDetails = document.getElementById('fileDetails');
        const validationPanel = document.getElementById('validationPanel');
        const uploadBtn = document.getElementById('uploadBtn');
        const clearBtn = document.getElementById('clearBtn');

        let uploadedFiles = [];

        // Drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            dropArea.classList.add('dragover');
        }

        function unhighlight(e) {
            dropArea.classList.remove('dragover');
        }

        dropArea.addEventListener('drop', handleDrop, false);
        dropArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelect);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            ([...files]).forEach(uploadFile);
            statusLights.process.classList.add('active');
        }

        function uploadFile(file) {
            uploadedFiles.push(file);
            
            const progressElement = createProgressElement(file);
            progressContainer.appendChild(progressElement);

            // Simulate upload progress
            let progress = 0;
            const progressBar = progressElement.querySelector('.progress-bar');
            const progressPercent = progressElement.querySelector('.progress-percent');
            const progressStatus = progressElement.querySelector('.progress-status-text');

            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) {
                    progress = 100;
                    clearInterval(interval);
                    progressStatus.textContent = 'Complete';
                    setTimeout(() => {
                        showPreview(file);
                        validateFile(file);
                    }, 500);
                }
                progressBar.style.width = progress + '%';
                progressPercent.textContent = Math.round(progress) + '%';
            }, 200);
        }

        function createProgressElement(file) {
            const div = document.createElement('div');
            div.className = 'file-progress';
            div.innerHTML = `
                <div class="file-info">
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${formatFileSize(file.size)}</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar"></div>
                </div>
                <div class="progress-status">
                    <span class="progress-status-text">Uploading...</span>
                    <span class="progress-percent">0%</span>
                </div>
            `;
            return div;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showPreview(file) {
            fileDetails.style.display = 'block';
            document.getElementById('detailName').textContent = file.name;
            document.getElementById('detailType').textContent = file.type || 'Unknown';
            document.getElementById('detailSize').textContent = formatFileSize(file.size);
            document.getElementById('detailModified').textContent = new Date(file.lastModified).toLocaleString();

            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewContent.innerHTML = `<img src="${e.target.result}" class="preview-image" alt="${file.name}">`;
                };
                reader.readAsDataURL(file);
            } else {
                let icon = '📄';
                if (file.type.includes('pdf')) icon = '📕';
                else if (file.type.includes('word')) icon = '📝';
                else if (file.type.includes('excel') || file.type.includes('spreadsheet')) icon = '📊';
                else if (file.type.includes('zip') || file.type.includes('rar')) icon = '📦';
                
                previewContent.innerHTML = `
                    <div class="preview-placeholder">
                        <div class="preview-icon">${icon}</div>
                        <div>${file.name}</div>
                    </div>
                `;
            }
        }

        function validateFile(file) {
            validationPanel.style.display = 'block';
            
            // Size check
            const sizeCheck = document.getElementById('sizeCheck');
            if (file.size < 100 * 1024 * 1024) { // 100MB limit
                sizeCheck.className = 'validation-icon pass';
                sizeCheck.textContent = '✓';
            } else {
                sizeCheck.className = 'validation-icon fail';
                sizeCheck.textContent = '✗';
            }

            // Type check
            const typeCheck = document.getElementById('typeCheck');
            const allowedTypes = ['image/', 'application/pdf', 'text/', 'application/msword', 'application/vnd.'];
            const isAllowed = allowedTypes.some(type => file.type.includes(type));
            if (isAllowed) {
                typeCheck.className = 'validation-icon pass';
                typeCheck.textContent = '✓';
            } else {
                typeCheck.className = 'validation-icon warning';
                typeCheck.textContent = '!';
            }

            // Simulate virus scan
            const virusCheck = document.getElementById('virusCheck');
            setTimeout(() => {
                virusCheck.className = 'validation-icon pass';
                virusCheck.textContent = '✓';
                virusCheck.nextElementSibling.textContent = 'Security scan complete';
            }, 2000);
        }

        clearBtn.addEventListener('click', () => {
            progressContainer.innerHTML = '';
            previewContent.innerHTML = `
                <div class="preview-placeholder">
                    <div class="preview-icon">⚙️</div>
                    <div>No file selected</div>
                </div>
            `;
            fileDetails.style.display = 'none';
            validationPanel.style.display = 'none';
            uploadedFiles = [];
            fileInput.value = '';
            statusLights.process.classList.remove('active');
        });

        uploadBtn.addEventListener('click', () => {
            if (uploadedFiles.length > 0) {
                alert(`Uploading ${uploadedFiles.length} file(s) to server...`);
                // Here you would implement actual upload logic
            } else {
                alert('No files selected for upload');
            }
        });

        // File browser interactions
        document.querySelectorAll('.folder-item, .file-item').forEach(item => {
            item.addEventListener('click', function() {
                console.log('Clicked:', this.textContent);
            });
        });

        // Add some industrial ambiance
        setInterval(() => {
            if (Math.random() > 0.8) {
                statusLights.process.classList.toggle('active');
                setTimeout(() => {
                    statusLights.process.classList.toggle('active');
                }, 100);
            }
        }, 3000);
    </script>
</body>
</html>