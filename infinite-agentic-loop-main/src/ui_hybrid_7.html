<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Computing Action Controller</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier Prime', 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            min-height: 100vh;
            background-image: 
                linear-gradient(rgba(0, 255, 0, 0.03) 50%, transparent 50%),
                linear-gradient(90deg, rgba(255, 0, 0, 0.03) 50%, transparent 50%);
            background-size: 2px 2px, 2px 2px;
            animation: scanlines 0.1s linear infinite;
            overflow-x: hidden;
        }
        
        @keyframes scanlines {
            0% { background-position: 0 0, 0 0; }
            100% { background-position: 0 2px, 2px 0; }
        }
        
        main {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            text-shadow: 0 0 10px #00ff00;
            letter-spacing: 2px;
            animation: flicker 2s infinite alternate;
        }
        
        @keyframes flicker {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .terminal-frame {
            border: 2px solid #00ff00;
            border-radius: 0;
            background: rgba(0, 0, 0, 0.9);
            box-shadow: 
                0 0 20px rgba(0, 255, 0, 0.3),
                inset 0 0 20px rgba(0, 255, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .terminal-header {
            background: #00ff00;
            color: #000;
            padding: 8px 15px;
            font-weight: bold;
            font-size: 14px;
            letter-spacing: 1px;
        }
        
        .terminal-content {
            padding: 20px;
            min-height: 400px;
        }
        
        .hybrid-component {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }
        
        .command-prompt {
            color: #00ff00;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .action-controller {
            width: 100%;
            max-width: 500px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }
        
        .main-action-btn {
            background: transparent;
            border: 2px solid #00ff00;
            color: #00ff00;
            padding: 15px 40px;
            font-family: inherit;
            font-size: 16px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            min-width: 200px;
            text-align: center;
        }
        
        .main-action-btn:hover {
            background: rgba(0, 255, 0, 0.1);
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
            transform: translateY(-2px);
        }
        
        .main-action-btn:active {
            transform: translateY(0);
        }
        
        .main-action-btn.loading {
            color: #ffff00;
            border-color: #ffff00;
            pointer-events: none;
        }
        
        .main-action-btn.success {
            color: #00ffff;
            border-color: #00ffff;
            animation: success-pulse 0.5s ease;
        }
        
        .main-action-btn.error {
            color: #ff0000;
            border-color: #ff0000;
            animation: error-shake 0.5s ease;
        }
        
        @keyframes success-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes error-shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .loading-display {
            display: none;
            align-items: center;
            gap: 10px;
            color: #ffff00;
            font-size: 14px;
        }
        
        .loading-display.active {
            display: flex;
        }
        
        .progress-bar {
            width: 200px;
            height: 20px;
            border: 1px solid #ffff00;
            background: transparent;
            position: relative;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffff00, #ff8800);
            width: 0%;
            transition: width 0.3s ease;
            position: relative;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: scanning 1.5s infinite;
        }
        
        @keyframes scanning {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .confirmation-dialog {
            display: none;
            border: 2px solid #ffff00;
            background: rgba(0, 0, 0, 0.95);
            padding: 20px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .confirmation-dialog.active {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .confirmation-text {
            color: #ffff00;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .confirmation-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .confirm-btn, .cancel-btn {
            background: transparent;
            border: 1px solid;
            color: inherit;
            padding: 8px 20px;
            font-family: inherit;
            font-size: 12px;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .confirm-btn {
            border-color: #00ff00;
            color: #00ff00;
        }
        
        .confirm-btn:hover {
            background: rgba(0, 255, 0, 0.2);
        }
        
        .cancel-btn {
            border-color: #ff0000;
            color: #ff0000;
        }
        
        .cancel-btn:hover {
            background: rgba(255, 0, 0, 0.2);
        }
        
        .status-display {
            display: none;
            padding: 15px;
            border: 1px solid;
            text-align: center;
            font-size: 14px;
            width: 100%;
            max-width: 400px;
            position: relative;
        }
        
        .status-display.active {
            display: block;
            animation: fadeIn 0.4s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        
        .status-display.success {
            border-color: #00ffff;
            color: #00ffff;
            background: rgba(0, 255, 255, 0.1);
        }
        
        .status-display.error {
            border-color: #ff0000;
            color: #ff0000;
            background: rgba(255, 0, 0, 0.1);
        }
        
        .status-icon {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }
        
        .terminal-output {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #333;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            line-height: 1.4;
            height: 150px;
            overflow-y: auto;
            color: #00ff00;
        }
        
        .output-line {
            margin-bottom: 5px;
            animation: typewriter 0.5s ease;
        }
        
        @keyframes typewriter {
            from { width: 0; overflow: hidden; }
            to { width: 100%; }
        }
        
        .timestamp {
            color: #666;
            font-size: 10px;
        }
        
        .ascii-art {
            font-size: 10px;
            line-height: 1;
            color: #00ff00;
            text-align: center;
            margin: 20px 0;
            opacity: 0.7;
        }
        
        .blinking-cursor {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        @media (max-width: 768px) {
            .main-action-btn {
                padding: 12px 30px;
                font-size: 14px;
                min-width: 180px;
            }
            
            .confirmation-buttons {
                flex-direction: column;
                gap: 10px;
            }
            
            .progress-bar {
                width: 150px;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Action Controller - Retro Computing Theme</h1>
        
        <div class="terminal-frame">
            <div class="terminal-header">
                SYSTEM CONTROL TERMINAL v2.3.1
            </div>
            <div class="terminal-content">
                <div class="command-prompt">
                    C:\SYSTEM> action_controller.exe --mode=interactive<span class="blinking-cursor">_</span>
                </div>
                
                <div class="ascii-art">
╔═══════════════════════════════════════╗<br>
║    ▄▀█ █▀▀ ▀█▀ █ █▀█ █▄░█              ║<br>
║    █▀█ █▄▄ ░█░ █ █▄█ █░▀█              ║<br>
║                                       ║<br>
║    █▀▀ █▀█ █▄░█ ▀█▀ █▀█ █▀█ █░░        ║<br>
║    █▄▄ █▄█ █░▀█ ░█░ █▄█ █▄█ █▄▄        ║<br>
╚═══════════════════════════════════════╝
                </div>
                
                <div class="hybrid-component">
                    <div class="action-controller">
                        <button class="main-action-btn" id="mainAction">
                            Execute Command
                        </button>
                        
                        <div class="loading-display" id="loadingDisplay">
                            <span>Processing...</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <span id="progressText">0%</span>
                        </div>
                        
                        <div class="confirmation-dialog" id="confirmDialog">
                            <div class="confirmation-text">
                                WARNING: This action will execute system commands.<br>
                                Are you sure you want to proceed?
                            </div>
                            <div class="confirmation-buttons">
                                <button class="confirm-btn" id="confirmBtn">Y - Confirm</button>
                                <button class="cancel-btn" id="cancelBtn">N - Cancel</button>
                            </div>
                        </div>
                        
                        <div class="status-display" id="statusDisplay">
                            <span class="status-icon" id="statusIcon"></span>
                            <div id="statusMessage"></div>
                        </div>
                    </div>
                    
                    <div class="terminal-output" id="terminalOutput">
                        <div class="output-line">
                            <span class="timestamp">[00:00:00]</span> System initialized successfully
                        </div>
                        <div class="output-line">
                            <span class="timestamp">[00:00:01]</span> Action controller module loaded
                        </div>
                        <div class="output-line">
                            <span class="timestamp">[00:00:02]</span> Ready for user input...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        class RetroActionController {
            constructor() {
                this.mainButton = document.getElementById('mainAction');
                this.loadingDisplay = document.getElementById('loadingDisplay');
                this.progressFill = document.getElementById('progressFill');
                this.progressText = document.getElementById('progressText');
                this.confirmDialog = document.getElementById('confirmDialog');
                this.confirmBtn = document.getElementById('confirmBtn');
                this.cancelBtn = document.getElementById('cancelBtn');
                this.statusDisplay = document.getElementById('statusDisplay');
                this.statusIcon = document.getElementById('statusIcon');
                this.statusMessage = document.getElementById('statusMessage');
                this.terminalOutput = document.getElementById('terminalOutput');
                
                this.state = 'idle'; // idle, confirming, loading, success, error
                this.actionCount = 0;
                
                this.initializeEventListeners();
                this.startTerminalClock();
            }
            
            initializeEventListeners() {
                this.mainButton.addEventListener('click', () => this.handleMainAction());
                this.confirmBtn.addEventListener('click', () => this.handleConfirm());
                this.cancelBtn.addEventListener('click', () => this.handleCancel());
                
                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (this.state === 'confirming') {
                        if (e.key === 'y' || e.key === 'Y' || e.key === 'Enter') {
                            this.handleConfirm();
                        } else if (e.key === 'n' || e.key === 'N' || e.key === 'Escape') {
                            this.handleCancel();
                        }
                    }
                });
            }
            
            handleMainAction() {
                if (this.state !== 'idle') return;
                
                this.playSound('beep');
                this.logToTerminal('User initiated action sequence');
                this.showConfirmation();
            }
            
            showConfirmation() {
                this.state = 'confirming';
                this.confirmDialog.classList.add('active');
                this.statusDisplay.classList.remove('active');
                this.logToTerminal('Awaiting user confirmation...');
            }
            
            handleConfirm() {
                this.confirmDialog.classList.remove('active');
                this.logToTerminal('Action confirmed - initiating execution');
                this.executeAction();
            }
            
            handleCancel() {
                this.confirmDialog.classList.remove('active');
                this.state = 'idle';
                this.logToTerminal('Action cancelled by user');
                this.playSound('cancel');
            }
            
            executeAction() {
                this.state = 'loading';
                this.mainButton.classList.add('loading');
                this.mainButton.textContent = 'Processing...';
                this.loadingDisplay.classList.add('active');
                this.statusDisplay.classList.remove('active');
                
                this.logToTerminal('Executing system commands...');
                this.playSound('process');
                
                // Simulate loading progress
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 100) progress = 100;
                    
                    this.progressFill.style.width = progress + '%';
                    this.progressText.textContent = Math.floor(progress) + '%';
                    
                    if (progress >= 100) {
                        clearInterval(progressInterval);
                        this.completeAction();
                    }
                }, 150);
            }
            
            completeAction() {
                this.actionCount++;
                this.loadingDisplay.classList.remove('active');
                
                // Randomly succeed or fail for demo purposes
                const success = Math.random() > 0.3;
                
                if (success) {
                    this.showSuccess();
                } else {
                    this.showError();
                }
                
                // Reset after 3 seconds
                setTimeout(() => this.resetController(), 3000);
            }
            
            showSuccess() {
                this.state = 'success';
                this.mainButton.classList.remove('loading');
                this.mainButton.classList.add('success');
                this.mainButton.textContent = 'Success!';
                
                this.statusDisplay.classList.add('active', 'success');
                this.statusIcon.textContent = '✓';
                this.statusMessage.textContent = 'Command executed successfully';
                
                this.logToTerminal('SUCCESS: Action completed without errors');
                this.logToTerminal(`Total successful actions: ${this.actionCount}`);
                this.playSound('success');
            }
            
            showError() {
                this.state = 'error';
                this.mainButton.classList.remove('loading');
                this.mainButton.classList.add('error');
                this.mainButton.textContent = 'Error!';
                
                this.statusDisplay.classList.add('active', 'error');
                this.statusIcon.textContent = '✗';
                this.statusMessage.textContent = 'ERROR: Command execution failed';
                
                this.logToTerminal('ERROR: System encountered an unexpected error');
                this.logToTerminal('Please check system logs for details');
                this.playSound('error');
            }
            
            resetController() {
                this.state = 'idle';
                this.mainButton.className = 'main-action-btn';
                this.mainButton.textContent = 'Execute Command';
                this.statusDisplay.classList.remove('active', 'success', 'error');
                this.progressFill.style.width = '0%';
                this.progressText.textContent = '0%';
                
                this.logToTerminal('Controller reset - ready for next action');
            }
            
            logToTerminal(message) {
                const timestamp = this.getCurrentTimestamp();
                const outputLine = document.createElement('div');
                outputLine.className = 'output-line';
                outputLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
                
                this.terminalOutput.appendChild(outputLine);
                this.terminalOutput.scrollTop = this.terminalOutput.scrollHeight;
                
                // Keep only last 20 lines
                const lines = this.terminalOutput.querySelectorAll('.output-line');
                if (lines.length > 20) {
                    lines[0].remove();
                }
            }
            
            getCurrentTimestamp() {
                const now = new Date();
                return now.toTimeString().split(' ')[0];
            }
            
            startTerminalClock() {
                // Update clock display every second
                setInterval(() => {
                    // This could update a terminal clock if desired
                }, 1000);
            }
            
            playSound(type) {
                // Create retro computer sounds using Web Audio API
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                const sounds = {
                    beep: { freq: 800, duration: 0.1 },
                    process: { freq: 400, duration: 0.05 },
                    success: { freq: 600, duration: 0.2 },
                    error: { freq: 200, duration: 0.3 },
                    cancel: { freq: 300, duration: 0.15 }
                };
                
                const sound = sounds[type];
                if (!sound) return;
                
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(sound.freq, audioContext.currentTime);
                oscillator.type = 'square';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + sound.duration);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + sound.duration);
            }
        }
        
        // Initialize the retro action controller
        document.addEventListener('DOMContentLoaded', () => {
            new RetroActionController();
        });
        
        // Add some terminal-style effects
        document.addEventListener('click', (e) => {
            // Create click effect
            const ripple = document.createElement('div');
            ripple.style.position = 'absolute';
            ripple.style.width = '10px';
            ripple.style.height = '10px';
            ripple.style.background = '#00ff00';
            ripple.style.borderRadius = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.left = e.pageX + 'px';
            ripple.style.top = e.pageY + 'px';
            ripple.style.pointerEvents = 'none';
            ripple.style.animation = 'ripple 0.6s ease-out forwards';
            
            document.body.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        });
        
        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                0% {
                    transform: translate(-50%, -50%) scale(0);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>