<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-morphic Universal Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            background: radial-gradient(ellipse at center, #0d2818, #051710);
            color: #7fb069;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Bio-morphic background pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(127, 176, 105, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(93, 138, 76, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(60, 100, 50, 0.05) 0%, transparent 50%);
            animation: cellularFlow 20s infinite ease-in-out;
            z-index: -1;
        }

        @keyframes cellularFlow {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(2deg); }
        }

        main {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #7fb069, #a3d982);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 0.1em;
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #7fb069, transparent);
            border-radius: 50px;
            animation: pulse 2s infinite ease-in-out;
        }

        .hybrid-component {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            grid-template-rows: auto auto 1fr;
            gap: 1.5rem;
            height: 85vh;
            position: relative;
        }

        /* Dashboard panels with bio-morphic styling */
        .bio-panel {
            background: linear-gradient(135deg, 
                rgba(127, 176, 105, 0.1), 
                rgba(93, 138, 76, 0.15));
            border: 2px solid rgba(127, 176, 105, 0.3);
            border-radius: 30px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .bio-panel::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, 
                rgba(127, 176, 105, 0.1) 0%, 
                transparent 70%);
            animation: cellularBreathing 8s infinite ease-in-out;
            z-index: -1;
        }

        @keyframes cellularBreathing {
            0%, 100% { transform: scale(0.8) rotate(0deg); opacity: 0.3; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
        }

        .bio-panel:hover {
            transform: scale(1.02);
            border-color: rgba(163, 217, 130, 0.6);
            box-shadow: 0 20px 40px rgba(127, 176, 105, 0.2);
        }

        /* Widget header styling */
        .widget-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(127, 176, 105, 0.2);
        }

        .widget-title {
            font-size: 1.1rem;
            font-weight: 500;
            color: #a3d982;
            letter-spacing: 0.05em;
        }

        .widget-controls {
            display: flex;
            gap: 0.5rem;
        }

        .bio-button {
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(45deg, #7fb069, #5d8a4c);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bio-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
        }

        .bio-button:hover::before {
            width: 100%;
            height: 100%;
        }

        /* Navigation panel */
        .nav-panel {
            grid-column: 1;
            grid-row: 1 / -1;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: block;
            padding: 1rem;
            color: #7fb069;
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(127, 176, 105, 0.2), 
                transparent);
            transition: left 0.6s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            background: rgba(127, 176, 105, 0.1);
            transform: translateX(10px);
            border-left: 3px solid #a3d982;
        }

        /* Main dashboard area */
        .main-dashboard {
            grid-column: 2;
            grid-row: 1 / -1;
            display: grid;
            grid-template-rows: auto 1fr auto;
            gap: 1.5rem;
        }

        /* Real-time metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .metric-cell {
            background: linear-gradient(135deg, 
                rgba(127, 176, 105, 0.15), 
                rgba(93, 138, 76, 0.1));
            border: 1px solid rgba(127, 176, 105, 0.3);
            border-radius: 20px;
            padding: 1rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-cell::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #7fb069, #a3d982);
            transform: scaleX(0);
            transform-origin: left;
            animation: growthPulse 3s infinite ease-in-out;
        }

        @keyframes growthPulse {
            0%, 100% { transform: scaleX(0); }
            50% { transform: scaleX(1); }
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: 600;
            color: #a3d982;
            display: block;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #7fb069;
            margin-top: 0.25rem;
        }

        /* Data visualization area */
        .data-viz {
            background: linear-gradient(135deg, 
                rgba(127, 176, 105, 0.05), 
                rgba(93, 138, 76, 0.1));
            border: 1px solid rgba(127, 176, 105, 0.2);
            border-radius: 25px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .chart-container {
            width: 100%;
            height: 200px;
            position: relative;
            display: flex;
            align-items: end;
            justify-content: space-around;
            padding: 1rem 0;
        }

        .chart-bar {
            width: 30px;
            background: linear-gradient(to top, #5d8a4c, #7fb069, #a3d982);
            border-radius: 15px 15px 0 0;
            position: relative;
            animation: chartGrowth 2s ease-out;
            transition: all 0.3s ease;
        }

        .chart-bar:nth-child(1) { height: 60%; animation-delay: 0.1s; }
        .chart-bar:nth-child(2) { height: 85%; animation-delay: 0.2s; }
        .chart-bar:nth-child(3) { height: 40%; animation-delay: 0.3s; }
        .chart-bar:nth-child(4) { height: 70%; animation-delay: 0.4s; }
        .chart-bar:nth-child(5) { height: 95%; animation-delay: 0.5s; }
        .chart-bar:nth-child(6) { height: 55%; animation-delay: 0.6s; }

        @keyframes chartGrowth {
            from { height: 0; }
        }

        .chart-bar:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(127, 176, 105, 0.4);
        }

        /* Activity feed */
        .activity-feed {
            max-height: 150px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(127, 176, 105, 0.1);
            border-radius: 15px;
            border-left: 3px solid #7fb069;
            position: relative;
            overflow: hidden;
        }

        .activity-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background: linear-gradient(to bottom, #a3d982, #7fb069);
            animation: activityPulse 2s infinite ease-in-out;
        }

        @keyframes activityPulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .activity-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(45deg, #7fb069, #a3d982);
            margin-right: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
        }

        .activity-text {
            flex: 1;
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .activity-time {
            font-size: 0.75rem;
            color: #5d8a4c;
        }

        /* Side panel */
        .side-panel {
            grid-column: 3;
            grid-row: 1 / -1;
        }

        /* Notification center */
        .notification-item {
            background: linear-gradient(135deg, 
                rgba(163, 217, 130, 0.1), 
                rgba(127, 176, 105, 0.1));
            border: 1px solid rgba(127, 176, 105, 0.3);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .notification-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 8px;
            background: #a3d982;
            border-radius: 50%;
            animation: notificationBlink 2s infinite ease-in-out;
        }

        @keyframes notificationBlink {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .notification-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(127, 176, 105, 0.2);
        }

        /* Quick actions */
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .action-button {
            padding: 1rem;
            border: none;
            border-radius: 20px;
            background: linear-gradient(135deg, #7fb069, #5d8a4c);
            color: white;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .action-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.6s ease;
            transform: translate(-50%, -50%);
        }

        .action-button:hover::before {
            width: 300px;
            height: 300px;
        }

        .action-button:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(127, 176, 105, 0.3);
        }

        /* Neural connection lines */
        .neural-connection {
            position: absolute;
            width: 2px;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(127, 176, 105, 0.4), 
                transparent);
            z-index: -1;
            animation: neuralFlow 4s infinite ease-in-out;
        }

        @keyframes neuralFlow {
            0%, 100% { opacity: 0.2; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.1); }
        }

        /* Responsive design */
        @media (max-width: 1024px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                height: auto;
            }
            
            .nav-panel, .main-dashboard, .side-panel {
                grid-column: 1;
            }
        }

        @media (max-width: 768px) {
            main {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .bio-panel {
                padding: 1rem;
            }
        }

        /* Accessibility enhancements */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(127, 176, 105, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #7fb069, #5d8a4c);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #a3d982, #7fb069);
        }
    </style>
</head>
<body>
    <main>
        <h1>Universal Dashboard - Bio-morphic Theme</h1>
        <div class="hybrid-component">
            <!-- Navigation Panel -->
            <div class="bio-panel nav-panel">
                <div class="widget-header">
                    <span class="widget-title">Navigation</span>
                    <div class="widget-controls">
                        <button class="bio-button" title="Expand">+</button>
                    </div>
                </div>
                <nav>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="overview">🌱 Overview</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="analytics">📊 Analytics</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="users">👥 Users</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="performance">⚡ Performance</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="security">🛡️ Security</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="integrations">🔗 Integrations</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="settings">⚙️ Settings</a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Main Dashboard -->
            <div class="bio-panel main-dashboard">
                <!-- Real-time Metrics -->
                <div class="metrics-container">
                    <div class="widget-header">
                        <span class="widget-title">Live Ecosystem Metrics</span>
                        <div class="widget-controls">
                            <button class="bio-button" title="Refresh">↻</button>
                            <button class="bio-button" title="Configure">⚙</button>
                        </div>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-cell">
                            <span class="metric-value" data-metric="users">24.7K</span>
                            <span class="metric-label">Active Users</span>
                        </div>
                        <div class="metric-cell">
                            <span class="metric-value" data-metric="revenue">$89.2K</span>
                            <span class="metric-label">Revenue</span>
                        </div>
                        <div class="metric-cell">
                            <span class="metric-value" data-metric="conversion">12.8%</span>
                            <span class="metric-label">Conversion</span>
                        </div>
                        <div class="metric-cell">
                            <span class="metric-value" data-metric="performance">98.9%</span>
                            <span class="metric-label">Uptime</span>
                        </div>
                    </div>
                </div>

                <!-- Data Visualization -->
                <div class="data-viz">
                    <div class="widget-header">
                        <span class="widget-title">Growth Analytics</span>
                        <div class="widget-controls">
                            <button class="bio-button" title="Export">📥</button>
                            <button class="bio-button" title="Fullscreen">⛶</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="chart-bar" data-value="60" title="Jan: 60%"></div>
                        <div class="chart-bar" data-value="85" title="Feb: 85%"></div>
                        <div class="chart-bar" data-value="40" title="Mar: 40%"></div>
                        <div class="chart-bar" data-value="70" title="Apr: 70%"></div>
                        <div class="chart-bar" data-value="95" title="May: 95%"></div>
                        <div class="chart-bar" data-value="55" title="Jun: 55%"></div>
                    </div>
                </div>

                <!-- Activity Feed -->
                <div class="activity-container">
                    <div class="widget-header">
                        <span class="widget-title">System Activity</span>
                        <div class="widget-controls">
                            <button class="bio-button" title="Filter">⚗</button>
                        </div>
                    </div>
                    <div class="activity-feed">
                        <div class="activity-item">
                            <div class="activity-icon">🔄</div>
                            <div class="activity-text">
                                New integration deployed successfully
                            </div>
                            <div class="activity-time">2m ago</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">👤</div>
                            <div class="activity-text">
                                User authentication system updated
                            </div>
                            <div class="activity-time">15m ago</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">📈</div>
                            <div class="activity-text">
                                Performance metrics exceeded targets
                            </div>
                            <div class="activity-time">1h ago</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">🔒</div>
                            <div class="activity-text">
                                Security scan completed - no issues
                            </div>
                            <div class="activity-time">3h ago</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="bio-panel side-panel">
                <!-- Notifications -->
                <div class="notifications-container">
                    <div class="widget-header">
                        <span class="widget-title">Alerts & Notifications</span>
                        <div class="widget-controls">
                            <button class="bio-button" title="Mark all read">✓</button>
                        </div>
                    </div>
                    <div class="notification-item">
                        <strong>System Health</strong>
                        <p>All systems operating within optimal parameters</p>
                    </div>
                    <div class="notification-item">
                        <strong>Growth Alert</strong>
                        <p>User engagement increased by 15% this week</p>
                    </div>
                    <div class="notification-item">
                        <strong>Integration</strong>
                        <p>New API connection established with external service</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="actions-container">
                    <div class="widget-header">
                        <span class="widget-title">Quick Actions</span>
                    </div>
                    <div class="quick-actions">
                        <button class="action-button" data-action="backup">
                            📋 Backup
                        </button>
                        <button class="action-button" data-action="deploy">
                            🚀 Deploy
                        </button>
                        <button class="action-button" data-action="analyze">
                            🔍 Analyze
                        </button>
                        <button class="action-button" data-action="optimize">
                            ⚡ Optimize
                        </button>
                    </div>
                </div>
            </div>

            <!-- Neural connection lines -->
            <div class="neural-connection" style="top: 20%; left: 33%; height: 60%;"></div>
            <div class="neural-connection" style="top: 40%; left: 66%; height: 40%;"></div>
        </div>
    </main>

    <script>
        // Bio-morphic Universal Dashboard functionality
        class BiomorphicDashboard {
            constructor() {
                this.initializeComponents();
                this.setupEventListeners();
                this.startRealTimeUpdates();
                this.initializeInterconnections();
            }

            initializeComponents() {
                this.metrics = new Map();
                this.chartData = [];
                this.activities = [];
                this.notifications = [];
                this.isAdaptiveMode = true;
                
                // Initialize metrics with base values
                this.updateMetric('users', 24700);
                this.updateMetric('revenue', 89200);
                this.updateMetric('conversion', 12.8);
                this.updateMetric('performance', 98.9);
            }

            setupEventListeners() {
                // Navigation system
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchSection(e.target.dataset.section);
                        this.animateNavigation(e.target);
                    });
                });

                // Widget controls
                document.querySelectorAll('.bio-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.handleWidgetAction(e.target);
                        this.createRippleEffect(e.target, e);
                    });
                });

                // Chart interactions
                document.querySelectorAll('.chart-bar').forEach(bar => {
                    bar.addEventListener('mouseover', (e) => {
                        this.showChartTooltip(e.target);
                        this.highlightConnectedElements(e.target);
                    });
                });

                // Quick actions
                document.querySelectorAll('.action-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.executeQuickAction(e.target.dataset.action);
                        this.animateActionExecution(e.target);
                    });
                });

                // Adaptive resizing
                window.addEventListener('resize', () => {
                    this.adaptLayout();
                });

                // Activity feed interactions
                document.querySelectorAll('.activity-item').forEach(item => {
                    item.addEventListener('click', () => {
                        this.expandActivityDetails(item);
                    });
                });
            }

            startRealTimeUpdates() {
                // Simulate real-time data updates
                setInterval(() => {
                    this.updateMetrics();
                    this.updateChart();
                    this.addNewActivity();
                    this.simulateBiologicalGrowth();
                }, 3000);

                // Cellular breathing animation updates
                setInterval(() => {
                    this.updateCellularPatterns();
                }, 8000);
            }

            initializeInterconnections() {
                // Create neural-like connections between widgets
                this.createNeuralNetwork();
                this.setupDataFlow();
                this.initializeAdaptiveIntelligence();
            }

            updateMetrics() {
                // Simulate organic growth patterns
                const growthFactors = {
                    users: 0.95 + Math.random() * 0.1,
                    revenue: 0.98 + Math.random() * 0.04,
                    conversion: 0.97 + Math.random() * 0.06,
                    performance: 0.995 + Math.random() * 0.01
                };

                this.metrics.forEach((value, key) => {
                    const newValue = value * growthFactors[key];
                    this.updateMetric(key, newValue);
                });
            }

            updateMetric(key, value) {
                this.metrics.set(key, value);
                const element = document.querySelector(`[data-metric="${key}"]`);
                if (element) {
                    this.animateValueChange(element, this.formatMetricValue(key, value));
                }
            }

            formatMetricValue(key, value) {
                switch (key) {
                    case 'users':
                        return `${(value / 1000).toFixed(1)}K`;
                    case 'revenue':
                        return `$${(value / 1000).toFixed(1)}K`;
                    case 'conversion':
                        return `${value.toFixed(1)}%`;
                    case 'performance':
                        return `${value.toFixed(1)}%`;
                    default:
                        return value.toString();
                }
            }

            animateValueChange(element, newValue) {
                element.style.transform = 'scale(1.1)';
                element.style.color = '#a3d982';
                
                setTimeout(() => {
                    element.textContent = newValue;
                    element.style.transform = 'scale(1)';
                    element.style.color = '#a3d982';
                }, 200);
            }

            updateChart() {
                const bars = document.querySelectorAll('.chart-bar');
                bars.forEach((bar, index) => {
                    const newHeight = Math.max(20, Math.random() * 100);
                    bar.style.height = `${newHeight}%`;
                    bar.setAttribute('data-value', Math.round(newHeight));
                    bar.title = `Month ${index + 1}: ${Math.round(newHeight)}%`;
                });
            }

            addNewActivity() {
                const activities = [
                    { icon: '🌱', text: 'System evolution cycle completed', time: 'now' },
                    { icon: '🔄', text: 'Adaptive learning algorithm updated', time: 'now' },
                    { icon: '📊', text: 'Cross-domain analytics synchronized', time: 'now' },
                    { icon: '🧬', text: 'Bio-morphic interface optimized', time: 'now' },
                    { icon: '⚡', text: 'Neural network pathways strengthened', time: 'now' }
                ];

                const randomActivity = activities[Math.floor(Math.random() * activities.length)];
                this.createActivityItem(randomActivity);
            }

            createActivityItem(activity) {
                const feed = document.querySelector('.activity-feed');
                const existingItems = feed.querySelectorAll('.activity-item');
                
                // Remove oldest item if too many
                if (existingItems.length >= 6) {
                    existingItems[existingItems.length - 1].remove();
                }

                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';
                activityItem.style.opacity = '0';
                activityItem.style.transform = 'translateY(-10px)';
                
                activityItem.innerHTML = `
                    <div class="activity-icon">${activity.icon}</div>
                    <div class="activity-text">${activity.text}</div>
                    <div class="activity-time">${activity.time}</div>
                `;

                feed.insertBefore(activityItem, feed.firstChild);
                
                // Animate in
                setTimeout(() => {
                    activityItem.style.opacity = '1';
                    activityItem.style.transform = 'translateY(0)';
                }, 100);
            }

            switchSection(section) {
                // Update main dashboard content based on section
                const mainDashboard = document.querySelector('.main-dashboard');
                this.animateSectionTransition(mainDashboard, section);
                this.updateMetricsForSection(section);
                this.triggerNeuralActivity();
            }

            animateNavigation(navLink) {
                // Remove active class from all nav items
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.style.backgroundColor = '';
                    link.style.borderLeft = '';
                });

                // Add active state
                navLink.style.backgroundColor = 'rgba(127, 176, 105, 0.2)';
                navLink.style.borderLeft = '3px solid #a3d982';
                
                // Create expanding bio-morphic effect
                this.createExpansionEffect(navLink);
            }

            createExpansionEffect(element) {
                const rect = element.getBoundingClientRect();
                const expansion = document.createElement('div');
                expansion.style.cssText = `
                    position: fixed;
                    top: ${rect.top}px;
                    left: ${rect.left}px;
                    width: ${rect.width}px;
                    height: ${rect.height}px;
                    background: radial-gradient(circle, rgba(127, 176, 105, 0.3), transparent);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                    animation: bioExpansion 0.6s ease-out forwards;
                `;

                document.body.appendChild(expansion);
                setTimeout(() => expansion.remove(), 600);
            }

            handleWidgetAction(button) {
                const title = button.getAttribute('title');
                switch (title) {
                    case 'Refresh':
                        this.refreshWidget(button.closest('.bio-panel'));
                        break;
                    case 'Configure':
                        this.openWidgetConfig(button.closest('.bio-panel'));
                        break;
                    case 'Expand':
                        this.expandWidget(button.closest('.bio-panel'));
                        break;
                    case 'Export':
                        this.exportData(button.closest('.bio-panel'));
                        break;
                    case 'Fullscreen':
                        this.toggleFullscreen(button.closest('.bio-panel'));
                        break;
                    case 'Filter':
                        this.showFilterOptions(button.closest('.bio-panel'));
                        break;
                    case 'Mark all read':
                        this.markNotificationsRead();
                        break;
                }
            }

            createRippleEffect(button, event) {
                const ripple = document.createElement('div');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    width: ${size}px;
                    height: ${size}px;
                    left: ${event.clientX - rect.left - size/2}px;
                    top: ${event.clientY - rect.top - size/2}px;
                    animation: rippleEffect 0.6s ease-out;
                    pointer-events: none;
                `;

                button.style.position = 'relative';
                button.appendChild(ripple);
                setTimeout(() => ripple.remove(), 600);
            }

            executeQuickAction(action) {
                const actions = {
                    backup: () => this.simulateBackup(),
                    deploy: () => this.simulateDeploy(),
                    analyze: () => this.simulateAnalysis(),
                    optimize: () => this.simulateOptimization()
                };

                if (actions[action]) {
                    actions[action]();
                }
            }

            simulateBackup() {
                this.showNotification('Backup', 'System backup initiated successfully');
                this.addNewActivity();
            }

            simulateDeploy() {
                this.showNotification('Deploy', 'Deployment process started');
                this.triggerSystemWideAnimation();
            }

            simulateAnalysis() {
                this.showNotification('Analysis', 'Deep system analysis in progress');
                this.updateChart();
            }

            simulateOptimization() {
                this.showNotification('Optimization', 'Bio-morphic optimization applied');
                this.enhancePerformanceMetrics();
            }

            showNotification(title, message) {
                const notification = document.createElement('div');
                notification.className = 'notification-item';
                notification.innerHTML = `
                    <strong>${title}</strong>
                    <p>${message}</p>
                `;

                const container = document.querySelector('.notifications-container');
                const existingNotifications = container.querySelectorAll('.notification-item');
                
                if (existingNotifications.length >= 4) {
                    existingNotifications[existingNotifications.length - 1].remove();
                }

                container.appendChild(notification);
                
                // Animate in
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(20px)';
                setTimeout(() => {
                    notification.style.opacity = '1';
                    notification.style.transform = 'translateX(0)';
                }, 100);
            }

            createNeuralNetwork() {
                // Create visual connections between dashboard elements
                const connections = document.querySelectorAll('.neural-connection');
                connections.forEach(connection => {
                    connection.style.animationDelay = `${Math.random() * 4}s`;
                });
            }

            simulateBiologicalGrowth() {
                // Animate panels with organic growth patterns
                document.querySelectorAll('.bio-panel').forEach(panel => {
                    const scale = 0.98 + Math.random() * 0.04;
                    panel.style.transform = `scale(${scale})`;
                    setTimeout(() => {
                        panel.style.transform = 'scale(1)';
                    }, 500);
                });
            }

            updateCellularPatterns() {
                // Update background cellular patterns
                const patterns = document.querySelectorAll('.bio-panel::before');
                // This would update CSS custom properties if implemented
            }

            triggerNeuralActivity() {
                // Animate neural connections
                document.querySelectorAll('.neural-connection').forEach(connection => {
                    connection.style.animation = 'none';
                    connection.offsetHeight; // Trigger reflow
                    connection.style.animation = 'neuralFlow 4s ease-in-out';
                });
            }

            adaptLayout() {
                // Responsive adaptation based on viewport
                const width = window.innerWidth;
                const dashboard = document.querySelector('.hybrid-component');
                
                if (width < 768) {
                    dashboard.style.gridTemplateColumns = '1fr';
                } else if (width < 1024) {
                    dashboard.style.gridTemplateColumns = '250px 1fr';
                } else {
                    dashboard.style.gridTemplateColumns = '1fr 2fr 1fr';
                }
            }

            enhancePerformanceMetrics() {
                // Boost performance metrics temporarily
                this.updateMetric('performance', Math.min(99.9, this.metrics.get('performance') * 1.01));
                this.updateMetric('conversion', Math.min(15, this.metrics.get('conversion') * 1.05));
            }

            triggerSystemWideAnimation() {
                // Create system-wide bio-morphic animation
                document.querySelectorAll('.bio-panel').forEach((panel, index) => {
                    setTimeout(() => {
                        panel.style.transform = 'scale(1.02)';
                        panel.style.borderColor = 'rgba(163, 217, 130, 0.8)';
                        setTimeout(() => {
                            panel.style.transform = 'scale(1)';
                            panel.style.borderColor = 'rgba(127, 176, 105, 0.3)';
                        }, 300);
                    }, index * 100);
                });
            }
        }

        // Enhanced CSS animations via JavaScript
        const style = document.createElement('style');
        style.textContent = `
            @keyframes bioExpansion {
                from { transform: scale(1); opacity: 0.8; }
                to { transform: scale(3); opacity: 0; }
            }
            
            @keyframes rippleEffect {
                from { transform: scale(0); opacity: 1; }
                to { transform: scale(2); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize the Bio-morphic Universal Dashboard
        document.addEventListener('DOMContentLoaded', () => {
            window.biomorphicDashboard = new BiomorphicDashboard();
            
            // Add keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case '1':
                            e.preventDefault();
                            document.querySelector('[data-section="overview"]').click();
                            break;
                        case '2':
                            e.preventDefault();
                            document.querySelector('[data-section="analytics"]').click();
                            break;
                        case 'r':
                            e.preventDefault();
                            window.biomorphicDashboard.updateMetrics();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>