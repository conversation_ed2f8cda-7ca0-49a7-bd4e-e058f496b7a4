<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk Future Form Wizard</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            background: #0a0a0a;
            color: #00f5ff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Cyberpunk background effects */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 127, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(127, 255, 0, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: atmosphericPulse 8s ease-in-out infinite alternate;
        }

        @keyframes atmosphericPulse {
            0% { opacity: 0.6; transform: scale(1); }
            100% { opacity: 1; transform: scale(1.05); }
        }

        /* Matrix rain effect */
        .matrix-rain {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }

        .matrix-char {
            position: absolute;
            color: #00f5ff;
            font-family: 'Orbitron', monospace;
            font-size: 14px;
            animation: matrixFall linear infinite;
        }

        @keyframes matrixFall {
            0% { transform: translateY(-100vh); opacity: 1; }
            100% { transform: translateY(100vh); opacity: 0; }
        }

        main {
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        h1 {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(45deg, #00f5ff, #ff007f, #7fff00, #00f5ff);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: holographicShift 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
            position: relative;
        }

        h1::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 2px;
            color: #ff007f;
            opacity: 0.8;
            z-index: -1;
            animation: glitch 2s infinite;
        }

        @keyframes holographicShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes glitch {
            0%, 90%, 100% { transform: translate(0); }
            10% { transform: translate(-2px, 1px); }
            20% { transform: translate(2px, -1px); }
            30% { transform: translate(-1px, 2px); }
            40% { transform: translate(1px, -2px); }
            50% { transform: translate(-2px, -1px); }
            60% { transform: translate(2px, 1px); }
            70% { transform: translate(-1px, -2px); }
            80% { transform: translate(1px, 2px); }
        }

        .hybrid-component {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(10, 10, 10, 0.95);
            border: 2px solid #00f5ff;
            border-radius: 15px;
            padding: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 0 50px rgba(0, 245, 255, 0.3),
                inset 0 0 50px rgba(0, 245, 255, 0.1);
        }

        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00f5ff, #ff007f, #7fff00, #00f5ff);
            background-size: 400% 400%;
            border-radius: 15px;
            z-index: -1;
            animation: borderFlow 4s ease-in-out infinite;
        }

        @keyframes borderFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Progress Indicator */
        .progress-container {
            margin-bottom: 3rem;
            position: relative;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            margin-bottom: 1rem;
        }

        .progress-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background: rgba(0, 245, 255, 0.2);
            transform: translateY(-50%);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00f5ff, #ff007f, #7fff00);
            background-size: 200% 100%;
            border-radius: 2px;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            animation: energyFlow 2s linear infinite;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.6);
        }

        @keyframes energyFlow {
            0% { background-position: 200% 0%; }
            100% { background-position: -200% 0%; }
        }

        .step-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(10, 10, 10, 0.9);
            border: 3px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.2rem;
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .step-indicator.active {
            border-color: #00f5ff;
            color: #00f5ff;
            background: rgba(0, 245, 255, 0.1);
            box-shadow: 
                0 0 30px rgba(0, 245, 255, 0.5),
                inset 0 0 20px rgba(0, 245, 255, 0.2);
            animation: holographicPulse 2s ease-in-out infinite;
        }

        .step-indicator.completed {
            border-color: #7fff00;
            color: #7fff00;
            background: rgba(127, 255, 0, 0.1);
            box-shadow: 0 0 25px rgba(127, 255, 0, 0.4);
        }

        .step-indicator.completed::before {
            content: '✓';
            position: absolute;
            animation: checkmarkGlow 0.6s ease-out;
        }

        @keyframes holographicPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 30px rgba(0, 245, 255, 0.5); }
            50% { transform: scale(1.05); box-shadow: 0 0 40px rgba(0, 245, 255, 0.8); }
        }

        @keyframes checkmarkGlow {
            0% { transform: scale(0) rotate(0deg); opacity: 0; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
            100% { transform: scale(1) rotate(360deg); opacity: 1; }
        }

        .step-label {
            text-align: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #666;
            transition: color 0.3s ease;
        }

        .step-indicator.active + .step-label {
            color: #00f5ff;
            text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        .step-indicator.completed + .step-label {
            color: #7fff00;
        }

        /* Form Steps */
        .form-step {
            display: none;
            animation: slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-step.active {
            display: block;
        }

        .form-step.prev {
            animation: slideOutToLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInFromRight {
            0% { transform: translateX(100px); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutToLeft {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(-100px); opacity: 0; }
        }

        .step-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #00f5ff;
            text-align: center;
            position: relative;
        }

        .step-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #00f5ff, #ff007f);
            border-radius: 2px;
            animation: titleUnderlineGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleUnderlineGlow {
            0% { box-shadow: 0 0 10px rgba(0, 245, 255, 0.5); }
            100% { box-shadow: 0 0 20px rgba(255, 0, 127, 0.8); }
        }

        /* Form Fields */
        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: #00f5ff;
            font-family: 'Orbitron', monospace;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #333;
            border-radius: 8px;
            color: #00f5ff;
            font-family: 'Rajdhani', sans-serif;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #00f5ff;
            box-shadow: 
                0 0 20px rgba(0, 245, 255, 0.4),
                inset 0 0 20px rgba(0, 245, 255, 0.1);
            background: rgba(0, 245, 255, 0.05);
        }

        .form-input.valid {
            border-color: #7fff00;
            box-shadow: 0 0 15px rgba(127, 255, 0, 0.3);
        }

        .form-input.invalid {
            border-color: #ff007f;
            box-shadow: 0 0 15px rgba(255, 0, 127, 0.3);
            animation: validationShake 0.5s ease-in-out;
        }

        @keyframes validationShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .validation-message {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .validation-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .validation-message.error {
            color: #ff007f;
            text-shadow: 0 0 10px rgba(255, 0, 127, 0.5);
        }

        .validation-message.success {
            color: #7fff00;
            text-shadow: 0 0 10px rgba(127, 255, 0, 0.5);
        }

        /* Navigation Controls */
        .form-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(0, 245, 255, 0.2);
        }

        .nav-button {
            padding: 12px 30px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00f5ff;
            border-radius: 25px;
            color: #00f5ff;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-button:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            border-color: #333;
            color: #666;
        }

        .nav-button:not(:disabled):hover {
            background: rgba(0, 245, 255, 0.1);
            box-shadow: 
                0 0 25px rgba(0, 245, 255, 0.4),
                inset 0 0 25px rgba(0, 245, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-button.primary {
            background: linear-gradient(45deg, #00f5ff, #ff007f);
            border-color: transparent;
            color: #000;
            font-weight: 700;
        }

        .nav-button.primary:not(:disabled):hover {
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.6);
            transform: translateY(-3px) scale(1.05);
        }

        .nav-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .nav-button:not(:disabled):active::before {
            width: 300px;
            height: 300px;
        }

        /* Auto-save Indicator */
        .autosave-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .autosave-status.show {
            opacity: 1;
            transform: translateY(0);
        }

        .autosave-status.saving {
            color: #ff007f;
        }

        .autosave-status.saved {
            color: #7fff00;
        }

        .autosave-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid #ff007f;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .autosave-icon {
            width: 16px;
            height: 16px;
            background: #7fff00;
            border-radius: 50%;
            position: relative;
            animation: saveSuccess 0.6s ease-out;
        }

        .autosave-icon::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            font-size: 10px;
            font-weight: bold;
        }

        @keyframes saveSuccess {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Data Stream Animation */
        .data-stream {
            position: absolute;
            top: 0;
            right: 20px;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, transparent, #00f5ff, transparent);
            animation: dataFlow 3s ease-in-out infinite;
            opacity: 0.3;
        }

        @keyframes dataFlow {
            0%, 100% { transform: translateY(-100%); }
            50% { transform: translateY(100%); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .progress-steps {
                flex-direction: column;
                gap: 1rem;
            }

            .progress-line {
                left: 50%;
                top: 0;
                bottom: 0;
                right: auto;
                width: 3px;
                height: auto;
                transform: translateX(-50%);
            }

            .progress-fill {
                width: 100% !important;
            }

            .step-indicator {
                width: 50px;
                height: 50px;
                font-size: 1rem;
                align-self: center;
            }

            h1 {
                font-size: 2rem;
            }

            .hybrid-component {
                padding: 1.5rem;
                margin: 1rem;
            }

            .form-navigation {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Matrix Rain Effect -->
    <div class="matrix-rain" id="matrixRain"></div>

    <main>
        <h1 data-text="Form Wizard - Cyberpunk Future Theme">Form Wizard - Cyberpunk Future Theme</h1>
        
        <div class="hybrid-component">
            <div class="data-stream"></div>
            
            <!-- Progress Indicator -->
            <div class="progress-container">
                <div class="progress-steps">
                    <div class="progress-line">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    
                    <div class="step-wrapper">
                        <div class="step-indicator active" data-step="1">1</div>
                        <div class="step-label">Neural ID</div>
                    </div>
                    
                    <div class="step-wrapper">
                        <div class="step-indicator" data-step="2">2</div>
                        <div class="step-label">Data Matrix</div>
                    </div>
                    
                    <div class="step-wrapper">
                        <div class="step-indicator" data-step="3">3</div>
                        <div class="step-label">Bio Metrics</div>
                    </div>
                    
                    <div class="step-wrapper">
                        <div class="step-indicator" data-step="4">4</div>
                        <div class="step-label">Upload</div>
                    </div>
                </div>
            </div>

            <!-- Form Steps -->
            <form id="cyberpunkForm">
                <!-- Step 1: Neural ID -->
                <div class="form-step active" data-step="1">
                    <h2 class="step-title">Neural Identity Verification</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="neuralId">Neural ID</label>
                        <input type="text" id="neuralId" class="form-input" placeholder="Enter your neural identification">
                        <div class="validation-message" id="neuralIdValidation"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="callSign">Call Sign</label>
                        <input type="text" id="callSign" class="form-input" placeholder="Your cyberpunk alias">
                        <div class="validation-message" id="callSignValidation"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="sector">Sector</label>
                        <select id="sector" class="form-select">
                            <option value="">Select your sector</option>
                            <option value="neo-tokyo">Neo Tokyo</option>
                            <option value="cyber-city">Cyber City</option>
                            <option value="digital-underground">Digital Underground</option>
                            <option value="neon-district">Neon District</option>
                        </select>
                        <div class="validation-message" id="sectorValidation"></div>
                    </div>
                </div>

                <!-- Step 2: Data Matrix -->
                <div class="form-step" data-step="2">
                    <h2 class="step-title">Data Matrix Configuration</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="encryptionLevel">Encryption Level</label>
                        <select id="encryptionLevel" class="form-select">
                            <option value="">Select encryption</option>
                            <option value="basic">Basic - 128-bit</option>
                            <option value="advanced">Advanced - 256-bit</option>
                            <option value="quantum">Quantum - 512-bit</option>
                        </select>
                        <div class="validation-message" id="encryptionValidation"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="dataPackets">Data Packets</label>
                        <input type="number" id="dataPackets" class="form-input" placeholder="Number of data packets" min="1" max="999">
                        <div class="validation-message" id="dataPacketsValidation"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="protocol">Transfer Protocol</label>
                        <select id="protocol" class="form-select">
                            <option value="">Select protocol</option>
                            <option value="quantum-tunnel">Quantum Tunnel</option>
                            <option value="neural-link">Neural Link</option>
                            <option value="ghost-protocol">Ghost Protocol</option>
                        </select>
                        <div class="validation-message" id="protocolValidation"></div>
                    </div>
                </div>

                <!-- Step 3: Bio Metrics -->
                <div class="form-step" data-step="3">
                    <h2 class="step-title">Biometric Authentication</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="retinalScan">Retinal Scan ID</label>
                        <input type="text" id="retinalScan" class="form-input" placeholder="Retinal pattern verification">
                        <div class="validation-message" id="retinalValidation"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="neuralPattern">Neural Pattern</label>
                        <input type="text" id="neuralPattern" class="form-input" placeholder="Brain wave signature">
                        <div class="validation-message" id="neuralPatternValidation"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="securityClearance">Security Clearance</label>
                        <select id="securityClearance" class="form-select">
                            <option value="">Select clearance level</option>
                            <option value="alpha">Alpha - Standard Access</option>
                            <option value="beta">Beta - Enhanced Access</option>
                            <option value="omega">Omega - Full Access</option>
                        </select>
                        <div class="validation-message" id="clearanceValidation"></div>
                    </div>
                </div>

                <!-- Step 4: Upload -->
                <div class="form-step" data-step="4">
                    <h2 class="step-title">Data Upload Complete</h2>
                    
                    <div style="text-align: center; padding: 2rem;">
                        <div style="font-size: 4rem; color: #7fff00; margin-bottom: 1rem; animation: holographicPulse 2s infinite;">
                            ◈
                        </div>
                        <p style="font-size: 1.2rem; color: #00f5ff; margin-bottom: 1rem;">Neural matrix synchronized successfully</p>
                        <p style="color: #7fff00;">Your cyberpunk profile has been uploaded to the mainframe.</p>
                        <p style="color: #666; font-size: 0.9rem; margin-top: 1rem;">Connection established • Security protocols activated</p>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="form-navigation">
                    <button type="button" class="nav-button" id="prevBtn" disabled>Previous</button>
                    
                    <div class="autosave-status" id="autosaveStatus">
                        <div class="autosave-spinner" id="autosaveSpinner" style="display: none;"></div>
                        <div class="autosave-icon" id="autosaveIcon" style="display: none;"></div>
                        <span id="autosaveText"></span>
                    </div>
                    
                    <button type="button" class="nav-button primary" id="nextBtn">Next</button>
                </div>
            </form>
        </div>
    </main>

    <script>
        // Cyberpunk Form Wizard Controller
        class CyberpunkFormWizard {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 4;
                this.formData = {};
                this.autoSaveTimer = null;
                
                this.init();
                this.createMatrixRain();
            }
            
            init() {
                this.bindEvents();
                this.updateProgress();
                this.loadSavedData();
                this.startAutoSave();
            }
            
            bindEvents() {
                // Navigation buttons
                document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
                document.getElementById('prevBtn').addEventListener('click', () => this.prevStep());
                
                // Step indicators
                document.querySelectorAll('.step-indicator').forEach(step => {
                    step.addEventListener('click', (e) => {
                        const stepNum = parseInt(e.target.dataset.step);
                        if (stepNum <= this.getMaxAccessibleStep()) {
                            this.goToStep(stepNum);
                        }
                    });
                });
                
                // Form inputs
                document.querySelectorAll('.form-input, .form-select').forEach(input => {
                    input.addEventListener('input', () => {
                        this.validateField(input);
                        this.triggerAutoSave();
                    });
                    
                    input.addEventListener('focus', () => {
                        this.addFocusEffects(input);
                    });
                    
                    input.addEventListener('blur', () => {
                        this.removeFocusEffects(input);
                    });
                });
            }
            
            nextStep() {
                if (this.validateCurrentStep()) {
                    if (this.currentStep < this.totalSteps) {
                        this.goToStep(this.currentStep + 1);
                    }
                } else {
                    this.shakeInvalidFields();
                }
            }
            
            prevStep() {
                if (this.currentStep > 1) {
                    this.goToStep(this.currentStep - 1);
                }
            }
            
            goToStep(stepNum) {
                // Hide current step
                const currentStepEl = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
                currentStepEl.classList.remove('active');
                currentStepEl.classList.add('prev');
                
                setTimeout(() => {
                    currentStepEl.classList.remove('prev');
                    currentStepEl.style.display = 'none';
                    
                    // Show new step
                    const newStepEl = document.querySelector(`.form-step[data-step="${stepNum}"]`);
                    newStepEl.style.display = 'block';
                    newStepEl.classList.add('active');
                    
                    this.currentStep = stepNum;
                    this.updateProgress();
                    this.updateNavigation();
                    this.addGlitchEffect();
                }, 300);
            }
            
            validateCurrentStep() {
                const currentStepEl = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
                const inputs = currentStepEl.querySelectorAll('.form-input, .form-select');
                let isValid = true;
                
                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isValid = false;
                    }
                });
                
                return isValid;
            }
            
            validateField(field) {
                const value = field.value.trim();
                const fieldId = field.id;
                const validationEl = document.getElementById(fieldId + 'Validation');
                let isValid = true;
                let message = '';
                
                // Required field validation
                if (!value) {
                    isValid = false;
                    message = 'This field is required for neural authentication';
                } else {
                    // Specific validations
                    switch (fieldId) {
                        case 'neuralId':
                            if (value.length < 8) {
                                isValid = false;
                                message = 'Neural ID must be at least 8 characters';
                            } else {
                                message = 'Neural ID verified ✓';
                            }
                            break;
                        
                        case 'callSign':
                            if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
                                isValid = false;
                                message = 'Call sign can only contain letters, numbers, _ and -';
                            } else {
                                message = 'Call sign accepted ✓';
                            }
                            break;
                        
                        case 'dataPackets':
                            const num = parseInt(value);
                            if (isNaN(num) || num < 1 || num > 999) {
                                isValid = false;
                                message = 'Data packets must be between 1 and 999';
                            } else {
                                message = `${num} data packets configured ✓`;
                            }
                            break;
                        
                        case 'retinalScan':
                            if (value.length < 12) {
                                isValid = false;
                                message = 'Retinal scan pattern too short';
                            } else {
                                message = 'Retinal pattern authenticated ✓';
                            }
                            break;
                        
                        case 'neuralPattern':
                            if (!/^[A-Z0-9]{6,}$/.test(value.toUpperCase())) {
                                isValid = false;
                                message = 'Neural pattern must be at least 6 alphanumeric characters';
                            } else {
                                message = 'Neural pattern synchronized ✓';
                            }
                            break;
                        
                        default:
                            if (value) {
                                message = 'Selection confirmed ✓';
                            }
                    }
                }
                
                // Update field appearance
                field.classList.remove('valid', 'invalid');
                field.classList.add(isValid ? 'valid' : 'invalid');
                
                // Update validation message
                if (validationEl) {
                    validationEl.textContent = message;
                    validationEl.className = `validation-message show ${isValid ? 'success' : 'error'}`;
                }
                
                return isValid;
            }
            
            updateProgress() {
                const progressFill = document.getElementById('progressFill');
                const stepIndicators = document.querySelectorAll('.step-indicator');
                
                // Update progress bar
                const progressPercent = ((this.currentStep - 1) / (this.totalSteps - 1)) * 100;
                progressFill.style.width = progressPercent + '%';
                
                // Update step indicators
                stepIndicators.forEach((indicator, index) => {
                    const stepNum = index + 1;
                    indicator.classList.remove('active', 'completed');
                    
                    if (stepNum < this.currentStep) {
                        indicator.classList.add('completed');
                    } else if (stepNum === this.currentStep) {
                        indicator.classList.add('active');
                    }
                });
            }
            
            updateNavigation() {
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                
                prevBtn.disabled = this.currentStep === 1;
                
                if (this.currentStep === this.totalSteps) {
                    nextBtn.textContent = 'Complete Upload';
                    nextBtn.classList.add('completion');
                } else {
                    nextBtn.textContent = 'Next';
                    nextBtn.classList.remove('completion');
                }
            }
            
            getMaxAccessibleStep() {
                // Users can only access steps they've validated up to
                for (let i = 1; i < this.currentStep; i++) {
                    const stepEl = document.querySelector(`.form-step[data-step="${i}"]`);
                    const inputs = stepEl.querySelectorAll('.form-input, .form-select');
                    
                    for (let input of inputs) {
                        if (!input.value.trim()) {
                            return i;
                        }
                    }
                }
                return this.currentStep;
            }
            
            shakeInvalidFields() {
                const currentStepEl = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
                const invalidFields = currentStepEl.querySelectorAll('.form-input.invalid, .form-select.invalid');
                
                invalidFields.forEach(field => {
                    field.style.animation = 'none';
                    setTimeout(() => {
                        field.style.animation = 'validationShake 0.5s ease-in-out';
                    }, 10);
                });
            }
            
            addFocusEffects(input) {
                // Add cyberpunk focus effects
                input.style.boxShadow = `
                    0 0 25px rgba(0, 245, 255, 0.6),
                    inset 0 0 25px rgba(0, 245, 255, 0.1)
                `;
            }
            
            removeFocusEffects(input) {
                // Remove focus effects
                setTimeout(() => {
                    if (document.activeElement !== input) {
                        input.style.boxShadow = '';
                    }
                }, 100);
            }
            
            addGlitchEffect() {
                // Add glitch effect to title on step change
                const title = document.querySelector('.step-title');
                title.style.animation = 'none';
                setTimeout(() => {
                    title.style.animation = 'glitch 0.5s ease-out';
                }, 10);
            }
            
            triggerAutoSave() {
                clearTimeout(this.autoSaveTimer);
                this.autoSaveTimer = setTimeout(() => {
                    this.autoSave();
                }, 1000);
            }
            
            autoSave() {
                const statusEl = document.getElementById('autosaveStatus');
                const spinnerEl = document.getElementById('autosaveSpinner');
                const iconEl = document.getElementById('autosaveIcon');
                const textEl = document.getElementById('autosaveText');
                
                // Show saving state
                statusEl.classList.add('show', 'saving');
                spinnerEl.style.display = 'block';
                iconEl.style.display = 'none';
                textEl.textContent = 'Uploading to mainframe...';
                
                // Collect form data
                this.formData = this.collectFormData();
                
                // Simulate save
                setTimeout(() => {
                    // Save to localStorage
                    localStorage.setItem('cyberpunkFormData', JSON.stringify(this.formData));
                    
                    // Show saved state
                    statusEl.classList.remove('saving');
                    statusEl.classList.add('saved');
                    spinnerEl.style.display = 'none';
                    iconEl.style.display = 'block';
                    textEl.textContent = 'Data synchronized';
                    
                    // Hide after delay
                    setTimeout(() => {
                        statusEl.classList.remove('show', 'saved');
                    }, 2000);
                }, 1500);
            }
            
            collectFormData() {
                const data = {};
                document.querySelectorAll('.form-input, .form-select').forEach(input => {
                    if (input.value.trim()) {
                        data[input.id] = input.value.trim();
                    }
                });
                return data;
            }
            
            loadSavedData() {
                const savedData = localStorage.getItem('cyberpunkFormData');
                if (savedData) {
                    this.formData = JSON.parse(savedData);
                    
                    // Populate form fields
                    Object.keys(this.formData).forEach(fieldId => {
                        const field = document.getElementById(fieldId);
                        if (field) {
                            field.value = this.formData[fieldId];
                            this.validateField(field);
                        }
                    });
                }
            }
            
            startAutoSave() {
                // Set up periodic auto-save
                setInterval(() => {
                    if (this.hasUnsavedChanges()) {
                        this.autoSave();
                    }
                }, 30000); // Auto-save every 30 seconds
            }
            
            hasUnsavedChanges() {
                const currentData = this.collectFormData();
                return JSON.stringify(currentData) !== JSON.stringify(this.formData);
            }
            
            createMatrixRain() {
                const matrixContainer = document.getElementById('matrixRain');
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                
                function createChar() {
                    const char = document.createElement('div');
                    char.className = 'matrix-char';
                    char.textContent = chars[Math.floor(Math.random() * chars.length)];
                    char.style.left = Math.random() * 100 + '%';
                    char.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    char.style.fontSize = (Math.random() * 8 + 10) + 'px';
                    
                    matrixContainer.appendChild(char);
                    
                    // Remove char after animation
                    setTimeout(() => {
                        if (char.parentNode) {
                            char.parentNode.removeChild(char);
                        }
                    }, 5000);
                }
                
                // Create matrix rain
                setInterval(createChar, 150);
            }
        }
        
        // Initialize the Cyberpunk Form Wizard
        document.addEventListener('DOMContentLoaded', () => {
            new CyberpunkFormWizard();
        });
        
        // Add cyberpunk console messages
        console.log('%c>> NEURAL INTERFACE INITIALIZED <<', 'color: #00f5ff; font-size: 14px; font-weight: bold;');
        console.log('%cCyberpunk Form Wizard v2.77.1', 'color: #7fff00; font-size: 12px;');
        console.log('%cSecurity Protocol: ACTIVE', 'color: #ff007f; font-size: 12px;');
    </script>
</body>
</html>