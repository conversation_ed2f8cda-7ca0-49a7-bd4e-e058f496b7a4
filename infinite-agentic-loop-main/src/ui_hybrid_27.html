<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Handcrafted Paper Content Cards - Hybrid UI 27</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Caveat:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', cursive;
            background: linear-gradient(to bottom, #f5f1e8, #e8e0d3);
            min-height: 100vh;
            padding: 2rem;
            position: relative;
            overflow-x: hidden;
        }
        
        /* Wood grain background texture */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(139, 69, 19, 0.03) 2px,
                    rgba(139, 69, 19, 0.03) 4px
                ),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(139, 69, 19, 0.03) 2px,
                    rgba(139, 69, 19, 0.03) 4px
                );
            pointer-events: none;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
        }
        
        h1 {
            font-family: 'Caveat', cursive;
            font-size: 3rem;
            color: #2c1810;
            text-align: center;
            margin-bottom: 3rem;
            transform: rotate(-2deg);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
            position: relative;
        }
        
        .paper-card {
            position: relative;
            background: #faf8f3;
            padding: 2rem;
            border-radius: 3px;
            transform: rotate(var(--rotation, 0deg));
            transition: all 0.3s ease;
            cursor: pointer;
            filter: drop-shadow(4px 6px 12px rgba(0, 0, 0, 0.15));
        }
        
        .paper-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    horizontal,
                    transparent,
                    transparent 20px,
                    rgba(51, 51, 51, 0.03) 21px,
                    transparent 22px
                ),
                radial-gradient(ellipse at top left, rgba(139, 69, 19, 0.05), transparent),
                radial-gradient(ellipse at bottom right, rgba(139, 69, 19, 0.05), transparent);
            pointer-events: none;
        }
        
        /* Torn paper edges */
        .paper-card::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: #faf8f3;
            z-index: -1;
            clip-path: polygon(
                0% 0%, 3% 2%, 6% 0%, 9% 2%, 12% 0%, 15% 2%, 18% 0%, 21% 2%, 24% 0%, 27% 2%, 30% 0%, 33% 2%, 36% 0%, 39% 2%, 42% 0%, 45% 2%, 48% 0%, 51% 2%, 54% 0%, 57% 2%, 60% 0%, 63% 2%, 66% 0%, 69% 2%, 72% 0%, 75% 2%, 78% 0%, 81% 2%, 84% 0%, 87% 2%, 90% 0%, 93% 2%, 96% 0%, 99% 2%, 100% 0%,
                100% 3%, 98% 6%, 100% 9%, 98% 12%, 100% 15%, 98% 18%, 100% 21%, 98% 24%, 100% 27%, 98% 30%, 100% 33%, 98% 36%, 100% 39%, 98% 42%, 100% 45%, 98% 48%, 100% 51%, 98% 54%, 100% 57%, 98% 60%, 100% 63%, 98% 66%, 100% 69%, 98% 72%, 100% 75%, 98% 78%, 100% 81%, 98% 84%, 100% 87%, 98% 90%, 100% 93%, 98% 96%, 100% 99%, 100% 100%,
                97% 100%, 94% 98%, 91% 100%, 88% 98%, 85% 100%, 82% 98%, 79% 100%, 76% 98%, 73% 100%, 70% 98%, 67% 100%, 64% 98%, 61% 100%, 58% 98%, 55% 100%, 52% 98%, 49% 100%, 46% 98%, 43% 100%, 40% 98%, 37% 100%, 34% 98%, 31% 100%, 28% 98%, 25% 100%, 22% 98%, 19% 100%, 16% 98%, 13% 100%, 10% 98%, 7% 100%, 4% 98%, 1% 100%, 0% 100%,
                0% 97%, 2% 94%, 0% 91%, 2% 88%, 0% 85%, 2% 82%, 0% 79%, 2% 76%, 0% 73%, 2% 70%, 0% 67%, 2% 64%, 0% 61%, 2% 58%, 0% 55%, 2% 52%, 0% 49%, 2% 46%, 0% 43%, 2% 40%, 0% 37%, 2% 34%, 0% 31%, 2% 28%, 0% 25%, 2% 22%, 0% 19%, 2% 16%, 0% 13%, 2% 10%, 0% 7%, 2% 4%, 0% 1%, 0% 0%
            );
        }
        
        .paper-card:hover {
            transform: rotate(0deg) scale(1.02);
            filter: drop-shadow(6px 8px 16px rgba(0, 0, 0, 0.2));
        }
        
        /* Paper clip */
        .paper-clip {
            position: absolute;
            top: -10px;
            right: 30px;
            width: 30px;
            height: 60px;
            background: linear-gradient(to bottom, #c0c0c0, #808080);
            border-radius: 15px 15px 0 0;
            border: 2px solid #606060;
            z-index: 10;
            transform: rotate(10deg);
        }
        
        .paper-clip::before {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 40px;
            background: linear-gradient(to bottom, #808080, #606060);
            border-radius: 0 0 10px 10px;
        }
        
        /* Content preview */
        .content-preview {
            position: relative;
            z-index: 1;
        }
        
        .content-type {
            display: inline-block;
            background: rgba(139, 69, 19, 0.1);
            color: #8b4513;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            border: 1px dashed #8b4513;
        }
        
        .content-title {
            font-family: 'Caveat', cursive;
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c1810;
            margin-bottom: 1rem;
            line-height: 1.3;
        }
        
        .content-excerpt {
            color: #4a3428;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        /* Hand-drawn underlines */
        .highlight {
            position: relative;
            display: inline;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #ff6b6b;
            transform: skew(-5deg);
            opacity: 0.6;
        }
        
        /* Metadata */
        .metadata {
            display: flex;
            gap: 1.5rem;
            font-size: 0.9rem;
            color: #6a5244;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .metadata-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        /* Hand-drawn icons */
        .icon {
            width: 20px;
            height: 20px;
            position: relative;
        }
        
        .icon-calendar::before {
            content: '';
            position: absolute;
            width: 18px;
            height: 18px;
            border: 2px solid currentColor;
            border-radius: 2px;
            transform: rotate(-2deg);
        }
        
        .icon-calendar::after {
            content: '';
            position: absolute;
            top: -3px;
            left: 3px;
            width: 3px;
            height: 6px;
            background: currentColor;
            box-shadow: 9px 0 currentColor;
        }
        
        .icon-clock::before {
            content: '';
            position: absolute;
            width: 18px;
            height: 18px;
            border: 2px solid currentColor;
            border-radius: 50%;
            transform: rotate(-5deg);
        }
        
        .icon-clock::after {
            content: '';
            position: absolute;
            top: 9px;
            left: 9px;
            width: 6px;
            height: 2px;
            background: currentColor;
            transform-origin: 0 50%;
            transform: rotate(-45deg);
            box-shadow: 0 -4px 0 currentColor;
        }
        
        .icon-user::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 6px;
            width: 8px;
            height: 8px;
            border: 2px solid currentColor;
            border-radius: 50%;
        }
        
        .icon-user::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 2px;
            width: 14px;
            height: 8px;
            border: 2px solid currentColor;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
        }
        
        /* Action buttons */
        .actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            position: relative;
            z-index: 2;
        }
        
        .action-btn {
            background: none;
            border: 2px dashed #8b4513;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-family: 'Kalam', cursive;
            font-size: 0.9rem;
            color: #8b4513;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .action-btn:hover {
            background: rgba(139, 69, 19, 0.1);
            transform: translateY(-2px) rotate(-1deg);
        }
        
        .action-btn.liked {
            background: #ff6b6b;
            color: white;
            border-color: #ff6b6b;
        }
        
        /* Doodles */
        .doodle {
            position: absolute;
            pointer-events: none;
            opacity: 0.3;
        }
        
        .doodle-star {
            width: 30px;
            height: 30px;
            top: 20px;
            left: 20px;
            transform: rotate(15deg);
        }
        
        .doodle-star::before {
            content: '✦';
            font-size: 30px;
            color: #ff6b6b;
        }
        
        .doodle-arrow {
            width: 50px;
            height: 20px;
            bottom: 30px;
            right: 40px;
            transform: rotate(-10deg);
        }
        
        .doodle-arrow::before {
            content: '→';
            font-size: 40px;
            color: #4ecdc4;
            font-weight: bold;
        }
        
        /* Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background: #faf8f3;
            padding: 3rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            transform: scale(0.8) rotate(5deg);
            transition: transform 0.3s ease;
            border-radius: 5px;
            filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
        }
        
        .modal-overlay.active .modal-content {
            transform: scale(1) rotate(-1deg);
        }
        
        /* Apply torn edges to modal */
        .modal-content::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: #faf8f3;
            z-index: -1;
            clip-path: polygon(
                0% 0%, 3% 2%, 6% 0%, 9% 2%, 12% 0%, 15% 2%, 18% 0%, 21% 2%, 24% 0%, 27% 2%, 30% 0%, 33% 2%, 36% 0%, 39% 2%, 42% 0%, 45% 2%, 48% 0%, 51% 2%, 54% 0%, 57% 2%, 60% 0%, 63% 2%, 66% 0%, 69% 2%, 72% 0%, 75% 2%, 78% 0%, 81% 2%, 84% 0%, 87% 2%, 90% 0%, 93% 2%, 96% 0%, 99% 2%, 100% 0%,
                100% 3%, 98% 6%, 100% 9%, 98% 12%, 100% 15%, 98% 18%, 100% 21%, 98% 24%, 100% 27%, 98% 30%, 100% 33%, 98% 36%, 100% 39%, 98% 42%, 100% 45%, 98% 48%, 100% 51%, 98% 54%, 100% 57%, 98% 60%, 100% 63%, 98% 66%, 100% 69%, 98% 72%, 100% 75%, 98% 78%, 100% 81%, 98% 84%, 100% 87%, 98% 90%, 100% 93%, 98% 96%, 100% 99%, 100% 100%,
                97% 100%, 94% 98%, 91% 100%, 88% 98%, 85% 100%, 82% 98%, 79% 100%, 76% 98%, 73% 100%, 70% 98%, 67% 100%, 64% 98%, 61% 100%, 58% 98%, 55% 100%, 52% 98%, 49% 100%, 46% 98%, 43% 100%, 40% 98%, 37% 100%, 34% 98%, 31% 100%, 28% 98%, 25% 100%, 22% 98%, 19% 100%, 16% 98%, 13% 100%, 10% 98%, 7% 100%, 4% 98%, 1% 100%, 0% 100%,
                0% 97%, 2% 94%, 0% 91%, 2% 88%, 0% 85%, 2% 82%, 0% 79%, 2% 76%, 0% 73%, 2% 70%, 0% 67%, 2% 64%, 0% 61%, 2% 58%, 0% 55%, 2% 52%, 0% 49%, 2% 46%, 0% 43%, 2% 40%, 0% 37%, 2% 34%, 0% 31%, 2% 28%, 0% 25%, 2% 22%, 0% 19%, 2% 16%, 0% 13%, 2% 10%, 0% 7%, 2% 4%, 0% 1%, 0% 0%
            );
        }
        
        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #8b4513;
            transition: transform 0.2s ease;
        }
        
        .close-btn:hover {
            transform: rotate(90deg);
        }
        
        /* Share menu */
        .share-menu {
            position: absolute;
            bottom: 100%;
            left: 0;
            background: #faf8f3;
            border: 2px dashed #8b4513;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 10px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.2s ease;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }
        
        .share-menu.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .share-option {
            display: block;
            padding: 0.5rem;
            border: none;
            background: none;
            cursor: pointer;
            font-family: 'Kalam', cursive;
            color: #4a3428;
            transition: all 0.2s ease;
            width: 100%;
            text-align: left;
        }
        
        .share-option:hover {
            background: rgba(139, 69, 19, 0.1);
            transform: translateX(5px);
        }
        
        /* Fold animation */
        @keyframes unfold {
            0% {
                transform: rotateX(90deg);
                opacity: 0;
            }
            100% {
                transform: rotateX(0);
                opacity: 1;
            }
        }
        
        .fold-content {
            animation: unfold 0.5s ease-out;
            transform-origin: top center;
        }
        
        /* Coffee stain */
        .coffee-stain {
            position: absolute;
            width: 80px;
            height: 80px;
            background: radial-gradient(ellipse at center, transparent 30%, rgba(139, 69, 19, 0.1) 40%, rgba(139, 69, 19, 0.05) 70%, transparent 71%);
            border-radius: 50%;
            pointer-events: none;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .modal-content {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>My Paper Collection</h1>
        
        <div class="cards-grid">
            <!-- Recipe Card -->
            <article class="paper-card" style="--rotation: -2deg;" data-id="1">
                <div class="paper-clip"></div>
                <div class="doodle doodle-star"></div>
                <div class="coffee-stain" style="bottom: 20px; right: 30px; transform: rotate(15deg);"></div>
                
                <div class="content-preview">
                    <span class="content-type">Recipe</span>
                    <h2 class="content-title">Grandma's Secret <span class="highlight">Chocolate Chip</span> Cookies</h2>
                    <p class="content-excerpt">
                        The most delicious cookies you'll ever taste! With a crispy edge and chewy center, 
                        these treats have been passed down through three generations...
                    </p>
                    
                    <div class="metadata">
                        <div class="metadata-item">
                            <span class="icon icon-clock"></span>
                            <span>30 mins</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-user"></span>
                            <span>Sarah M.</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-calendar"></span>
                            <span>Jan 5, 2025</span>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button class="action-btn read-more">Read More</button>
                        <button class="action-btn favorite">♥ Favorite</button>
                        <div style="position: relative;">
                            <button class="action-btn share">Share</button>
                            <div class="share-menu">
                                <button class="share-option">Email</button>
                                <button class="share-option">Copy Link</button>
                                <button class="share-option">Print</button>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            
            <!-- Blog Post Card -->
            <article class="paper-card" style="--rotation: 1.5deg;" data-id="2">
                <div class="doodle doodle-arrow"></div>
                
                <div class="content-preview">
                    <span class="content-type">Blog Post</span>
                    <h2 class="content-title">Finding <span class="highlight">Peace</span> in Daily Chaos</h2>
                    <p class="content-excerpt">
                        Life moves fast, but sometimes we need to slow down and appreciate the small moments. 
                        Here are five simple techniques I've discovered for finding calm...
                    </p>
                    
                    <div class="metadata">
                        <div class="metadata-item">
                            <span class="icon icon-clock"></span>
                            <span>5 min read</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-user"></span>
                            <span>Alex Chen</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-calendar"></span>
                            <span>Jan 4, 2025</span>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button class="action-btn read-more">Read More</button>
                        <button class="action-btn favorite">♥ Favorite</button>
                        <div style="position: relative;">
                            <button class="action-btn share">Share</button>
                            <div class="share-menu">
                                <button class="share-option">Email</button>
                                <button class="share-option">Copy Link</button>
                                <button class="share-option">Print</button>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            
            <!-- Journal Entry Card -->
            <article class="paper-card" style="--rotation: -1deg;" data-id="3">
                <div class="paper-clip"></div>
                <div class="coffee-stain" style="top: 40px; left: 20px; width: 60px; height: 60px;"></div>
                
                <div class="content-preview">
                    <span class="content-type">Journal Entry</span>
                    <h2 class="content-title">Mountain Hike <span class="highlight">Adventure</span></h2>
                    <p class="content-excerpt">
                        Today marked my first solo hike up Mt. Wilson. The morning mist was thick, 
                        and the trail seemed to disappear into the clouds. I packed light but...
                    </p>
                    
                    <div class="metadata">
                        <div class="metadata-item">
                            <span class="icon icon-clock"></span>
                            <span>3 min read</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-user"></span>
                            <span>Jamie R.</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-calendar"></span>
                            <span>Jan 3, 2025</span>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button class="action-btn read-more">Read More</button>
                        <button class="action-btn favorite">♥ Favorite</button>
                        <div style="position: relative;">
                            <button class="action-btn share">Share</button>
                            <div class="share-menu">
                                <button class="share-option">Email</button>
                                <button class="share-option">Copy Link</button>
                                <button class="share-option">Print</button>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            
            <!-- Travel Guide Card -->
            <article class="paper-card" style="--rotation: 2deg;" data-id="4">
                <div class="doodle doodle-star" style="top: auto; bottom: 30px; right: 20px; left: auto;"></div>
                
                <div class="content-preview">
                    <span class="content-type">Travel Guide</span>
                    <h2 class="content-title">Hidden Gems of <span class="highlight">Kyoto</span></h2>
                    <p class="content-excerpt">
                        Beyond the famous temples and gardens, Kyoto holds secret spots that most tourists miss. 
                        Let me share my favorite hidden cafes and quiet paths...
                    </p>
                    
                    <div class="metadata">
                        <div class="metadata-item">
                            <span class="icon icon-clock"></span>
                            <span>8 min read</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-user"></span>
                            <span>Maya T.</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-calendar"></span>
                            <span>Jan 2, 2025</span>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button class="action-btn read-more">Read More</button>
                        <button class="action-btn favorite">♥ Favorite</button>
                        <div style="position: relative;">
                            <button class="action-btn share">Share</button>
                            <div class="share-menu">
                                <button class="share-option">Email</button>
                                <button class="share-option">Copy Link</button>
                                <button class="share-option">Print</button>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            
            <!-- DIY Tutorial Card -->
            <article class="paper-card" style="--rotation: -1.5deg;" data-id="5">
                <div class="paper-clip"></div>
                <div class="doodle doodle-arrow" style="top: 50px; left: 30px; bottom: auto; right: auto; transform: rotate(45deg);"></div>
                
                <div class="content-preview">
                    <span class="content-type">DIY Tutorial</span>
                    <h2 class="content-title">Macrame Plant <span class="highlight">Hanger</span> Guide</h2>
                    <p class="content-excerpt">
                        Transform simple rope into beautiful plant hangers! This beginner-friendly tutorial 
                        will walk you through creating your first macrame masterpiece...
                    </p>
                    
                    <div class="metadata">
                        <div class="metadata-item">
                            <span class="icon icon-clock"></span>
                            <span>45 mins</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-user"></span>
                            <span>Craft Club</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-calendar"></span>
                            <span>Jan 1, 2025</span>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button class="action-btn read-more">Read More</button>
                        <button class="action-btn favorite">♥ Favorite</button>
                        <div style="position: relative;">
                            <button class="action-btn share">Share</button>
                            <div class="share-menu">
                                <button class="share-option">Email</button>
                                <button class="share-option">Copy Link</button>
                                <button class="share-option">Print</button>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            
            <!-- Poetry Card -->
            <article class="paper-card" style="--rotation: 1deg;" data-id="6">
                <div class="coffee-stain" style="top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(30deg); opacity: 0.5;"></div>
                
                <div class="content-preview">
                    <span class="content-type">Poetry</span>
                    <h2 class="content-title">Whispers of <span class="highlight">Autumn</span> Leaves</h2>
                    <p class="content-excerpt">
                        Golden memories drift down,<br>
                        Each leaf a story untold,<br>
                        Dancing through October's crown...
                    </p>
                    
                    <div class="metadata">
                        <div class="metadata-item">
                            <span class="icon icon-clock"></span>
                            <span>2 min read</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-user"></span>
                            <span>L. Winters</span>
                        </div>
                        <div class="metadata-item">
                            <span class="icon icon-calendar"></span>
                            <span>Dec 31, 2024</span>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button class="action-btn read-more">Read More</button>
                        <button class="action-btn favorite">♥ Favorite</button>
                        <div style="position: relative;">
                            <button class="action-btn share">Share</button>
                            <div class="share-menu">
                                <button class="share-option">Email</button>
                                <button class="share-option">Copy Link</button>
                                <button class="share-option">Print</button>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
    
    <!-- Modal -->
    <div class="modal-overlay">
        <div class="modal-content">
            <button class="close-btn">✕</button>
            <div class="fold-content">
                <!-- Content will be dynamically inserted here -->
            </div>
        </div>
    </div>
    
    <script>
        // Full content for modal
        const fullContent = {
            1: {
                title: "Grandma's Secret Chocolate Chip Cookies",
                type: "Recipe",
                content: `
                    <h3>Ingredients:</h3>
                    <ul style="list-style: none; padding-left: 0;">
                        <li>✓ 2¼ cups all-purpose flour</li>
                        <li>✓ 1 tsp baking soda</li>
                        <li>✓ 1 tsp salt</li>
                        <li>✓ 1 cup butter, softened</li>
                        <li>✓ ¾ cup granulated sugar</li>
                        <li>✓ ¾ cup packed brown sugar</li>
                        <li>✓ 2 large eggs</li>
                        <li>✓ 2 tsp vanilla extract</li>
                        <li>✓ 2 cups chocolate chips</li>
                    </ul>
                    
                    <h3>Instructions:</h3>
                    <ol style="line-height: 1.8;">
                        <li>Preheat oven to 375°F (190°C)</li>
                        <li>Mix flour, baking soda and salt in small bowl</li>
                        <li>Beat butter and sugars until creamy</li>
                        <li>Add eggs and vanilla, mix well</li>
                        <li>Gradually blend in flour mixture</li>
                        <li>Stir in chocolate chips</li>
                        <li>Drop rounded tablespoons onto ungreased baking sheets</li>
                        <li>Bake 9-11 minutes until golden brown</li>
                    </ol>
                    
                    <p style="margin-top: 2rem; font-style: italic;">
                        <span class="highlight">Secret tip:</span> Chill the dough for 30 minutes before baking for extra chewy cookies!
                    </p>
                `,
                author: "Sarah M.",
                date: "Jan 5, 2025"
            },
            2: {
                title: "Finding Peace in Daily Chaos",
                type: "Blog Post",
                content: `
                    <p>Life moves at an incredible pace these days. Between work deadlines, family responsibilities, and the constant ping of notifications, finding a moment of peace can feel impossible. But I've discovered that tranquility isn't about escaping the chaos—it's about finding calm within it.</p>
                    
                    <h3>Five Simple Techniques:</h3>
                    
                    <h4>1. The Two-Minute Breathing Reset</h4>
                    <p>Whenever you feel overwhelmed, stop everything and take two minutes to focus solely on your breath. Inhale for four counts, hold for four, exhale for six. This simple practice activates your parasympathetic nervous system, naturally calming your mind.</p>
                    
                    <h4>2. Morning Intention Setting</h4>
                    <p>Before checking your phone or diving into tasks, spend five minutes setting an intention for the day. What energy do you want to bring? What's truly important today?</p>
                    
                    <h4>3. The Gratitude Pause</h4>
                    <p>Three times throughout your day, pause and identify one thing you're grateful for in that exact moment. It could be as simple as warm coffee or a kind smile from a stranger.</p>
                    
                    <h4>4. Digital Sunset</h4>
                    <p>Choose a time each evening to disconnect from all screens. Even 30 minutes of tech-free time before bed can dramatically improve your sleep and mental clarity.</p>
                    
                    <h4>5. Walking Meditation</h4>
                    <p>Transform your daily walk into a meditation. Focus on the sensation of your feet touching the ground, the rhythm of your breath, the sounds around you. No headphones, just presence.</p>
                    
                    <p style="margin-top: 2rem;">
                        Remember, <span class="highlight">peace isn't a destination—it's a practice</span>. Start small, be patient with yourself, and watch how these tiny moments of calm can transform your entire day.
                    </p>
                `,
                author: "Alex Chen",
                date: "Jan 4, 2025"
            },
            3: {
                title: "Mountain Hike Adventure",
                type: "Journal Entry",
                content: `
                    <p><em>6:00 AM - Trail Head</em></p>
                    <p>The parking lot was empty except for my car and one other—a good sign. The morning mist was so thick I could barely see twenty feet ahead. My backpack felt light with just water, snacks, and my emergency kit. This was it—my first solo hike up Mt. Wilson.</p>
                    
                    <p><em>7:30 AM - First Overlook</em></p>
                    <p>The fog began to lift as I reached the first viewpoint. Below, the valley was still shrouded in white, but above, the sky was turning a brilliant pink. I've never felt so alone yet so connected to everything around me. The silence was profound—just my breathing and the occasional bird call.</p>
                    
                    <p><em>9:00 AM - The Challenge</em></p>
                    <p>The trail got steep. Really steep. My legs burned, and I had to stop every few minutes to catch my breath. Doubt crept in. Was I prepared enough? Should I turn back? But then I remembered why I came—to prove to myself that I could do hard things alone.</p>
                    
                    <p><em>10:30 AM - Summit</em></p>
                    <p>I made it. The view from the top was... there are no words. 360 degrees of mountains, valleys, and endless sky. I sat on a rock, ate my sandwich, and cried a little. Not from sadness, but from the overwhelming realization that <span class="highlight">I am capable of so much more than I believed</span>.</p>
                    
                    <p><em>2:00 PM - Back at the Car</em></p>
                    <p>Legs shaky, spirit soaring. The descent was harder on my knees but easier on my soul. I passed several groups heading up and smiled, knowing what awaited them at the top. This won't be my last solo adventure. In fact, I think it's just the beginning.</p>
                    
                    <p style="margin-top: 2rem; text-align: center;">
                        <em>"The mountains are calling, and I must go." - John Muir</em>
                    </p>
                `,
                author: "Jamie R.",
                date: "Jan 3, 2025"
            },
            4: {
                title: "Hidden Gems of Kyoto",
                type: "Travel Guide",
                content: `
                    <p>Everyone knows about Fushimi Inari, Kinkaku-ji, and the Bamboo Grove. But after living in Kyoto for six months, I discovered the city's true magic lies in its hidden corners. Here are my favorite secret spots that most tourists never find.</p>
                    
                    <h3>Secret Cafes</h3>
                    
                    <h4>Sarasa Nishijin (さらさ西陣)</h4>
                    <p>Tucked inside a converted public bathhouse, this cafe retains the original tiles and architecture. Order their signature curry and enjoy it while sitting in what used to be the bathing area. The atmosphere is unlike anywhere else in Kyoto.</p>
                    
                    <h4>Cafe Aaliya</h4>
                    <p>Hidden on the second floor of an unmarked building near Nijo Castle. Their French toast is legendary among locals, but the real treasure is the owner's collection of vintage jazz records playing softly in the background.</p>
                    
                    <h3>Quiet Paths</h3>
                    
                    <h4>The Philosopher's Path (Early Morning)</h4>
                    <p>Yes, it's famous, but visit before 7 AM and you'll have it almost to yourself. The morning light filtering through cherry trees (even without blossoms) is magical. Stop at the small shrines along the way—each has its own character.</p>
                    
                    <h4>Yoshida Hill Trail</h4>
                    <p>Start behind Yoshida Shrine and climb the unmarked trail. In 20 minutes, you'll reach a clearing with views over the entire city. I've never seen another tourist here, only locals walking their dogs.</p>
                    
                    <h3>Hidden Temples</h3>
                    
                    <h4>Sekizo-in Temple</h4>
                    <p>No entrance fee, no crowds, just a peaceful garden and the sound of water. The priest sometimes offers informal tea ceremonies if you show genuine interest and speak a little Japanese.</p>
                    
                    <p style="margin-top: 2rem;">
                        <span class="highlight">Pro tip:</span> Get lost intentionally. My best discoveries came from taking wrong turns and following interesting alleyways. Kyoto rewards the curious wanderer.
                    </p>
                `,
                author: "Maya T.",
                date: "Jan 2, 2025"
            },
            5: {
                title: "Macrame Plant Hanger Guide",
                type: "DIY Tutorial",
                content: `
                    <p>Welcome to the wonderful world of macrame! This beginner-friendly project will have you creating beautiful plant hangers in no time. All you need is some cord and patience.</p>
                    
                    <h3>Materials Needed:</h3>
                    <ul style="list-style: none; padding-left: 0;">
                        <li>✓ 100 feet of 3mm cotton cord</li>
                        <li>✓ 1 metal ring (2-3 inches diameter)</li>
                        <li>✓ Scissors</li>
                        <li>✓ Measuring tape</li>
                        <li>✓ A plant pot (6-8 inches)</li>
                    </ul>
                    
                    <h3>Step-by-Step Instructions:</h3>
                    
                    <h4>Step 1: Cut Your Cords</h4>
                    <p>Cut 8 pieces of cord, each 12 feet long. This might seem like a lot, but trust me, you'll need it!</p>
                    
                    <h4>Step 2: Attach to Ring</h4>
                    <p>Fold each cord in half and attach to the ring using a Lark's Head knot. You'll have 16 strands hanging down.</p>
                    
                    <h4>Step 3: Create the First Row</h4>
                    <p>Divide strands into 4 groups of 4. Tie a Square knot with each group about 5 inches below the ring.</p>
                    
                    <h4>Step 4: Alternate Pattern</h4>
                    <p>Take 2 strands from one group and 2 from the adjacent group. Tie Square knots 3 inches below the first row. This creates the net pattern.</p>
                    
                    <h4>Step 5: Continue the Pattern</h4>
                    <p>Repeat the alternating pattern for 3-4 more rows, depending on your pot size.</p>
                    
                    <h4>Step 6: Create the Basket</h4>
                    <p>Gather all strands and tie a large knot at the bottom, leaving about 6 inches of fringe.</p>
                    
                    <h4>Step 7: Finishing Touches</h4>
                    <p>Trim the fringe evenly and unravel for a boho look, or leave as is for a cleaner appearance.</p>
                    
                    <p style="margin-top: 2rem;">
                        <span class="highlight">Tips for Success:</span>
                        <br>• Keep your knots consistent in size
                        <br>• Work on a flat surface to maintain even spacing
                        <br>• Don't pull too tight—macrame should have a relaxed feel
                        <br>• Practice knots on scrap cord first
                    </p>
                `,
                author: "Craft Club",
                date: "Jan 1, 2025"
            },
            6: {
                title: "Whispers of Autumn Leaves",
                type: "Poetry",
                content: `
                    <div style="text-align: center; line-height: 2;">
                        <p>
                            Golden memories drift down,<br>
                            Each leaf a story untold,<br>
                            Dancing through October's crown,<br>
                            Treasures more precious than gold.
                        </p>
                        
                        <p>
                            The maple speaks in crimson tongue,<br>
                            Of summers past and winters near,<br>
                            While oak trees hum the songs they've sung,<br>
                            Through every joy and every tear.
                        </p>
                        
                        <p>
                            I catch a leaf mid-flight today,<br>
                            Its veins like paths I've yet to take,<br>
                            And wonder at the gentle way,<br>
                            That letting go is no mistake.
                        </p>
                        
                        <p>
                            For trees know secrets we forget,<br>
                            That endings birth the brightest hues,<br>
                            And though the branches now seem wet,<br>
                            With morning's melancholy dews...
                        </p>
                        
                        <p>
                            Come spring, they'll dress in green once more,<br>
                            Their naked truth transformed to grace,<br>
                            <span class="highlight">What seemed like loss was just the door,<br>
                            To beauty's ever-changing face.</span>
                        </p>
                        
                        <p style="margin-top: 3rem; font-style: italic;">
                            - L. Winters<br>
                            <small>Written beneath the old maple tree<br>
                            on Riverside Path</small>
                        </p>
                    </div>
                `,
                author: "L. Winters",
                date: "Dec 31, 2024"
            }
        };
        
        // Modal functionality
        const modalOverlay = document.querySelector('.modal-overlay');
        const modalContent = document.querySelector('.fold-content');
        const closeBtn = document.querySelector('.close-btn');
        
        // Handle read more clicks
        document.querySelectorAll('.read-more').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.paper-card');
                const id = card.dataset.id;
                const content = fullContent[id];
                
                modalContent.innerHTML = `
                    <span class="content-type">${content.type}</span>
                    <h2 class="content-title" style="font-size: 2.2rem; margin-bottom: 1.5rem;">${content.title}</h2>
                    <div style="margin-bottom: 1.5rem; color: #6a5244; font-size: 0.9rem;">
                        By ${content.author} • ${content.date}
                    </div>
                    ${content.content}
                `;
                
                modalOverlay.classList.add('active');
            });
        });
        
        // Handle favorite clicks
        document.querySelectorAll('.favorite').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                btn.classList.toggle('liked');
                if (btn.classList.contains('liked')) {
                    btn.innerHTML = '♥ Favorited';
                } else {
                    btn.innerHTML = '♥ Favorite';
                }
            });
        });
        
        // Handle share menu
        document.querySelectorAll('.share').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const menu = btn.nextElementSibling;
                
                // Close all other share menus
                document.querySelectorAll('.share-menu').forEach(m => {
                    if (m !== menu) m.classList.remove('active');
                });
                
                menu.classList.toggle('active');
            });
        });
        
        // Handle share options
        document.querySelectorAll('.share-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = option.textContent;
                const card = option.closest('.paper-card');
                const title = card.querySelector('.content-title').textContent;
                
                switch(action) {
                    case 'Email':
                        window.location.href = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent('Check out this great content!')}`;
                        break;
                    case 'Copy Link':
                        navigator.clipboard.writeText(window.location.href);
                        option.textContent = 'Copied!';
                        setTimeout(() => {
                            option.textContent = 'Copy Link';
                        }, 2000);
                        break;
                    case 'Print':
                        window.print();
                        break;
                }
                
                option.closest('.share-menu').classList.remove('active');
            });
        });
        
        // Close modal
        closeBtn.addEventListener('click', () => {
            modalOverlay.classList.remove('active');
        });
        
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                modalOverlay.classList.remove('active');
            }
        });
        
        // Close share menus when clicking outside
        document.addEventListener('click', () => {
            document.querySelectorAll('.share-menu').forEach(menu => {
                menu.classList.remove('active');
            });
        });
        
        // Add paper fold animation on card click
        document.querySelectorAll('.paper-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (!e.target.closest('.actions')) {
                    this.style.transform = 'rotateX(15deg) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                }
            });
        });
        
        // Add subtle hover animations
        document.querySelectorAll('.paper-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                const randomX = (Math.random() - 0.5) * 4;
                const randomY = (Math.random() - 0.5) * 4;
                this.style.setProperty('--hover-x', `${randomX}px`);
                this.style.setProperty('--hover-y', `${randomY}px`);
            });
        });
    </script>
</body>
</html>