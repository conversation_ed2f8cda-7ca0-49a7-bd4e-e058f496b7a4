<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Arcade Analytics - Dashboard Media Player Hybrid</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0a0a0a;
            font-family: 'Press Start 2P', cursive;
            color: #00ffff;
            overflow: hidden;
            position: relative;
        }
        
        /* CRT Effect */
        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                rgba(0, 0, 0, 0.15),
                rgba(0, 0, 0, 0.15) 1px,
                transparent 1px,
                transparent 2px
            );
            pointer-events: none;
            z-index: 1000;
        }
        
        .arcade-container {
            width: 100vw;
            height: 100vh;
            background: radial-gradient(ellipse at center, #1a0033 0%, #000000 100%);
            padding: 20px;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        /* Neon Grid Background */
        .grid-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(255, 0, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 0, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 10s linear infinite;
        }
        
        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }
        
        /* Header */
        .arcade-header {
            text-align: center;
            padding: 20px;
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 32px;
            text-shadow: 
                0 0 20px #ff00ff,
                0 0 40px #00ffff,
                0 0 60px #ff00ff;
            animation: neonFlicker 2s ease-in-out infinite;
            z-index: 10;
        }
        
        @keyframes neonFlicker {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        /* Main Dashboard */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            flex: 1;
            z-index: 10;
        }
        
        /* Widget Base */
        .widget {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 0 20px #00ffff,
                inset 0 0 20px rgba(0, 255, 255, 0.1);
        }
        
        .widget::before {
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff00ff, #00ffff, #ff00ff);
            border-radius: 10px;
            opacity: 0.5;
            z-index: -1;
            animation: borderRotate 3s linear infinite;
        }
        
        @keyframes borderRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Leaderboard Widget */
        .leaderboard {
            grid-row: span 2;
        }
        
        .leaderboard-title {
            font-size: 16px;
            color: #ffff00;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 0 10px #ffff00;
        }
        
        .player-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 10px 0;
            background: rgba(255, 0, 255, 0.1);
            border: 1px solid #ff00ff;
            position: relative;
            overflow: hidden;
        }
        
        .player-entry::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: var(--score-width);
            background: linear-gradient(90deg, #ff00ff, #00ffff);
            opacity: 0.3;
            z-index: -1;
        }
        
        .player-rank {
            font-size: 20px;
            color: #ffff00;
            text-shadow: 0 0 10px #ffff00;
        }
        
        .player-name {
            font-size: 12px;
            flex: 1;
            margin: 0 20px;
        }
        
        .player-score {
            font-size: 14px;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }
        
        /* Stats Widget */
        .stats-widget {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .stat-item {
            background: rgba(0, 255, 255, 0.1);
            padding: 15px;
            border: 1px solid #00ffff;
            border-radius: 5px;
            text-align: center;
        }
        
        .stat-label {
            font-size: 10px;
            color: #ff00ff;
            margin-bottom: 10px;
        }
        
        .stat-value {
            font-size: 24px;
            color: #00ff00;
            text-shadow: 0 0 20px #00ff00;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* Audio Visualizer */
        .audio-visualizer {
            grid-column: span 2;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .visualizer-bars {
            display: flex;
            gap: 5px;
            height: 200px;
            align-items: flex-end;
        }
        
        .bar {
            width: 20px;
            background: linear-gradient(to top, #ff00ff, #00ffff);
            box-shadow: 0 0 10px currentColor;
            transition: height 0.1s ease;
            border-radius: 5px 5px 0 0;
        }
        
        /* Media Controls */
        .media-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border: 2px solid #ff00ff;
            border-radius: 10px;
        }
        
        .control-btn {
            width: 40px;
            height: 40px;
            background: transparent;
            border: 2px solid #00ffff;
            color: #00ffff;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
            font-family: 'Press Start 2P', cursive;
        }
        
        .control-btn:hover {
            background: #00ffff;
            color: #000;
            box-shadow: 0 0 20px #00ffff;
            transform: scale(1.1);
        }
        
        /* Achievement Popup */
        .achievement {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            background: linear-gradient(45deg, #ff00ff, #00ffff);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            z-index: 1001;
            box-shadow: 0 0 50px #ff00ff;
            animation: achievementPop 3s ease-out;
        }
        
        @keyframes achievementPop {
            0% { transform: translate(-50%, -50%) scale(0) rotate(0deg); }
            50% { transform: translate(-50%, -50%) scale(1.2) rotate(360deg); }
            100% { transform: translate(-50%, -50%) scale(0) rotate(720deg); }
        }
        
        .achievement-text {
            font-size: 24px;
            color: #000;
            text-shadow: 2px 2px 0 #fff;
        }
        
        /* Pixel Stars */
        .stars {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .star {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #fff;
            box-shadow: 0 0 10px #fff;
            animation: twinkle 3s ease-in-out infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }
        
        /* Score Counter */
        .score-counter {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            color: #00ff00;
            text-shadow: 0 0 20px #00ff00;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="arcade-container">
        <div class="grid-bg"></div>
        <div class="stars"></div>
        
        <h1 class="arcade-header">ARCADE ANALYTICS</h1>
        
        <div class="score-counter">SCORE: <span id="totalScore">0</span></div>
        
        <div class="dashboard-grid">
            <!-- Leaderboard Widget -->
            <div class="widget leaderboard">
                <h2 class="leaderboard-title">HIGH SCORES</h2>
                <div id="leaderboardContent"></div>
            </div>
            
            <!-- Stats Widget -->
            <div class="widget stats-widget">
                <div class="stat-item">
                    <div class="stat-label">GAMES PLAYED</div>
                    <div class="stat-value" id="gamesPlayed">0</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">WIN RATE</div>
                    <div class="stat-value" id="winRate">0%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">COMBO MAX</div>
                    <div class="stat-value" id="comboMax">0</div>
                </div>
            </div>
            
            <!-- Audio Visualizer -->
            <div class="widget audio-visualizer">
                <div class="visualizer-bars" id="visualizer"></div>
                <div class="media-controls">
                    <button class="control-btn" id="playBtn">▶</button>
                    <button class="control-btn" id="pauseBtn">❚❚</button>
                    <button class="control-btn" id="stopBtn">◼</button>
                </div>
            </div>
        </div>
        
        <div class="achievement" id="achievement">
            <div class="achievement-text">NEW HIGH SCORE!</div>
        </div>
    </div>
    
    <script>
        // Initialize leaderboard data
        const players = [
            { name: 'PLAYER_1', score: 999999, rank: 1 },
            { name: 'ARCADE_MASTER', score: 875420, rank: 2 },
            { name: 'NEON_KNIGHT', score: 654321, rank: 3 },
            { name: 'PIXEL_HERO', score: 543210, rank: 4 },
            { name: 'RETRO_KING', score: 432100, rank: 5 }
        ];
        
        // Create leaderboard
        function createLeaderboard() {
            const leaderboardContent = document.getElementById('leaderboardContent');
            leaderboardContent.innerHTML = '';
            
            players.forEach((player, index) => {
                const entry = document.createElement('div');
                entry.className = 'player-entry';
                entry.style.setProperty('--score-width', `${(player.score / 999999) * 100}%`);
                entry.innerHTML = `
                    <div class="player-rank">#${player.rank}</div>
                    <div class="player-name">${player.name}</div>
                    <div class="player-score">${player.score.toLocaleString()}</div>
                `;
                leaderboardContent.appendChild(entry);
            });
        }
        
        // Create audio visualizer
        function createVisualizer() {
            const visualizer = document.getElementById('visualizer');
            const barCount = 20;
            
            for (let i = 0; i < barCount; i++) {
                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.height = '10px';
                visualizer.appendChild(bar);
            }
        }
        
        // Animate visualizer
        let isPlaying = false;
        function animateVisualizer() {
            if (!isPlaying) return;
            
            const bars = document.querySelectorAll('.bar');
            bars.forEach(bar => {
                const height = Math.random() * 180 + 20;
                bar.style.height = `${height}px`;
            });
            
            requestAnimationFrame(animateVisualizer);
        }
        
        // Media controls
        document.getElementById('playBtn').addEventListener('click', () => {
            isPlaying = true;
            animateVisualizer();
            updateStats();
        });
        
        document.getElementById('pauseBtn').addEventListener('click', () => {
            isPlaying = false;
        });
        
        document.getElementById('stopBtn').addEventListener('click', () => {
            isPlaying = false;
            document.querySelectorAll('.bar').forEach(bar => {
                bar.style.height = '10px';
            });
        });
        
        // Update stats with animation
        function updateStats() {
            let games = 0;
            let wins = 0;
            let combo = 0;
            let score = 0;
            
            const gamesInterval = setInterval(() => {
                games += Math.floor(Math.random() * 10) + 1;
                document.getElementById('gamesPlayed').textContent = games;
                
                if (games >= 1337) {
                    clearInterval(gamesInterval);
                    document.getElementById('gamesPlayed').textContent = 1337;
                }
            }, 100);
            
            const winInterval = setInterval(() => {
                wins += Math.floor(Math.random() * 2) + 1;
                const rate = Math.min(Math.floor((wins / 100) * 100), 87);
                document.getElementById('winRate').textContent = rate + '%';
                
                if (rate >= 87) {
                    clearInterval(winInterval);
                }
            }, 150);
            
            const comboInterval = setInterval(() => {
                combo += Math.floor(Math.random() * 50) + 10;
                document.getElementById('comboMax').textContent = combo + 'x';
                
                if (combo >= 999) {
                    clearInterval(comboInterval);
                    document.getElementById('comboMax').textContent = '999x';
                    showAchievement();
                }
            }, 200);
            
            const scoreInterval = setInterval(() => {
                score += Math.floor(Math.random() * 10000) + 1000;
                document.getElementById('totalScore').textContent = score.toLocaleString();
                
                if (score >= 1000000) {
                    clearInterval(scoreInterval);
                }
            }, 50);
        }
        
        // Show achievement
        function showAchievement() {
            const achievement = document.getElementById('achievement');
            achievement.style.animation = 'achievementPop 3s ease-out';
            setTimeout(() => {
                achievement.style.animation = '';
            }, 3000);
        }
        
        // Create pixel stars
        function createStars() {
            const starsContainer = document.querySelector('.stars');
            const starCount = 50;
            
            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                starsContainer.appendChild(star);
            }
        }
        
        // Initialize
        createLeaderboard();
        createVisualizer();
        createStars();
        
        // Simulate random score updates
        setInterval(() => {
            if (isPlaying && Math.random() > 0.7) {
                const currentScore = parseInt(document.getElementById('totalScore').textContent.replace(/,/g, ''));
                const newScore = currentScore + Math.floor(Math.random() * 1000) + 100;
                document.getElementById('totalScore').textContent = newScore.toLocaleString();
            }
        }, 1000);
    </script>
</body>
</html>