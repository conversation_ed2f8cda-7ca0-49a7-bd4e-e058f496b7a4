<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brutalist Navigation Center - Hybrid UI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #1a1a1a;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
            background-image: 
                repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,.03) 2px, rgba(255,255,255,.03) 4px),
                repeating-linear-gradient(-45deg, transparent, transparent 2px, rgba(255,255,255,.02) 2px, rgba(255,255,255,.02) 4px);
        }

        .brutalist-nav-center {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #2a2a2a;
            position: relative;
        }

        /* Top Monolith - Search and Quick Actions */
        .nav-monolith {
            background: linear-gradient(180deg, #3a3a3a 0%, #2a2a2a 100%);
            border-bottom: 8px solid #1a1a1a;
            box-shadow: 
                0 10px 30px rgba(0,0,0,0.8),
                inset 0 -2px 10px rgba(0,0,0,0.5);
            position: relative;
            z-index: 1000;
        }

        .search-fortress {
            padding: 30px;
            display: flex;
            gap: 20px;
            align-items: center;
            background: 
                repeating-linear-gradient(90deg, transparent, transparent 100px, rgba(255,255,255,.02) 100px, rgba(255,255,255,.02) 101px),
                repeating-linear-gradient(0deg, transparent, transparent 100px, rgba(255,255,255,.02) 100px, rgba(255,255,255,.02) 101px);
        }

        .search-block {
            flex: 1;
            position: relative;
            background: #1a1a1a;
            border: 4px solid #3a3a3a;
            box-shadow: 
                inset 0 0 20px rgba(0,0,0,0.8),
                0 5px 15px rgba(0,0,0,0.6);
            transform: skewX(-2deg);
        }

        .search-block input {
            width: 100%;
            padding: 20px 60px 20px 25px;
            background: transparent;
            border: none;
            color: #fff;
            font-size: 18px;
            font-weight: 700;
            letter-spacing: 1px;
            transform: skewX(2deg);
        }

        .search-block::before {
            content: '⌕';
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%) skewX(2deg);
            font-size: 24px;
            color: #666;
        }

        /* Quick Actions - Concrete Blocks */
        .quick-actions {
            display: flex;
            gap: 15px;
        }

        .action-block {
            width: 60px;
            height: 60px;
            background: #2a2a2a;
            border: 3px solid #3a3a3a;
            cursor: pointer;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 4px 10px rgba(0,0,0,0.6),
                inset 0 0 10px rgba(0,0,0,0.4);
            transform: skewX(-5deg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .action-block:hover {
            transform: skewX(-5deg) translateY(-2px);
            box-shadow: 
                0 6px 20px rgba(0,0,0,0.8),
                inset 0 0 15px rgba(255,255,255,0.1);
            border-color: #4a4a4a;
        }

        .action-block:active {
            transform: skewX(-5deg) translateY(1px);
            box-shadow: 
                0 2px 5px rgba(0,0,0,0.8),
                inset 0 0 20px rgba(0,0,0,0.8);
        }

        /* Breadcrumb Fortress */
        .breadcrumb-fortress {
            background: #2a2a2a;
            padding: 15px 30px;
            border-bottom: 4px solid #1a1a1a;
            box-shadow: 0 4px 10px rgba(0,0,0,0.6);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .breadcrumb-path {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .crumb {
            padding: 8px 16px;
            background: #1a1a1a;
            border: 2px solid #3a3a3a;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 600;
            letter-spacing: 1px;
            clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%, 15px 50%);
            margin-left: -10px;
            box-shadow: 2px 2px 8px rgba(0,0,0,0.4);
        }

        .crumb:first-child {
            margin-left: 0;
            clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%);
        }

        .crumb:hover {
            background: #2a2a2a;
            transform: translateX(2px);
            box-shadow: 4px 4px 12px rgba(0,0,0,0.6);
        }

        .crumb.active {
            background: #3a3a3a;
            color: #fff;
            border-color: #4a4a4a;
        }

        /* Tab Citadel */
        .tab-citadel {
            background: #1a1a1a;
            border-bottom: 6px solid #0a0a0a;
            box-shadow: 0 4px 20px rgba(0,0,0,0.8);
            overflow-x: auto;
            scrollbar-width: none;
        }

        .tab-citadel::-webkit-scrollbar {
            display: none;
        }

        .tab-row {
            display: flex;
            padding: 0 20px;
            min-width: max-content;
        }

        .tab-pillar {
            padding: 20px 30px;
            background: #2a2a2a;
            border: 3px solid #3a3a3a;
            border-bottom: none;
            margin-right: 10px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s;
            font-weight: 700;
            letter-spacing: 1px;
            text-transform: uppercase;
            clip-path: polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0 100%);
            box-shadow: 
                0 -4px 10px rgba(0,0,0,0.4),
                inset 0 0 20px rgba(0,0,0,0.3);
        }

        .tab-pillar:hover {
            background: #3a3a3a;
            transform: translateY(-3px);
            box-shadow: 
                0 -6px 20px rgba(0,0,0,0.6),
                inset 0 0 25px rgba(255,255,255,0.05);
        }

        .tab-pillar.active {
            background: #4a4a4a;
            color: #fff;
            transform: translateY(-5px);
            border-color: #5a5a5a;
            box-shadow: 
                0 -8px 30px rgba(0,0,0,0.8),
                inset 0 0 30px rgba(255,255,255,0.1);
        }

        .tab-pillar .tab-close {
            margin-left: 15px;
            font-size: 18px;
            color: #666;
            transition: color 0.3s;
        }

        .tab-pillar .tab-close:hover {
            color: #ff4444;
        }

        /* Content Vault */
        .content-vault {
            flex: 1;
            background: #2a2a2a;
            padding: 30px;
            overflow-y: auto;
            position: relative;
            background-image: 
                repeating-linear-gradient(0deg, transparent, transparent 200px, rgba(255,255,255,.02) 200px, rgba(255,255,255,.02) 201px),
                repeating-linear-gradient(90deg, transparent, transparent 200px, rgba(255,255,255,.02) 200px, rgba(255,255,255,.02) 201px);
        }

        .content-panel {
            display: none;
            animation: vaultOpen 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .content-panel.active {
            display: block;
        }

        @keyframes vaultOpen {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* State Memory Panel */
        .memory-vault {
            position: fixed;
            right: -300px;
            top: 0;
            width: 300px;
            height: 100vh;
            background: #1a1a1a;
            border-left: 6px solid #0a0a0a;
            box-shadow: -10px 0 30px rgba(0,0,0,0.8);
            transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2000;
            overflow-y: auto;
        }

        .memory-vault.open {
            right: 0;
        }

        .memory-toggle {
            position: absolute;
            left: -40px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 80px;
            background: #1a1a1a;
            border: 3px solid #0a0a0a;
            border-right: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: -4px 0 10px rgba(0,0,0,0.6);
        }

        .memory-content {
            padding: 30px 20px;
        }

        .memory-block {
            background: #2a2a2a;
            border: 3px solid #3a3a3a;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 
                0 4px 10px rgba(0,0,0,0.4),
                inset 0 0 15px rgba(0,0,0,0.3);
        }

        .memory-block h4 {
            color: #888;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 10px;
        }

        .memory-item {
            padding: 8px 0;
            border-bottom: 1px solid #3a3a3a;
            cursor: pointer;
            transition: all 0.3s;
        }

        .memory-item:hover {
            padding-left: 10px;
            color: #fff;
        }

        /* Utility Classes */
        .concrete-text {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        .power-indicator {
            width: 8px;
            height: 8px;
            background: #44ff44;
            border-radius: 50%;
            box-shadow: 0 0 10px #44ff44;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Demo Content */
        .demo-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .content-block {
            background: #1a1a1a;
            border: 4px solid #3a3a3a;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 
                0 8px 20px rgba(0,0,0,0.6),
                inset 0 0 30px rgba(0,0,0,0.4);
        }

        .content-block h2 {
            font-size: 28px;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #fff;
        }

        .content-block p {
            line-height: 1.8;
            color: #aaa;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="brutalist-nav-center">
        <!-- Top Monolith -->
        <div class="nav-monolith">
            <!-- Search Fortress -->
            <div class="search-fortress">
                <div class="search-block">
                    <input type="text" placeholder="SEARCH COMMAND CENTER..." class="concrete-text">
                </div>
                <div class="quick-actions">
                    <div class="action-block" title="Dashboard">📊</div>
                    <div class="action-block" title="Messages">✉️</div>
                    <div class="action-block" title="Settings">⚙️</div>
                    <div class="action-block" title="Profile">👤</div>
                </div>
            </div>

            <!-- Breadcrumb Fortress -->
            <div class="breadcrumb-fortress">
                <div class="breadcrumb-path">
                    <div class="crumb" onclick="navigateToCrumb('home')">HOME</div>
                    <div class="crumb" onclick="navigateToCrumb('projects')">PROJECTS</div>
                    <div class="crumb" onclick="navigateToCrumb('brutalism')">BRUTALISM</div>
                    <div class="crumb active" onclick="navigateToCrumb('navigation')">NAVIGATION</div>
                </div>
                <div class="power-indicator"></div>
            </div>

            <!-- Tab Citadel -->
            <div class="tab-citadel">
                <div class="tab-row" id="tabRow">
                    <div class="tab-pillar active" data-tab="overview">
                        OVERVIEW
                        <span class="tab-close" onclick="closeTab(event, 'overview')">×</span>
                    </div>
                    <div class="tab-pillar" data-tab="architecture">
                        ARCHITECTURE
                        <span class="tab-close" onclick="closeTab(event, 'architecture')">×</span>
                    </div>
                    <div class="tab-pillar" data-tab="components">
                        COMPONENTS
                        <span class="tab-close" onclick="closeTab(event, 'components')">×</span>
                    </div>
                    <div class="tab-pillar" data-tab="documentation">
                        DOCUMENTATION
                        <span class="tab-close" onclick="closeTab(event, 'documentation')">×</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Vault -->
        <div class="content-vault">
            <div class="content-panel active" id="overview">
                <div class="demo-content">
                    <div class="content-block">
                        <h2 class="concrete-text">BRUTALIST NAVIGATION CENTER</h2>
                        <p>Welcome to the monolithic navigation system. This imposing structure combines the raw power of brutalist architecture with advanced navigation capabilities.</p>
                        <p>Each element has been forged from digital concrete, creating an interface that commands attention and respect. Navigate through the fortress of information with confidence.</p>
                    </div>
                    <div class="content-block">
                        <h2 class="concrete-text">SYSTEM STATUS</h2>
                        <p>All navigation systems operational. Memory vault synchronized. State persistence active.</p>
                        <p>Current session: 47 minutes. Navigation history: 23 locations. Quick actions executed: 15.</p>
                    </div>
                </div>
            </div>

            <div class="content-panel" id="architecture">
                <div class="demo-content">
                    <div class="content-block">
                        <h2 class="concrete-text">ARCHITECTURAL FOUNDATION</h2>
                        <p>The navigation center is built on solid brutalist principles. Heavy forms, raw textures, and imposing geometry create a navigation experience unlike any other.</p>
                        <p>Each component serves a vital purpose in the overall structure, from the search fortress at the summit to the content vault below.</p>
                    </div>
                </div>
            </div>

            <div class="content-panel" id="components">
                <div class="demo-content">
                    <div class="content-block">
                        <h2 class="concrete-text">COMPONENT MANIFEST</h2>
                        <p>Search Fortress: Central command for system-wide search operations.</p>
                        <p>Quick Actions: Rapid deployment blocks for common operations.</p>
                        <p>Breadcrumb Path: Historical navigation tracking with angular precision.</p>
                        <p>Tab Citadel: Multi-dimensional content management system.</p>
                        <p>Memory Vault: State persistence and navigation history archive.</p>
                    </div>
                </div>
            </div>

            <div class="content-panel" id="documentation">
                <div class="demo-content">
                    <div class="content-block">
                        <h2 class="concrete-text">OPERATIONAL MANUAL</h2>
                        <p>To navigate this brutalist interface, understand that each element has weight and purpose. The angular forms and heavy shadows are not merely aesthetic - they guide your interaction.</p>
                        <p>Use the memory vault to access your navigation history. Quick actions provide immediate access to critical functions. The tab system allows parallel exploration of multiple data streams.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Memory Vault -->
        <div class="memory-vault" id="memoryVault">
            <div class="memory-toggle" onclick="toggleMemoryVault()">
                <span id="memoryToggleIcon">◀</span>
            </div>
            <div class="memory-content">
                <div class="memory-block">
                    <h4>RECENT LOCATIONS</h4>
                    <div class="memory-item">Overview / System Status</div>
                    <div class="memory-item">Projects / Alpha Complex</div>
                    <div class="memory-item">Documentation / API Reference</div>
                    <div class="memory-item">Settings / Security</div>
                </div>
                <div class="memory-block">
                    <h4>SAVED SEARCHES</h4>
                    <div class="memory-item">brutalist components</div>
                    <div class="memory-item">navigation patterns</div>
                    <div class="memory-item">concrete textures</div>
                </div>
                <div class="memory-block">
                    <h4>QUICK ACCESS</h4>
                    <div class="memory-item">Dashboard Analytics</div>
                    <div class="memory-item">User Management</div>
                    <div class="memory-item">System Logs</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation State Management
        const navigationState = {
            currentTab: 'overview',
            breadcrumbs: ['home', 'projects', 'brutalism', 'navigation'],
            searchHistory: [],
            tabHistory: ['overview'],
            memoryVaultOpen: false
        };

        // Tab Management
        document.querySelectorAll('.tab-pillar').forEach(tab => {
            tab.addEventListener('click', function(e) {
                if (e.target.classList.contains('tab-close')) return;
                
                const tabName = this.dataset.tab;
                switchTab(tabName);
            });
        });

        function switchTab(tabName) {
            // Update tabs
            document.querySelectorAll('.tab-pillar').forEach(t => t.classList.remove('active'));
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            
            // Update content
            document.querySelectorAll('.content-panel').forEach(p => p.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            
            // Update state
            navigationState.currentTab = tabName;
            navigationState.tabHistory.push(tabName);
            saveState();
        }

        function closeTab(event, tabName) {
            event.stopPropagation();
            const tab = document.querySelector(`[data-tab="${tabName}"]`);
            const wasActive = tab.classList.contains('active');
            
            tab.style.transform = 'translateY(-100px)';
            tab.style.opacity = '0';
            
            setTimeout(() => {
                tab.remove();
                if (wasActive && navigationState.tabHistory.length > 1) {
                    const previousTab = navigationState.tabHistory[navigationState.tabHistory.length - 2];
                    switchTab(previousTab);
                }
            }, 300);
        }

        // Breadcrumb Navigation
        function navigateToCrumb(crumbName) {
            const index = navigationState.breadcrumbs.indexOf(crumbName);
            if (index !== -1) {
                navigationState.breadcrumbs = navigationState.breadcrumbs.slice(0, index + 1);
                updateBreadcrumbs();
                saveState();
            }
        }

        function updateBreadcrumbs() {
            const container = document.querySelector('.breadcrumb-path');
            container.innerHTML = navigationState.breadcrumbs.map((crumb, index) => {
                const isActive = index === navigationState.breadcrumbs.length - 1;
                return `<div class="crumb ${isActive ? 'active' : ''}" onclick="navigateToCrumb('${crumb}')">${crumb.toUpperCase()}</div>`;
            }).join('');
        }

        // Memory Vault
        function toggleMemoryVault() {
            navigationState.memoryVaultOpen = !navigationState.memoryVaultOpen;
            const vault = document.getElementById('memoryVault');
            const icon = document.getElementById('memoryToggleIcon');
            
            if (navigationState.memoryVaultOpen) {
                vault.classList.add('open');
                icon.textContent = '▶';
            } else {
                vault.classList.remove('open');
                icon.textContent = '◀';
            }
            
            saveState();
        }

        // Search Functionality
        const searchInput = document.querySelector('.search-block input');
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    navigationState.searchHistory.push(query);
                    performSearch(query);
                    this.value = '';
                    saveState();
                }
            }
        });

        function performSearch(query) {
            console.log('Searching for:', query);
            // Add visual feedback
            const searchBlock = document.querySelector('.search-block');
            searchBlock.style.boxShadow = 'inset 0 0 30px rgba(68, 255, 68, 0.3), 0 5px 15px rgba(0,0,0,0.6)';
            setTimeout(() => {
                searchBlock.style.boxShadow = 'inset 0 0 20px rgba(0,0,0,0.8), 0 5px 15px rgba(0,0,0,0.6)';
            }, 500);
        }

        // Quick Actions
        document.querySelectorAll('.action-block').forEach(action => {
            action.addEventListener('click', function() {
                const title = this.getAttribute('title');
                console.log('Quick action:', title);
                
                // Visual feedback
                this.style.transform = 'skewX(-5deg) scale(0.9)';
                setTimeout(() => {
                    this.style.transform = 'skewX(-5deg)';
                }, 200);
            });
        });

        // State Persistence
        function saveState() {
            localStorage.setItem('brutalNavState', JSON.stringify(navigationState));
        }

        function loadState() {
            const saved = localStorage.getItem('brutalNavState');
            if (saved) {
                const state = JSON.parse(saved);
                Object.assign(navigationState, state);
                
                // Restore UI state
                if (state.currentTab) {
                    switchTab(state.currentTab);
                }
                if (state.memoryVaultOpen) {
                    toggleMemoryVault();
                }
                updateBreadcrumbs();
            }
        }

        // Initialize
        loadState();

        // Add some interactive effects
        document.addEventListener('mousemove', function(e) {
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;
            
            document.querySelector('.content-vault').style.backgroundPosition = 
                `${x * 50}px ${y * 50}px`;
        });
    </script>
</body>
</html>