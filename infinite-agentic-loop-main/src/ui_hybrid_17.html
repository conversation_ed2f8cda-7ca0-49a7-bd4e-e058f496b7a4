<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neo-Memphis Smart Content Hub</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Righteous:wght@400&family=Poppins:wght@300;600;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --hot-pink: #FF1493;
            --electric-blue: #00BFFF;
            --neon-yellow: #FFFF00;
            --lime-green: #32CD32;
            --magenta: #FF00FF;
            --orange: #FF4500;
            --dark-bg: #1a1a1a;
            --light-text: #fff;
            --shadow: rgba(255, 20, 147, 0.4);
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(45deg, var(--dark-bg), #2a2a2a);
            color: var(--light-text);
            overflow-x: hidden;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, var(--hot-pink) 0%, transparent 30%),
                radial-gradient(circle at 80% 70%, var(--electric-blue) 0%, transparent 30%),
                radial-gradient(circle at 60% 40%, var(--neon-yellow) 0%, transparent 20%);
            opacity: 0.1;
            z-index: -1;
            animation: geometricFloat 20s infinite ease-in-out;
        }
        
        @keyframes geometricFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        main {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            font-family: 'Righteous', cursive;
            font-size: clamp(2rem, 5vw, 3.5rem);
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(45deg, var(--hot-pink), var(--electric-blue), var(--neon-yellow));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            animation: titlePulse 3s infinite ease-in-out;
        }
        
        @keyframes titlePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        
        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 8px;
            background: var(--lime-green);
            border-radius: 10px;
            clip-path: polygon(0 0, 20% 100%, 40% 0, 60% 100%, 80% 0, 100% 100%, 100% 0);
        }
        
        .hybrid-component {
            background: rgba(255, 255, 255, 0.05);
            border: 3px solid var(--hot-pink);
            border-radius: 25px;
            padding: 30px;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 20px 40px var(--shadow),
                inset 0 0 30px rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }
        
        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                repeating-linear-gradient(45deg, transparent, transparent 10px, var(--electric-blue) 10px, var(--electric-blue) 12px),
                repeating-linear-gradient(-45deg, transparent, transparent 15px, var(--neon-yellow) 15px, var(--neon-yellow) 17px);
            opacity: 0.1;
            animation: memphisPattern 15s linear infinite;
            z-index: -1;
        }
        
        @keyframes memphisPattern {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .content-discovery {
            background: linear-gradient(135deg, rgba(255, 20, 147, 0.2), rgba(0, 191, 255, 0.2));
            border: 2px solid var(--electric-blue);
            border-radius: 20px;
            padding: 25px;
            position: relative;
            overflow: hidden;
        }
        
        .content-discovery::before {
            content: '◆◇◆';
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 1.5rem;
            color: var(--neon-yellow);
            animation: symbolBounce 2s infinite ease-in-out;
        }
        
        @keyframes symbolBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        .discovery-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .discovery-icon {
            width: 50px;
            height: 50px;
            background: var(--hot-pink);
            border-radius: 50% 20% 50% 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            animation: iconSpin 4s infinite ease-in-out;
        }
        
        @keyframes iconSpin {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }
        
        .discovery-title {
            font-family: 'Righteous', cursive;
            font-size: 1.5rem;
            color: var(--neon-yellow);
        }
        
        .content-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }
        
        .content-item {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid var(--lime-green);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
        }
        
        .content-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 0, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .content-item:hover::before {
            left: 100%;
        }
        
        .content-item:hover {
            transform: translateY(-10px) rotate(2deg);
            border-color: var(--hot-pink);
            box-shadow: 0 15px 30px rgba(255, 20, 147, 0.3);
        }
        
        .item-type {
            display: inline-block;
            padding: 5px 12px;
            background: var(--magenta);
            color: white;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 10px;
            clip-path: polygon(0 0, 90% 0, 100% 100%, 10% 100%);
        }
        
        .item-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--light-text);
        }
        
        .item-meta {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 15px;
        }
        
        .item-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .bookmark-btn {
            background: var(--electric-blue);
            color: white;
        }
        
        .share-btn {
            background: var(--lime-green);
            color: white;
        }
        
        .discuss-btn {
            background: var(--orange);
            color: white;
        }
        
        .action-btn:hover {
            transform: scale(1.1) rotate(-2deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .ai-recommendations {
            background: linear-gradient(135deg, rgba(50, 205, 50, 0.2), rgba(255, 69, 0, 0.2));
            border: 2px solid var(--lime-green);
            border-radius: 20px;
            padding: 20px;
            position: relative;
        }
        
        .ai-recommendations::after {
            content: '▲▼▲';
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--hot-pink);
            font-size: 1.2rem;
            animation: symbolFloat 3s infinite ease-in-out;
        }
        
        @keyframes symbolFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-8px) rotate(180deg); }
        }
        
        .ai-title {
            font-family: 'Righteous', cursive;
            color: var(--lime-green);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .ai-icon {
            width: 30px;
            height: 30px;
            background: var(--neon-yellow);
            border-radius: 50% 20%;
            animation: aiPulse 2s infinite ease-in-out;
        }
        
        @keyframes aiPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        .recommendation-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--electric-blue);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .recommendation-item:hover {
            transform: translateX(10px);
            border-color: var(--hot-pink);
            background: rgba(255, 20, 147, 0.2);
        }
        
        .social-sharing {
            background: linear-gradient(135deg, rgba(255, 0, 255, 0.2), rgba(0, 191, 255, 0.2));
            border: 2px solid var(--magenta);
            border-radius: 20px;
            padding: 20px;
            position: relative;
        }
        
        .social-sharing::before {
            content: '◆◇◆◇';
            position: absolute;
            bottom: 10px;
            left: 20px;
            color: var(--neon-yellow);
            font-size: 1rem;
            animation: socialBlink 2s infinite;
        }
        
        @keyframes socialBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .social-title {
            font-family: 'Righteous', cursive;
            color: var(--magenta);
            margin-bottom: 15px;
        }
        
        .social-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .social-btn {
            padding: 12px;
            border: 2px solid var(--electric-blue);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
        }
        
        .social-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: var(--hot-pink);
            border-radius: 50%;
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
            z-index: -1;
        }
        
        .social-btn:hover::before {
            width: 200%;
            height: 200%;
        }
        
        .social-btn:hover {
            transform: scale(1.05) rotate(-5deg);
            border-color: var(--neon-yellow);
        }
        
        .collaborative-tools {
            background: linear-gradient(135deg, rgba(255, 69, 0, 0.2), rgba(255, 255, 0, 0.2));
            border: 2px solid var(--orange);
            border-radius: 20px;
            padding: 20px;
        }
        
        .collab-title {
            font-family: 'Righteous', cursive;
            color: var(--orange);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .collab-icon {
            width: 25px;
            height: 25px;
            background: var(--electric-blue);
            border-radius: 30% 70% 70% 30%;
            animation: collabWiggle 3s infinite ease-in-out;
        }
        
        @keyframes collabWiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(10deg); }
            75% { transform: rotate(-10deg); }
        }
        
        .collab-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .collab-btn {
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--lime-green);
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .collab-btn::after {
            content: '→';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
        }
        
        .collab-btn:hover {
            background: rgba(50, 205, 50, 0.3);
            transform: translateX(5px);
        }
        
        .collab-btn:hover::after {
            transform: translateY(-50%) translateX(5px);
        }
        
        .search-bar {
            background: rgba(255, 255, 255, 0.1);
            border: 3px solid var(--electric-blue);
            border-radius: 25px;
            padding: 15px 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .search-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 0, 0.1), transparent);
            animation: searchGlow 3s infinite;
        }
        
        @keyframes searchGlow {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .search-input {
            width: 100%;
            background: transparent;
            border: none;
            outline: none;
            color: white;
            font-size: 1.1rem;
            font-family: 'Poppins', sans-serif;
        }
        
        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            animation: floatAround 20s infinite linear;
        }
        
        .shape:nth-child(1) {
            top: 10%;
            left: 5%;
            width: 30px;
            height: 30px;
            background: var(--hot-pink);
            border-radius: 50% 20% 50% 20%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 60%;
            right: 10%;
            width: 40px;
            height: 20px;
            background: var(--neon-yellow);
            clip-path: polygon(0 0, 100% 0, 80% 100%, 20% 100%);
            animation-delay: -5s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            width: 25px;
            height: 25px;
            background: var(--lime-green);
            transform: rotate(45deg);
            animation-delay: -10s;
        }
        
        .shape:nth-child(4) {
            top: 30%;
            right: 25%;
            width: 35px;
            height: 35px;
            background: var(--electric-blue);
            border-radius: 50% 0 50% 0;
            animation-delay: -15s;
        }
        
        @keyframes floatAround {
            0% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-50px) rotate(90deg); }
            50% { transform: translateY(0) rotate(180deg); }
            75% { transform: translateY(50px) rotate(270deg); }
            100% { transform: translateY(0) rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .hybrid-component {
                padding: 20px;
            }
            
            .content-items {
                grid-template-columns: 1fr;
            }
            
            .social-buttons {
                grid-template-columns: 1fr;
            }
        }
        
        .achievement-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, var(--hot-pink), var(--electric-blue));
            border: 2px solid var(--neon-yellow);
            border-radius: 15px;
            padding: 15px 20px;
            color: white;
            font-weight: 600;
            transform: translateX(400px);
            transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
            box-shadow: 0 10px 25px rgba(255, 20, 147, 0.4);
        }
        
        .achievement-notification.show {
            transform: translateX(0);
        }
        
        .loading-spiral {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 4px solid transparent;
            border-top: 4px solid var(--hot-pink);
            border-right: 4px solid var(--electric-blue);
            border-bottom: 4px solid var(--neon-yellow);
            border-left: 4px solid var(--lime-green);
            border-radius: 50%;
            animation: spiralSpin 1s linear infinite;
            z-index: 1000;
        }
        
        @keyframes spiralSpin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="loading-spiral" id="loadingSpiral"></div>
    <div class="achievement-notification" id="achievementNotification"></div>
    
    <main>
        <h1>Smart Content Hub - Neo-Memphis Theme</h1>
        <div class="hybrid-component">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="🔍 Discover amazing content across all formats..." id="searchInput">
            </div>
            
            <div class="content-grid">
                <div class="main-content">
                    <div class="content-discovery">
                        <div class="discovery-header">
                            <div class="discovery-icon">🎯</div>
                            <div class="discovery-title">AI-Powered Discovery</div>
                        </div>
                        <div class="content-items" id="contentItems">
                            <!-- Dynamic content items will be inserted here -->
                        </div>
                    </div>
                </div>
                
                <div class="sidebar">
                    <div class="ai-recommendations">
                        <div class="ai-title">
                            <div class="ai-icon"></div>
                            Smart Recommendations
                        </div>
                        <div id="recommendationsList">
                            <!-- Dynamic recommendations will be inserted here -->
                        </div>
                    </div>
                    
                    <div class="social-sharing">
                        <div class="social-title">Community Sharing</div>
                        <div class="social-buttons">
                            <button class="social-btn" onclick="shareContent('twitter')">Tweet It</button>
                            <button class="social-btn" onclick="shareContent('linkedin')">LinkedIn</button>
                            <button class="social-btn" onclick="shareContent('discord')">Discord</button>
                            <button class="social-btn" onclick="shareContent('reddit')">Reddit</button>
                        </div>
                    </div>
                    
                    <div class="collaborative-tools">
                        <div class="collab-title">
                            <div class="collab-icon"></div>
                            Collaboration Hub
                        </div>
                        <div class="collab-actions">
                            <button class="collab-btn" onclick="createCollection()">Create Collection</button>
                            <button class="collab-btn" onclick="joinDiscussion()">Join Discussion</button>
                            <button class="collab-btn" onclick="inviteCollaborators()">Invite Team</button>
                            <button class="collab-btn" onclick="scheduleReview()">Schedule Review</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Content Hub Data and State Management
        class SmartContentHub {
            constructor() {
                this.contentData = [];
                this.bookmarks = new Set();
                this.recommendations = [];
                this.userPreferences = {
                    topics: ['technology', 'design', 'innovation'],
                    formats: ['article', 'video', 'podcast', 'image'],
                    difficulty: 'intermediate'
                };
                this.collaborativeState = {
                    activeCollections: [],
                    discussions: [],
                    sharedItems: []
                };
                this.init();
            }
            
            init() {
                this.generateMockContent();
                this.generateRecommendations();
                this.renderContent();
                this.setupEventListeners();
                this.startPeriodicUpdates();
            }
            
            generateMockContent() {
                const contentTypes = [
                    { type: 'article', icon: '📄', color: '--electric-blue' },
                    { type: 'video', icon: '🎥', color: '--hot-pink' },
                    { type: 'podcast', icon: '🎧', color: '--lime-green' },
                    { type: 'image', icon: '🖼️', color: '--neon-yellow' },
                    { type: 'tool', icon: '🛠️', color: '--orange' },
                    { type: 'course', icon: '🎓', color: '--magenta' }
                ];
                
                const titles = [
                    'The Future of Neo-Memphis Design',
                    'AI-Powered Content Curation',
                    'Building Collaborative Workspaces',
                    'Visual Storytelling Techniques',
                    'Cross-Platform Content Strategy',
                    'Community-Driven Innovation',
                    'Postmodern UI Patterns',
                    'Smart Recommendation Systems',
                    'Interactive Content Experiences',
                    'Digital Collaboration Tools'
                ];
                
                const authors = [
                    'Dr. Sarah Chen', 'Alex Rivera', 'Maya Patel',
                    'Jordan Kim', 'Taylor Swift', 'Casey Johnson',
                    'Riley Cooper', 'Morgan Davis', 'Quinn Wilson'
                ];
                
                this.contentData = Array.from({length: 12}, (_, i) => {
                    const contentType = contentTypes[Math.floor(Math.random() * contentTypes.length)];
                    return {
                        id: `content_${i}`,
                        title: titles[Math.floor(Math.random() * titles.length)],
                        type: contentType.type,
                        icon: contentType.icon,
                        color: contentType.color,
                        author: authors[Math.floor(Math.random() * authors.length)],
                        duration: this.generateDuration(contentType.type),
                        engagement: Math.floor(Math.random() * 1000) + 100,
                        difficulty: ['beginner', 'intermediate', 'advanced'][Math.floor(Math.random() * 3)],
                        tags: this.generateTags(),
                        aiScore: (Math.random() * 0.4 + 0.6).toFixed(2) // 0.6 to 1.0
                    };
                });
            }
            
            generateDuration(type) {
                switch(type) {
                    case 'article': return `${Math.floor(Math.random() * 15) + 3} min read`;
                    case 'video': return `${Math.floor(Math.random() * 45) + 5} min watch`;
                    case 'podcast': return `${Math.floor(Math.random() * 60) + 15} min listen`;
                    case 'course': return `${Math.floor(Math.random() * 8) + 2} hours`;
                    default: return `${Math.floor(Math.random() * 10) + 1} min`;
                }
            }
            
            generateTags() {
                const allTags = ['trending', 'beginner-friendly', 'expert-level', 'community-favorite', 
                               'ai-curated', 'collaborative', 'visual', 'interactive', 'innovative'];
                const numTags = Math.floor(Math.random() * 3) + 1;
                return allTags.sort(() => 0.5 - Math.random()).slice(0, numTags);
            }
            
            generateRecommendations() {
                const recommendations = [
                    {
                        title: 'Trending in Your Network',
                        description: 'Based on your team\'s recent activity',
                        confidence: 0.95,
                        type: 'social'
                    },
                    {
                        title: 'Similar to Your Bookmarks',
                        description: 'AI found related content',
                        confidence: 0.87,
                        type: 'similarity'
                    },
                    {
                        title: 'Weekly Learning Goal',
                        description: 'Complete 3 more articles this week',
                        confidence: 0.92,
                        type: 'goal'
                    },
                    {
                        title: 'Collaborative Opportunity',
                        description: 'Join "Design Systems" discussion',
                        confidence: 0.78,
                        type: 'collaboration'
                    }
                ];
                
                this.recommendations = recommendations;
            }
            
            renderContent() {
                const container = document.getElementById('contentItems');
                container.innerHTML = '';
                
                this.contentData.forEach(item => {
                    const contentElement = this.createContentElement(item);
                    container.appendChild(contentElement);
                });
                
                this.renderRecommendations();
            }
            
            createContentElement(item) {
                const element = document.createElement('div');
                element.className = 'content-item';
                element.setAttribute('data-id', item.id);
                
                const isBookmarked = this.bookmarks.has(item.id);
                
                element.innerHTML = `
                    <div class="item-type" style="background: var(${item.color})">${item.icon} ${item.type}</div>
                    <div class="item-title">${item.title}</div>
                    <div class="item-meta">
                        by ${item.author} • ${item.duration} • 
                        <span style="color: var(--neon-yellow)">AI Score: ${item.aiScore}</span>
                    </div>
                    <div class="item-actions">
                        <button class="action-btn bookmark-btn ${isBookmarked ? 'bookmarked' : ''}" 
                                onclick="contentHub.toggleBookmark('${item.id}')">
                            ${isBookmarked ? '★ Saved' : '☆ Save'}
                        </button>
                        <button class="action-btn share-btn" 
                                onclick="contentHub.shareItem('${item.id}')">
                            🔗 Share
                        </button>
                        <button class="action-btn discuss-btn" 
                                onclick="contentHub.startDiscussion('${item.id}')">
                            💬 Discuss
                        </button>
                    </div>
                `;
                
                return element;
            }
            
            renderRecommendations() {
                const container = document.getElementById('recommendationsList');
                container.innerHTML = '';
                
                this.recommendations.forEach(rec => {
                    const recElement = document.createElement('div');
                    recElement.className = 'recommendation-item';
                    recElement.innerHTML = `
                        <div style="font-weight: 600; margin-bottom: 5px;">${rec.title}</div>
                        <div style="font-size: 0.9rem; margin-bottom: 8px;">${rec.description}</div>
                        <div style="color: var(--neon-yellow); font-size: 0.8rem;">
                            Confidence: ${(rec.confidence * 100).toFixed(0)}%
                        </div>
                    `;
                    recElement.addEventListener('click', () => this.applyRecommendation(rec));
                    container.appendChild(recElement);
                });
            }
            
            setupEventListeners() {
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', (e) => {
                    this.handleSearch(e.target.value);
                });
                
                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'k':
                                e.preventDefault();
                                searchInput.focus();
                                break;
                            case 'b':
                                e.preventDefault();
                                this.showBookmarks();
                                break;
                            case 'n':
                                e.preventDefault();
                                this.createNewCollection();
                                break;
                        }
                    }
                });
            }
            
            handleSearch(query) {
                if (!query.trim()) {
                    this.renderContent();
                    return;
                }
                
                this.showLoading();
                
                // Simulate AI-powered search with delay
                setTimeout(() => {
                    const filtered = this.contentData.filter(item => 
                        item.title.toLowerCase().includes(query.toLowerCase()) ||
                        item.author.toLowerCase().includes(query.toLowerCase()) ||
                        item.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
                    );
                    
                    // Sort by AI relevance score
                    filtered.sort((a, b) => b.aiScore - a.aiScore);
                    
                    this.renderFilteredContent(filtered);
                    this.hideLoading();
                    
                    if (filtered.length > 0) {
                        this.showAchievement(`Found ${filtered.length} relevant items!`);
                    }
                }, 800);
            }
            
            renderFilteredContent(items) {
                const container = document.getElementById('contentItems');
                container.innerHTML = '';
                
                if (items.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--neon-yellow);">
                            <div style="font-size: 2rem; margin-bottom: 10px;">🔍</div>
                            <div>No content found. Try different keywords!</div>
                        </div>
                    `;
                    return;
                }
                
                items.forEach(item => {
                    const contentElement = this.createContentElement(item);
                    container.appendChild(contentElement);
                });
            }
            
            toggleBookmark(contentId) {
                if (this.bookmarks.has(contentId)) {
                    this.bookmarks.delete(contentId);
                    this.showAchievement('Removed from bookmarks');
                } else {
                    this.bookmarks.add(contentId);
                    this.showAchievement('Added to bookmarks!');
                }
                this.renderContent();
                this.updateRecommendations();
            }
            
            shareItem(contentId) {
                const item = this.contentData.find(c => c.id === contentId);
                if (item) {
                    this.collaborativeState.sharedItems.push({
                        contentId,
                        timestamp: Date.now(),
                        sharedBy: 'current_user'
                    });
                    this.showAchievement(`Shared "${item.title}" with your network!`);
                    this.updateRecommendations();
                }
            }
            
            startDiscussion(contentId) {
                const item = this.contentData.find(c => c.id === contentId);
                if (item) {
                    const discussionId = `discussion_${Date.now()}`;
                    this.collaborativeState.discussions.push({
                        id: discussionId,
                        contentId,
                        title: `Discussion: ${item.title}`,
                        participants: ['current_user'],
                        messages: [],
                        createdAt: Date.now()
                    });
                    this.showAchievement(`Started discussion about "${item.title}"!`);
                }
            }
            
            applyRecommendation(recommendation) {
                this.showLoading();
                
                setTimeout(() => {
                    switch(recommendation.type) {
                        case 'social':
                            this.loadSocialContent();
                            break;
                        case 'similarity':
                            this.loadSimilarContent();
                            break;
                        case 'goal':
                            this.showLearningProgress();
                            break;
                        case 'collaboration':
                            this.joinCollaboration();
                            break;
                    }
                    this.hideLoading();
                    this.showAchievement(`Applied recommendation: ${recommendation.title}`);
                }, 1000);
            }
            
            loadSocialContent() {
                // Simulate loading social trending content
                const socialItems = this.contentData.filter(item => 
                    item.tags.includes('trending') || item.engagement > 500
                );
                this.renderFilteredContent(socialItems);
            }
            
            loadSimilarContent() {
                // Simulate AI similarity matching
                const bookmarkedItems = Array.from(this.bookmarks);
                if (bookmarkedItems.length > 0) {
                    const similarItems = this.contentData.filter(item => 
                        !this.bookmarks.has(item.id) && 
                        parseFloat(item.aiScore) > 0.8
                    );
                    this.renderFilteredContent(similarItems);
                }
            }
            
            showLearningProgress() {
                const progressHtml = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 2rem; margin-bottom: 20px;">📈</div>
                        <div style="font-size: 1.2rem; margin-bottom: 10px;">Learning Progress</div>
                        <div style="color: var(--neon-yellow);">7/10 articles completed this week</div>
                        <div style="margin-top: 15px; height: 20px; background: rgba(255,255,255,0.2); border-radius: 10px; overflow: hidden;">
                            <div style="width: 70%; height: 100%; background: linear-gradient(90deg, var(--lime-green), var(--electric-blue));"></div>
                        </div>
                    </div>
                `;
                document.getElementById('contentItems').innerHTML = progressHtml;
            }
            
            joinCollaboration() {
                this.collaborativeState.activeCollections.push({
                    id: 'design_systems_collection',
                    name: 'Design Systems',
                    participants: ['current_user', 'sarah_chen', 'alex_rivera'],
                    contentCount: 23,
                    lastActivity: Date.now()
                });
            }
            
            updateRecommendations() {
                // Simulate AI updating recommendations based on user activity
                const newRec = {
                    title: 'Based on Recent Activity',
                    description: 'New content matching your interests',
                    confidence: 0.89,
                    type: 'activity'
                };
                
                this.recommendations.unshift(newRec);
                if (this.recommendations.length > 4) {
                    this.recommendations.pop();
                }
                
                this.renderRecommendations();
            }
            
            startPeriodicUpdates() {
                // Simulate real-time content updates
                setInterval(() => {
                    if (Math.random() > 0.7) {
                        this.addNewContent();
                    }
                }, 15000);
                
                // Update AI recommendations periodically
                setInterval(() => {
                    this.updateRecommendations();
                }, 30000);
            }
            
            addNewContent() {
                const newItem = {
                    id: `content_${Date.now()}`,
                    title: 'Just Published: Breaking Design Trends',
                    type: 'article',
                    icon: '📄',
                    color: '--hot-pink',
                    author: 'Live Feed',
                    duration: '5 min read',
                    engagement: Math.floor(Math.random() * 100) + 50,
                    difficulty: 'intermediate',
                    tags: ['trending', 'fresh'],
                    aiScore: (Math.random() * 0.3 + 0.7).toFixed(2)
                };
                
                this.contentData.unshift(newItem);
                if (this.contentData.length > 15) {
                    this.contentData.pop();
                }
                
                this.renderContent();
                this.showAchievement('New content discovered!');
            }
            
            showLoading() {
                document.getElementById('loadingSpiral').style.display = 'block';
            }
            
            hideLoading() {
                document.getElementById('loadingSpiral').style.display = 'none';
            }
            
            showAchievement(message) {
                const notification = document.getElementById('achievementNotification');
                notification.textContent = message;
                notification.classList.add('show');
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }
        
        // Global functions for UI interactions
        function shareContent(platform) {
            const messages = {
                twitter: 'Shared to Twitter! Your network will love this.',
                linkedin: 'Posted to LinkedIn! Professional networking enhanced.',
                discord: 'Shared in Discord! Community engaged.',
                reddit: 'Posted to Reddit! Discussion started.'
            };
            
            contentHub.showAchievement(messages[platform]);
            
            // Simulate social engagement
            setTimeout(() => {
                contentHub.showAchievement(`${Math.floor(Math.random() * 20) + 5} people engaged with your share!`);
            }, 3000);
        }
        
        function createCollection() {
            const collectionName = `Collection_${Date.now()}`;
            contentHub.collaborativeState.activeCollections.push({
                id: collectionName.toLowerCase(),
                name: collectionName,
                participants: ['current_user'],
                contentCount: 0,
                lastActivity: Date.now()
            });
            contentHub.showAchievement('New collection created! Start adding content.');
        }
        
        function joinDiscussion() {
            contentHub.showAchievement('Joined active discussions! Check your notifications.');
        }
        
        function inviteCollaborators() {
            contentHub.showAchievement('Collaboration invites sent! Team members will be notified.');
        }
        
        function scheduleReview() {
            contentHub.showAchievement('Content review scheduled for tomorrow at 2 PM.');
        }
        
        // Initialize the Smart Content Hub
        const contentHub = new SmartContentHub();
        
        // Add some Neo-Memphis flair with periodic visual effects
        setInterval(() => {
            if (Math.random() > 0.8) {
                document.body.style.filter = 'hue-rotate(10deg)';
                setTimeout(() => {
                    document.body.style.filter = 'none';
                }, 200);
            }
        }, 5000);
        
        // Add keyboard shortcuts help
        document.addEventListener('keydown', (e) => {
            if (e.key === '?' && e.shiftKey) {
                contentHub.showAchievement('Shortcuts: Ctrl+K (search), Ctrl+B (bookmarks), Ctrl+N (new collection)');
            }
        });
        
        console.log('🎨 Neo-Memphis Smart Content Hub initialized!');
        console.log('Features: AI Discovery, Social Sharing, Collaboration, Smart Recommendations');
        console.log('Try: Search, bookmark content, share items, create collections!');
    </script>
</body>
</html>