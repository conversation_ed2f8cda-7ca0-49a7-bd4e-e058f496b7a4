<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space Age Navigation Hub</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
            color: #e6f3ff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated starfield background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #8dd7f7, transparent),
                radial-gradient(1px 1px at 130px 80px, #fff, transparent),
                radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.6), transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: twinkle 4s ease-in-out infinite alternate;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        main {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 3rem;
            font-size: 2.5rem;
            font-weight: 900;
            color: #8dd7f7;
            text-shadow: 0 0 20px rgba(141, 215, 247, 0.5);
            letter-spacing: 2px;
        }

        .hybrid-component {
            background: rgba(26, 26, 46, 0.8);
            border: 2px solid rgba(141, 215, 247, 0.3);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 0 30px rgba(141, 215, 247, 0.2),
                inset 0 0 30px rgba(141, 215, 247, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* Orbital rings decoration */
        .hybrid-component::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(141, 215, 247, 0.1), transparent);
            border-radius: 50%;
            animation: orbit 20s linear infinite;
            pointer-events: none;
        }

        @keyframes orbit {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Navigation Hub Container */
        .nav-hub {
            position: relative;
            z-index: 2;
        }

        /* Breadcrumb System */
        .stellar-breadcrumbs {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: linear-gradient(45deg, rgba(141, 215, 247, 0.1), rgba(141, 215, 247, 0.05));
            border-radius: 15px;
            border: 1px solid rgba(141, 215, 247, 0.2);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            color: #8dd7f7;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .breadcrumb-item:hover {
            background: rgba(141, 215, 247, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(141, 215, 247, 0.3);
        }

        .breadcrumb-separator {
            margin: 0 1rem;
            color: #4a9eff;
            font-size: 1.2rem;
        }

        /* Search Hub */
        .cosmic-search {
            position: relative;
            margin-bottom: 2rem;
        }

        .search-container {
            display: flex;
            align-items: center;
            background: linear-gradient(45deg, rgba(22, 33, 62, 0.8), rgba(26, 26, 46, 0.8));
            border: 2px solid rgba(141, 215, 247, 0.3);
            border-radius: 25px;
            padding: 0.5rem;
            transition: all 0.3s ease;
        }

        .search-container:focus-within {
            border-color: #8dd7f7;
            box-shadow: 0 0 20px rgba(141, 215, 247, 0.4);
        }

        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 1rem 1.5rem;
            color: #e6f3ff;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
            outline: none;
        }

        .search-input::placeholder {
            color: rgba(230, 243, 255, 0.6);
        }

        .search-btn {
            background: linear-gradient(45deg, #4a9eff, #8dd7f7);
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 20px;
            color: #0a0a0f;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(141, 215, 247, 0.4);
        }

        /* Tab Navigation */
        .orbital-tabs {
            display: flex;
            margin-bottom: 2rem;
            background: rgba(22, 33, 62, 0.6);
            border-radius: 15px;
            padding: 0.5rem;
            position: relative;
        }

        .tab-indicator {
            position: absolute;
            background: linear-gradient(45deg, #4a9eff, #8dd7f7);
            border-radius: 10px;
            transition: all 0.3s ease;
            height: calc(100% - 1rem);
            top: 0.5rem;
        }

        .tab-btn {
            flex: 1;
            background: transparent;
            border: none;
            padding: 1rem 2rem;
            color: #e6f3ff;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            cursor: pointer;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .tab-btn.active {
            color: #0a0a0f;
        }

        .tab-btn:hover:not(.active) {
            background: rgba(141, 215, 247, 0.1);
        }

        /* Quick Actions */
        .space-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-btn {
            background: linear-gradient(135deg, rgba(22, 33, 62, 0.8), rgba(26, 26, 46, 0.8));
            border: 2px solid rgba(141, 215, 247, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            color: #e6f3ff;
            text-decoration: none;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(141, 215, 247, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            border-color: #8dd7f7;
            box-shadow: 0 10px 25px rgba(141, 215, 247, 0.3);
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        /* State Memory Panel */
        .memory-panel {
            background: linear-gradient(135deg, rgba(22, 33, 62, 0.6), rgba(26, 26, 46, 0.6));
            border: 1px solid rgba(141, 215, 247, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .memory-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .memory-title {
            color: #8dd7f7;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .memory-status {
            background: linear-gradient(45deg, #4a9eff, #8dd7f7);
            color: #0a0a0f;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 700;
        }

        .memory-items {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }

        .memory-item {
            background: rgba(141, 215, 247, 0.1);
            border: 1px solid rgba(141, 215, 247, 0.2);
            border-radius: 10px;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .memory-item:hover {
            background: rgba(141, 215, 247, 0.2);
            transform: scale(1.02);
        }

        .memory-item-title {
            color: #8dd7f7;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .memory-item-desc {
            color: rgba(230, 243, 255, 0.8);
            font-size: 0.9rem;
        }

        /* Tab Content */
        .tab-content {
            background: rgba(22, 33, 62, 0.4);
            border: 1px solid rgba(141, 215, 247, 0.2);
            border-radius: 15px;
            padding: 2rem;
            min-height: 300px;
        }

        .content-panel {
            display: none;
        }

        .content-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-title {
            color: #8dd7f7;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .content-description {
            color: rgba(230, 243, 255, 0.9);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .feature-card {
            background: rgba(141, 215, 247, 0.1);
            border: 1px solid rgba(141, 215, 247, 0.2);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            background: rgba(141, 215, 247, 0.2);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stellar-breadcrumbs {
                flex-wrap: wrap;
            }

            .orbital-tabs {
                flex-direction: column;
            }

            .tab-indicator {
                display: none;
            }

            .space-actions {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <main>
        <h1>Navigation Hub - Space Age Theme</h1>
        <div class="hybrid-component">
            <div class="nav-hub">
                <!-- Stellar Breadcrumbs -->
                <nav class="stellar-breadcrumbs" aria-label="Breadcrumb">
                    <a href="#" class="breadcrumb-item">🌌 Home</a>
                    <span class="breadcrumb-separator">→</span>
                    <a href="#" class="breadcrumb-item">🚀 Navigation</a>
                    <span class="breadcrumb-separator">→</span>
                    <a href="#" class="breadcrumb-item">⭐ Current Mission</a>
                </nav>

                <!-- Cosmic Search -->
                <div class="cosmic-search">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search the cosmos..." id="searchInput">
                        <button class="search-btn" onclick="performSearch()">🔍 Scan</button>
                    </div>
                </div>

                <!-- Space Actions -->
                <div class="space-actions">
                    <a href="#" class="action-btn" onclick="quickAction('dashboard')">
                        <div class="action-icon">🌟</div>
                        <div>Command Center</div>
                    </a>
                    <a href="#" class="action-btn" onclick="quickAction('missions')">
                        <div class="action-icon">🚀</div>
                        <div>Active Missions</div>
                    </a>
                    <a href="#" class="action-btn" onclick="quickAction('fleet')">
                        <div class="action-icon">🛸</div>
                        <div>Fleet Status</div>
                    </a>
                    <a href="#" class="action-btn" onclick="quickAction('communications')">
                        <div class="action-icon">📡</div>
                        <div>Communications</div>
                    </a>
                </div>

                <!-- State Memory Panel -->
                <div class="memory-panel">
                    <div class="memory-header">
                        <h3 class="memory-title">Navigation Memory Bank</h3>
                        <div class="memory-status">ACTIVE</div>
                    </div>
                    <div class="memory-items">
                        <div class="memory-item">
                            <div class="memory-item-title">Last Destination</div>
                            <div class="memory-item-desc">Andromeda Sector 7</div>
                        </div>
                        <div class="memory-item">
                            <div class="memory-item-title">Favorite Route</div>
                            <div class="memory-item-desc">Earth → Mars → Jupiter</div>
                        </div>
                        <div class="memory-item">
                            <div class="memory-item-title">Recent Search</div>
                            <div class="memory-item-desc">"wormhole coordinates"</div>
                        </div>
                        <div class="memory-item">
                            <div class="memory-item-title">Bookmark</div>
                            <div class="memory-item-desc">Alpha Centauri Station</div>
                        </div>
                    </div>
                </div>

                <!-- Orbital Tab Navigation -->
                <div class="orbital-tabs">
                    <div class="tab-indicator" id="tabIndicator"></div>
                    <button class="tab-btn active" data-tab="explore">🌌 Explore</button>
                    <button class="tab-btn" data-tab="missions">🚀 Missions</button>
                    <button class="tab-btn" data-tab="data">📊 Data Banks</button>
                    <button class="tab-btn" data-tab="settings">⚙️ Controls</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <div class="content-panel active" id="explore">
                        <h3 class="content-title">Cosmic Exploration Hub</h3>
                        <p class="content-description">
                            Navigate through the infinite expanse of space using our advanced exploration systems. 
                            Discover new worlds, chart unknown territories, and expand the boundaries of human knowledge.
                        </p>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>🌟 Star Charts</h4>
                                <p>Interactive stellar mapping</p>
                            </div>
                            <div class="feature-card">
                                <h4>🌍 Planet Scanner</h4>
                                <p>Deep surface analysis</p>
                            </div>
                            <div class="feature-card">
                                <h4>🌌 Galaxy View</h4>
                                <p>Macro navigation system</p>
                            </div>
                        </div>
                    </div>

                    <div class="content-panel" id="missions">
                        <h3 class="content-title">Mission Control Center</h3>
                        <p class="content-description">
                            Coordinate complex space missions across multiple star systems. Monitor crew status, 
                            resource allocation, and mission objectives in real-time.
                        </p>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>🎯 Objectives</h4>
                                <p>Mission goal tracking</p>
                            </div>
                            <div class="feature-card">
                                <h4>👨‍🚀 Crew Status</h4>
                                <p>Personnel monitoring</p>
                            </div>
                            <div class="feature-card">
                                <h4>⛽ Resources</h4>
                                <p>Supply management</p>
                            </div>
                        </div>
                    </div>

                    <div class="content-panel" id="data">
                        <h3 class="content-title">Galactic Data Repository</h3>
                        <p class="content-description">
                            Access vast databases of cosmic information collected from across the universe. 
                            Analyze patterns, research phenomena, and contribute to galactic knowledge.
                        </p>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>📡 Signals</h4>
                                <p>Communication logs</p>
                            </div>
                            <div class="feature-card">
                                <h4>🔬 Research</h4>
                                <p>Scientific findings</p>
                            </div>
                            <div class="feature-card">
                                <h4>📈 Analytics</h4>
                                <p>Pattern recognition</p>
                            </div>
                        </div>
                    </div>

                    <div class="content-panel" id="settings">
                        <h3 class="content-title">Navigation Controls</h3>
                        <p class="content-description">
                            Configure your space navigation preferences, customize interface elements, 
                            and optimize system performance for your specific mission requirements.
                        </p>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>🎛️ Interface</h4>
                                <p>UI customization</p>
                            </div>
                            <div class="feature-card">
                                <h4>🔧 Systems</h4>
                                <p>Performance tuning</p>
                            </div>
                            <div class="feature-card">
                                <h4>🛡️ Security</h4>
                                <p>Access protocols</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tab Navigation System
        const tabs = document.querySelectorAll('.tab-btn');
        const panels = document.querySelectorAll('.content-panel');
        const indicator = document.getElementById('tabIndicator');

        function updateTabIndicator(activeTab) {
            const tabRect = activeTab.getBoundingClientRect();
            const containerRect = activeTab.parentElement.getBoundingClientRect();
            const left = tabRect.left - containerRect.left;
            const width = tabRect.width;
            
            indicator.style.left = left + 'px';
            indicator.style.width = width + 'px';
        }

        tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and panels
                tabs.forEach(t => t.classList.remove('active'));
                panels.forEach(p => p.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding panel
                tab.classList.add('active');
                const targetPanel = document.getElementById(tab.dataset.tab);
                targetPanel.classList.add('active');
                
                // Update tab indicator
                updateTabIndicator(tab);
                
                // Store navigation state
                storeNavigationState('currentTab', tab.dataset.tab);
                addToNavigationHistory('Tab: ' + tab.textContent.trim());
            });
        });

        // Initialize tab indicator
        window.addEventListener('load', () => {
            const activeTab = document.querySelector('.tab-btn.active');
            if (activeTab) {
                updateTabIndicator(activeTab);
            }
        });

        // Search Functionality
        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            const query = searchInput.value.trim();
            
            if (query) {
                // Simulate search with cosmic effect
                searchInput.style.background = 'linear-gradient(45deg, rgba(141, 215, 247, 0.2), rgba(74, 158, 255, 0.2))';
                
                setTimeout(() => {
                    searchInput.style.background = 'transparent';
                    alert(`🔍 Scanning cosmos for: "${query}"\n\n🌌 Found 42 stellar objects matching your search criteria!`);
                    
                    // Store search in memory
                    storeNavigationState('lastSearch', query);
                    addToNavigationHistory('Search: ' + query);
                    updateMemoryPanel();
                }, 500);
            }
        }

        // Quick Actions
        function quickAction(action) {
            const actions = {
                dashboard: '🌟 Accessing Command Center...\nAll systems operational!',
                missions: '🚀 Loading Active Missions...\n3 missions in progress, 2 completed today.',
                fleet: '🛸 Fleet Status Report...\n12 vessels deployed, all reporting nominal status.',
                communications: '📡 Opening Communications Channel...\nConnected to Deep Space Network.'
            };
            
            alert(actions[action] || 'Unknown action');
            addToNavigationHistory('Action: ' + action);
        }

        // Navigation State Management
        const navigationState = {
            currentTab: 'explore',
            lastSearch: '',
            favoriteDestination: 'Andromeda Sector 7',
            history: []
        };

        function storeNavigationState(key, value) {
            navigationState[key] = value;
            localStorage.setItem('spaceNavState', JSON.stringify(navigationState));
        }

        function loadNavigationState() {
            const stored = localStorage.getItem('spaceNavState');
            if (stored) {
                Object.assign(navigationState, JSON.parse(stored));
            }
        }

        function addToNavigationHistory(entry) {
            const timestamp = new Date().toLocaleTimeString();
            navigationState.history.unshift(`${timestamp}: ${entry}`);
            
            // Keep only last 10 entries
            if (navigationState.history.length > 10) {
                navigationState.history = navigationState.history.slice(0, 10);
            }
            
            storeNavigationState('history', navigationState.history);
        }

        // Update Memory Panel
        function updateMemoryPanel() {
            const memoryItems = document.querySelectorAll('.memory-item');
            
            // Update last search if available
            if (navigationState.lastSearch && memoryItems[2]) {
                memoryItems[2].querySelector('.memory-item-desc').textContent = `"${navigationState.lastSearch}"`;
            }
        }

        // Breadcrumb Navigation
        document.querySelectorAll('.breadcrumb-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const destination = item.textContent.trim();
                
                // Cosmic navigation effect
                item.style.transform = 'scale(1.1)';
                item.style.boxShadow = '0 0 20px rgba(141, 215, 247, 0.6)';
                
                setTimeout(() => {
                    item.style.transform = '';
                    item.style.boxShadow = '';
                    addToNavigationHistory('Navigate: ' + destination);
                }, 300);
            });
        });

        // Search on Enter key
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Initialize
        window.addEventListener('load', () => {
            loadNavigationState();
            updateMemoryPanel();
            addToNavigationHistory('Navigation Hub initialized');
        });

        // Add cosmic interaction effects
        document.querySelectorAll('.action-btn, .memory-item, .feature-card').forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.transform = 'translateY(-2px) scale(1.02)';
            });
            
            element.addEventListener('mouseleave', () => {
                element.style.transform = '';
            });
        });

        // Orbital animation control
        let orbitSpeed = 1;
        const orbitElement = document.querySelector('.hybrid-component::before');

        function adjustOrbitSpeed(speed) {
            orbitSpeed = speed;
            document.documentElement.style.setProperty('--orbit-duration', `${20 / speed}s`);
        }

        // Enhanced accessibility
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
</body>
</html>