<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organic Tech Cognitive Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bio-green: #2d5a3d;
            --tech-blue: #1a3a52;
            --silicon-gray: #4a5568;
            --neural-cyan: #00a3cc;
            --growth-lime: #7dd87d;
            --symbiotic-teal: #20b2aa;
            --organic-amber: #f7931e;
            --bio-dark: #1a2f1a;
            --tech-dark: #0d1a26;
        }

        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, var(--bio-dark) 0%, var(--tech-dark) 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Organic neural network background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 30%, var(--neural-cyan)15 1px, transparent 1px),
                radial-gradient(circle at 80% 70%, var(--growth-lime)15 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, var(--symbiotic-teal)15 1px, transparent 1px);
            background-size: 100px 100px, 150px 150px, 200px 200px;
            opacity: 0.1;
            z-index: -1;
            animation: neuralPulse 8s ease-in-out infinite alternate;
        }

        @keyframes neuralPulse {
            0% { opacity: 0.05; transform: scale(1); }
            100% { opacity: 0.15; transform: scale(1.02); }
        }

        main {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, var(--growth-lime), var(--neural-cyan), var(--symbiotic-teal));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px var(--neural-cyan)30;
            animation: bioGlow 3s ease-in-out infinite alternate;
        }

        @keyframes bioGlow {
            0% { filter: brightness(1) hue-rotate(0deg); }
            100% { filter: brightness(1.2) hue-rotate(10deg); }
        }

        .hybrid-component {
            display: grid;
            grid-template-columns: 1fr 350px;
            grid-template-rows: auto 1fr auto;
            gap: 1.5rem;
            height: 80vh;
            position: relative;
        }

        /* Bio-tech growing borders */
        .bio-panel {
            background: linear-gradient(135deg, var(--bio-green)20 0%, var(--tech-blue)20 100%);
            border: 2px solid transparent;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .bio-panel::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--growth-lime), var(--neural-cyan), var(--symbiotic-teal), var(--organic-amber));
            border-radius: 12px;
            z-index: -1;
            animation: organicBorder 4s linear infinite;
        }

        @keyframes organicBorder {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        /* Conversation Interface */
        .conversation-panel {
            grid-column: 1;
            grid-row: 1 / 3;
            display: flex;
            flex-direction: column;
        }

        .conversation-header {
            padding: 1rem;
            border-bottom: 1px solid var(--neural-cyan)30;
            background: linear-gradient(90deg, var(--bio-green)40, var(--tech-blue)40);
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .bio-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--growth-lime);
            animation: synapseFireBrightness 2s ease-in-out infinite;
            box-shadow: 0 0 10px var(--growth-lime);
        }

        @keyframes synapseFireBrightness {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--neural-cyan) transparent;
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            border-radius: 12px;
            max-width: 80%;
            position: relative;
            animation: messageGrow 0.3s ease-out;
        }

        @keyframes messageGrow {
            0% { opacity: 0; transform: scale(0.9) translateY(10px); }
            100% { opacity: 1; transform: scale(1) translateY(0); }
        }

        .message.user {
            background: linear-gradient(135deg, var(--neural-cyan)30, var(--symbiotic-teal)30);
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }

        .message.ai {
            background: linear-gradient(135deg, var(--bio-green)40, var(--tech-blue)40);
            border-bottom-left-radius: 4px;
        }

        .message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--growth-lime), var(--neural-cyan));
            border-radius: 2px;
            animation: synapticActivity 3s ease-in-out infinite;
        }

        @keyframes synapticActivity {
            0%, 100% { opacity: 0.3; transform: scaleX(0.5); }
            50% { opacity: 0.8; transform: scaleX(1); }
        }

        .chat-input {
            padding: 1rem;
            border-top: 1px solid var(--neural-cyan)30;
        }

        .input-container {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .bio-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid var(--neural-cyan)40;
            border-radius: 25px;
            background: var(--bio-dark)60;
            color: #e2e8f0;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .bio-input:focus {
            outline: none;
            border-color: var(--growth-lime);
            box-shadow: 0 0 15px var(--growth-lime)30;
            background: var(--bio-dark)80;
        }

        .send-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--growth-lime), var(--neural-cyan));
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            transform: scale(1.1) rotate(15deg);
            box-shadow: 0 0 20px var(--growth-lime)50;
        }

        /* Analytics Panel */
        .analytics-panel {
            grid-column: 2;
            grid-row: 1;
            display: flex;
            flex-direction: column;
        }

        .analytics-header {
            padding: 1rem;
            border-bottom: 1px solid var(--symbiotic-teal)30;
            background: linear-gradient(90deg, var(--tech-blue)40, var(--bio-green)40);
        }

        .analytics-content {
            flex: 1;
            padding: 1rem;
        }

        .metric-card {
            background: var(--silicon-gray)20;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            border-left: 4px solid var(--growth-lime);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateX(5px);
            border-left-color: var(--neural-cyan);
            box-shadow: -5px 0 15px var(--neural-cyan)20;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--growth-lime);
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #a0aec0;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--silicon-gray)30;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--growth-lime), var(--neural-cyan));
            border-radius: 3px;
            animation: bioProgress 2s ease-out;
        }

        @keyframes bioProgress {
            0% { width: 0%; }
            100% { width: var(--progress-width, 50%); }
        }

        /* Context Awareness Panel */
        .context-panel {
            grid-column: 2;
            grid-row: 2;
            display: flex;
            flex-direction: column;
        }

        .context-header {
            padding: 1rem;
            border-bottom: 1px solid var(--organic-amber)30;
            background: linear-gradient(90deg, var(--bio-green)40, var(--silicon-gray)40);
        }

        .context-content {
            flex: 1;
            padding: 1rem;
        }

        .context-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 6px;
            background: var(--tech-blue)20;
            transition: all 0.3s ease;
        }

        .context-item:hover {
            background: var(--tech-blue)40;
            transform: scale(1.02);
        }

        .context-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--symbiotic-teal), var(--organic-amber));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { box-shadow: 0 0 5px var(--symbiotic-teal)30; }
            50% { box-shadow: 0 0 15px var(--symbiotic-teal)60; }
        }

        /* Task Automation Panel */
        .automation-panel {
            grid-column: 1 / 3;
            grid-row: 3;
            display: flex;
            flex-direction: column;
        }

        .automation-header {
            padding: 1rem;
            border-bottom: 1px solid var(--growth-lime)30;
            background: linear-gradient(90deg, var(--bio-green)40, var(--tech-blue)40);
        }

        .automation-content {
            padding: 1rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .task-card {
            background: var(--silicon-gray)15;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid var(--neural-cyan)30;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .task-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px var(--neural-cyan)20;
            border-color: var(--growth-lime);
        }

        .task-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--growth-lime), transparent);
            animation: taskScan 3s ease-in-out infinite;
        }

        @keyframes taskScan {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: -100%; }
        }

        .task-title {
            font-weight: bold;
            color: var(--growth-lime);
            margin-bottom: 0.5rem;
        }

        .task-status {
            font-size: 0.8rem;
            color: var(--neural-cyan);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .confidence-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .confidence-bar {
            flex: 1;
            height: 4px;
            background: var(--silicon-gray)40;
            border-radius: 2px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--organic-amber), var(--growth-lime));
            border-radius: 2px;
            animation: confidenceGrow 1.5s ease-out;
        }

        @keyframes confidenceGrow {
            0% { width: 0%; }
            100% { width: var(--confidence-level, 80%); }
        }

        /* Knowledge Graph Overlay */
        .knowledge-graph {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 80vw;
            height: 80vh;
            background: var(--bio-dark)95;
            border-radius: 20px;
            border: 2px solid var(--neural-cyan);
            backdrop-filter: blur(20px);
            z-index: 1000;
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .knowledge-graph.active {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }

        .graph-content {
            padding: 2rem;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .graph-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .close-graph {
            background: none;
            border: 2px solid var(--neural-cyan);
            color: var(--neural-cyan);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-graph:hover {
            background: var(--neural-cyan);
            color: var(--bio-dark);
            transform: rotate(90deg);
        }

        .knowledge-nodes {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--growth-lime), var(--neural-cyan));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: nodeFloat 4s ease-in-out infinite;
        }

        .node:hover {
            transform: scale(1.2);
            box-shadow: 0 0 20px var(--neural-cyan)50;
        }

        @keyframes nodeFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        .connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, var(--symbiotic-teal)50, var(--organic-amber)50);
            transform-origin: left center;
            animation: connectionPulse 2s ease-in-out infinite;
        }

        @keyframes connectionPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        /* Emotional Intelligence Indicator */
        .emotion-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--organic-amber), var(--symbiotic-teal));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 500;
            animation: emotionalBreathing 4s ease-in-out infinite;
        }

        @keyframes emotionalBreathing {
            0%, 100% { transform: scale(1); box-shadow: 0 0 10px var(--organic-amber)30; }
            50% { transform: scale(1.1); box-shadow: 0 0 25px var(--organic-amber)60; }
        }

        .emotion-indicator:hover {
            transform: scale(1.2) rotate(15deg);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hybrid-component {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
                height: auto;
            }

            .conversation-panel {
                grid-column: 1;
                grid-row: 1;
                height: 400px;
            }

            .analytics-panel {
                grid-column: 1;
                grid-row: 2;
            }

            .context-panel {
                grid-column: 1;
                grid-row: 3;
            }

            .automation-panel {
                grid-column: 1;
                grid-row: 4;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <main>
        <h1>Cognitive Assistant - Organic Tech Theme</h1>
        <div class="hybrid-component">
            <!-- AI Conversation Interface -->
            <div class="conversation-panel bio-panel">
                <div class="conversation-header">
                    <div class="ai-status">
                        <div class="bio-indicator" role="status" aria-label="AI system active"></div>
                        <span>Neural Network Active - Symbiotic Mode</span>
                    </div>
                </div>
                <div class="chat-messages" id="chatMessages" role="log" aria-live="polite">
                    <div class="message ai">
                        <span class="sr-only">AI Assistant:</span>
                        Welcome to the Organic Tech Cognitive Assistant! I'm your symbiotic AI companion, ready to learn and grow with you. How can I help you today?
                    </div>
                    <div class="message user">
                        <span class="sr-only">You:</span>
                        Help me analyze my productivity patterns and suggest improvements.
                    </div>
                    <div class="message ai">
                        <span class="sr-only">AI Assistant:</span>
                        I've analyzed your recent activity patterns. Based on your bio-rhythms and cognitive load, I recommend focusing on creative tasks during your peak neural activity periods (9-11 AM and 2-4 PM). Would you like me to create an automated schedule optimization?
                    </div>
                </div>
                <div class="chat-input">
                    <div class="input-container">
                        <input type="text" class="bio-input" placeholder="Ask your cognitive assistant..." aria-label="Chat input">
                        <button class="send-btn" aria-label="Send message">
                            <span>→</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Learning Analytics Dashboard -->
            <div class="analytics-panel bio-panel">
                <div class="analytics-header">
                    <h3>Learning Analytics</h3>
                </div>
                <div class="analytics-content">
                    <div class="metric-card">
                        <div class="metric-value">87%</div>
                        <div class="metric-label">Cognitive Efficiency</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: 87%;"></div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">142</div>
                        <div class="metric-label">Neural Pathways Formed</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: 71%;"></div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">23</div>
                        <div class="metric-label">Skills Enhanced</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: 92%;"></div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">6.2h</div>
                        <div class="metric-label">Deep Focus Time</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: 77%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Context Awareness Panel -->
            <div class="context-panel bio-panel">
                <div class="context-header">
                    <h3>Context Awareness</h3>
                </div>
                <div class="context-content">
                    <div class="context-item">
                        <div class="context-icon">📅</div>
                        <div>
                            <div>Meeting in 15 minutes</div>
                            <small>Prepare research summary</small>
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-icon">🧠</div>
                        <div>
                            <div>High cognitive load detected</div>
                            <small>Break recommended</small>
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-icon">📊</div>
                        <div>
                            <div>Pattern analysis ready</div>
                            <small>Review insights</small>
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-icon">💡</div>
                        <div>
                            <div>Learning opportunity</div>
                            <small>New skill pathway detected</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Automation & Predictions -->
            <div class="automation-panel bio-panel">
                <div class="automation-header">
                    <h3>Intelligent Task Automation & Predictions</h3>
                </div>
                <div class="automation-content">
                    <div class="task-card">
                        <div class="task-title">Email Prioritization</div>
                        <div class="task-status">Auto-processing</div>
                        <div class="confidence-indicator">
                            <span>Confidence:</span>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="--confidence-level: 94%;"></div>
                            </div>
                            <span>94%</span>
                        </div>
                    </div>
                    <div class="task-card">
                        <div class="task-title">Calendar Optimization</div>
                        <div class="task-status">Predictive suggestions ready</div>
                        <div class="confidence-indicator">
                            <span>Confidence:</span>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="--confidence-level: 87%;"></div>
                            </div>
                            <span>87%</span>
                        </div>
                    </div>
                    <div class="task-card">
                        <div class="task-title">Learning Path Generation</div>
                        <div class="task-status">Analyzing neural patterns</div>
                        <div class="confidence-indicator">
                            <span>Confidence:</span>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="--confidence-level: 76%;"></div>
                            </div>
                            <span>76%</span>
                        </div>
                    </div>
                    <div class="task-card">
                        <div class="task-title">Productivity Forecasting</div>
                        <div class="task-status">Bio-rhythm analysis complete</div>
                        <div class="confidence-indicator">
                            <span>Confidence:</span>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="--confidence-level: 91%;"></div>
                            </div>
                            <span>91%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emotional Intelligence Indicator -->
        <div class="emotion-indicator" id="emotionIndicator" role="button" tabindex="0" aria-label="Current emotional state: Engaged">
            😊
        </div>

        <!-- Knowledge Graph Overlay -->
        <div class="knowledge-graph" id="knowledgeGraph" role="dialog" aria-labelledby="graphTitle" aria-hidden="true">
            <div class="graph-content">
                <div class="graph-header">
                    <h2 id="graphTitle">Knowledge Network Visualization</h2>
                    <button class="close-graph" aria-label="Close knowledge graph">×</button>
                </div>
                <div class="knowledge-nodes">
                    <div class="node" style="top: 20%; left: 30%;">ML</div>
                    <div class="node" style="top: 40%; left: 50%;">AI</div>
                    <div class="node" style="top: 60%; left: 20%;">Data</div>
                    <div class="node" style="top: 30%; left: 70%;">Logic</div>
                    <div class="node" style="top: 70%; left: 60%;">Pattern</div>
                    <div class="connection" style="top: 35%; left: 35%; width: 120px; transform: rotate(25deg);"></div>
                    <div class="connection" style="top: 50%; left: 25%; width: 180px; transform: rotate(-15deg);"></div>
                    <div class="connection" style="top: 45%; left: 55%; width: 100px; transform: rotate(45deg);"></div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Cognitive Assistant Core System
        class OrganicCognitiveAssistant {
            constructor() {
                this.initializeComponents();
                this.setupEventListeners();
                this.startBioRhythmSimulation();
                this.enablePredictiveAnalysis();
            }

            initializeComponents() {
                this.chatMessages = document.getElementById('chatMessages');
                this.emotionIndicator = document.getElementById('emotionIndicator');
                this.knowledgeGraph = document.getElementById('knowledgeGraph');
                
                // Simulated cognitive state
                this.cognitiveState = {
                    attention: 0.8,
                    mood: 'engaged',
                    learningRate: 0.75,
                    contextAwareness: 0.9,
                    taskComplexity: 0.6
                };

                // Learning analytics data
                this.learningData = {
                    sessions: 0,
                    pathwaysFormed: 142,
                    skillsEnhanced: 23,
                    focusTime: 6.2
                };
            }

            setupEventListeners() {
                // Chat input handling
                const chatInput = document.querySelector('.bio-input');
                const sendBtn = document.querySelector('.send-btn');
                
                sendBtn.addEventListener('click', () => this.sendMessage(chatInput.value));
                chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage(chatInput.value);
                });

                // Emotion indicator
                this.emotionIndicator.addEventListener('click', () => this.cycleEmotionalState());

                // Knowledge graph toggle
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'k' && e.ctrlKey) {
                        e.preventDefault();
                        this.toggleKnowledgeGraph();
                    }
                });

                // Close knowledge graph
                const closeBtn = document.querySelector('.close-graph');
                closeBtn.addEventListener('click', () => this.toggleKnowledgeGraph());

                // Context-aware task suggestions
                this.setupContextualSuggestions();
            }

            sendMessage(text) {
                if (!text.trim()) return;

                // Add user message
                this.addMessage(text, 'user');
                
                // Clear input
                document.querySelector('.bio-input').value = '';
                
                // Generate AI response with bio-tech context
                setTimeout(() => {
                    const response = this.generateContextualResponse(text);
                    this.addMessage(response, 'ai');
                    this.updateCognitiveState();
                }, 800 + Math.random() * 1200);
            }

            addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const srSpan = document.createElement('span');
                srSpan.className = 'sr-only';
                srSpan.textContent = sender === 'ai' ? 'AI Assistant:' : 'You:';
                
                messageDiv.appendChild(srSpan);
                messageDiv.appendChild(document.createTextNode(text));
                
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;

                // Trigger neural network activity
                this.simulateNeuralActivity();
            }

            generateContextualResponse(userInput) {
                const responses = [
                    "Based on your bio-rhythmic patterns, I suggest optimizing your workflow during peak neural efficiency periods.",
                    "I've detected elevated cognitive complexity in your request. Let me break this down into symbiotic sub-tasks.",
                    "Your learning pathways show strong adaptation. I'm updating my predictive models to better assist you.",
                    "Analyzing context... I see connections to your previous work patterns. Would you like me to create automated workflows?",
                    "Neural pattern analysis indicates you're in a prime state for creative problem-solving. How can I amplify this?",
                    "I'm integrating this new data into your knowledge graph. The connections are forming beautiful organic patterns.",
                    "Your emotional intelligence metrics suggest high engagement. I'm calibrating my responses to match your current state."
                ];
                
                return responses[Math.floor(Math.random() * responses.length)];
            }

            updateCognitiveState() {
                // Simulate dynamic cognitive state changes
                this.cognitiveState.attention = Math.max(0.3, this.cognitiveState.attention + (Math.random() - 0.5) * 0.2);
                this.cognitiveState.learningRate = Math.max(0.4, this.cognitiveState.learningRate + (Math.random() - 0.5) * 0.15);
                
                this.updateAnalytics();
                this.updateContextualSuggestions();
            }

            updateAnalytics() {
                // Update learning analytics with bio-tech inspired calculations
                this.learningData.pathwaysFormed += Math.floor(Math.random() * 3);
                this.learningData.focusTime += Math.random() * 0.1;
                
                // Update progress bars with organic growth
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const currentWidth = parseFloat(bar.style.getPropertyValue('--progress-width')) || 0;
                    const newWidth = Math.min(100, currentWidth + Math.random() * 2);
                    bar.style.setProperty('--progress-width', `${newWidth}%`);
                });
            }

            cycleEmotionalState() {
                const emotions = ['😊', '🤔', '💡', '🎯', '🌱', '⚡', '🧠'];
                const current = emotions.indexOf(this.emotionIndicator.textContent);
                const next = (current + 1) % emotions.length;
                
                this.emotionIndicator.textContent = emotions[next];
                
                // Update aria-label for accessibility
                const states = ['Engaged', 'Thoughtful', 'Insightful', 'Focused', 'Growing', 'Energized', 'Analytical'];
                this.emotionIndicator.setAttribute('aria-label', `Current emotional state: ${states[next]}`);
                
                // Trigger bio-tech effect
                this.triggerEmotionalTransition();
            }

            triggerEmotionalTransition() {
                // Create ripple effect
                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: fixed;
                    top: 60px;
                    right: 60px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: radial-gradient(circle, var(--organic-amber)50, transparent);
                    pointer-events: none;
                    z-index: 600;
                    animation: emotionalRipple 1s ease-out forwards;
                `;
                
                // Add keyframes for ripple effect
                if (!document.querySelector('#emotionalRippleStyles')) {
                    const style = document.createElement('style');
                    style.id = 'emotionalRippleStyles';
                    style.textContent = `
                        @keyframes emotionalRipple {
                            0% { transform: scale(1); opacity: 0.8; }
                            100% { transform: scale(4); opacity: 0; }
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                document.body.appendChild(ripple);
                setTimeout(() => ripple.remove(), 1000);
            }

            toggleKnowledgeGraph() {
                const isActive = this.knowledgeGraph.classList.contains('active');
                
                if (isActive) {
                    this.knowledgeGraph.classList.remove('active');
                    this.knowledgeGraph.setAttribute('aria-hidden', 'true');
                } else {
                    this.knowledgeGraph.classList.add('active');
                    this.knowledgeGraph.setAttribute('aria-hidden', 'false');
                    this.animateKnowledgeNodes();
                }
            }

            animateKnowledgeNodes() {
                const nodes = document.querySelectorAll('.node');
                nodes.forEach((node, index) => {
                    setTimeout(() => {
                        node.style.animation = 'none';
                        node.offsetHeight; // Trigger reflow
                        node.style.animation = 'nodeFloat 4s ease-in-out infinite';
                        node.style.animationDelay = `${index * 0.2}s`;
                    }, index * 100);
                });
            }

            setupContextualSuggestions() {
                // Simulate dynamic context awareness
                setInterval(() => {
                    this.updateContextItems();
                }, 5000);
            }

            updateContextItems() {
                const contextItems = document.querySelectorAll('.context-item');
                const suggestions = [
                    { icon: '🎯', title: 'Optimal focus detected', subtitle: 'Deep work recommended' },
                    { icon: '📈', title: 'Learning velocity high', subtitle: 'Expand skill tree' },
                    { icon: '🔄', title: 'Pattern evolution ready', subtitle: 'Synthesize connections' },
                    { icon: '⚡', title: 'Neural spike detected', subtitle: 'Creative breakthrough likely' },
                    { icon: '🌊', title: 'Flow state approaching', subtitle: 'Minimize interruptions' }
                ];
                
                contextItems.forEach((item, index) => {
                    if (Math.random() < 0.3) { // 30% chance to update each item
                        const suggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
                        const icon = item.querySelector('.context-icon');
                        const content = item.querySelector('div:last-child');
                        
                        icon.textContent = suggestion.icon;
                        content.querySelector('div').textContent = suggestion.title;
                        content.querySelector('small').textContent = suggestion.subtitle;
                        
                        // Trigger organic update animation
                        item.style.transform = 'scale(1.05)';
                        setTimeout(() => {
                            item.style.transform = 'scale(1)';
                        }, 200);
                    }
                });
            }

            startBioRhythmSimulation() {
                // Simulate biological rhythm effects on the interface
                setInterval(() => {
                    this.adjustBioRhythms();
                }, 2000);
            }

            adjustBioRhythms() {
                // Subtle bio-tech environmental adjustments
                const intensity = 0.5 + Math.sin(Date.now() / 5000) * 0.3;
                document.documentElement.style.setProperty('--bio-intensity', intensity);
                
                // Update neural background opacity
                const bgElement = document.body;
                if (bgElement) {
                    const opacity = 0.05 + Math.sin(Date.now() / 3000) * 0.05;
                    bgElement.style.setProperty('--neural-opacity', opacity);
                }
            }

            simulateNeuralActivity() {
                // Create temporary neural firing effect
                const indicators = document.querySelectorAll('.bio-indicator');
                indicators.forEach(indicator => {
                    indicator.style.animation = 'none';
                    indicator.offsetHeight; // Trigger reflow
                    indicator.style.animation = 'synapseFireBrightness 0.5s ease-out';
                });
            }

            enablePredictiveAnalysis() {
                // Simulate predictive task analysis
                setInterval(() => {
                    this.updateTaskPredictions();
                }, 3000);
            }

            updateTaskPredictions() {
                const taskCards = document.querySelectorAll('.task-card');
                taskCards.forEach(card => {
                    const confidenceBar = card.querySelector('.confidence-fill');
                    const confidenceText = card.querySelector('.confidence-indicator span:last-child');
                    
                    if (Math.random() < 0.4) { // 40% chance to update
                        const newConfidence = 60 + Math.random() * 35; // 60-95%
                        confidenceBar.style.setProperty('--confidence-level', `${newConfidence}%`);
                        confidenceText.textContent = `${Math.round(newConfidence)}%`;
                        
                        // Reset animation
                        confidenceBar.style.animation = 'none';
                        confidenceBar.offsetHeight;
                        confidenceBar.style.animation = 'confidenceGrow 1.5s ease-out';
                    }
                });
            }
        }

        // Initialize the Organic Tech Cognitive Assistant
        document.addEventListener('DOMContentLoaded', () => {
            const assistant = new OrganicCognitiveAssistant();
            
            // Add global keyboard shortcuts for power users
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'e':
                            e.preventDefault();
                            assistant.cycleEmotionalState();
                            break;
                        case 'Enter':
                            e.preventDefault();
                            document.querySelector('.bio-input').focus();
                            break;
                    }
                }
            });
            
            // Accessibility: Focus management
            const focusableElements = document.querySelectorAll('button, input, [tabindex="0"]');
            focusableElements.forEach(el => {
                el.addEventListener('focus', () => {
                    el.style.boxShadow = '0 0 0 3px var(--growth-lime)40';
                });
                el.addEventListener('blur', () => {
                    el.style.boxShadow = '';
                });
            });
        });

        // Bio-tech performance optimization
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                // Optimize neural network background patterns during idle time
                console.log('Organic Tech Cognitive Assistant: Neural pathways optimized');
            });
        }
    </script>
</body>
</html>