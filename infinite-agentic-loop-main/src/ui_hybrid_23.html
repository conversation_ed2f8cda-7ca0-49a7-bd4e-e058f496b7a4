<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glass Morphism Analytics Dashboard Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        /* Animated background orbs */
        .bg-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            opacity: 0.6;
            animation: float 20s infinite ease-in-out;
        }

        .orb-1 {
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, #ff006e, #8338ec);
            top: -200px;
            left: -200px;
            animation-delay: 0s;
        }

        .orb-2 {
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, #3a86ff, #06ffa5);
            bottom: -150px;
            right: -150px;
            animation-delay: 5s;
        }

        .orb-3 {
            width: 350px;
            height: 350px;
            background: radial-gradient(circle, #ffbe0b, #fb5607);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(50px, -50px) scale(1.1); }
            50% { transform: translate(-50px, 50px) scale(0.9); }
            75% { transform: translate(30px, 30px) scale(1.05); }
        }

        /* Main dashboard container */
        .dashboard-widget {
            width: 90%;
            max-width: 1200px;
            height: 600px;
            position: relative;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            padding: 20px;
        }

        /* Glass panel base */
        .glass-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px 0 rgba(31, 38, 135, 0.37),
                inset 0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* Light refraction effect */
        .glass-panel::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 70%
            );
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        /* Main content area */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Header section */
        .header-section {
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .title-area h1 {
            color: #fff;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .title-area p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        /* Action buttons */
        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .glass-button {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 10px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .glass-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(255, 255, 255, 0.2);
        }

        .glass-button:active {
            transform: translateY(0);
        }

        /* Chart container */
        .chart-container {
            flex: 1;
            padding: 25px;
            position: relative;
            z-index: 1;
        }

        .chart-wrapper {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* Canvas for chart */
        #salesChart {
            width: 100%;
            height: 100%;
        }

        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Filter panel */
        .filter-panel {
            padding: 25px;
            position: relative;
            z-index: 1;
        }

        .filter-panel h3 {
            color: #fff;
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .filter-group {
            margin-bottom: 20px;
        }

        .filter-group label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            display: block;
            margin-bottom: 8px;
        }

        .glass-select {
            width: 100%;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .glass-select:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .glass-select option {
            background: #764ba2;
            color: #fff;
        }

        /* Alerts panel */
        .alerts-panel {
            padding: 25px;
            position: relative;
            z-index: 1;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .alerts-panel h3 {
            color: #fff;
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .alert-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .alert-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .alert-icon {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .alert-icon.success { background: #06ffa5; }
        .alert-icon.warning { background: #ffbe0b; }
        .alert-icon.danger { background: #ff006e; }

        .alert-title {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-message {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            line-height: 1.4;
        }

        .alert-time {
            color: rgba(255, 255, 255, 0.5);
            font-size: 11px;
            margin-top: 5px;
        }

        /* Settings modal */
        .settings-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .settings-modal.active {
            display: flex;
        }

        .settings-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .settings-header h2 {
            color: #fff;
            font-size: 24px;
            font-weight: 600;
        }

        .close-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-item label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            display: block;
            margin-bottom: 8px;
        }

        .glass-checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }

        .glass-checkbox input {
            width: 20px;
            height: 20px;
            accent-color: #06ffa5;
            cursor: pointer;
        }

        .glass-checkbox span {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        /* Tooltip */
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip.show {
            opacity: 1;
        }

        /* Loading animation */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .loading.active {
            display: block;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-widget {
                grid-template-columns: 1fr;
                height: auto;
            }

            .sidebar {
                grid-row: 2;
            }
        }
    </style>
</head>
<body>
    <!-- Background orbs -->
    <div class="bg-orb orb-1"></div>
    <div class="bg-orb orb-2"></div>
    <div class="bg-orb orb-3"></div>

    <!-- Main dashboard widget -->
    <div class="dashboard-widget">
        <!-- Main content area -->
        <div class="main-content">
            <!-- Header section -->
            <div class="glass-panel header-section">
                <div class="title-area">
                    <h1>Revenue Analytics</h1>
                    <p>Real-time performance monitoring</p>
                </div>
                <div class="action-buttons">
                    <button class="glass-button" onclick="refreshData()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"/>
                        </svg>
                        Refresh
                    </button>
                    <button class="glass-button" onclick="exportData()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3"/>
                        </svg>
                        Export
                    </button>
                    <button class="glass-button" onclick="openSettings()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m4.22-10.22l4.24 4.24m-4.24 4.24l4.24 4.24M6.34 6.34L2.1 2.1m4.24 15.8l-4.24 4.24M1 12h6m6 0h6"/>
                        </svg>
                        Settings
                    </button>
                </div>
            </div>

            <!-- Chart container -->
            <div class="glass-panel chart-container">
                <div class="chart-wrapper">
                    <canvas id="salesChart"></canvas>
                </div>
                <div class="loading" id="chartLoading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Filter panel -->
            <div class="glass-panel filter-panel">
                <h3>Filters</h3>
                <div class="filter-group">
                    <label>Time Period</label>
                    <select class="glass-select" id="periodFilter" onchange="applyFilters()">
                        <option value="7">Last 7 Days</option>
                        <option value="30" selected>Last 30 Days</option>
                        <option value="90">Last 90 Days</option>
                        <option value="365">Last Year</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Region</label>
                    <select class="glass-select" id="regionFilter" onchange="applyFilters()">
                        <option value="all">All Regions</option>
                        <option value="north">North America</option>
                        <option value="europe">Europe</option>
                        <option value="asia">Asia Pacific</option>
                        <option value="latin">Latin America</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Product Category</label>
                    <select class="glass-select" id="categoryFilter" onchange="applyFilters()">
                        <option value="all">All Categories</option>
                        <option value="electronics">Electronics</option>
                        <option value="software">Software</option>
                        <option value="services">Services</option>
                        <option value="hardware">Hardware</option>
                    </select>
                </div>
            </div>

            <!-- Alerts panel -->
            <div class="glass-panel alerts-panel">
                <h3>Alerts</h3>
                <div id="alertsList">
                    <div class="alert-item">
                        <div class="alert-header">
                            <div class="alert-icon success"></div>
                            <div class="alert-title">Revenue Target Achieved</div>
                        </div>
                        <div class="alert-message">Q4 revenue exceeded projections by 12%</div>
                        <div class="alert-time">2 hours ago</div>
                    </div>
                    <div class="alert-item">
                        <div class="alert-header">
                            <div class="alert-icon warning"></div>
                            <div class="alert-title">Inventory Alert</div>
                        </div>
                        <div class="alert-message">Electronics category stock running low in APAC region</div>
                        <div class="alert-time">5 hours ago</div>
                    </div>
                    <div class="alert-item">
                        <div class="alert-header">
                            <div class="alert-icon danger"></div>
                            <div class="alert-title">System Maintenance</div>
                        </div>
                        <div class="alert-message">Scheduled maintenance window: Sunday 2-4 AM EST</div>
                        <div class="alert-time">Yesterday</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings modal -->
    <div class="settings-modal" id="settingsModal">
        <div class="settings-content">
            <div class="settings-header">
                <h2>Dashboard Settings</h2>
                <button class="close-button" onclick="closeSettings()">✕</button>
            </div>
            <div class="setting-item">
                <label>Display Options</label>
                <div class="glass-checkbox">
                    <input type="checkbox" id="showDataLabels" checked>
                    <span>Show data labels on chart</span>
                </div>
            </div>
            <div class="setting-item">
                <div class="glass-checkbox">
                    <input type="checkbox" id="enableAnimations" checked>
                    <span>Enable chart animations</span>
                </div>
            </div>
            <div class="setting-item">
                <div class="glass-checkbox">
                    <input type="checkbox" id="autoRefresh">
                    <span>Auto-refresh every 5 minutes</span>
                </div>
            </div>
            <div class="setting-item">
                <label>Chart Type</label>
                <select class="glass-select" id="chartType">
                    <option value="line">Line Chart</option>
                    <option value="bar">Bar Chart</option>
                    <option value="area">Area Chart</option>
                </select>
            </div>
            <div class="setting-item">
                <label>Theme</label>
                <select class="glass-select" id="themeSelect">
                    <option value="glass">Glass Morphism</option>
                    <option value="dark">Dark Crystal</option>
                    <option value="light">Light Prism</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Tooltip -->
    <div class="tooltip" id="tooltip"></div>

    <script>
        // Chart initialization
        let chart = null;
        let chartData = {
            labels: [],
            revenue: [],
            orders: [],
            users: []
        };

        // Generate sample data
        function generateData(days) {
            const data = {
                labels: [],
                revenue: [],
                orders: [],
                users: []
            };

            const now = new Date();
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                data.labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
                
                // Generate realistic data with trends
                const baseRevenue = 50000 + Math.random() * 30000;
                const trend = (days - i) * 100;
                data.revenue.push(Math.round(baseRevenue + trend + Math.random() * 10000));
                data.orders.push(Math.round(100 + Math.random() * 50 + (days - i) * 2));
                data.users.push(Math.round(1000 + Math.random() * 500 + (days - i) * 10));
            }

            return data;
        }

        // Initialize chart
        function initChart() {
            const canvas = document.getElementById('salesChart');
            const ctx = canvas.getContext('2d');
            
            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            chartData = generateData(30);
            drawChart(ctx);
        }

        // Draw chart
        function drawChart(ctx) {
            const width = ctx.canvas.width;
            const height = ctx.canvas.height;
            const padding = 40;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Find max value
            const maxRevenue = Math.max(...chartData.revenue);
            const scale = chartHeight / maxRevenue;

            // Draw grid lines
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            for (let i = 0; i <= 5; i++) {
                const y = padding + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();

                // Y-axis labels
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
                ctx.textAlign = 'right';
                const value = Math.round(maxRevenue - (maxRevenue / 5) * i);
                ctx.fillText(`$${(value / 1000).toFixed(0)}k`, padding - 10, y + 4);
            }

            // Draw data line
            ctx.strokeStyle = '#06ffa5';
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // Create gradient for area fill
            const gradient = ctx.createLinearGradient(0, padding, 0, height - padding);
            gradient.addColorStop(0, 'rgba(6, 255, 165, 0.3)');
            gradient.addColorStop(1, 'rgba(6, 255, 165, 0)');

            // Draw area
            ctx.beginPath();
            chartData.revenue.forEach((value, index) => {
                const x = padding + (chartWidth / (chartData.revenue.length - 1)) * index;
                const y = padding + chartHeight - (value * scale);
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });

            // Complete area path
            ctx.lineTo(width - padding, height - padding);
            ctx.lineTo(padding, height - padding);
            ctx.closePath();
            ctx.fillStyle = gradient;
            ctx.fill();

            // Draw line
            ctx.beginPath();
            chartData.revenue.forEach((value, index) => {
                const x = padding + (chartWidth / (chartData.revenue.length - 1)) * index;
                const y = padding + chartHeight - (value * scale);
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            ctx.stroke();

            // Draw points
            ctx.fillStyle = '#06ffa5';
            chartData.revenue.forEach((value, index) => {
                const x = padding + (chartWidth / (chartData.revenue.length - 1)) * index;
                const y = padding + chartHeight - (value * scale);
                
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, Math.PI * 2);
                ctx.fill();
            });

            // X-axis labels
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'center';
            
            const labelInterval = Math.ceil(chartData.labels.length / 10);
            chartData.labels.forEach((label, index) => {
                if (index % labelInterval === 0) {
                    const x = padding + (chartWidth / (chartData.labels.length - 1)) * index;
                    ctx.fillText(label, x, height - padding + 20);
                }
            });
        }

        // Apply filters
        function applyFilters() {
            const period = document.getElementById('periodFilter').value;
            const loading = document.getElementById('chartLoading');
            
            loading.classList.add('active');
            
            setTimeout(() => {
                chartData = generateData(parseInt(period));
                const canvas = document.getElementById('salesChart');
                const ctx = canvas.getContext('2d');
                drawChart(ctx);
                loading.classList.remove('active');
                
                // Add new alert
                addAlert('success', 'Filters Applied', 'Data updated based on selected filters');
            }, 500);
        }

        // Refresh data
        function refreshData() {
            const loading = document.getElementById('chartLoading');
            loading.classList.add('active');
            
            setTimeout(() => {
                applyFilters();
                addAlert('success', 'Data Refreshed', 'Dashboard updated with latest information');
            }, 1000);
        }

        // Export data
        function exportData() {
            const data = {
                period: document.getElementById('periodFilter').value,
                region: document.getElementById('regionFilter').value,
                category: document.getElementById('categoryFilter').value,
                data: chartData
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics-export-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            addAlert('success', 'Export Complete', 'Data exported successfully');
        }

        // Settings functions
        function openSettings() {
            document.getElementById('settingsModal').classList.add('active');
        }

        function closeSettings() {
            document.getElementById('settingsModal').classList.remove('active');
        }

        // Add alert
        function addAlert(type, title, message) {
            const alertsList = document.getElementById('alertsList');
            const alert = document.createElement('div');
            alert.className = 'alert-item';
            alert.innerHTML = `
                <div class="alert-header">
                    <div class="alert-icon ${type}"></div>
                    <div class="alert-title">${title}</div>
                </div>
                <div class="alert-message">${message}</div>
                <div class="alert-time">Just now</div>
            `;
            
            alertsList.insertBefore(alert, alertsList.firstChild);
            
            // Remove oldest alert if more than 5
            if (alertsList.children.length > 5) {
                alertsList.removeChild(alertsList.lastChild);
            }
        }

        // Auto-refresh
        let autoRefreshInterval;
        document.getElementById('autoRefresh').addEventListener('change', function(e) {
            if (e.target.checked) {
                autoRefreshInterval = setInterval(refreshData, 300000); // 5 minutes
                addAlert('success', 'Auto-refresh Enabled', 'Dashboard will refresh every 5 minutes');
            } else {
                clearInterval(autoRefreshInterval);
                addAlert('warning', 'Auto-refresh Disabled', 'Manual refresh required for updates');
            }
        });

        // Initialize on load
        window.addEventListener('load', () => {
            initChart();
            
            // Resize handler
            window.addEventListener('resize', () => {
                const canvas = document.getElementById('salesChart');
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
                const ctx = canvas.getContext('2d');
                drawChart(ctx);
            });
        });

        // Close settings on outside click
        document.getElementById('settingsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSettings();
            }
        });
    </script>
</body>
</html>