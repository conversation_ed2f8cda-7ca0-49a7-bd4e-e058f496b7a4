<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steampunk Process Controller - Patent Submission System</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600&family=Crimson+Text:ital@0;1&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Crimson Text', serif;
            background: #1a1611;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(139, 69, 19, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 50%, rgba(184, 134, 11, 0.2) 0%, transparent 50%),
                repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(139, 69, 19, 0.1) 2px, rgba(139, 69, 19, 0.1) 4px);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #f4e4bc;
            overflow-x: hidden;
        }
        
        .controller-frame {
            width: 90%;
            max-width: 900px;
            background: linear-gradient(135deg, #2c241b 0%, #1a1611 100%);
            border: 12px solid transparent;
            border-image: linear-gradient(45deg, #b8860b, #cd853f, #b8860b) 1;
            border-radius: 20px;
            padding: 40px;
            position: relative;
            box-shadow: 
                0 0 50px rgba(184, 134, 11, 0.3),
                inset 0 0 30px rgba(0, 0, 0, 0.5),
                0 20px 40px rgba(0, 0, 0, 0.8);
        }
        
        .controller-frame::before,
        .controller-frame::after {
            content: '⚙';
            position: absolute;
            font-size: 40px;
            color: #b8860b;
            animation: rotateSlow 20s linear infinite;
        }
        
        .controller-frame::before {
            top: 10px;
            left: 10px;
        }
        
        .controller-frame::after {
            bottom: 10px;
            right: 10px;
            animation-direction: reverse;
        }
        
        .patent-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .patent-header h1 {
            font-family: 'Cinzel', serif;
            font-size: 2.5em;
            color: #cd853f;
            text-shadow: 
                2px 2px 4px rgba(0, 0, 0, 0.8),
                0 0 20px rgba(184, 134, 11, 0.5);
            margin-bottom: 10px;
            letter-spacing: 3px;
        }
        
        .patent-header .subtitle {
            font-size: 1.1em;
            font-style: italic;
            color: #b8860b;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }
        
        .gear-progress {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            position: relative;
            height: 120px;
        }
        
        .gear-item {
            width: 80px;
            height: 80px;
            position: relative;
            margin: 0 20px;
        }
        
        .gear {
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, #b8860b 30%, #8b6914 70%);
            border-radius: 50%;
            position: relative;
            box-shadow: 
                inset 0 0 20px rgba(0, 0, 0, 0.5),
                0 0 20px rgba(184, 134, 11, 0.5);
            transition: all 0.5s ease;
        }
        
        .gear::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: 
                conic-gradient(
                    from 0deg,
                    transparent 0deg 30deg,
                    #8b6914 30deg 60deg,
                    transparent 60deg 90deg,
                    #8b6914 90deg 120deg,
                    transparent 120deg 150deg,
                    #8b6914 150deg 180deg,
                    transparent 180deg 210deg,
                    #8b6914 210deg 240deg,
                    transparent 240deg 270deg,
                    #8b6914 270deg 300deg,
                    transparent 300deg 330deg,
                    #8b6914 330deg 360deg
                );
            border-radius: 50%;
            animation: rotateSlow 10s linear infinite paused;
        }
        
        .gear-item.active .gear::before {
            animation-play-state: running;
        }
        
        .gear-item.complete .gear {
            background: radial-gradient(circle, #ffd700 30%, #b8860b 70%);
            box-shadow: 
                inset 0 0 20px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(255, 215, 0, 0.8);
        }
        
        .gear-number {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1.5em;
            color: #1a1611;
            text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
        }
        
        .gear-pipe {
            position: absolute;
            width: 40px;
            height: 8px;
            background: linear-gradient(90deg, #8b6914 0%, #cd853f 50%, #8b6914 100%);
            top: 50%;
            right: -40px;
            transform: translateY(-50%);
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.5),
                0 2px 4px rgba(184, 134, 11, 0.3);
        }
        
        .gear-pipe::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.8), transparent);
            animation: steamFlow 2s ease-in-out infinite;
            opacity: 0;
        }
        
        .gear-item.active ~ .gear-item .gear-pipe::after {
            opacity: 1;
        }
        
        .form-section {
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid #8b6914;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            position: relative;
            display: none;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
        }
        
        .form-section.active {
            display: block;
            animation: steamReveal 0.8s ease-out;
        }
        
        .form-section h2 {
            font-family: 'Cinzel', serif;
            color: #ffd700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #cd853f;
            font-size: 1.1em;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            background: rgba(0, 0, 0, 0.4);
            border: 2px solid #8b6914;
            border-radius: 5px;
            color: #f4e4bc;
            font-family: 'Crimson Text', serif;
            font-size: 1em;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .action-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.3) 0%, rgba(0, 0, 0, 0.3) 100%);
            border: 2px solid #8b6914;
            border-radius: 10px;
            position: relative;
        }
        
        .pressure-gauge {
            width: 100px;
            height: 100px;
            position: relative;
            background: radial-gradient(circle, #2c241b 0%, #1a1611 70%);
            border-radius: 50%;
            border: 4px solid #b8860b;
            box-shadow: 
                inset 0 0 20px rgba(0, 0, 0, 0.8),
                0 0 20px rgba(184, 134, 11, 0.3);
        }
        
        .gauge-needle {
            position: absolute;
            width: 2px;
            height: 40px;
            background: #ff4444;
            bottom: 50%;
            left: 50%;
            transform-origin: bottom center;
            transform: translateX(-50%) rotate(-90deg);
            transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
        }
        
        .gauge-needle::before {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            background: radial-gradient(circle, #b8860b, #8b6914);
            border-radius: 50%;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .gauge-markings {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }
        
        .gauge-marking {
            position: absolute;
            width: 2px;
            height: 10px;
            background: #b8860b;
            top: 5px;
            left: 50%;
            transform-origin: center 45px;
        }
        
        .gauge-label {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8em;
            color: #b8860b;
            font-family: 'Cinzel', serif;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .brass-button {
            padding: 15px 30px;
            background: linear-gradient(135deg, #b8860b 0%, #cd853f 50%, #b8860b 100%);
            border: 3px solid #8b6914;
            border-radius: 50px;
            color: #1a1611;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1.1em;
            cursor: pointer;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 2px;
            transition: all 0.3s ease;
            box-shadow: 
                0 5px 15px rgba(0, 0, 0, 0.5),
                inset 0 -2px 5px rgba(0, 0, 0, 0.3);
        }
        
        .brass-button:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 7px 20px rgba(0, 0, 0, 0.6),
                inset 0 -2px 5px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(255, 215, 0, 0.5);
        }
        
        .brass-button:active {
            transform: translateY(0);
            box-shadow: 
                0 3px 10px rgba(0, 0, 0, 0.5),
                inset 0 2px 5px rgba(0, 0, 0, 0.3);
        }
        
        .brass-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .steam-effect {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .steam-particle {
            position: absolute;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
            border-radius: 50%;
            animation: steamRise 3s ease-out infinite;
            opacity: 0;
        }
        
        @keyframes rotateSlow {
            to { transform: rotate(360deg); }
        }
        
        @keyframes steamFlow {
            0% { transform: translateX(-100%); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }
        
        @keyframes steamReveal {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes steamRise {
            0% {
                bottom: 0;
                opacity: 0;
                transform: translateX(0) scale(0.5);
            }
            20% {
                opacity: 0.8;
            }
            100% {
                bottom: 100%;
                opacity: 0;
                transform: translateX(20px) scale(1.5);
            }
        }
        
        .ornate-divider {
            text-align: center;
            margin: 30px 0;
            font-size: 1.5em;
            color: #8b6914;
            text-shadow: 0 0 10px rgba(184, 134, 11, 0.5);
        }
        
        .success-message {
            display: none;
            text-align: center;
            padding: 40px;
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid #ffd700;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .success-message h3 {
            font-family: 'Cinzel', serif;
            color: #ffd700;
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }
        
        .success-message p {
            font-size: 1.2em;
            color: #cd853f;
        }
        
        .patent-number {
            font-family: 'Cinzel', serif;
            font-size: 1.5em;
            color: #ffd700;
            margin-top: 20px;
            letter-spacing: 3px;
        }
    </style>
</head>
<body>
    <div class="controller-frame">
        <div class="patent-header">
            <h1>Victorian Patent Registry</h1>
            <p class="subtitle">Automated Invention Submission Apparatus</p>
        </div>
        
        <div class="gear-progress">
            <div class="gear-item active" data-step="1">
                <div class="gear">
                    <span class="gear-number">I</span>
                </div>
                <div class="gear-pipe"></div>
            </div>
            <div class="gear-item" data-step="2">
                <div class="gear">
                    <span class="gear-number">II</span>
                </div>
                <div class="gear-pipe"></div>
            </div>
            <div class="gear-item" data-step="3">
                <div class="gear">
                    <span class="gear-number">III</span>
                </div>
                <div class="gear-pipe"></div>
            </div>
            <div class="gear-item" data-step="4">
                <div class="gear">
                    <span class="gear-number">IV</span>
                </div>
            </div>
        </div>
        
        <div class="ornate-divider">⚙ ◆ ⚙ ◆ ⚙</div>
        
        <!-- Step 1: Inventor Information -->
        <div class="form-section active" data-step="1">
            <h2>Phase I: Inventor's Credentials</h2>
            <div class="form-group">
                <label>Full Name of Inventor</label>
                <input type="text" id="inventorName" placeholder="Sir/Madam..." required>
            </div>
            <div class="form-group">
                <label>Workshop Address</label>
                <input type="text" id="workshopAddress" placeholder="123 Cogwheel Lane..." required>
            </div>
            <div class="form-group">
                <label>Guild Membership Number</label>
                <input type="text" id="guildNumber" placeholder="GUILD-XXXX" required>
            </div>
        </div>
        
        <!-- Step 2: Invention Details -->
        <div class="form-section" data-step="2">
            <h2>Phase II: Contraption Specifications</h2>
            <div class="form-group">
                <label>Invention Title</label>
                <input type="text" id="inventionTitle" placeholder="Automated Steam-Powered..." required>
            </div>
            <div class="form-group">
                <label>Category of Innovation</label>
                <select id="category" required>
                    <option value="">Select Category...</option>
                    <option value="steam">Steam Propulsion</option>
                    <option value="clockwork">Clockwork Mechanisms</option>
                    <option value="electrical">Electrical Apparatus</option>
                    <option value="mechanical">Mechanical Engineering</option>
                    <option value="aether">Aetheric Sciences</option>
                </select>
            </div>
            <div class="form-group">
                <label>Primary Function</label>
                <textarea id="primaryFunction" rows="4" placeholder="Describe the primary function..." required></textarea>
            </div>
        </div>
        
        <!-- Step 3: Technical Drawings -->
        <div class="form-section" data-step="3">
            <h2>Phase III: Technical Schematics</h2>
            <div class="form-group">
                <label>Blueprint Reference Number</label>
                <input type="text" id="blueprintRef" placeholder="BP-XXXX" required>
            </div>
            <div class="form-group">
                <label>Material Components</label>
                <textarea id="materials" rows="3" placeholder="List primary materials..." required></textarea>
            </div>
            <div class="form-group">
                <label>Power Source</label>
                <select id="powerSource" required>
                    <option value="">Select Power Source...</option>
                    <option value="steam">Steam Engine</option>
                    <option value="spring">Mainspring Mechanism</option>
                    <option value="coal">Coal Combustion</option>
                    <option value="electricity">Galvanic Cell</option>
                    <option value="manual">Manual Crank</option>
                </select>
            </div>
        </div>
        
        <!-- Step 4: Final Declaration -->
        <div class="form-section" data-step="4">
            <h2>Phase IV: Inventor's Declaration</h2>
            <div class="form-group">
                <label>Oath of Originality</label>
                <textarea id="oath" rows="4" placeholder="I hereby declare that this invention..." required></textarea>
            </div>
            <div class="form-group">
                <label>Proposed Patent Duration</label>
                <select id="duration" required>
                    <option value="">Select Duration...</option>
                    <option value="7">7 Years</option>
                    <option value="14">14 Years</option>
                    <option value="21">21 Years</option>
                </select>
            </div>
            <div class="form-group">
                <label>Date of Submission</label>
                <input type="date" id="submissionDate" required>
            </div>
        </div>
        
        <div class="action-panel">
            <div class="pressure-gauge">
                <div class="gauge-markings">
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(0deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(30deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(60deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(90deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(120deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(150deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(180deg);"></div>
                </div>
                <div class="gauge-needle" id="pressureNeedle"></div>
                <div class="gauge-label">Pressure</div>
            </div>
            
            <button class="brass-button" id="prevButton" onclick="navigateStep(-1)" disabled>
                Previous
            </button>
            
            <button class="brass-button" id="nextButton" onclick="navigateStep(1)">
                Next Phase
            </button>
            
            <div class="pressure-gauge">
                <div class="gauge-markings">
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(0deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(30deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(60deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(90deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(120deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(150deg);"></div>
                    <div class="gauge-marking" style="transform: translateX(-50%) rotate(180deg);"></div>
                </div>
                <div class="gauge-needle" id="progressNeedle"></div>
                <div class="gauge-label">Progress</div>
            </div>
        </div>
        
        <div class="success-message" id="successMessage">
            <h3>Patent Successfully Registered!</h3>
            <p>Your invention has been catalogued in the Royal Registry</p>
            <div class="patent-number" id="patentNumber"></div>
        </div>
        
        <div class="steam-effect" id="steamEffect"></div>
    </div>
    
    <script>
        let currentStep = 1;
        const totalSteps = 4;
        let formData = {};
        
        function navigateStep(direction) {
            // Validate current step before moving
            if (direction > 0 && !validateCurrentStep()) {
                showPressureSpike();
                return;
            }
            
            // Save current step data
            saveStepData();
            
            // Update step
            currentStep += direction;
            currentStep = Math.max(1, Math.min(currentStep, totalSteps));
            
            // Update UI
            updateStepDisplay();
            updateGauges();
            
            // Check if final step
            if (currentStep === totalSteps && direction > 0) {
                document.getElementById('nextButton').textContent = 'Submit Patent';
                document.getElementById('nextButton').onclick = submitPatent;
            } else {
                document.getElementById('nextButton').textContent = 'Next Phase';
                document.getElementById('nextButton').onclick = () => navigateStep(1);
            }
        }
        
        function updateStepDisplay() {
            // Update form sections
            document.querySelectorAll('.form-section').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelector(`.form-section[data-step="${currentStep}"]`).classList.add('active');
            
            // Update gears
            document.querySelectorAll('.gear-item').forEach((gear, index) => {
                const stepNum = index + 1;
                gear.classList.remove('active', 'complete');
                if (stepNum < currentStep) {
                    gear.classList.add('complete');
                } else if (stepNum === currentStep) {
                    gear.classList.add('active');
                }
            });
            
            // Update buttons
            document.getElementById('prevButton').disabled = currentStep === 1;
            
            // Create steam effect
            createSteamBurst();
        }
        
        function updateGauges() {
            const progressAngle = (currentStep - 1) / (totalSteps - 1) * 180 - 90;
            document.getElementById('progressNeedle').style.transform = 
                `translateX(-50%) rotate(${progressAngle}deg)`;
            
            // Random pressure fluctuation
            const pressureAngle = Math.random() * 60 + 30 - 90;
            document.getElementById('pressureNeedle').style.transform = 
                `translateX(-50%) rotate(${pressureAngle}deg)`;
        }
        
        function validateCurrentStep() {
            const section = document.querySelector(`.form-section[data-step="${currentStep}"]`);
            const inputs = section.querySelectorAll('input[required], textarea[required], select[required]');
            
            for (let input of inputs) {
                if (!input.value.trim()) {
                    input.focus();
                    input.style.borderColor = '#ff4444';
                    setTimeout(() => {
                        input.style.borderColor = '#8b6914';
                    }, 2000);
                    return false;
                }
            }
            return true;
        }
        
        function saveStepData() {
            const section = document.querySelector(`.form-section[data-step="${currentStep}"]`);
            const inputs = section.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                formData[input.id] = input.value;
            });
        }
        
        function showPressureSpike() {
            const pressureNeedle = document.getElementById('pressureNeedle');
            pressureNeedle.style.transform = 'translateX(-50%) rotate(90deg)';
            setTimeout(() => {
                updateGauges();
            }, 500);
        }
        
        function createSteamBurst() {
            const steamEffect = document.getElementById('steamEffect');
            
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'steam-particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 0.5 + 's';
                    steamEffect.appendChild(particle);
                    
                    setTimeout(() => particle.remove(), 3000);
                }, i * 100);
            }
        }
        
        function submitPatent() {
            if (!validateCurrentStep()) {
                showPressureSpike();
                return;
            }
            
            saveStepData();
            
            // Show maximum pressure
            document.getElementById('pressureNeedle').style.transform = 'translateX(-50%) rotate(90deg)';
            document.getElementById('progressNeedle').style.transform = 'translateX(-50%) rotate(90deg)';
            
            // Create intense steam effect
            for (let i = 0; i < 20; i++) {
                setTimeout(() => createSteamBurst(), i * 100);
            }
            
            // Generate patent number
            const patentNumber = `PAT-${Date.now().toString(36).toUpperCase()}-VICT`;
            document.getElementById('patentNumber').textContent = patentNumber;
            
            // Hide form and show success
            setTimeout(() => {
                document.querySelectorAll('.form-section').forEach(section => {
                    section.style.display = 'none';
                });
                document.querySelector('.action-panel').style.display = 'none';
                document.getElementById('successMessage').style.display = 'block';
                
                // Mark all gears as complete
                document.querySelectorAll('.gear-item').forEach(gear => {
                    gear.classList.add('complete');
                    gear.classList.remove('active');
                });
            }, 2000);
        }
        
        // Initialize gauges with random starting positions
        document.addEventListener('DOMContentLoaded', () => {
            updateGauges();
            
            // Set today's date as default
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('submissionDate').value = today;
            
            // Create initial steam particles
            setInterval(createSteamBurst, 5000);
        });
        
        // Add some Victorian flair to form interactions
        document.querySelectorAll('input, textarea, select').forEach(element => {
            element.addEventListener('focus', () => {
                createSteamBurst();
            });
        });
    </script>
</body>
</html>