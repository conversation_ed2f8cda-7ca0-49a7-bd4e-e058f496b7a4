<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Living Circuit Interface - Neural Data Explorer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Monaco', 'Courier New', monospace;
            background: #0a0a0f;
            color: #e0f7ff;
            overflow: hidden;
            position: relative;
        }

        /* Neural Circuit Background */
        .circuit-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .circuit-path {
            stroke: #00ff88;
            stroke-width: 2;
            fill: none;
            filter: drop-shadow(0 0 3px #00ff88);
            opacity: 0.3;
        }

        .pulse {
            fill: #00ffff;
            filter: drop-shadow(0 0 10px #00ffff);
        }

        /* Main Container */
        .neural-container {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        /* Header - Neural Core */
        .neural-core {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .core-title {
            font-size: 2.5em;
            background: linear-gradient(45deg, #00ff88, #00ffff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            animation: pulse-glow 2s ease-in-out infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { filter: brightness(1) drop-shadow(0 0 20px rgba(0, 255, 255, 0.5)); }
            50% { filter: brightness(1.2) drop-shadow(0 0 40px rgba(0, 255, 255, 0.8)); }
        }

        /* Data Stream Grid */
        .data-grid {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        /* Synaptic Input Panel */
        .synaptic-panel {
            background: rgba(0, 20, 40, 0.3);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .synaptic-panel::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ff88, #00ffff, #ff00ff, #00ff88);
            border-radius: 15px;
            opacity: 0;
            animation: border-pulse 4s linear infinite;
            z-index: -1;
        }

        @keyframes border-pulse {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.5; }
        }

        .panel-title {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #00ffff;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        /* Neural Input Fields */
        .neural-input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            background: rgba(0, 50, 80, 0.2);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            color: #e0f7ff;
            font-family: inherit;
            transition: all 0.3s ease;
            position: relative;
        }

        .neural-input:focus {
            outline: none;
            border-color: #00ffff;
            background: rgba(0, 100, 150, 0.3);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5), inset 0 0 10px rgba(0, 255, 255, 0.2);
        }

        .input-synapse {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .neural-input:focus + .input-synapse {
            opacity: 1;
            animation: synapse-fire 1s ease-in-out infinite;
        }

        @keyframes synapse-fire {
            0%, 100% { 
                box-shadow: 0 0 5px #00ff88; 
                transform: translateY(-50%) scale(1);
            }
            50% { 
                box-shadow: 0 0 20px #00ff88, 0 0 40px #00ff88; 
                transform: translateY(-50%) scale(1.5);
            }
        }

        /* Data Explorer - Central Neural Network */
        .neural-explorer {
            background: rgba(0, 20, 40, 0.2);
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 20px;
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .data-nodes {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .data-node {
            background: rgba(0, 50, 100, 0.2);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-node::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(0, 255, 255, 0.8), transparent);
            transition: all 0.5s ease;
            transform: translate(-50%, -50%);
        }

        .data-node:hover::before {
            width: 100%;
            height: 100%;
        }

        .data-node:hover {
            border-color: #00ffff;
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        }

        .node-value {
            font-size: 2em;
            font-weight: bold;
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .node-label {
            font-size: 0.9em;
            margin-top: 5px;
            opacity: 0.8;
        }

        /* Neural Activity Monitor */
        .activity-monitor {
            background: rgba(0, 20, 40, 0.3);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 15px;
            padding: 20px;
        }

        .signal-wave {
            height: 100px;
            position: relative;
            overflow: hidden;
            margin: 15px 0;
        }

        .wave-path {
            stroke: #00ffff;
            stroke-width: 2;
            fill: none;
            filter: drop-shadow(0 0 5px #00ffff);
        }

        /* Adaptive Learning Indicators */
        .learning-indicator {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }

        .synapse-strength {
            flex: 1;
            height: 6px;
            background: rgba(0, 50, 80, 0.3);
            border-radius: 3px;
            margin: 0 10px;
            position: relative;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #00ffff);
            width: 0%;
            transition: width 0.5s ease;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        /* Bio-luminescent Buttons */
        .bio-button {
            padding: 12px 24px;
            background: linear-gradient(45deg, rgba(0, 255, 136, 0.2), rgba(0, 255, 255, 0.2));
            border: 1px solid rgba(0, 255, 255, 0.5);
            border-radius: 25px;
            color: #e0f7ff;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 5px;
        }

        .bio-button:hover {
            background: linear-gradient(45deg, rgba(0, 255, 136, 0.4), rgba(0, 255, 255, 0.4));
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.6), inset 0 0 10px rgba(0, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Neural Connection Lines */
        .connection-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .neural-connection {
            stroke: #00ff88;
            stroke-width: 1;
            fill: none;
            opacity: 0.4;
            stroke-dasharray: 5, 5;
            animation: dash-flow 2s linear infinite;
        }

        @keyframes dash-flow {
            to { stroke-dashoffset: -10; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .data-nodes {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Circuit Background -->
    <svg class="circuit-bg" id="circuitBg">
        <defs>
            <radialGradient id="pulseGradient">
                <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#00ffff;stop-opacity:0" />
            </radialGradient>
        </defs>
    </svg>

    <!-- Neural Connection Lines -->
    <svg class="connection-svg" id="connectionSvg"></svg>

    <div class="neural-container">
        <!-- Neural Core Header -->
        <div class="neural-core">
            <h1 class="core-title">NEURAL CIRCUIT INTERFACE</h1>
            <p style="opacity: 0.7; margin-top: 10px;">Living Data Explorer v3.5</p>
        </div>

        <div class="data-grid">
            <!-- Synaptic Input Panel -->
            <div class="synaptic-panel">
                <h2 class="panel-title">Synaptic Input</h2>
                
                <div style="position: relative;">
                    <input type="text" class="neural-input" placeholder="Neural Query..." id="queryInput">
                    <div class="input-synapse"></div>
                </div>
                
                <div style="position: relative;">
                    <input type="number" class="neural-input" placeholder="Threshold Level..." id="thresholdInput">
                    <div class="input-synapse"></div>
                </div>
                
                <div style="position: relative;">
                    <select class="neural-input" id="modeSelect">
                        <option>Exploration Mode</option>
                        <option>Learning Mode</option>
                        <option>Synthesis Mode</option>
                        <option>Evolution Mode</option>
                    </select>
                    <div class="input-synapse"></div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="bio-button" onclick="initiateScan()">Initiate Scan</button>
                    <button class="bio-button" onclick="evolveNetwork()">Evolve</button>
                </div>

                <div class="learning-indicator" style="margin-top: 20px;">
                    <span style="font-size: 0.9em;">Learning</span>
                    <div class="synapse-strength">
                        <div class="strength-fill" id="learningBar"></div>
                    </div>
                    <span style="font-size: 0.9em;" id="learningPercent">0%</span>
                </div>
            </div>

            <!-- Central Neural Explorer -->
            <div class="neural-explorer">
                <h2 class="panel-title">Neural Network Explorer</h2>
                
                <div class="data-nodes" id="dataNodes">
                    <div class="data-node" data-node="1">
                        <div class="node-value">87.3</div>
                        <div class="node-label">Synapse α</div>
                    </div>
                    <div class="data-node" data-node="2">
                        <div class="node-value">142.7</div>
                        <div class="node-label">Neuron β</div>
                    </div>
                    <div class="data-node" data-node="3">
                        <div class="node-value">56.9</div>
                        <div class="node-label">Dendrite γ</div>
                    </div>
                    <div class="data-node" data-node="4">
                        <div class="node-value">203.1</div>
                        <div class="node-label">Axon δ</div>
                    </div>
                    <div class="data-node" data-node="5">
                        <div class="node-value">178.4</div>
                        <div class="node-label">Cortex ε</div>
                    </div>
                    <div class="data-node" data-node="6">
                        <div class="node-value">92.6</div>
                        <div class="node-label">Signal ζ</div>
                    </div>
                </div>

                <div style="margin-top: 30px; text-align: center;">
                    <div style="display: inline-block; margin: 0 10px;">
                        <small style="opacity: 0.7;">Network Health</small>
                        <div style="font-size: 1.5em; color: #00ff88; margin-top: 5px;">
                            <span id="networkHealth">98.7</span>%
                        </div>
                    </div>
                    <div style="display: inline-block; margin: 0 10px;">
                        <small style="opacity: 0.7;">Active Synapses</small>
                        <div style="font-size: 1.5em; color: #00ffff; margin-top: 5px;">
                            <span id="activeSynapses">1,247</span>
                        </div>
                    </div>
                    <div style="display: inline-block; margin: 0 10px;">
                        <small style="opacity: 0.7;">Evolution Stage</small>
                        <div style="font-size: 1.5em; color: #ff00ff; margin-top: 5px;">
                            <span id="evolutionStage">7</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Monitor -->
            <div class="activity-monitor">
                <h2 class="panel-title">Neural Activity</h2>
                
                <div class="signal-wave">
                    <svg width="100%" height="100%" id="waveform">
                        <path class="wave-path" id="wavePath"></path>
                    </svg>
                </div>

                <div class="learning-indicator">
                    <span>Alpha</span>
                    <div class="synapse-strength">
                        <div class="strength-fill" style="width: 75%;"></div>
                    </div>
                </div>
                
                <div class="learning-indicator">
                    <span>Beta</span>
                    <div class="synapse-strength">
                        <div class="strength-fill" style="width: 60%;"></div>
                    </div>
                </div>
                
                <div class="learning-indicator">
                    <span>Gamma</span>
                    <div class="synapse-strength">
                        <div class="strength-fill" style="width: 85%;"></div>
                    </div>
                </div>
                
                <div class="learning-indicator">
                    <span>Delta</span>
                    <div class="synapse-strength">
                        <div class="strength-fill" style="width: 45%;"></div>
                    </div>
                </div>

                <button class="bio-button" style="width: 100%; margin-top: 20px;" onclick="recalibrateSignals()">
                    Recalibrate Signals
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize circuit background
        function createCircuitBackground() {
            const svg = document.getElementById('circuitBg');
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            // Create random circuit paths
            for (let i = 0; i < 15; i++) {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const startX = Math.random() * width;
                const startY = Math.random() * height;
                const endX = Math.random() * width;
                const endY = Math.random() * height;
                const midX = (startX + endX) / 2 + (Math.random() - 0.5) * 200;
                const midY = (startY + endY) / 2 + (Math.random() - 0.5) * 200;
                
                path.setAttribute('d', `M${startX},${startY} Q${midX},${midY} ${endX},${endY}`);
                path.setAttribute('class', 'circuit-path');
                svg.appendChild(path);
                
                // Add traveling pulses
                if (Math.random() > 0.5) {
                    createPulse(path);
                }
            }
        }

        // Create traveling pulse animation
        function createPulse(path) {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('r', '4');
            circle.setAttribute('class', 'pulse');
            
            const animate = document.createElementNS('http://www.w3.org/2000/svg', 'animateMotion');
            animate.setAttribute('dur', `${3 + Math.random() * 4}s`);
            animate.setAttribute('repeatCount', 'indefinite');
            
            const mpath = document.createElementNS('http://www.w3.org/2000/svg', 'mpath');
            mpath.setAttributeNS('http://www.w3.org/1999/xlink', 'href', '#' + path.id);
            
            animate.appendChild(mpath);
            circle.appendChild(animate);
            path.parentNode.appendChild(circle);
        }

        // Draw neural connections
        function drawConnections() {
            const svg = document.getElementById('connectionSvg');
            const nodes = document.querySelectorAll('.data-node');
            
            nodes.forEach((node, i) => {
                nodes.forEach((targetNode, j) => {
                    if (i < j && Math.random() > 0.6) {
                        const rect1 = node.getBoundingClientRect();
                        const rect2 = targetNode.getBoundingClientRect();
                        
                        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                        line.setAttribute('x1', rect1.left + rect1.width / 2);
                        line.setAttribute('y1', rect1.top + rect1.height / 2);
                        line.setAttribute('x2', rect2.left + rect2.width / 2);
                        line.setAttribute('y2', rect2.top + rect2.height / 2);
                        line.setAttribute('class', 'neural-connection');
                        svg.appendChild(line);
                    }
                });
            });
        }

        // Animate waveform
        function animateWaveform() {
            const path = document.getElementById('wavePath');
            const width = path.parentElement.clientWidth;
            const height = path.parentElement.clientHeight;
            
            let d = `M0,${height/2}`;
            for (let x = 0; x < width; x += 5) {
                const y = height/2 + Math.sin((x + Date.now() * 0.002) * 0.02) * 30 * Math.random();
                d += ` L${x},${y}`;
            }
            
            path.setAttribute('d', d);
            requestAnimationFrame(animateWaveform);
        }

        // Update node values with organic variation
        function updateNodeValues() {
            const nodes = document.querySelectorAll('.node-value');
            nodes.forEach(node => {
                const currentValue = parseFloat(node.textContent);
                const change = (Math.random() - 0.5) * 10;
                const newValue = Math.max(0, currentValue + change);
                node.textContent = newValue.toFixed(1);
                
                // Color based on value
                const hue = (newValue / 300) * 120; // Green to red
                node.style.color = `hsl(${120 - hue}, 100%, 50%)`;
            });
        }

        // Evolve network
        function evolveNetwork() {
            const stage = document.getElementById('evolutionStage');
            const currentStage = parseInt(stage.textContent);
            stage.textContent = currentStage + 1;
            
            // Trigger visual evolution
            document.querySelectorAll('.data-node').forEach((node, i) => {
                setTimeout(() => {
                    node.style.transform = 'scale(1.2)';
                    node.style.boxShadow = '0 0 50px rgba(255, 0, 255, 0.8)';
                    setTimeout(() => {
                        node.style.transform = 'scale(1)';
                        node.style.boxShadow = '';
                    }, 300);
                }, i * 100);
            });
            
            // Update learning bar
            const learningBar = document.getElementById('learningBar');
            const learningPercent = document.getElementById('learningPercent');
            const newPercent = Math.min(100, parseInt(learningPercent.textContent) + 15);
            learningBar.style.width = newPercent + '%';
            learningPercent.textContent = newPercent + '%';
        }

        // Initiate scan
        function initiateScan() {
            const queryInput = document.getElementById('queryInput').value;
            console.log('Initiating neural scan for:', queryInput);
            
            // Visual feedback
            document.querySelectorAll('.circuit-path').forEach(path => {
                path.style.opacity = '0.8';
                path.style.strokeWidth = '3';
                setTimeout(() => {
                    path.style.opacity = '0.3';
                    path.style.strokeWidth = '2';
                }, 1000);
            });
            
            // Update values
            updateNodeValues();
            
            // Update active synapses
            const synapses = document.getElementById('activeSynapses');
            synapses.textContent = (parseInt(synapses.textContent.replace(',', '')) + Math.floor(Math.random() * 100)).toLocaleString();
        }

        // Recalibrate signals
        function recalibrateSignals() {
            document.querySelectorAll('.strength-fill').forEach(fill => {
                const newWidth = Math.random() * 100;
                fill.style.width = newWidth + '%';
            });
            
            // Update network health
            const health = document.getElementById('networkHealth');
            health.textContent = (90 + Math.random() * 10).toFixed(1);
        }

        // Input intelligence - adapt based on user patterns
        document.querySelectorAll('.neural-input').forEach(input => {
            input.addEventListener('input', function() {
                // Simulate learning from input
                const learningBar = document.getElementById('learningBar');
                const currentWidth = parseInt(learningBar.style.width) || 0;
                learningBar.style.width = Math.min(100, currentWidth + 1) + '%';
                document.getElementById('learningPercent').textContent = learningBar.style.width;
            });
        });

        // Node interaction
        document.querySelectorAll('.data-node').forEach(node => {
            node.addEventListener('click', function() {
                this.style.animation = 'pulse-glow 0.5s ease';
                setTimeout(() => {
                    this.style.animation = '';
                }, 500);
                
                // Create ripple effect
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(0, 255, 255, 0.5)';
                ripple.style.transform = 'translate(-50%, -50%)';
                ripple.style.pointerEvents = 'none';
                ripple.style.animation = 'expand 1s ease-out';
                
                const rect = this.getBoundingClientRect();
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                
                this.appendChild(ripple);
                setTimeout(() => ripple.remove(), 1000);
            });
        });

        // Add expanding animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes expand {
                to {
                    width: 200px;
                    height: 200px;
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize
        createCircuitBackground();
        setTimeout(drawConnections, 100);
        animateWaveform();
        
        // Continuous updates
        setInterval(updateNodeValues, 3000);
        setInterval(() => {
            const health = document.getElementById('networkHealth');
            const currentHealth = parseFloat(health.textContent);
            const variation = (Math.random() - 0.5) * 2;
            health.textContent = Math.max(90, Math.min(100, currentHealth + variation)).toFixed(1);
        }, 2000);

        // Assign unique IDs to circuit paths for pulse animation
        document.querySelectorAll('.circuit-path').forEach((path, i) => {
            path.id = `circuit-path-${i}`;
        });
    </script>
</body>
</html>